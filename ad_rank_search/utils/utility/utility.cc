#include "teams/ad/ad_rank_search/utils/utility/utility.h"

#include <stdlib.h>

#include <unordered_set>
#include <unordered_map>
#include <utility>
#include <ctime>
#include "base/time/timestamp.h"
#include "absl/strings/numbers.h"

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "absl/strings/str_join.h"
#include "base/encoding/base64.h"
#include "base/strings/string_util.h"
#include "google/protobuf/repeated_field.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"
#include "teams/ad/ad_base/src/common/multi_retrieval_tag.h"
#include "teams/ad/ad_base/src/pos_manager/pos_manager_util.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/service/ad_predict_service.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_rank_search/common/ad_common.h"
#include "teams/ad/ad_rank_search/common/context_data.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf.h"
#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"
#include "teams/ad/engine_base/knews_pos_util/knews_pos_util.h"
#include "teams/ad/engine_base/p2p_cache_loader/universe_phy_pos_aggr.h"
#include "teams/ad/ad_base/src/perf/perf.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/ad_rank_search/processor/framework/ad_context_wrapper.h"

DECLARE_bool(enable_no_diff_check);

extern const char *__progname;  // NOLINT
namespace ks {
namespace ad_rank {
namespace utility {
using kuaishou::ad::ContextPhotoCommonAttr;
using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::CommonTypeEnum;
using kuaishou::ad::NodeStatus;

static std::unordered_map<int64_t, int64_t> feas_map = {
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_SHOW_CNT,
      ContextInfoCommonAttr::Q_1D_SHOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_CLICK_CNT,
      ContextInfoCommonAttr::Q_1D_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_PLAY_CNT,
      ContextInfoCommonAttr::Q_1D_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_LONG_VIEW_CNT,
      ContextInfoCommonAttr::Q_1D_LONG_VIEW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_FIRST_CLICK_CNT,
      ContextInfoCommonAttr::Q_1D_FIRST_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_FOLLOW_CNT,
      ContextInfoCommonAttr::Q_1D_FOLLOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_LIKE_CNT,
      ContextInfoCommonAttr::Q_1D_LIKE_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_PV,
      ContextInfoCommonAttr::Q_1D_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_CTR,
      ContextInfoCommonAttr::Q_1D_CTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_1D_LVTR,
      ContextInfoCommonAttr::Q_1D_LVTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_SHOW_CNT,
      ContextInfoCommonAttr::Q_7D_SHOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_CLICK_CNT,
      ContextInfoCommonAttr::Q_7D_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_PLAY_CNT,
      ContextInfoCommonAttr::Q_7D_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_LONG_VIEW_CNT,
      ContextInfoCommonAttr::Q_7D_LONG_VIEW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_FIRST_CLICK_CNT,
      ContextInfoCommonAttr::Q_7D_FIRST_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_FOLLOW_CNT,
      ContextInfoCommonAttr::Q_7D_FOLLOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_LIKE_CNT,
      ContextInfoCommonAttr::Q_7D_LIKE_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_PV,
      ContextInfoCommonAttr::Q_7D_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_CTR,
      ContextInfoCommonAttr::Q_7D_CTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::Q_7D_LVTR,
      ContextInfoCommonAttr::Q_7D_LVTR},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_1W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_1W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_10W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_10W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_100W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_100W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CLUSTER_ID_1000W,
      ContextInfoCommonAttr::QUERY_CLUSTER_ID_1000W},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UV_1D,
      ContextInfoCommonAttr::DUP_UV_1D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UV_7D,
      ContextInfoCommonAttr::DUP_UV_7D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UV_30D,
      ContextInfoCommonAttr::DUP_UV_30D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UQV_1D,
      ContextInfoCommonAttr::DUP_UQV_1D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UQV_7D,
      ContextInfoCommonAttr::DUP_UQV_7D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::DUP_UQV_30D,
      ContextInfoCommonAttr::DUP_UQV_30D},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::COMBO_SEARCH_SOURCE,
      ContextInfoCommonAttr::COMBO_SEARCH_SOURCE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::IS_FRESHNESS_SEARCH,
      ContextInfoCommonAttr::IS_FRESHNESS_SEARCH},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::CATEGORY_ID_LIST,
      ContextInfoCommonAttr::CATEGORY_ID_LIST},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_TYPE,
      ContextInfoCommonAttr::QUERY_TYPE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::PIC_INTENT,
      ContextInfoCommonAttr::PIC_INTENT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::TF_IDF,
      ContextInfoCommonAttr::TF_IDF},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::INTENT_AUTHOR_NAME,
      ContextInfoCommonAttr::INTENT_AUTHOR_NAME},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::VERTICAL_SOURCE,
      ContextInfoCommonAttr::VERTICAL_SOURCE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::IS_EPIDEMIC_SEARCH,
      ContextInfoCommonAttr::IS_EPIDEMIC_SEARCH},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SHOW_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SHOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_FOLLOW_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_FOLLOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_LIKE_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_LIKE_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT,
      ContextInfoCommonAttr::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT,
      ContextInfoCommonAttr::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_SHORTPLAY,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_DO_NOTHING_SHORTPLAY,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_DO_NOTHING_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_OR_FOLLOW_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_OR_FOLLOW_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_NO_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_NO_COMP_NO_LIKE,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_NO_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_EARLY_SKIPED_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_EARLY_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_MID_SKIPED_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_MID_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_MIN_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_MAX_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_MIN_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_COMP_MAX_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_COMP_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_BAD_CLICK_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_BAD_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_ABNORM_PLAY_NUM,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_ABNORM_PLAY_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          QUERY_STAT_FEATURE_DC_FLAG,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_DC_FLAG},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_STAT_FEATURE_PV,
      ContextInfoCommonAttr::QUERY_STAT_FEATURE_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SHOW_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SHOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_FOLLOW_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_FOLLOW_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_LIKE_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_LIKE_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT,
      ContextInfoCommonAttr::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT,
      ContextInfoCommonAttr::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_COMPLETE_PLAY_NOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SEARCH_PHOTO_PLAY_DURATION},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_SHORTPLAY,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_DO_NOTHING_SHORTPLAY,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_DO_NOTHING_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_OR_FOLLOW_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_OR_FOLLOW_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_FOLLOW_OR_LIKE_SHORTPLAY},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_NO_FOLLOW,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_NO_FOLLOW},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_NO_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_NO_COMP_NO_LIKE,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_NO_COMP_NO_LIKE},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_ABNORM_REPEAT_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_EARLY_SKIPED_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_EARLY_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_MID_SKIPED_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_MID_SKIPED_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_MIN_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_MAX_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_MIN_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_MIN_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_COMP_MAX_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_COMP_MAX_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_BAD_CLICK_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_BAD_CLICK_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_ABNORM_PLAY_NUM,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_ABNORM_PLAY_NUM},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          NGRAM_STAT_FEATURE_DC_FLAG,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_DC_FLAG},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::NGRAM_STAT_FEATURE_PV,
      ContextInfoCommonAttr::NGRAM_STAT_FEATURE_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::PICSET_QUERY_CONSUM_PV,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_PV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::PICSET_QUERY_CONSUM_UV,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_UV},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_SHOW_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_SHOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_PLAY_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_LONG_PLAY_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_LONG_PLAY_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_CLICK_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_CLICK_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_FOLLOW_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_FOLLOW_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_DOWNLOAD_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_DOWNLOAD_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_DOWNLOAD_FIRST_FRAME_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_DOWNLOAD_FIRST_FRAME_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_AVG_PLAY_DURATION,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_AVG_PLAY_DURATION},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_LIKE_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_LIKE_CNT},
    {::kuaishou::ad::search_ads::QueryFeaItemInfo::
          PICSET_QUERY_CONSUM_DOWNLOAD_CLICK_CNT,
      ContextInfoCommonAttr::PICSET_QUERY_CONSUM_DOWNLOAD_CLICK_CNT}
    };
  static std::unordered_map<int64_t, int64_t> feas_map_str = {
      {::kuaishou::ad::search_ads::QueryFeaItemInfo::TF_IDF,
       ContextInfoCommonAttr::TF_IDF},
      {::kuaishou::ad::search_ads::QueryFeaItemInfo::INTENT_AUTHOR_NAME,
       ContextInfoCommonAttr::INTENT_AUTHOR_NAME},
      {::kuaishou::ad::search_ads::QueryFeaItemInfo::VERTICAL_SOURCE,
       ContextInfoCommonAttr::VERTICAL_SOURCE}};

// 临时改成全局函数
void InnerFillHeritageRelation(AdList* ad_list, ContextData* session_data_) {
  bool enable_unit_to_unit_heritage = true;
  // 每个老账户下 impression 最多的 creative
  std::unordered_map<int64_t, std::pair<int64_t, int64_t>> parent_unit_stats;
  if (!enable_unit_to_unit_heritage) {
    return;
  }
  if (ad_list == nullptr) {
    return;
  }
  // 填充继承账户信息
  for (auto* p_ad : ad_list->Ads()) {
    // 限定内循环硬广短视频
    if (!(p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD &&
        p_ad->Is(AdFlag::is_inner_loop_ad))) {
      continue;
    }
    int64_t parent_unit_id = p_ad->get_extends_unit_id();
    if (parent_unit_id > 0) {
      int64_t creative_id = p_ad->get_creative_id();
      int64_t impression = p_ad->get_unit_ad_counter_item_impression();
      impression = p_ad->get_unit_ad_counter_item_impression_ad_feature();
      auto iter = parent_unit_stats.find(parent_unit_id);
      if (iter == parent_unit_stats.end() || impression > iter->second.second) {
        parent_unit_stats[parent_unit_id] = std::make_pair(creative_id, impression);
      }
    }
  }
  int heritage_relation_count = 0;
  double power_base =
        session_data_->get_spdm_ctx().TryGetDouble("account_heritage_power_base", 0.9999);
  for (auto* p_ad : ad_list->Ads()) {
    SEARCH_AD_LIST_SKIP_LIVE_AD(session_data_, p_ad)
    SEARCH_AD_LIST_SKIP_FANSTOP(session_data_, p_ad)
    AD_LIST_SKIP_LIVE_AD(session_data_, p_ad)
    int64_t parent_unit_id = p_ad->get_extends_unit_id();
    if (parent_unit_id > 0) {
      p_ad->set_heritage_creative_id(parent_unit_stats[parent_unit_id].first);
      int64_t impression = p_ad->get_unit_ad_counter_item_impression();
      impression = p_ad->get_unit_ad_counter_item_impression_ad_feature();
      p_ad->set_heritage_ratio(std::pow(power_base, impression));
      LOG_EVERY_N(INFO, 100000) << "find heritage relation: " << p_ad->get_heritage_creative_id()
                                << "creative id" << p_ad->get_creative_id()
                                << "ratio: " << p_ad->get_heritage_ratio();
      ++heritage_relation_count;
    }
  }
  RANK_DOT_COUNT(session_data_, heritage_relation_count, "ad_server.ranking_prepare.heritage_relation_count");
}

bool IsRedisEnable(const std::string& redis_name) {
  auto iter = RankKconfUtil::redisDropdownMap()->find(redis_name);
  if (iter != RankKconfUtil::redisDropdownMap()->end() && iter->second) {
    return true;
  }

  ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_rank", "adrank_redis_drop_down", redis_name);
  return false;
}

double GetBidServerRatio(AdCommon *p_ad) {
  double ratio = 1.0;
  if (p_ad->get_deep_conversion_type() == 0) {
    if (p_ad->get_cpa_bid() > 0) {
      ratio = static_cast<double>(p_ad->get_auto_cpa_bid()) / p_ad->get_cpa_bid();
    } else {
      if (p_ad->Is(AdFlag::is_hard_ad_roi) &&
            p_ad->get_auto_roas() > 0 && p_ad->get_roi_ratio() > 0) {
        ratio = static_cast<double>(p_ad->get_roi_ratio()) / p_ad->get_auto_roas();
      } else if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_ROAS &&
              p_ad->get_auto_roas() > 0 && p_ad->get_roi_ratio() > 0) {
        ratio = static_cast<double>(p_ad->get_roi_ratio()) / p_ad->get_auto_roas();
      }
    }
  } else if (p_ad->get_deep_conversion_type() ==
                kuaishou::ad::AdCallbackLog_EventType_EVENT_PAY_FIRST_DAY &&
            p_ad->get_auto_roas() > 0 && p_ad->get_roi_ratio() > 0) {
    ratio = static_cast<double>(p_ad->get_roi_ratio()) / p_ad->get_auto_roas();
  }
  return ratio;
}

bool StrTimeCompare(const std::string& limit_time, const int64& time_now) {
  tm tm_limit;
  strptime(limit_time.c_str(), "%Y-%m-%d %H:%M:%S", &tm_limit);
  auto t_l = mktime(&tm_limit);
  if (time_now < t_l) {
    return true;
  }
  return false;
}

bool IsFanstopAutoBidLiExp(AdCommon *p_ad) {
  if (RankKconfUtil::enableAutobidliExpAll()) {
    return true;
  }

  using kuaishou::ad::AdEnum;
  const auto& account_type = p_ad->get_account_type();
  const auto& campaign_type = p_ad->get_campaign_type();
  const auto& ocpx_action_type = p_ad->get_ocpx_action_type();
  const auto& bid_type = static_cast<AdEnum::BidType>(p_ad->get_bid_type());
  const auto& bid_strategy = p_ad->get_bid_strategy();
  const auto& live_launch_type =
                static_cast<AdEnum::FansTopLiveLaunchType>(p_ad->get_live_launch_type());

  if (account_type == AdEnum::ACCOUNT_ESP_MOBILE) {
    const std::string& campaign_type_str =
                      kuaishou::ad::AdEnum_CampaignType_Name(campaign_type);
    const std::string& ocpx_action_type_str = kuaishou::ad::AdActionType_Name(ocpx_action_type);
    const std::string& bid_strategy_str = kuaishou::ad::AdEnum_BidStrategy_Name(bid_strategy);
    uint64_t key_tail_id;
    std::string white_key;
    if (bid_type == AdEnum::OCPM_DSP
        && bid_strategy == AdEnum::SMART_BID_STRATEGY
        && live_launch_type == AdEnum::LIVE_AND_USER_DEFINE_PHOTO) {
      // 移动端混投
      key_tail_id = p_ad->get_campaign_id();
      white_key = absl::Substitute("$0_$1_$2_$3",
                  campaign_type_str, ocpx_action_type_str, bid_strategy_str, "LIVE_AND_USER_DEFINE_PHOTO");
    } else {
      // 移动端非混投
      key_tail_id = p_ad->get_unit_id();
      white_key = absl::Substitute("$0_$1_$2", campaign_type_str, ocpx_action_type_str, bid_strategy_str);
    }

    // unit 测试
    if (RankKconfUtil::mobileAutoBidLiUnitSet()->count(key_tail_id) > 0
        || RankKconfUtil::mobileAutoBidLiCampaignSet()->count(key_tail_id) > 0) {
      return true;
    }
    // campaign_type + ocpc_action_type + bid_strategy + unit_tail 实验
    const auto ocpx_unit_tail_map = RankKconfUtil::mobileAutoBidLiOcpxUnitTailMap();
    const auto iter = ocpx_unit_tail_map->data().value_map().find(white_key);
    if (iter != ocpx_unit_tail_map->data().value_map().end()) {
      const auto &unit_tail_set = iter->second.unit_tail_set();
      if (std::count(unit_tail_set.begin(), unit_tail_set.end(), key_tail_id % 100)) {
        ks::ad_base::AdPerf::CountLogStash(1, "ad.adRank", "FanstopAutoBidLi",
                                           campaign_type_str, ocpx_action_type_str, bid_strategy_str);
        return true;
      }
    }
  }
  // 粉条兼容
  bool is_biz_fanstop
      = RankKconfUtil::enableBizFanstopAutobidliExpRanksvr()
        && account_type == kuaishou::ad::AdEnum::ACCOUNT_CPC
        && IsFanstopCampaign(campaign_type);
  if (account_type == kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2 || is_biz_fanstop) {
    auto unit_id = p_ad->get_unit_id();
    const std::string& order_traffic_type =
        (campaign_type == kuaishou::ad::AdEnum_CampaignType_AD_FANSTOP_TO_ALL
         || campaign_type == kuaishou::ad::AdEnum_CampaignType_AD_FANSTOP_TO_FANS
         || campaign_type == kuaishou::ad::AdEnum_CampaignType_AD_FANSTOP_TO_SHOW)
        ? "PHOTO" : "LIVE";
    const std::string& bid_strategy_str
      = kuaishou::ad::AdEnum_BidStrategy_Name(bid_strategy);
    const std::string& ocpx_action_type_str
      = kuaishou::ad::AdActionType_Name(ocpx_action_type);
    auto check_exp = [&](const std::string& key, uint64_t id) {
      const auto& config = RankKconfUtil::fanstopv2AutoBidLiOcpxUnitTailMap();
      const auto iter = config->data().value_map().find(key);
      if (iter != config->data().value_map().end()) {
        const auto &unit_tail_set = iter->second.unit_tail_set();
        if ((std::count(unit_tail_set.begin(), unit_tail_set.end(), id % 100) > 0) ||
            (std::count(unit_tail_set.begin(), unit_tail_set.end(), id) > 0)) {
          ks::ad_base::AdPerf::CountLogStash(1, "ad.ad_rank", "FanstopV2AutoBidLiExp",
                                             key);
          return true;
        }
      }
      return false;
    };
    // PHOTO/LIVE + ocpx + bid_strategy, unit_tail 实验
    std::string white_key = absl::StrCat(order_traffic_type,
                                         "_", bid_strategy_str,
                                         "_", ocpx_action_type_str);
    if (check_exp(white_key, unit_id)) {
      return true;
    } else {
      std::string white_key_default = absl::StrCat(order_traffic_type, "_", bid_strategy_str, "_DEFAULT");
      return check_exp(white_key_default, unit_id);
    }
  }
  return false;
}

void GetCouponDiscountRatio(AdCommon *p_ad, ContextData* session_data_) {
  auto pec_coupon_config = RankKconfUtil::pecCouponDiscountConfig();
  std::string exp_tag = SPDM_pec_coupon_strategy_exp_tag(session_data_->get_spdm_ctx());
  if (session_data_->get_is_rewarded() &&
       SPDM_enable_optimal_deep_rewarded_coin(session_data_->get_spdm_ctx())) {
    // 激励视频下，区分营销目标
    pec_coupon_config = RankKconfUtil::DeepRewardedCoinConfig();
    exp_tag = absl::StrCat(SPDM_rewarded_deep_coin_exp_tag(session_data_->get_spdm_ctx()),
            "_", kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type()));
  }
  auto& pec_coupon_conf = pec_coupon_config->data().exp_confs();
  std::string keystr = "";
  std::string key_string_debug = "";
  // 解析特征参数
  if (pec_coupon_conf.find(exp_tag) == pec_coupon_conf.end()) {
    return;
  }
  auto& exp_coupon_param = pec_coupon_conf.find(exp_tag)->second;
  std::unordered_map<std::string, int32_t> coupon_tag_dim;
  if (exp_coupon_param.tag_size()  > 0) {
    for (int i = 0; i < exp_coupon_param.tag_size(); ++i) {
      auto k = exp_coupon_param.tag(i);
      auto v = exp_coupon_param.dim(i);
      coupon_tag_dim[k] = v;
    }
  }
  // 用户层级特征
  if (coupon_tag_dim.size() > 0 && coupon_tag_dim.count("user_level") > 0
    && coupon_tag_dim["user_level"] > 0) {
    auto buyer_effective_type =
        session_data_->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
    if (session_data_->get_rank_request()->ad_request().ad_user_info().has_user_level_v2_risk()) {
      buyer_effective_type =
           session_data_->get_rank_request()->ad_request().ad_user_info().user_level_v2_risk();
    }
    bool no_user_tag = !session_data_->get_rank_request()->ad_request().ad_user_info().has_buyer_effective_type() &&  // NOLINT
      !session_data_->get_rank_request()->ad_request().ad_user_info().has_user_level_v2_risk();
    int32_t cur_user = -1;
    if (buyer_effective_type == "u0-potential" || buyer_effective_type == "risk" || no_user_tag) {
      cur_user = 0;
    } else if (buyer_effective_type == "u0" || buyer_effective_type == "U0") {
      cur_user = 1;
    } else if (buyer_effective_type == "u1" || buyer_effective_type == "U1") {
      cur_user = 2;
     } else if (buyer_effective_type == "u2" || buyer_effective_type == "U2") {
      cur_user = 3;
     } else if (buyer_effective_type == "u3" || buyer_effective_type == "U3") {
      cur_user = 4;
    } else if (buyer_effective_type == "u4" || buyer_effective_type == "U4") {
      cur_user = 5;
    } else if (buyer_effective_type == "u4+" || buyer_effective_type == "U4+") {
      cur_user = 6;
    } else {
      cur_user = 7;
    }
    key_string_debug.append("user=").append(buyer_effective_type)
                    .append(";cur_user=").append(absl::StrCat(cur_user))
                    .append(";");
    keystr.append(absl::StrCat(cur_user)).append("_");
  }
  // 物料类型
  if (coupon_tag_dim.size() > 0 && coupon_tag_dim.count("item_type") > 0 && coupon_tag_dim["item_type"] > 0) {
      int32_t cur_item = -1;
      if (p_ad->Is(AdFlag::is_p2l_ad_inner)) {
        cur_item = 0;
      } else if (p_ad->Is(AdFlag::is_live_ad_inner)) {
        cur_item = 1;
      } else if (p_ad->Is(AdFlag::is_photo_ad_inner)) {
        cur_item = 2;
      } else {
        cur_item = 3;
      }
    key_string_debug.append("item=").append(kuaishou::ad::AdEnum_ItemType_Name(p_ad->get_item_type()))
                    .append(";cur_item=").append(absl::StrCat(cur_item))
                    .append(";");
    keystr.append(absl::StrCat(cur_item)).append("_");
  }
  // 转化目标
  if (coupon_tag_dim.size() > 0 && coupon_tag_dim.count("action_type") > 0
    && coupon_tag_dim["action_type"] > 0) {
    int32_t cur_action = -1;
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
      cur_action = 0;
    } else {
      cur_action  = 1;
    }
    key_string_debug.append("action=").append(
                    kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type()))
                    .append(";cur_action=").append(absl::StrCat(cur_action))
                    .append(";");
    keystr.append(absl::StrCat(cur_action)).append("_");
  }
  // 模型转化率
  if (coupon_tag_dim.size() > 0 && coupon_tag_dim.count("cvr") > 0 && coupon_tag_dim["cvr"] > 0) {
    double cvr = -1.0;
    int32_t cur_cvr = -1;
    if (p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::CID_ROAS ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_MERCHANT_T7_ROI ||
        p_ad->get_ocpx_action_type() == kuaishou::ad::AD_STOREWIDE_ROAS) {
      // 两段式下，直接使用 pcvr， 不再使用 pltv
      cvr = p_ad->get_unify_cvr_info().value;
      cur_cvr = std::lower_bound(exp_coupon_param.roas_cvr_bucket_list().begin(),
          exp_coupon_param.roas_cvr_bucket_list().end(), cvr)
          - exp_coupon_param.roas_cvr_bucket_list().begin();
    } else {
      cvr  = p_ad->get_unify_cvr_info().value;
       cur_cvr = std::lower_bound(exp_coupon_param.cvr_bucket_list().begin(),
         exp_coupon_param.cvr_bucket_list().end(), cvr) - exp_coupon_param.cvr_bucket_list().begin();
    }
    key_string_debug.append("cvr=").append(absl::StrCat(cvr))
                    .append(";cur_cvr=").append(absl::StrCat(cur_cvr))
                    .append(";");
    keystr.append(absl::StrCat(cur_cvr)).append("_");
  }
  std::string key = keystr.substr(0, keystr.length() - 1);
  auto& discount_config = exp_coupon_param.optimal_discount();
  if (discount_config.find(key) != discount_config.end()) {
    p_ad->set_pec_final_ratio(discount_config.find(key)->second);
  }
  auto& uplift_cvr_stat = exp_coupon_param.uplift_cvr_stat();
  if (uplift_cvr_stat.find(key) != uplift_cvr_stat.end()) {
    auto& uplift_cvrs = (uplift_cvr_stat.find(key)->second).uplift_cvrs();
    p_ad->mutable_pec_coupon_uplift_list()->assign(uplift_cvrs.begin(), uplift_cvrs.end());
  }
  LOG_EVERY_N(INFO, 1000000) << "pec coupon select ratio:" << key_string_debug;
}

bool IsMerchantMcb(AdCommon *p_ad) {
  // 单元白名单
  const auto unit_set = RankKconfUtil::merchantMcbUnitWhiteList();
  if (unit_set->count(p_ad->get_unit_id()) > 0) {
    return true;
  }
  // mcb 产品
  if (p_ad->get_simultaneous_optimization_selected()
      && RankKconfUtil::mcbOptActionTypeSet()->count(
         kuaishou::ad::AdActionType_Name(p_ad->get_simultaneous_optimization_type())) > 0) {
    return true;
  }
  // costcap 产品, 待添加
  const auto& mcb_ocpx_unit_tail_conf = RankKconfUtil::merchantMcbOcpxUnitTailMap();
  const auto& ocpx_unit_tails = mcb_ocpx_unit_tail_conf->data().value_map();
  std::string ocpx_action_type = kuaishou::ad::AdActionType_Name(p_ad->get_ocpx_action_type());
  std::string campaign_type = kuaishou::ad::AdEnum::CampaignType_Name(p_ad->get_campaign_type());
  // 分 campaign_type + ocpc_action_type 尾号白名单
  std::string key = campaign_type + "_" + ocpx_action_type;
  const auto iter1 = ocpx_unit_tails.find(key);
  if (iter1 != ocpx_unit_tails.end()) {
    const auto &unit_tail_set = iter1->second.unit_tail_set();
    if (std::count(unit_tail_set.begin(), unit_tail_set.end(), p_ad->get_unit_id() % 100)) {
      return true;
    }
  }
  // 分优化目标尾号白名单
  const auto iter = ocpx_unit_tails.find(ocpx_action_type);
  if (iter != ocpx_unit_tails.end()) {
    const auto &unit_tail_set = iter->second.unit_tail_set();
    if (std::count(unit_tail_set.begin(), unit_tail_set.end(), p_ad->get_unit_id() % 100)) {
      return true;
    }
  }
  return false;
}

// mcb 计算 auto_bid 逻辑，支持 costcap & mcb
int64_t GetMcbAutoCpaBid(AdCommon *p_ad, double ctr, double cvr, double gmv) {
  int64_t unit_id = p_ad->get_unit_id();
  kuaishou::ad::AdActionType opt_type = p_ad->get_simultaneous_optimization_type();
  kuaishou::ad::AdActionType ocpx_action_type = p_ad->get_ocpx_action_type();
  bool is_cost_cap = opt_type == kuaishou::ad::UNKNOWN_ACTION_TYPE;
  double p = p_ad->get_first_coef();
  double q = p_ad->get_second_coef();
  double bid = p_ad->get_cpa_bid();  // 单位：厘
  double auto_bid = bid;
  double opt_value = 0.0;  // 调价目标
  double bid_value = 0.0;  // 成本目标
  const auto alpha_config = is_cost_cap ? RankKconfUtil::amdCostCapAlphaConfig()
                                        : RankKconfUtil::amdMcbAlphaConfig();
  const auto beta_config = is_cost_cap ? RankKconfUtil::amdCostCapBetaConfig()
                                       : RankKconfUtil::amdMcbBetaConfig();
  auto GetAmdMcbParams = [&](kuaishou::ad::AdActionType action_type, double dft_value, bool is_alpha) {
    auto config = is_alpha ? alpha_config : beta_config;
    auto iter = config->find(kuaishou::ad::AdActionType_Name(action_type));
    if (iter != config->end()) {
      return iter->second;
    } else {
      return dft_value;
    }
  };
  double alpha = 1.0;
  double beta = 1.0;
  if (is_cost_cap) {
    alpha = GetAmdMcbParams(ocpx_action_type, RankKconfUtil::amdMcbAutoBidAlpha(), true);
    beta = GetAmdMcbParams(ocpx_action_type, RankKconfUtil::amdMcbAutoBidBeta(), false);
  } else {
    alpha = GetAmdMcbParams(opt_type, RankKconfUtil::amdMcbAutoBidAlpha(), true);
    beta = GetAmdMcbParams(opt_type, RankKconfUtil::amdMcbAutoBidBeta(), false);
  }
  double upper = is_cost_cap ? RankKconfUtil::amdCostCapAutoBidUpperBound()
                             : RankKconfUtil::amdMcbAutoBidUpperBound();
  double lower = is_cost_cap ? RankKconfUtil::amdCostCapAutoBidLowerBound()
                             : RankKconfUtil::amdMcbAutoBidLowerBound();
  if ((p + q) > 0.000001 && ctr > 0.000001 && cvr > 0.000001) {
    switch (opt_type) {
      case kuaishou::ad::AD_MERCHANT_ROAS:
      case kuaishou::ad::AD_MERCHANT_T7_ROI:
      case kuaishou::ad::AD_STOREWIDE_ROAS:
      case kuaishou::ad::EVENT_ORDER_PAIED:
      case kuaishou::ad::CID_EVENT_ORDER_PAID:
      case kuaishou::ad::CID_ROAS:
        opt_value = ctr * gmv;
        break;
      // cost cap
      default:
        opt_value = ctr * cvr * bid;
        break;
    }
    bid_value = ctr * cvr;
    auto_bid = (alpha * opt_value + beta * q * bid * bid_value) / (p + q) / bid_value;
  }
  if (is_cost_cap && (p + q) > 0.000001) {
    double p_upper = RankKconfUtil::amdCostCapPUpperBound();
    double p_lower = RankKconfUtil::amdCostCapPLowerBound();
    p = std::min(std::max(p, p_lower), p_upper);
    auto_bid = beta * (1 + q) / (p + q) * bid;
  }
  auto_bid = static_cast<int64>(std::min(std::max(auto_bid, bid * lower), bid * upper));
  if (ad_base::AdRandom::GetDouble() < RankKconfUtil::amdMcbPurfRatio()) {
    ks::ad_base::AdPerf::SetLogStash(p * 100000, "ad.fanstop_server", "cost_cap_p_rank",
                                     std::to_string(unit_id));
    ks::ad_base::AdPerf::SetLogStash(q * 100000, "ad.fanstop_server", "cost_cap_q_rank",
                                     std::to_string(unit_id));
    ks::ad_base::AdPerf::SetLogStash(bid * 1000, "ad.fanstop_server", "cost_cap_bid",
                                     std::to_string(unit_id));
    ks::ad_base::AdPerf::SetLogStash(auto_bid * 1000, "ad.fanstop_server", "cost_cap_auto_bid",
                                     std::to_string(unit_id));
  }
  LOG_EVERY_N(INFO, 1000) << "MerchantMcb, unit_id: " << unit_id
                          << " promotion_type: " << p_ad->get_promotion_type()
                          << " auto_bid:" << auto_bid
                          << " is_cost_cap:" << is_cost_cap
                          << " ocpx_action_type:" << ocpx_action_type
                          << " opt_type:" << opt_type << " p:" << p << " q:" << q
                          << " opt_value:" << opt_value << " bid_value:" << bid_value
                          << " bid:" << bid << " alpha:" << alpha
                          << " beta:" << beta << " lower:" << bid * lower
                          << " upper:" << bid * upper
                          << " ctr:" << ctr << " cvr:" << cvr << " gmv:" << gmv;
  return auto_bid;
}

std::string VecIntToStr(const std::vector<int32_t> *p_vec) {
  if (0 >= p_vec->size()) {
    return "";
  }
  std::stringstream ss;
  ss << (*p_vec)[0];
  for (int i = 1; i < p_vec->size(); i++) {
    ss << "_" << (*p_vec)[i];
  }
  return ss.str();
}

bool StringToDoubleVec(const std::string &s, std::vector<double> *p_result, const std::string &sep) {
  if (!p_result) {
    return false;
  }
  std::vector<std::string> items = absl::StrSplit(s, sep);
  if (items.size() == 0) {
    return false;
  }
  p_result->clear();
  for (auto &item : items) {
    double value = .0;
    if (absl::SimpleAtod(item, &value)) {
      p_result->emplace_back(value);
    }
  }
  return true;
}

using kuaishou::ad::ContextPhotoCommonAttr;
using kuaishou::ad::ContextInfoCommonAttr;
using kuaishou::ad::CommonTypeEnum;

void AddPhotoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                        google::protobuf::int64 name, int value) {
  auto mmu_audio_cluster = ps_request->mutable_context()->add_photo_common_attr();
  mmu_audio_cluster->set_name_value(name);
  mmu_audio_cluster->set_type(CommonTypeEnum::INT_ATTR);
  mmu_audio_cluster->set_int_value(value);
}

void AddPhotoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                        google::protobuf::int64 name, const std::string& value) {
  auto mmu_audio_cluster = ps_request->mutable_context()->add_photo_common_attr();
  mmu_audio_cluster->set_name_value(name);
  mmu_audio_cluster->set_type(CommonTypeEnum::STRING_ATTR);
  mmu_audio_cluster->set_string_value(value);
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                              google::protobuf::int64 name, const std::string& value) {
  auto int_common_attr = ps_request->mutable_context()->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::STRING_ATTR);
  int_common_attr->set_string_value(value);
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                              google::protobuf::int64 name, google::protobuf::int64 value) {
  auto int_common_attr = ps_request->mutable_context()->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::INT_ATTR);
  int_common_attr->set_int_value(value);
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                              google::protobuf::int64 name, google::protobuf::uint64 value) {
  auto int_common_attr = ps_request->mutable_context()->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::UINT64_ATTR);
  int_common_attr->set_uint64_value(value);
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                              google::protobuf::int64 name, float value) {
  auto int_common_attr = ps_request->mutable_context()->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::FLOAT_ATTR);
  int_common_attr->set_float_value(value);
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                              google::protobuf::int64 name, std::vector<int64_t> value) {
  auto int_list_common_attr = ps_request->mutable_context()->add_info_common_attr();
  auto int_list_value = int_list_common_attr->mutable_int_list_value();
  int size = value.size();
  int_list_value->Reserve(size);
  int_list_common_attr->set_name_value(name);
  int_list_common_attr->set_type(CommonTypeEnum::INT_LIST_ATTR);
  for (const auto& v : value) {
    int_list_value->Add(v);
  }
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                              google::protobuf::int64 name, const std::vector<float>& value) {
  auto float_list_common_attr = ps_request->mutable_context()->add_info_common_attr();
  auto float_list_value = float_list_common_attr->mutable_float_list_value();
  int size = value.size();
  float_list_value->Reserve(size);
  float_list_common_attr->set_name_value(name);
  float_list_common_attr->set_type(CommonTypeEnum::FLOAT_LIST_ATTR);
  for (const auto& v : value) {
    float_list_value->Add(v);
  }
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
    google::protobuf::int64 name, std::unordered_map<int64_t, std::vector<int64_t>> value) {
  auto* info_common_attr = ps_request->mutable_context()->add_info_common_attr();
  auto* map_int64_multi_value = info_common_attr->mutable_map_int64_multi_value();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_INT64_MULTI_ATTR);
  for (const auto& v : value) {
    for (const auto& item : v.second) {
      (*map_int64_multi_value)[v.first].add_int_value(item);
    }
  }
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
    google::protobuf::int64 name, std::unordered_map<int64_t, std::vector<int32_t>> value) {
  auto* info_common_attr = ps_request->mutable_context()->add_info_common_attr();
  auto* map_int64_multi_value = info_common_attr->mutable_map_int64_multi_value();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_INT64_MULTI_ATTR);
  for (const auto& v : value) {
    for (const auto& item : v.second) {
      (*map_int64_multi_value)[v.first].add_int_value(item);
    }
  }
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
   google::protobuf::int64 name, std::unordered_map<std::string, int32_t> value) {
  auto* info_common_attr = ps_request->mutable_context()->add_info_common_attr();
  auto* map_string_int64_value = info_common_attr->mutable_map_string_int64_value();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_STRING_INT64_ATTR);
  for (const auto& v : value) {
    (*map_string_int64_value)[v.first] = v.second;
  }
}

void AddContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
   google::protobuf::int64 name, std::unordered_map<int64_t, int64_t> value) {
  auto* info_common_attr = ps_request->mutable_context()->add_info_common_attr();
  auto* map_int64_int64_value = info_common_attr->mutable_map_int64_int64_value();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_INT64_INT64_ATTR);
  for (const auto& v : value) {
    (*map_int64_int64_value)[v.first] = v.second;
  }
}

void AddContextInfoCommonAttr(const ContextData& context,
                              const kuaishou::ad::AdRequest& request,
                              kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  int64_t user_id = request.ad_user_info().id();
  // map_int64_common_attr 为 map 结构, key : user_id / value : CommonInfoAttr 结构
  auto map_int64_common_attr = ps_request->mutable_context()->mutable_filter_info();

  // 新建一个空的 CommonInfoAttr, 其结构类型为 map, key : name_value, value : time
  kuaishou::ad::CommonInfoAttr map_int64_int64_common_attr;
  map_int64_int64_common_attr.set_name_value(kuaishou::ad::CommonInfoAttr_Name_UNIVERSE_USER_COLD);
  map_int64_int64_common_attr.set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);
  auto map_pxtr_val = map_int64_int64_common_attr.mutable_map_int64_int64_value();

  // 向 CommonInfoAttr 结构中塞入数据
  std::string user_cold_json("");
  if (request.ad_user_info().has_special_field() &&
          (request.ad_user_info().special_field().has_new_user_info())) {
      user_cold_json = request.ad_user_info().special_field().new_user_info();
  }
  Json llsid_json(StringToJson(user_cold_json));
  const auto last_ts = llsid_json.GetInt("impress_last_timestamp", 0);
  (*map_pxtr_val)[0] = last_ts;
  // 向 map_int64_common_attr 结构中塞入数据
  (*map_int64_common_attr)[user_id] = map_int64_int64_common_attr;

  // write ssp posid_map
  if (request.universe_ad_request_info().imp_info_size() > 0) {
    int64_t pos_id = request.universe_ad_request_info().imp_info().begin()->position_id();
    int64_t physical_position_id =
        request.universe_ad_request_info().imp_info().begin()->physical_position_id();

    // 新建一个空的 CommonInfoAttr, 其结构类型为 int64
    kuaishou::ad::CommonInfoAttr int64_common_attr;
    int64_common_attr.set_name_value(kuaishou::ad::CommonInfoAttr_Name_UNIVERSE_POS_ID_CLUSTER);
    int64_common_attr.set_type(kuaishou::ad::CommonTypeEnum_AttrType_UINT64_ATTR);
    int64_common_attr.set_int_value(physical_position_id);
    (*map_int64_common_attr)[pos_id] = int64_common_attr;
  }
}
void AddCmdContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                                bool is_disable) {
  auto uint_list_common_attr = ps_request->mutable_context()->add_info_common_attr();
  uint_list_common_attr->set_name_value(ContextInfoCommonAttr::RANK_CMDS);
  uint_list_common_attr->set_type(CommonTypeEnum::UINT64_LIST_ATTR);
  if (is_disable) {
    // 内循环屏蔽该特征
    uint_list_common_attr->add_uint64_list_value(0);
  } else {
    for (const auto& cmd : ps_request->cmd()) {
      uint_list_common_attr->add_uint64_list_value(ks::ad_base::fnv_hashstr(cmd.c_str()));
    }
  }
}

void AddCmdContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                                 const std::string& cmd) {
  auto uint_list_common_attr = ps_request->mutable_context()->add_info_common_attr();
  uint_list_common_attr->set_name_value(ContextInfoCommonAttr::EXP_RANK_CMDS);
  uint_list_common_attr->set_type(CommonTypeEnum::UINT64_ATTR);
  uint_list_common_attr->set_uint64_value(ks::ad_base::fnv_hashstr(cmd.c_str()));
}

void AddUniverseExtContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                                         const std::string& ext_data) {
  if (!ext_data.empty()) {
    base::Json ext_data_json(base::StringToJson(ext_data));
    if (ext_data_json.IsObject()) {
      base::Json* model_info_json = ext_data_json.Get("modeInfo");
      if (model_info_json != NULL && !model_info_json->IsObject()) {
        model_info_json = ext_data_json.Get("modelInfo");
      }
      if (model_info_json != NULL && model_info_json->IsObject()) {
        auto map_int64_int64_common_attr = ps_request->mutable_context()->add_info_common_attr();
        map_int64_int64_common_attr->set_name_value(ContextInfoCommonAttr::MEDIUM_DEVICE_EXT_DATA);
        map_int64_int64_common_attr->set_type(CommonTypeEnum::MAP_INT64_INT64_ATTR);
        auto map_val = map_int64_int64_common_attr->mutable_map_int64_int64_value();
        int cpu_count = model_info_json->GetInt("cpuCount", 0);
        (*map_val)[0] = cpu_count;
        int battery_percent = model_info_json->GetInt("batteryPercent", -1);
        (*map_val)[1] = battery_percent;
        int64 total_memory_size = model_info_json->GetInt("totalMemorySize", -1);
        (*map_val)[2] = total_memory_size;
        int64 available_memory_size = model_info_json->GetInt("availableMemorySize", -1);
        (*map_val)[3] = available_memory_size;
        int64 total_disk_size = model_info_json->GetInt("totalDiskSize", -1);
        (*map_val)[4] = total_disk_size;
        int64 available_disk_size = model_info_json->GetInt("availableDiskSize", -1);
        (*map_val)[5] = available_disk_size;
      }
    }
  }
}

void FillSmallDirectCallInfo(const ContextData& context,
                             kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  auto* small_game_force_direct_attr = ps_request->mutable_context()->add_info_common_attr();
  small_game_force_direct_attr->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_UNIT64_BOOL_ATTR);
  small_game_force_direct_attr->set_name_value(
      kuaishou::ad::ContextInfoCommonAttr_Name_SMALL_GAME_DIRECT_CALL_ITEM_LIST);
  auto* direct_call_map = small_game_force_direct_attr->mutable_map_unit64_bool_value();

  auto* small_game_site_id_attr = ps_request->mutable_context()->add_info_common_attr();
  small_game_site_id_attr->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_UNIT64_UNIT64_ATTR);
  small_game_site_id_attr->set_name_value(
      kuaishou::ad::ContextInfoCommonAttr_Name_SMALL_GAME_DIRECT_CALL_SITE_ID_LIST);
  auto* site_id_map = small_game_site_id_attr->mutable_map_unit64_unit64_value();

  std::string tmp_ab_key{""};
  for (const auto* ad : context.get_ad_list().Ads()) {
    // 判断是否启用了小游戏组件
    if (((ad->get_landing_page_component() & kuaishou::ad::AdEnum_PageComponentType_SMALL_GAME_NEW) >> 1) !=
        1) {
      continue;
    }
    if (ad->get_direct_call_type() == 1) {
      // 广告主开了直调
      direct_call_map->insert({ad->get_creative_id(), true});
    } else {
      // 广告主没开直调，判断是否能进行暗改
      if (!RankKconfUtil::smallGameForceDirectAbTestConf()->data().GetProductAbTestSwitch(
              ad->get_product_name(), &tmp_ab_key)) {
        // 这个广告不在暗改配置里面, 直接传 false
        direct_call_map->insert({ad->get_creative_id(), false});
      } else {
        auto iter = context.get_small_game_ab_test_values().find(tmp_ab_key);
        if (iter == context.get_small_game_ab_test_values().end()) {
          continue;
        }
        // 在暗改配置里面, 看看是否命中了实验
        direct_call_map->insert({ad->get_creative_id(), iter->second});
      }
    }
    site_id_map->insert({ad->get_creative_id(), ad->get_site_id()});
  }
}

void FillSearchPhotoInfoFromContext(const ContextData& context_data,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
    std::vector<int64_t> values;
    std::vector<int64_t> lengths;
    std::vector<int64_t> creative_ids;

    const auto* query_photo_feas = context_data.common_r_->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("photo_cross_feature_map");
    const auto* query_live_feas = context_data.common_r_->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("author_cross_feature_map");
    auto set_list_fea = [&] (const AdList& ad_list) {
      for (auto* p_ad : ad_list.Ads()) {
        int64 creative_id = p_ad->get_creative_id();
        auto author_id = p_ad->get_author_id();
        auto photo_id = p_ad->get_photo_id();
        int64_t total = 0;
        if (query_photo_feas) {
          auto photo_it = query_photo_feas->find(photo_id);
          if (photo_it != query_photo_feas->end()) {
            for (const auto & feature : photo_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        if (query_live_feas) {
          auto live_it = query_live_feas->find(author_id);
          if (live_it != query_live_feas->end()) {
            for (const auto & feature : live_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        creative_ids.push_back(creative_id);
        lengths.push_back(total);
      }
  };
  set_list_fea(context_data.get_ad_list());
  set_list_fea(context_data.get_live_ad_list());
  set_list_fea(context_data.get_fanstop_ad_list());
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_QUERY2AD_STAT_FEA, values);
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_QUERY2AD_STAT_LENGTH, lengths);
  AddContextInfoCommonAttr(ps_request,
    ContextInfoCommonAttr::SEARCH_QUERY2AD_STAT_KEY, creative_ids);
}

void FillSearchQueryToAdInfoFromContext(const ContextData& context_data,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
    std::vector<int64_t> values;
    std::vector<int64_t> lengths;
    std::vector<int64_t> creative_ids;

    const auto* inner_q2a_feas = context_data.common_r_->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("inner_q2a_feature_map");
    const auto* outer_q2a_feas = context_data.common_r_->GetPtrCommonAttr<std::unordered_map<std::string,
        kuaishou::ad::search_ads::QueryFeaInfo>>("outer_q2a_feature_map");
    const auto* serial_q2a_feas = context_data.common_r_->GetPtrCommonAttr<std::unordered_map<int64,
        kuaishou::ad::search_ads::QueryFeaInfo>>("serial_q2a_feature_map");
    auto set_list_fea = [&] (const AdList& ad_list) {
      for (auto* p_ad : ad_list.Ads()) {
        if (p_ad == nullptr) {
          continue;
        }
        int64 creative_id = p_ad->get_creative_id();
        auto merchant_product_id = p_ad->get_merchant_product_id();
        auto product_name = p_ad->get_product_name();
        auto serial_id = p_ad->Attr(ItemIdx::fd_UNIT_series_id).GetIntValue(p_ad->AttrIndex()).value_or(0);

        int64_t total = 0;
        if (inner_q2a_feas) {
          auto merchant_product_it = inner_q2a_feas->find(merchant_product_id);
          if (merchant_product_it != inner_q2a_feas->end()) {
            for (const auto & feature : merchant_product_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        if (outer_q2a_feas) {
          auto product_name_it = outer_q2a_feas->find(product_name);
          if (product_name_it != outer_q2a_feas->end()) {
            for (const auto & feature : product_name_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        if (serial_q2a_feas) {
          auto serial_id_it = serial_q2a_feas->find(serial_id);
          if (serial_id_it != serial_q2a_feas->end()) {
            for (const auto & feature : serial_id_it->second.query_fea_infos()) {
              values.push_back(feature.int_value());
              total += 1;
            }
          }
        }

        creative_ids.push_back(creative_id);
        lengths.push_back(total);
      }
  };
  set_list_fea(context_data.get_ad_list());
  set_list_fea(context_data.get_fanstop_ad_list());
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_Q2A_V2_ID, values);
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_Q2A_V2_LENGTH, lengths);
  AddContextInfoCommonAttr(ps_request,
    ContextInfoCommonAttr::SEARCH_Q2A_V2_KEY, creative_ids);
  LOG_EVERY_N(INFO, 100000) << "search Q2A V2 debug: "
            << "; SEARCH_Q2A_V2_ID list: " << absl::StrJoin(values, ",")
            << "; SEARCH_Q2A_V2_LENGTH list: " << absl::StrJoin(lengths, ",")
            << "; SEARCH_Q2A_V2_KEY list: " << absl::StrJoin(creative_ids, ",");
}

void FillSearchPhotoInfo(const ContextData& context, const kuaishou::ad::AdRequest& request,
                         kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  auto* recall_features = ps_request->mutable_context()->add_info_common_attr();
  recall_features->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_RECALL_FEATURE_SET);
  recall_features->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_MULTI_ATTR);
  auto* recall_features_map = recall_features->mutable_map_int64_multi_value();

  auto bidword = ps_request->mutable_context()->add_info_common_attr();
  bidword->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_AD_BIDWORD);
  bidword->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_STRING_ATTR);
  auto& bidword_map = (*bidword->mutable_map_int64_string_value());

  auto kbox_type_calc = [&] (const auto* p_ad) -> int {
    int ktype = 0;
    if (p_ad->get_is_search_celebrity()) {
      ktype = 1;
    } else if (p_ad->get_allow_search_app_card()) {
      ktype = 2;
    } else if (p_ad->get_allow_search_form_card()) {
      ktype = 3;
    } else if (p_ad->get_is_search_kbox_item()) {
      ktype = 4;
    } else if (p_ad->get_is_search_kbox_live()) {
      ktype = 5;
    } else if (p_ad->get_is_search_kbox_local_life()) {
      ktype = 6;
    } else if (p_ad->get_is_search_kbox_series()) {
      ktype = 7;
    } else if (p_ad->get_is_search_kbox_mini_game()) {
      ktype = 8;
    }
    return ktype;
  };

  auto set_list_fea = [&] (const AdList& ad_list) {
    for (size_t i = 0; i < ad_list.Size(); ++i) {
      auto *p_ad = ad_list.At(i);
      if (p_ad == nullptr)
        continue;
      int64 creative_id = p_ad->get_creative_id();
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_multi_retrieval_tag());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_multi_overlay_tag());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_multi_overlay_tag_extend());
      (*recall_features_map)[creative_id].add_int_value(
          static_cast<int64>(p_ad->get_search_relevance_score() * 1000));
      (*recall_features_map)[creative_id].add_int_value(static_cast<int64>(p_ad->get_match_type()));
      (*recall_features_map)[creative_id].add_int_value(static_cast<int64>(p_ad->get_sub_match_type()));
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_recall_strategy_type());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_sub_recall_strategy_type());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_extend_type());
      (*recall_features_map)[creative_id].add_int_value(p_ad->get_sub_extend_type());
      (*recall_features_map)[creative_id].add_int_value(static_cast<int64>(p_ad->get_qr_score() * 1000));
      std::string rewrite_query = p_ad->get_rewrite_query();
      int64 rewrite_query_sign = base::CityHash64(rewrite_query.c_str(), rewrite_query.length());
      (*recall_features_map)[creative_id].add_int_value(rewrite_query_sign);
      int ktype = kbox_type_calc(p_ad);
      (*recall_features_map)[creative_id].add_int_value(ktype);
      (*recall_features_map)[creative_id].add_int_value(
          p_ad->Attr(ItemIdx::is_search_series_card).GetIntValue(p_ad->AttrIndex()).value_or(0));
      bidword_map[creative_id] = p_ad->get_search_bidword();
    }
  };
  set_list_fea(context.get_ad_list());
  set_list_fea(context.get_live_ad_list());
  set_list_fea(context.get_native_ad_list());
  set_list_fea(context.get_fanstop_ad_list());
}

void FillSearchQueryToken(const kuaishou::ad::AdRequest& request,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  if (request.query_token_size() > 0) {
    auto query_token = ps_request->mutable_context()->add_info_common_attr();
    query_token->set_name_value(kuaishou::ad::ContextInfoCommonAttr_Name_SEARCH_QUERY_TOKEN);
    query_token->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_STRING_FLOAT_ATTR);
    auto& query_token_map = (*query_token->mutable_map_string_float_value());

    for (size_t i = 0; i < request.query_token_size(); ++i) {
      const auto& qt = request.query_token(i);
      query_token_map[qt.value()] = qt.weight();
    }
  }
}

// search info 提取的特征汇总在这个函数
void FillSearchFeaturesFromSearchInfor(const kuaishou::ad::AdRequest& request,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
    // 内流 trigger photo/author
    const auto inner_stream_params_v2 = request.search_info().inner_stream_params_v2();
    if (!inner_stream_params_v2.empty()) {
      std::string inner_stream_params_v2_str;
      kuaishou::search::SeClientRealTimeActionList inner_stream_params_v2_pb;
      if (base::Base64Decode(inner_stream_params_v2, &inner_stream_params_v2_str)) {
        if (inner_stream_params_v2_pb.ParseFromString(inner_stream_params_v2_str)) {
            // see: message InnerStreamParams
            auto params = inner_stream_params_v2_pb.inner_stream_params();

            int64_t refer_photo_id = 0;
            if (absl::SimpleAtoi(params.refer_photo_id(), &refer_photo_id)) {
              AddContextInfoCommonAttr(ps_request,
                                       ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_PHOTO_ID,
                                       refer_photo_id);
            }
            AddContextInfoCommonAttr(ps_request,
                                     ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_USER_ID,
                                     params.refer_user_id());
            int64_t refer_live_id = 0;
            if (absl::SimpleAtoi(params.refer_live_id(), &refer_live_id)) {
                AddContextInfoCommonAttr(ps_request,
                                     ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_LIVE_ID,
                                     refer_live_id);
            }
            int64_t refer_goods_id = 0;
            if (absl::SimpleAtoi(params.refer_goods_id(), &refer_goods_id)) {
                AddContextInfoCommonAttr(ps_request,
                                     ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_GOODS_ID,
                                     refer_goods_id);
            }
            AddContextInfoCommonAttr(ps_request,
                                     ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_POSITION,
                                     static_cast<int64_t>(params.pos()));
            AddContextInfoCommonAttr(ps_request,
                                     ContextInfoCommonAttr::SEARCH_ADS_TRIGGER_PAGE,
                                     static_cast<int64_t>(params.page()));
        }
      }
    }
}

void FillSearchReferPhotoId(const kuaishou::ad::AdRequest& request,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  const auto& ext_params =
      request.search_info().combo_search_params().ext_params();
  if (!ext_params.empty()) {
    base::Json ext_params_json(StringToJson(ext_params));
    uint64_t refer_photo_id = 0;
    if (ext_params_json.IsObject()) {
      base::StringToUint64(ext_params_json.GetString("refer_photo_id", "0"),
                           &refer_photo_id);
      if (refer_photo_id != 0) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_ADS_REFER_PHOTO_ID,
                                 refer_photo_id);
      }
    }
  }
}

void FillSearchQueryFeatureInfos(const kuaishou::ad::AdRequest& request,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                          const ContextData& context_data) {
  const auto& query_feas = request.search_query_transport_info().query_fea_infos();

  // 新增 query-hot-sequence 特征
  const auto& query_hot_sequence_info = request.search_query_transport_info().query_hot_sequence_info();
  std::vector<int64_t> query_hot_seq_photo_ids;
  std::vector<int64_t> query_hot_seq_photo_imps;
  std::vector<int64_t> query_hot_seq_photo_clks;
  std::vector<int64_t> query_hot_seq_info_sources;
  std::vector<int64_t> query_hot_seq_item_types;

  // 新增 query 大模型语义 id 特征
  const auto& query_quantize_id_info = request.search_query_transport_info().query_quantize_id();
  std::string query_quantize_id = "0";

  for (const auto &query_fea_info : query_feas) {
    // query 一级分类
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CAT_1) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_ADS_QUERY_CATEGORY_CLASS1,
                                 query_cats);
      }
    }
    // query 二级分类
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CAT_2) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_ADS_QUERY_CATEGORY_CLASS2,
                                 query_cats);
      }
    }
    // query 三级分类
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QUERY_CAT_3) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_ADS_QUERY_CATEGORY_CLASS3,
                                 query_cats);
      }
    }
    // relevance twin tower query embedding
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::SEARCH_QUERY_BERT_EMB) {
      std::vector<float> query_rele_emb;
      for (const auto& emb : query_fea_info.float_list_value()) {
        query_rele_emb.push_back(emb);
      }
      if (query_rele_emb.size() == 256) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QUERY_BERT_EMB,
                                 query_rele_emb);
      }
    }
    // query akg embedding
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::AKG_QUERY_EMB) {
      std::vector<float> akg_emb;
      for (const auto& emb : query_fea_info.float_list_value()) {
        akg_emb.push_back(emb);
      }
      if (akg_emb.size() == 64) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_ADS_AKG_QUERY_EMBEDDING,
                                 akg_emb);
      }
    }
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QIN_SEARCH_EMB) {
      std::vector<float> qin_emb;
      for (const auto& emb : query_fea_info.float_list_value()) {
        qin_emb.push_back(emb);
      }
      if (qin_emb.size() == 64) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_QUERY_EMB,
                                 qin_emb);
      }
    }
    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::QIN_ITEM_LIST) {
      std::vector<int64_t> photo_ids, author_ids, durations, play_times,
          photo_tags, channels, labels, timestamps;
      for (const auto& qin_item : query_fea_info.qin_sim_items()) {
        photo_ids.push_back(qin_item.photo_id());
        author_ids.push_back(qin_item.author_id());
        durations.push_back(qin_item.duration());
        play_times.push_back(qin_item.play_time());
        photo_tags.push_back(qin_item.tag());
        channels.push_back(qin_item.channel());
        labels.push_back(qin_item.label());
        timestamps.push_back(qin_item.timestamp());
      }
      if (photo_ids.size() != 0 &&
          photo_ids.size() == author_ids.size() &&
          photo_ids.size() == durations.size() &&
          photo_ids.size() == play_times.size() &&
          photo_ids.size() == photo_tags.size() &&
          photo_ids.size() == channels.size() &&
          photo_ids.size() == labels.size() &&
          photo_ids.size() == timestamps.size()) {
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_PHOTO_ID,
                                 photo_ids);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_AUTHOR_ID,
                                 author_ids);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_DURATION,
                                 durations);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_PLAY_TIME,
                                 play_times);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_PHOTO_TAG,
                                 photo_tags);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_CHANNEL,
                                 channels);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_LABEL,
                                 labels);
        AddContextInfoCommonAttr(ps_request,
                                 ContextInfoCommonAttr::SEARCH_QIN_TIMESTAMP,
                                 timestamps);
      }
    }

    if (feas_map.find(query_fea_info.name_value()) != feas_map.end()) {
      int64_t value = query_fea_info.int_value();
      auto context_name = feas_map[query_fea_info.name_value()];
      AddContextInfoCommonAttr(ps_request,
                                  context_name,
                                  value);
    }

    if (feas_map_str.find(query_fea_info.name_value()) != feas_map_str.end()) {
      std::string value = query_fea_info.string_value();
      auto context_name = feas_map_str[query_fea_info.name_value()];
      AddContextInfoCommonAttr(ps_request,
                                  context_name,
                                  value);
    }

    if (query_fea_info.name_value() ==
        ::kuaishou::ad::search_ads::QueryFeaItemInfo::CATEGORY_ID_LIST) {
      std::vector<int64_t> query_cats;
      std::unordered_set<int64_t> query_sets;
      for (const auto& cat : query_fea_info.int_list_value()) {
        if (!query_sets.count(cat)) {
          query_cats.push_back(cat);
          query_sets.insert(cat);
        }
      }
      if (query_cats.size() > 0) {
        AddContextInfoCommonAttr(ps_request,
                                  ContextInfoCommonAttr::CATEGORY_ID_LIST,
                                  query_cats);
      }
    }
  }

  if (query_quantize_id_info.size() != 0) {
    // vector 第一个元素是大模型语义 id, 之后元素是改写 id
    query_quantize_id = query_quantize_id_info[0];
  }
  AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::SEARCH_QUERY_QUANTIZE_ID,
                            query_quantize_id);
  for (const auto &query_info_piece : query_hot_sequence_info.query_hot_sequence_detail_info()) {
    int64_t photo_id = static_cast<int64_t>(query_info_piece.photo_id());
    int64_t photo_imp_count = static_cast<int64_t>(query_info_piece.photo_imp_count());
    int64_t photo_clk_count = static_cast<int64_t>(query_info_piece.photo_clk_count());
    int64_t info_source = static_cast<int64_t>(query_info_piece.info_source());
    int64_t item_type = static_cast<int64_t>(query_info_piece.item_type());
    query_hot_seq_photo_ids.push_back(photo_id);
    query_hot_seq_photo_imps.push_back(photo_imp_count);
    query_hot_seq_photo_clks.push_back(photo_clk_count);
    query_hot_seq_info_sources.push_back(info_source);
    query_hot_seq_item_types.push_back(item_type);
  }
  if (query_hot_seq_photo_ids.size() != 0
      && query_hot_seq_photo_ids.size() == query_hot_seq_photo_imps.size()
      && query_hot_seq_photo_imps.size() == query_hot_seq_photo_clks.size()
      && query_hot_seq_photo_clks.size() == query_hot_seq_info_sources.size()
      && query_hot_seq_info_sources.size() == query_hot_seq_item_types.size()) {
    AddContextInfoCommonAttr(ps_request,
                           ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_PHOTO_ID_14D,
                           query_hot_seq_photo_ids);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_PHOTO_IMP_14D,
                            query_hot_seq_photo_imps);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_PHOTO_CLK_14D,
                            query_hot_seq_photo_clks);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_SOURCE_ID_14D,
                            query_hot_seq_info_sources);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::SEARCH_QUERY_HOT_SEQ_ITEM_TYPE_14D,
                            query_hot_seq_item_types);
  }
}

void FillSearchQuerySource(const kuaishou::ad::AdRequest& request,
                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  const std::unordered_map<std::string, int64_t> query_source_map = {{"INSPIRE", 1}};
  int64_t query_source = 0;
  if (request.search_info().ad_query_source() != "" &&
      query_source_map.count(request.search_info().ad_query_source()) > 0) {
    query_source = query_source_map.at(request.search_info().ad_query_source());
  }
  AddContextInfoCommonAttr(ps_request,
                           ContextInfoCommonAttr::SEARCH_ADS_AD_QUERY_SOURCE,
                           query_source);
}

void FillRequestContextInfo(ContextData& context, const kuaishou::ad::AdRequest& request,  // NOLINT
                            kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                            const std::string& scene_type) {
  if (request.universe_ad_request_info().imp_info_size() > 0) {
    if (context.get_pos_manager_base().GetRequestAppId() == "kuaishou_nebula" ||
        engine_base::KwaiMatrixAppidAdmit::IsKwaiMatrixSeriesAppid(
            context.get_pos_manager_base().GetRequestAppId()) ||
        context.get_pos_manager_base().GetRequestAppId() == "kswechatapp" ||
        context.get_pos_manager_base().IsMicroAppRequest()) {
      //  极速版设置为 DEFAULT 避免污染主站数据流
      ps_request->set_tab_type(kuaishou::ad::algorithm::TabType::DEFAULT);
    }

    if (context.get_pos_manager_base().GetAdRequestType() == kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEARBY  // NOLINT
        || context.get_pos_manager_base().GetAdRequestType() ==
           kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_NEBULA_NEARBY) {
      ps_request->set_tab_type((kuaishou::ad::algorithm::TabType::NEARBY));
    }

    if (scene_type == "native") {  // 只影响速推
      if (context.get_pos_manager_base().GetAdRequestType() ==
          kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_FOLLOW) {
        ps_request->set_tab_type(kuaishou::ad::algorithm::FOLLOW);
      } else if (context.get_pos_manager_base().GetAdRequestType() ==
                 kuaishou::ad::AdEnum_AdRequestFlowType_FLOW_EXPLORE) {
        ps_request->set_tab_type(kuaishou::ad::algorithm::EXPLORE);
      } else {  // 这里可能包含同城、激励直播
        ps_request->set_tab_type(kuaishou::ad::algorithm::NEARBY);
      }
    }

    if (context.get_pos_manager_base().IsSearchRequest()) {
      ps_request->mutable_search_info()->CopyFrom(request.search_info());
    }

    ps_request->mutable_context()->set_app_id(request.universe_ad_request_info().app_id());
    ps_request->mutable_context()->set_page_id(
        request.universe_ad_request_info().imp_info().begin()->page_id());
    ps_request->mutable_context()->set_sub_page_id(
        request.universe_ad_request_info().imp_info().begin()->sub_page_id());
    // 精排阶段联盟物理 pos 特征替换实验
    auto ad_style = request.universe_ad_request_info().imp_info().begin()->ad_style();
    auto white_list = context.get_spdm_ctx().TryGetString("universe_agg_pos_white_list", "-1");
    auto ad_style_white_list_str = absl::StrSplit(white_list, ',');
    std::unordered_set<int64> ad_style_white_list;
    for (const auto &ad_style_str : ad_style_white_list_str) {
      int64 temp;
      if (absl::SimpleAtoi(ad_style_str, &temp)) {
        ad_style_white_list.insert(temp);
      }
    }
    {
      ps_request->mutable_context()->set_pos_id(
      request.universe_ad_request_info().imp_info().begin()->position_id());
    }
  }

  if (request.ad_user_session_info().has_ad_request_times()) {
    ps_request->mutable_context()->set_ad_request_times(request.ad_user_session_info().ad_request_times());
  }

  bool enable_context_info_completion = SPDM_enable_context_info_completion(context.get_spdm_ctx());
  if (enable_context_info_completion) {
    if (request.ad_user_session_info().has_ad_session_request_times()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::AD_SESSION_REQUEST_TIMES,
        static_cast<int64_t>(request.ad_user_session_info().ad_session_request_times()));
    }
    if (request.ad_user_session_info().has_ad_inspire_style_pos_request_times()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::AD_INSPIRE_STYLE_POS_REQUEST_TIMES,
        static_cast<int64_t>(request.ad_user_session_info().ad_inspire_style_pos_request_times()));
    }
    if (request.reco_request_info().has_dark_mode()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::DARK_MODE,
        static_cast<int64_t>(request.reco_request_info().dark_mode()));
    }
    if (request.reco_request_info().has_client_volume()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::CLIENT_VOLUME,
        static_cast<float>(request.reco_request_info().client_volume()));
    }
    if (request.reco_request_info().has_teenage_age()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::TEENAGE_AGE,
        static_cast<int64_t>(request.reco_request_info().teenage_age()));
    }
    if (request.reco_request_info().has_width()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SCREEN_WIDTH,
        static_cast<int64_t>(request.reco_request_info().width()));
    }
    if (request.reco_request_info().has_height()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SCREEN_HEIGHT,
        static_cast<int64_t>(request.reco_request_info().height()));
    }
    if (request.reco_request_info().has_origin_channel()) {
      AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::ORIGIN_CHANNEL,
        static_cast<std::string>(request.reco_request_info().origin_channel()));
    }
  }

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::BROWSE_TYPE,
                           static_cast<int64_t>(request.reco_request_info().browse_type()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SOURCE,
                           static_cast<int64_t>(request.reco_request_info().source()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SOURCE_PHOTO_ID,
                           static_cast<int64_t>(request.reco_request_info().source_photo_id()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_ATTRIBUTE,
                           static_cast<int64_t>(request.universe_ad_request_info().medium_attribute()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_UID,
                           static_cast<int64_t>(request.universe_ad_request_info().medium_uid()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_INDUSTRY_ID,
                           static_cast<int64_t>(request.universe_ad_request_info().medium_industry_id()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_INDUSTRY_ID_V2,
                           static_cast<int64_t>(request.universe_ad_request_info().medium_industry_id_v2()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_SUB_INDUSTRY_ID, static_cast<int64_t>(
          request.universe_ad_request_info().medium_sub_industry_id()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_SUB_INDUSTRY_ID_V2, static_cast<int64_t>(
          request.universe_ad_request_info().medium_sub_industry_id_v2()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_GAME_CATEGORY_ID, static_cast<int64_t>(
          request.universe_ad_request_info().medium_game_category_id()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::DEVICE_STAT_BATTERY,
                           static_cast<int64_t>(request.reco_request_info().device_stats()
                           .device_stat_battery()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::DEVICE_STAT_MEMORY,
                           static_cast<int64_t>(request.reco_request_info().device_stats()
                           .device_stat_memory()));

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::DEVICE_STAT_DISKFREE,
                           static_cast<int64_t>(request.reco_request_info().device_stats()
                           .device_stat_diskfree()));
  // 将鸿蒙等同于安卓
  std::string platform = request.ad_user_info().platform();
  if (platform == "harmony") {
    platform = "android";
  }
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::DEVICE_PLATFORM,
                           platform);

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::DEVICE_NETWORK,
                           request.ad_user_info().network());

  if (SPDM_enableNewProductCategoryForModel()) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_ENABLE_NEW_PRODUCT_CATEGORY_FOR_MODEL,
                            static_cast<int64_t>(
                              SPDM_enable_new_product_category_for_model(context.get_spdm_ctx()) ? 1 : 0));
  }

  if (SPDM_enable_model_explore(context.get_spdm_ctx())) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MODEL_EXPLORE_TAG,
                           static_cast<int64_t>(context.get_is_model_explore()));
  }

  bool is_inner_ps_request =
      (scene_type == "native" || scene_type == "inner_normal" || scene_type == "fanstop");

  uint64_t ad_list_size = 0;
  if (context.get_is_default_deploy()) {
    (*context.mutable_ad_list()).ForEach([&is_inner_ps_request, &ad_list_size] (auto* p_ad) {
      if (is_inner_ps_request) {
        if (p_ad->Is(AdFlag::is_inner_loop_ad)) ++ad_list_size;
      } else {
        if (p_ad->Is(AdFlag::is_outer_loop_ad)) ++ad_list_size;
      }
    });
  } else {
    ad_list_size = (*context.mutable_ad_list()).Size();
  }

  if (context.get_is_kuaishou_traffic() || context.get_is_nebula_request()) {
    // 直调只支持 kuaishou 和 kuaishou_nebula
    FillSmallDirectCallInfo(context, ps_request);
  }
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::CANDIDATE_QUEUE_SIZE,
                           ad_list_size);

  // 搜索广告相关字段
  if (context.get_pos_manager_base().IsSearchRequest()) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::QUERY,
                              request.search_info().query());

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::TAB_KEYWORD,
                              request.search_info().tab_keyword());

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::PAGE_NUM,
                              static_cast<int64_t>(request.search_info().page_num()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::PAGE_SIZE,
                              static_cast<int64_t>(request.search_info().page_size()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_SRC,
                              static_cast<int64_t>(request.search_info().search_source()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_FROM_PAGE,
                              static_cast<int64_t>(request.search_info().from_page()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::SEARCH_ENTER_SOURCE,
                              request.search_info().combo_search_params().enter_source());

    FillSearchPhotoInfo(context, request, ps_request);

    FillSearchQueryToken(request, ps_request);

    FillSearchReferPhotoId(request, ps_request);

    FillSearchQuerySource(request, ps_request);

    // query 侧特征收敛到这里
    FillSearchQueryFeatureInfos(request, ps_request, context);

    if (SPDM_enableRankSearchCrossFeature()) {
      FillSearchPhotoInfoFromContext(context, ps_request);
    }
    FillSearchFeaturesFromSearchInfor(request, ps_request);
    FillSearchQueryToAdInfoFromContext(context, ps_request);
  }

  // 激励视频场景相关字段
  if (context.get_is_rewarded()) {
    auto& reward_req_info = request.inspire_req_info();
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::REWARD_VIEW_COUNT_DI,
                             static_cast<int64_t>(reward_req_info.current_view_count()));
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::REWARD_LAST_TIME_COIN_AMOUNT,
                             static_cast<int64_t>(reward_req_info.last_inspire_amount()));
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::REWARD_IS_SHOW_CONV_INSPIRE,
                             static_cast<int64_t>(reward_req_info.show_activate_inspire()));
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::REWARD_IS_SHOW_ORDER_PAIED_INSPIRE,
                             static_cast<int64_t>(reward_req_info.show_order_inspire()));

    auto reward_wangzhuan_tag = ps_request->mutable_context()->add_info_common_attr();
    reward_wangzhuan_tag->set_name_value(
        kuaishou::ad::ContextInfoCommonAttr_Name_REWARD_CREATIVE_WANGZHUAN_TAG);
    reward_wangzhuan_tag->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_INT64_ATTR);
    auto& reward_wangzhuan_tag_map = (*reward_wangzhuan_tag->mutable_map_int64_int64_value());

    auto reward_delivery_rate = ps_request->mutable_context()->add_info_common_attr();
    reward_delivery_rate->set_name_value(
        kuaishou::ad::ContextInfoCommonAttr_Name_REWARD_PRERANK_DELIVERY_RATE);
    reward_delivery_rate->set_type(kuaishou::ad::CommonTypeEnum_AttrType_MAP_INT64_FLOAT_ATTR);
    auto& reward_delivery_rate_map = (*reward_delivery_rate->mutable_map_int64_float_value());

    for (size_t i = 0; i < (*context.mutable_ad_list()).Size(); ++i) {
      auto* p_ad = (*context.mutable_ad_list()).At(i);
      if (is_inner_ps_request) {
        if (!p_ad->Is(AdFlag::is_inner_loop_ad)) continue;
      } else {
        if (!p_ad->Is(AdFlag::is_outer_loop_ad)) continue;
      }
      int64_t creative_id = p_ad->get_creative_id();
      int64_t wangzhuan_tag = static_cast<int64_t>(p_ad->get_wangzhuan_tag());
      float delivery_rate = p_ad->get_delivery_rate();
      reward_wangzhuan_tag_map[creative_id] = wangzhuan_tag;
      reward_delivery_rate_map[creative_id] = delivery_rate;
    }
  }

  AddUniverseExtContextInfoCommonAttr(ps_request, request.universe_ad_request_info().ext_data());

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::AB_TEST_HASH_ID,
                           static_cast<uint64_t>(context.get_ab_test_hash_id()));

  if (request.universe_ad_request_info().imp_info_size() > 0) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::AD_STYLE,
                             static_cast<int64_t>(request.universe_ad_request_info().
                                                  imp_info().begin()->ad_style()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_RENDER_TYPE,
                             static_cast<int64_t>(request.universe_ad_request_info().
                                                  imp_info().begin()->render_type()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_TEMPLATE_ID,
                             static_cast<int64_t>(request.universe_ad_request_info().
                                                  imp_info().begin()->template_id()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_REWARDED_TYPE,
                             static_cast<int64_t>(request.universe_ad_request_info().
                                                  imp_info().begin()->rewarded_type()));

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::MEDIUM_REWARDED_NUM,
                             static_cast<int64_t>(request.universe_ad_request_info().
                                                  imp_info().begin()->rewarded_num()));
    int64_t ad_style =
        static_cast<int64_t>(request.universe_ad_request_info().imp_info().begin()->ad_style());
    int64_t medium_industry_id_v2 =
        static_cast<int64_t>(request.universe_ad_request_info().medium_industry_id_v2());
    int64_t medium_key = medium_industry_id_v2 * 1000 + ad_style;

    // 内循环媒体行业叉乘广告场景下 7 日 CTR 均值
    float ctr_7d = 0.0;
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::
                              UNIV_NEW_MEDIUM_INDUSTRY_ID_X_AD_STYLE_CTR_7D, ctr_7d);

    // 内循环媒体行业叉乘广告场景下 30 日 GMV 均值
    float gmv_30d = 0;
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::
                              UNIV_NEW_MEDIUM_INDUSTRY_ID_X_AD_STYLE_GMV_30D, gmv_30d);

    // 内循环媒体行业叉乘广告场景下 TOP GMV 作者列表
    const std::vector<int64_t> top_gmv_author_list;
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::
                        UNIV_NEW_MEDIUM_INDUSTRY_ID_X_AD_STYLE_TOP_GMV_AUTHOR_LIST_30D, top_gmv_author_list);
  }

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::COOPERATION_MODE,
                           static_cast<int64_t>(request.universe_ad_request_info().cooperation_mode()));

  if (context.get_is_splash_request()) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::INTERACTIVE_FORM,
        static_cast<int64_t>(kuaishou::ad::AdEnum_InteractiveForm_INTERACTIVE_THANOS));
  } else {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::INTERACTIVE_FORM,
        static_cast<int64_t>(context.get_pos_manager_base().GetInteractiveForm()));
  }
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::REFRESH_DIRECTION,
                           static_cast<int64_t>(context.get_pos_manager_base().GetRefreshDirection()));
  if (scene_type == "native") {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::IS_NATIVE_AD_REQUEST,
                             static_cast<int64_t>(1));
  }

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::LAST_PV_AD_POS,
                           static_cast<int64_t>(request.debug_message().req_pv_info().last_explore_pv_last_ad_pos()));  // NOLINT

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::LAST_PV_TIMESTAMP,
                           static_cast<int64_t>(request.debug_message().req_pv_info().last_explore_pv_timestamp()));  // NOLINT

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::LAST_FIRST_SCREEN_AD_TIMESTAMP,
                           static_cast<int64_t>(request.debug_message().req_pv_info().first_screen_ad_shw_timestamp()));  // NOLINT

  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::LAST_PV_PAGE_SIZE,
                           static_cast<int64_t>(request.debug_message().req_pv_info().last_explore_pv_page_size()));  // NOLINT
  if (request.has_page()) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::CURRENT_PAGE_NUMBER,
                             request.page());
  }

  if (context.get_pos_manager_base().IsWanhe()) {
    AddWanheCreatorContextInfoCommonAttr(context, ps_request);
  }

  AddUser5RAuthorLstContextInfoCommonAttr(ps_request, request);
  AddBuyerEffectiveTypeContextInfoCommonAttr(ps_request, request);
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::GESTURE_TYPE,
                             request.gesture_type());
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::CLIENT_VOLUME_V2,
                             request.client_volume());

  if (request.ad_user_info().history_request_infos_size() > 0) {
    size_t l = request.ad_user_info().history_request_infos_size();
    const auto& tmp = request.ad_user_info().history_request_infos();

    std::vector<int64_t> sub_page_ids(l);
    std::vector<int64_t> request_timestamps(l);
    std::vector<int64_t> page_numbers(l);
    for (size_t i = 0; i < l; ++i) {
      sub_page_ids[i] = tmp[i].sub_page_id();
      request_timestamps[i] = static_cast<int64_t>(tmp[i].request_timestamp());
      // 没有刷次, 填 -2 默认值, 区分开其他值
      page_numbers[i] = tmp[i].has_page_number()? tmp[i].page_number(): -2;
    }

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::HIST_REQ_SUB_PAGE_IDS, sub_page_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::HIST_REQ_TIMESTAMPS, request_timestamps);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::HIST_REQ_PAGE_NUMS, page_numbers);
  }
}

void AddUser5RAuthorLstContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                                                const kuaishou::ad::AdRequest& request) {
  const int32_t max_author_num = 100;
  int32_t r3_author_num = 0;
  int32_t r4_author_num = 0;
  int32_t r5_author_num = 0;

  std::vector<int64_t> r3_author_lst(max_author_num);
  std::vector<int64_t> r4_author_lst(max_author_num);
  std::vector<int64_t> r5_author_lst(max_author_num);

  const auto& strategy_crowd_info = request.ad_user_info().strategy_crowd_info();
  for (const auto& crowd_info : strategy_crowd_info) {
    int64_t author_id = crowd_info.author_id();
    for (const auto& crowd_tag : crowd_info.tag()) {
      if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R3 && r3_author_num <= max_author_num) {
        r3_author_lst.push_back(author_id);
        r3_author_num++;
      } else if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R4
        && r4_author_num <= max_author_num) {
        r4_author_lst.push_back(author_id);
        r4_author_num++;
      } else if (crowd_tag == kuaishou::ad::StrategyCrowdTag::BRAND_OFFLINE_R5
        && r5_author_num <= max_author_num) {
        r5_author_lst.push_back(author_id);
        r5_author_num++;
      }
    }
  }
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::USER_5R_R3_AUTHOR_LST, r3_author_lst);
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::USER_5R_R4_AUTHOR_LST, r4_author_lst);
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::USER_5R_R5_AUTHOR_LST, r5_author_lst);
}

void AddBuyerEffectiveTypeContextInfoCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                                                const kuaishou::ad::AdRequest& request) {
  std::string buyer_type_value;
  if (request.ad_user_info().has_user_level_v2_risk()) {
    auto buyer_type_realtime
      = request.ad_user_info().user_level_v2_risk();
    if (buyer_type_realtime == "u0-potential" || buyer_type_realtime == "risk") {
      buyer_type_value = "U0";
    } else if (buyer_type_realtime == "u0") {
      buyer_type_value = "U0+";
    } else if (buyer_type_realtime == "u1") {
      buyer_type_value = "U1";
    } else if (buyer_type_realtime == "u2") {
      buyer_type_value = "U2";
    } else if (buyer_type_realtime == "u3") {
      buyer_type_value = "U3";
    } else if (buyer_type_realtime == "u4") {
      buyer_type_value = "U4";
    } else if (buyer_type_realtime == "u4+") {
      buyer_type_value = "U4+";
    }
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::BUYER_EFFECTIVE_TYPE,
                            buyer_type_value);
  } else {
    auto buyer_type
      = request.ad_user_info().buyer_effective_type();
    if (!request.ad_user_info().has_buyer_effective_type()) {
      buyer_type_value = "U0";
    } else if (buyer_type== "U0") {
      buyer_type_value = "U0+";
    } else if (buyer_type == "U1") {
      buyer_type_value = "U1";
    } else if (buyer_type == "U2") {
      buyer_type_value = "U2";
    } else if (buyer_type == "U3") {
      buyer_type_value = "U3";
    } else if (buyer_type == "U4") {
      buyer_type_value = "U4";
    } else if (buyer_type == "U4+") {
      buyer_type_value = "U4+";
    }
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::BUYER_EFFECTIVE_TYPE,
                            buyer_type_value);
  }
}

void AddWanheCreatorContextInfoCommonAttr(const ContextData& context,
                                          kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  int64_t creator_id = context.get_rank_request()->ad_request().creator_uid();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_ID, creator_id);
  int64_t final_cross_section_first_class_id =
          context.get_rank_request()->ad_request().wanhe_creator_info().final_cross_section_first_class_id();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_CROSS_SECTION_FIRST_CLASSS_ID,
                           final_cross_section_first_class_id);
  int64_t final_cross_section_second_class_id =
          context.get_rank_request()->ad_request().wanhe_creator_info().final_cross_section_second_class_id();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_CROSS_SECTION_SECOND_CLASSS_ID,
                           final_cross_section_second_class_id);
  int64_t fans_user_num = context.get_rank_request()->ad_request().wanhe_creator_info().fans_user_num();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_FNAS_USER_NUM, fans_user_num);
  int64_t is_op_author = context.get_rank_request()->ad_request().wanhe_creator_info().is_op_author();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_IS_OP_AUTHOR, is_op_author);
  int64_t photo_active_status =
          context.get_rank_request()->ad_request().wanhe_creator_info().photo_active_status();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_PHOTO_ACTIVE_STATUS,
                           photo_active_status);
  int64_t quality_first_key = context.get_rank_request()->ad_request().wanhe_creator_info().quality_first_key();  // NOLINT
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_QUALITY_FIRST_KEY,
                           quality_first_key);
  std::string gender = context.get_rank_request()->ad_request().wanhe_creator_info().gender();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_GENDER, gender);
  std::string fre_city_level = context.get_rank_request()->ad_request().wanhe_creator_info().fre_city_level();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_FRE_CITY_LEVEL, fre_city_level);
  int64_t is_photo_mcn_user = context.get_rank_request()->ad_request().wanhe_creator_info().is_photo_mcn_user(); // NOLINT
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_IS_PHOTO_MCN_USER,
                           is_photo_mcn_user);
  int64_t is_busi_author = context.get_rank_request()->ad_request().wanhe_creator_info().is_busi_author();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_IS_BUSI_AUTHOR, is_busi_author);
  int64_t is_community_good_author =
          context.get_rank_request()->ad_request().wanhe_creator_info().is_community_good_author();
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_IS_COMMUNITY_GOOD_AUTHOR,
                           is_community_good_author);
  int64_t first_category_id = context.get_rank_request()->ad_request().wanhe_creator_info().first_category_id(); // NOLINT
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_FIRST_CATEGORY_ID,
                           first_category_id);
  int64_t second_category_id = context.get_rank_request()->ad_request().wanhe_creator_info().second_category_id(); // NOLINT
  AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::WANHE_CREATOR_SECOND_CATEGORY_ID,
                           second_category_id);
}

void AddRankCandidatesInfoContextInfoCommonAttr(ContextData* context,
                            kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  // 内循环软、硬广队列最多各取 max_candidates_num 个
  const int32_t max_candidates_num = 50;
  int32_t candidates_num1 = 0;
  int32_t candidates_num2 = 0;

  int32_t sv_candidates_num1 = 0;
  int32_t sv_candidates_num2 = 0;

  std::vector<int64_t> creative_ids;
  std::vector<int64_t> unit_ids;
  std::vector<int64_t> campaign_ids;
  std::vector<int64_t> account_ids;
  std::vector<int64_t> author_ids;

  std::vector<int64_t> photo_ids;
  std::vector<int64_t> live_stream_ids;
  std::vector<int64_t> campaign_types;
  std::vector<int64_t> ocpx_action_types;
  std::vector<int64_t> creative_types;
  std::vector<int64_t> ad_queue_types;

  std::vector<float> prerank_ecpms;
  std::vector<int64_t> auto_cpa_bids;
  std::vector<int64_t> cpa_bids;
  std::vector<float> prerank_pctrs;
  std::vector<float> prerank_pcvrs;

  std::vector<int64_t> sv_photo_ids;
  std::vector<int64_t> sv_author_ids;
  std::vector<int64_t> sv_account_ids;
  std::vector<int64_t> sv_merchant_product_ids;
  std::vector<int64_t> sv_spu_ids;
  std::vector<int64_t> sv_ocpx_action_types;

  std::vector<int64_t> prerank_first_industry_id;
  std::vector<int64_t> prerank_second_industry_id;
  std::vector<int64_t> prerank_product_id;

  creative_ids.reserve(max_candidates_num*2);
  unit_ids.reserve(max_candidates_num*2);
  campaign_ids.reserve(max_candidates_num*2);
  account_ids.reserve(max_candidates_num*2);
  author_ids.reserve(max_candidates_num*2);

  photo_ids.reserve(max_candidates_num*2);
  live_stream_ids.reserve(max_candidates_num*2);
  campaign_types.reserve(max_candidates_num*2);
  ocpx_action_types.reserve(max_candidates_num*2);
  creative_types.reserve(max_candidates_num*2);
  ad_queue_types.reserve(max_candidates_num*2);

  prerank_ecpms.reserve(max_candidates_num*2);
  auto_cpa_bids.reserve(max_candidates_num*2);
  cpa_bids.reserve(max_candidates_num*2);
  prerank_pctrs.reserve(max_candidates_num*2);
  prerank_pcvrs.reserve(max_candidates_num*2);

  sv_photo_ids.reserve(max_candidates_num*2);
  sv_author_ids.reserve(max_candidates_num*2);
  sv_account_ids.reserve(max_candidates_num*2);
  sv_merchant_product_ids.reserve(max_candidates_num*2);
  sv_spu_ids.reserve(max_candidates_num*2);
  sv_ocpx_action_types.reserve(max_candidates_num*2);

  prerank_first_industry_id.reserve(max_candidates_num*2);
  prerank_second_industry_id.reserve(max_candidates_num*2);
  prerank_product_id.reserve(max_candidates_num*2);

  auto enable_sv_rank_candidante = SPDM_enable_sv_rank_candidante(context->get_spdm_ctx());

  const auto& append_item_info = [&] (const AdCommon* p_ad) {
    creative_ids.push_back(p_ad->get_creative_id());
    unit_ids.push_back(p_ad->get_unit_id());
    campaign_ids.push_back(p_ad->get_campaign_id());
    account_ids.push_back(p_ad->get_account_id());
    author_ids.push_back(p_ad->get_author_id());

    photo_ids.push_back(p_ad->get_photo_id());
    live_stream_ids.push_back(p_ad->get_live_stream_id());
    campaign_types.push_back(p_ad->get_campaign_type());
    ocpx_action_types.push_back(p_ad->get_ocpx_action_type());
    // creative_types 取值为空
    creative_types.push_back(p_ad->get_live_creative_type());
    ad_queue_types.push_back(p_ad->get_ad_queue_type());

    prerank_ecpms.push_back(p_ad->get_prerank_ecpm());
    // prerank_ecpms.push_back(p_ad->get_score());
    auto_cpa_bids.push_back(p_ad->get_auto_cpa_bid());
    cpa_bids.push_back(p_ad->get_cpa_bid());
    prerank_pctrs.push_back(p_ad->get_prerank_unify_ctr());
    prerank_pcvrs.push_back(p_ad->get_prerank_unify_cvr());

    auto* p_ad_no_const = const_cast<AdCommon*>(p_ad);
    if (p_ad_no_const != nullptr) {
      prerank_first_industry_id.push_back(p_ad_no_const->GetFirstIndustryId());
      prerank_second_industry_id.push_back(p_ad_no_const->GetSecondIndustryId());
      prerank_product_id.push_back(base::CityHash64(p_ad_no_const->get_product_name().c_str(),
        p_ad_no_const->get_product_name().size()));
    }
  };

  const auto& append_sv_item_info = [&] (const AdCommon* p_ad) {
    const auto& p_ocpx_action_type = p_ad->get_ocpx_action_type();
    sv_photo_ids.push_back(p_ad->get_photo_id());
    sv_account_ids.push_back(p_ad->get_account_id());
    sv_author_ids.push_back(p_ad->get_author_id());
    sv_ocpx_action_types.push_back(p_ad->get_ocpx_action_type());
    sv_spu_ids.push_back(p_ad->get_spu_id_v2());
    sv_merchant_product_ids.push_back(p_ad->get_merchant_product_id());
  };

  // 内循环软广
  for (const auto& ads : {std::cref((*context->mutable_native_ad_list()).Ads()),
  std::cref((*context->mutable_fanstop_ad_list()).Ads())}) {
    bool is_full = false;
    for (const auto& p_ad : ads.get()) {
      if (p_ad->Is(AdFlag::is_outer_loop_ad) || !p_ad->Is(AdFlag::GetValid)) {
        continue;
      }
      const auto& p_ocpx_action_type = p_ad->get_ocpx_action_type();
      if (p_ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
        p_ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
        p_ocpx_action_type == kuaishou::ad::CID_ROAS ||
        p_ocpx_action_type == kuaishou::ad::AD_FANS_TOP_ROI ||
        p_ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
        p_ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS) {
        append_item_info(p_ad);
        ++candidates_num1;
      }

      if (candidates_num1 >= max_candidates_num) {
        is_full = true;
        break;
      }
    }
    // 退出外层循环
    if (is_full) {
      break;
    }
  }

  // 内循环短视频软广
  for (const auto& ads : {std::cref((*context->mutable_native_ad_list()).Ads()),
  std::cref((*context->mutable_fanstop_ad_list()).Ads())}) {
    bool is_full = false;
    for (const auto& p_ad : ads.get()) {
      const auto& p_ocpx_action_type = p_ad->get_ocpx_action_type();
      if (enable_sv_rank_candidante && p_ad->Is(AdFlag::GetValid) &&
          (p_ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
          p_ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
          p_ocpx_action_type == kuaishou::ad::CID_ROAS ||
          p_ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
          p_ocpx_action_type == kuaishou::ad::EVENT_ORDER_SUBMIT)) {
        append_sv_item_info(p_ad);
        ++sv_candidates_num1;
      }
      if (sv_candidates_num1 >= max_candidates_num) {
        is_full = true;
        break;
      }
    }
    // 退出外层循环
    if (is_full) {
      break;
    }
  }

  // 内循环硬广
  std::vector<AdCommon*> photo_ad_vec;
  photo_ad_vec.reserve((*context->mutable_ad_list()).Size());
  for (const auto& p_ad : (*context->mutable_ad_list()).Ads()) {
    if (p_ad->Is(AdFlag::is_outer_loop_ad) || !p_ad->Is(AdFlag::GetValid)) {
      continue;
    }
    const auto& p_ocpx_action_type = p_ad->get_ocpx_action_type();
    if (p_ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
      p_ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
      p_ocpx_action_type == kuaishou::ad::AD_FANS_TOP_ROI ||
      p_ocpx_action_type == kuaishou::ad::CID_ROAS ||
      p_ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
      p_ocpx_action_type == kuaishou::ad::AD_STOREWIDE_ROAS) {
      // NORMAL_LIVE_AD 优先于 NORMAL_PHOTO_AD 填充
      if (p_ad->get_queue_type() == RankAdListType::NORMAL_PHOTO_AD) {
        photo_ad_vec.push_back(p_ad);
        continue;
      }
      append_item_info(p_ad);
      ++candidates_num2;
    }

    if (candidates_num2 >= max_candidates_num) {
      break;
    }
  }

  int idx = 0;
  while (candidates_num2 < max_candidates_num && idx < photo_ad_vec.size()) {
    append_item_info(photo_ad_vec[idx]);
    ++candidates_num2;
    ++idx;
  }

  // 内循环短视频硬广
  for (const auto& p_ad : (*context->mutable_ad_list()).Ads()) {
    const auto& p_ocpx_action_type = p_ad->get_ocpx_action_type();
    if (enable_sv_rank_candidante && p_ad->Is(AdFlag::GetValid) &&
          (p_ocpx_action_type == kuaishou::ad::EVENT_ORDER_PAIED ||
          p_ocpx_action_type == kuaishou::ad::AD_MERCHANT_ROAS ||
          p_ocpx_action_type == kuaishou::ad::CID_ROAS ||
          p_ocpx_action_type == kuaishou::ad::CID_EVENT_ORDER_PAID ||
          p_ocpx_action_type == kuaishou::ad::EVENT_ORDER_SUBMIT)) {
      append_sv_item_info(p_ad);
      ++sv_candidates_num2;
      if (sv_candidates_num2 >= max_candidates_num) {
        break;
      }
    }
  }

  if (candidates_num1 > 0 || candidates_num2 > 0) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_CREATIVES_IDS, creative_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_UNIT_IDS, unit_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_CAMPAIGN_IDS, campaign_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_ACCOUNT_IDS, account_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_AUTHOR_IDS, author_ids);

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PHOTO_IDS, photo_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_LIVE_STREAM_IDS,
      live_stream_ids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_CAMPAIGN_TYPES,
      campaign_types);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_OCPX_ACTION_TYPES,
      ocpx_action_types);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_CREATIVE_TYPES,
      creative_types);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_AD_QUEUE_TYPES,
      ad_queue_types);

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PRERANK_ECPMS, prerank_ecpms);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_AUTO_CPA_BIDS, auto_cpa_bids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_CPA_BIDS, cpa_bids);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PRERANK_PCTRS, prerank_pctrs);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PRERANK_PCVRS, prerank_pcvrs);

    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_FIRST_INDUSTRY_ID_VALUE,
      prerank_first_industry_id);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_SECOND_INDUSTRY_ID_VALUE,
      prerank_second_industry_id);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PRODUCT_NAME,
      prerank_product_id);
  }

  if (sv_candidates_num1 > 0  || sv_candidates_num2 > 0) {
    AddContextInfoCommonAttr(ps_request,
      ContextInfoCommonAttr::SV_RANK_CANDIDATES_PHOTO_ID_LIST, sv_photo_ids);
    AddContextInfoCommonAttr(ps_request,
      ContextInfoCommonAttr::SV_RANK_CANDIDATES_ACCOUNT_ID_LIST, sv_account_ids);
    AddContextInfoCommonAttr(ps_request,
      ContextInfoCommonAttr::SV_RANK_CANDIDATES_AUTHOR_ID_LIST, sv_author_ids);
    AddContextInfoCommonAttr(ps_request,
      ContextInfoCommonAttr::SV_RANK_CANDIDATES_OCPX_ACTION_TYPE_LIST, sv_ocpx_action_types);
    AddContextInfoCommonAttr(ps_request,
      ContextInfoCommonAttr::SV_RANK_CANDIDATES_MERCHANT_SPU_ID_LIST, sv_spu_ids);
    AddContextInfoCommonAttr(ps_request,
      ContextInfoCommonAttr::SV_RANK_CANDIDATES_MERCHANT_PRODUCT_ID_LIST, sv_merchant_product_ids);
  }
}

void AddRankCandidatesFeaContextInfoCommonAttr(ContextData* context,
                            kuaishou::ad::algorithm::UniversePredictRequest* ps_request) {
  // 填充候选 ad 列表到 ContextInfoCommonAttr, 目前只处理表单和激活
  int32_t max_candidates_num = 100;
  int32_t candidates_num = 0;
  std::vector<int64_t> account_id;
  std::vector<int64_t> author_id;
  std::vector<int64_t> photo_id;
  std::vector<int64_t> industry_id_v3;
  std::vector<int64_t> industry_parent_id_v3;
  std::vector<int64_t> city_product_id;
  std::vector<int64_t> package_id;
  std::vector<int64_t> ocpx_action_type;
  for (auto p_ad : (*context->mutable_ad_list()).Ads()) {
    if (!p_ad->Is(AdFlag::is_outer_loop_ad)) continue;
    const auto& p_ocpx_action_type = p_ad->get_ocpx_action_type();
    if (
      p_ocpx_action_type == kuaishou::ad::AD_CONVERSION ||
      p_ocpx_action_type == kuaishou::ad::AD_PURCHASE ||
      p_ocpx_action_type == kuaishou::ad::AD_ROAS ||
      p_ocpx_action_type == kuaishou::ad::EVENT_KEY_INAPP_ACTION ||
      p_ocpx_action_type == kuaishou::ad::AD_SEVEN_DAY_ROAS ||
      p_ocpx_action_type == kuaishou::ad::EVENT_7_DAY_PAY_TIMES ||
      p_ocpx_action_type == kuaishou::ad::AD_LANDING_PAGE_FORM_SUBMITTED
    ) {
      account_id.push_back(p_ad->get_account_id());
      author_id.push_back(p_ad->get_author_id());
      photo_id.push_back(p_ad->get_photo_id());
      industry_id_v3.push_back(p_ad->get_industry_id_v3());
      industry_parent_id_v3.push_back(p_ad->get_industry_parent_id_v3());
      city_product_id.push_back(p_ad->get_city_product_id());
      package_id.push_back(p_ad->get_package_id());
      ocpx_action_type.push_back(p_ocpx_action_type);
      candidates_num++;
      if (candidates_num >= max_candidates_num) {
        break;
      }
    }
  }
  if (candidates_num > 0) {
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_ACCOUNT_ID_LIST, account_id);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_AUTHOR_ID_LIST, author_id);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PHOTO_ID_LIST, photo_id);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::RANK_CANDIDATES_INDUSTRY_V3_ID_LIST, industry_id_v3);
    AddContextInfoCommonAttr(ps_request,
                  ContextInfoCommonAttr::RANK_CANDIDATES_INDUSTRY_PARENT_V3_LIST, industry_parent_id_v3);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::RANK_CANDIDATES_CITY_PRODUCT_ID_LIST, city_product_id);
    AddContextInfoCommonAttr(ps_request, ContextInfoCommonAttr::RANK_CANDIDATES_PACKAGE_ID_LIST, package_id);
    AddContextInfoCommonAttr(ps_request,
                            ContextInfoCommonAttr::RANK_CANDIDATES_OCPX_ACTION_TYPE_LIST, ocpx_action_type);
  }
}

void FillVirtualItem(const AdCommon& ad, kuaishou::ad::VirtualItem* virtual_item) {
  if (virtual_item == nullptr) {
    LOG_EVERY_N(ERROR, 1000) << "virtual item is a nullptr";
    return;
  }
  virtual_item->mutable_virtual_creative()->set_creative_id(ad.get_creative_id());
  virtual_item->mutable_virtual_creative()->set_version(ad.get_version());
  virtual_item->mutable_extra()->set_data_index(ad.item_mapping_key_id());
  if (ad.get_version() > 1) {
    virtual_item->mutable_virtual_creative()->set_photo_id(ad.get_photo_id());
    virtual_item->mutable_virtual_creative()->set_sticker_id(ad.get_sticker_id());
    virtual_item->mutable_virtual_creative()->set_bg_cover_id(ad.get_cover_id());
    virtual_item->mutable_virtual_creative()->set_title_id(ad.get_title_id());
  }
}

void RecordNodeProcessTime(const std::string& node_name,
                           const int64 cost_time,
                           bool is_timeout,
                           kuaishou::ad::AdRankResponse* ad_rank_response) {
  auto node = ad_rank_response->add_node_process_time();
  node->set_node_name(node_name);
  node->set_node_time_ms(cost_time);
  if (is_timeout) {
    node->set_status(kuaishou::ad::NodeStatus::NODE_TIMEOUT);
  } else {
    node->set_status(kuaishou::ad::NodeStatus::NODE_OK);
  }
  node->set_bin_name(__progname);  // NOLINT
  auto kws_name = getenv("KWS_SERVICE_NAME") == nullptr ?
      "unknown" : getenv("KWS_SERVICE_NAME");
  node->set_kws_name(kws_name);
  return;
}

void ReplaceUrlMacro(std::string* url,
    const std::unordered_map<std::string, std::string>& macro_replace_map) {
  std::string replace_url;
  for (auto iter : macro_replace_map) {
    base::FastStringReplace(*url, iter.first, iter.second, true, &replace_url);
    *url = replace_url;
    replace_url.clear();
  }
}

void AddPsContextInfoCommonAttr(kuaishou::ad::algorithm::Context* ps_context,
                                google::protobuf::int64 name, float value) {
  auto* int_common_attr = ps_context->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::FLOAT_ATTR);
  int_common_attr->set_float_value(value);
}

void AddPsContextInfoCommonAttr(kuaishou::ad::algorithm::Context* ps_context,
                                google::protobuf::int64 name, int64 value) {
  auto* int_common_attr = ps_context->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::INT_ATTR);
  int_common_attr->set_int_value(value);
}

void AddPsContextInfoCommonAttr(kuaishou::ad::algorithm::Context* ps_context,
                                google::protobuf::int64 name, const std::vector<int64>& value) {
  auto* int_common_attr = ps_context->add_info_common_attr();
  int_common_attr->set_name_value(name);
  int_common_attr->set_type(CommonTypeEnum::INT_LIST_ATTR);
  for (const auto& v : value) {
    int_common_attr->add_int_list_value(v);
  }
}

void AddPsContextInfoCommonAttr(kuaishou::ad::algorithm::Context* ps_context,
    google::protobuf::int64 name, const std::unordered_map<int64_t, int64_t>& value) {
  auto* info_common_attr = ps_context->add_info_common_attr();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_INT64_INT64_ATTR);
  auto* map_int64_int64_value = info_common_attr->mutable_map_int64_int64_value();
  for (const auto& v : value) {
    (*map_int64_int64_value)[v.first] = v.second;
  }
}

void AddPsContextInfoCommonAttr(kuaishou::ad::algorithm::Context* ps_context,
    const google::protobuf::int64& name, const std::unordered_map<int64_t, float>& value
    ) {
  auto* info_common_attr = ps_context->add_info_common_attr();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_INT64_FLOAT_ATTR);
  auto* map_int64_float_value = info_common_attr->mutable_map_int64_float_value();
  for (const auto& v : value) {
    (*map_int64_float_value)[v.first] = v.second;
  }
}

ContextInfoCommonAttr* AddStyleFeature(kuaishou::ad::algorithm::Context* ps_context,
                                       google::protobuf::int64 name) {
  auto* info_common_attr = ps_context->add_info_common_attr();
  info_common_attr->set_name_value(static_cast<int64_t>(name));
  info_common_attr->set_type(CommonTypeEnum::MAP_INT64_STRING_ATTR);
  return info_common_attr;
}

void CopyDataFromPsRequest(ContextData& context,  // NOLINT
                           const kuaishou::ad::algorithm::UniversePredictRequest& ps_request,
                           bool update_context) {
  *context.mutable_tab_type() = ps_request.tab_type();
  if (update_context)
    context.mutable_context()->CopyFrom(ps_request.context());
}

void CopyDataFromPsRequest(kuaishou::ad::algorithm::Context* ps_context,  // NOLINT
                           const kuaishou::ad::algorithm::UniversePredictRequest& ps_request) {
  ps_context->CopyFrom(ps_request.context());
}

void MergePsContext(kuaishou::ad::algorithm::Context* dest, kuaishou::ad::algorithm::Context* from) {
  if (dest == nullptr || from == nullptr) {
    return;
  }
  if (dest->app_id() == "" && from->app_id() != "") {
    dest->set_app_id(from->app_id());
  }

  if (dest->page_id() == 0 && from->page_id() != 0) {
    dest->set_page_id(from->page_id());
  }

  if (dest->sub_page_id() == 0 && from->sub_page_id() != 0) {
    dest->set_sub_page_id(from->sub_page_id());
  }

  if (dest->pos_id() == 0 && from->pos_id() != 0) {
    dest->set_pos_id(from->pos_id());
  }

#define MERGE_COMMON_ATTR(attr_name) \
  dest->mutable_##attr_name()->Reserve(\
      dest->attr_name##_size() + from->attr_name##_size()); \
  for (int i = 0; i < from->attr_name##_size(); ++i) {  \
    auto* p_attr = dest->add_##attr_name();   \
    if (!p_attr) {  \
      LOG(WARNING) << "dest->add_" #attr_name "() failed!"; \
      continue; \
    } \
    p_attr->CopyFrom(from->attr_name(i)); \
  }
  MERGE_COMMON_ATTR(photo_common_attr);
  MERGE_COMMON_ATTR(author_common_attr);
  MERGE_COMMON_ATTR(info_common_attr);
#undef MERGE_COMMON_ATTR
}



bool IsFanstopCampaign(::kuaishou::ad::AdEnum_CampaignType campaign_type) {
  return campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_FANS ||
    campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_SHOW ||
    campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_TO_ALL ||
    campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_FANS ||
    campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_SHOW ||
    campaign_type == kuaishou::ad::AdEnum::AD_FANSTOP_LIVE_TO_ALL;
}

void DotSearchAdlistSize(ContextData* context_data, const std::string& node_name) {
  if (context_data == nullptr) {
    return;
  }
  int32_t live_count = 0;
  int32_t fanstop_count = 0;
  int32_t dsp_count = 0;
  int32_t first_index = 0;
  int32_t search_intervene_size = 0;
  int32_t search_bidword_backup_size = 0;
  std::unordered_map<int, int> strategy_ad_count;
  std::unordered_map<std::string, int> item_type_ad_count;
  auto search_ab_dot_name = *RankKconfUtil::SearchAbDotName();
  const std::string& search_rank_common_exp_name =
    context_data->get_spdm_ctx().TryGetString(search_ab_dot_name, "base");
  int64_t backup_ad_quota = SPDM_backup_ad_quota(context_data->get_spdm_ctx());
  for (auto p_ad : (*context_data->mutable_ad_list()).Ads()) {
    if (first_index == 0) {
      RANK_DOT_STATS(context_data, p_ad->get_price(),
        "ad_rank.search.first_pos",
        search_rank_common_exp_name,
        std::to_string(p_ad->get_campaign_type()), std::to_string(p_ad->get_promotion_type()));
    }
    ++first_index;
    if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE) {
      ++live_count;
    } else if (utility::IsFanstopCampaign(p_ad->get_campaign_type())) {
      ++fanstop_count;
    } else {
      ++dsp_count;
    }
    if (context_data->get_enable_search_intervene() &&
        p_ad->get_multi_retrieval_tag() == ks::ad_target::multi_retr::RetrievalTag::SEARCHAD_INTERVENE) {
      search_intervene_size++;
    }
    if (backup_ad_quota > 0 && p_ad->get_is_bidword_backup_ad()) {
      search_bidword_backup_size++;
    }
    ++strategy_ad_count[p_ad->get_multi_retrieval_tag()];
    ++item_type_ad_count[kuaishou::ad::AdEnum::ItemType_Name(p_ad->get_item_type())];
  }
  for (const auto &kv : strategy_ad_count) {
    RANK_DOT_STATS(context_data, kv.second, "search_rank_postproc.sub_strategy_ad_list_cnt",
                  std::to_string(kv.first), context_data->get_search_ab_group_name(), node_name);
  }
  for (const auto &kv : item_type_ad_count) {
     context_data->dot_perf->Interval(kv.second, "item_type_ad_list_cnt", kv.first, node_name,
     context_data->get_search_rank_exp_name());
  }
  RANK_DOT_STATS(context_data, live_count,
      "ad_rank.post_proc.merge_ad_list.search_live_ad_cnt", node_name,
      search_rank_common_exp_name);
  RANK_DOT_STATS(context_data, fanstop_count,
      "ad_rank.post_proc.merge_ad_list.search_fanstop_ad_cnt", node_name,
      search_rank_common_exp_name);
  RANK_DOT_STATS(context_data, dsp_count,
      "ad_rank.post_proc.merge_ad_list.search_dsp_ad_cnt", node_name,
      search_rank_common_exp_name);
  RANK_DOT_STATS(context_data, (*context_data->mutable_ad_list()).Size(),
      "ad_rank.post_proc.merge_ad_list.merged_search_ad_cnt", node_name,
      search_rank_common_exp_name);
  if (context_data->get_enable_search_intervene()) {
    context_data->dot_perf->Interval(search_intervene_size, "ad_rank.search_intervene_size", "output");
  }
  if (search_bidword_backup_size > 0) {
    context_data->dot_perf->Count(1, "ad_rank.search_bidword_backup_request", "output");
  }
  context_data->dot_perf->Interval(search_bidword_backup_size, "ad_rank.search_bidword_backup_size",
                                   "output");
}

bool IsInnerLoopHardCampaign(const ::kuaishou::ad::AdEnum_CampaignType& campaign_type) {
  return campaign_type == kuaishou::ad::AdEnum::MERCHANT_RECO_PROMOTE ||
         campaign_type == kuaishou::ad::AdEnum::LIVE_STREAM_PROMOTE;
}

void AddAdLevelCommonAttr(kuaishou::ad::algorithm::UniversePredictRequest* ps_request,
                          const std::vector<std::vector<AdCommon*>*>& ad_list_vec) {
  if (!ps_request) return;
  // map<cid, 是否关注关系>
  auto* common_attr = ps_request->mutable_context()->add_info_common_attr();
  common_attr->set_name_value(kuaishou::ad::ContextInfoCommonAttr::RANK_IS_FAN_FOLLOW);
  common_attr->set_type(kuaishou::ad::CommonTypeEnum::MAP_UNIT64_BOOL_ATTR);
  auto* map_val = common_attr->mutable_map_unit64_bool_value();
  for (const auto* ad_list_ptr : ad_list_vec) {
    if (!ad_list_ptr) continue;
    const auto& ad_list = *ad_list_ptr;
    for (auto* p_ad : ad_list) {
      if (!(p_ad && p_ad->Is(AdFlag::GetValid))) continue;
      (*map_val)[p_ad->get_creative_id()] = p_ad->get_is_fan_follow();
    }
  }
}

void AdDotCounter(ContextData* session_data, const std::string& tag) {
  std::string exp_tag = session_data->get_spdm_ctx().TryGetString("rank_degrade_debug_tag", "");
  std::string flow_type = session_data->get_is_rewarded() ? "reward" : "default";
#define PERF_DOT_AD_COUNTER(Value) { \
  if (Value > 0 ) { \
  RANK_DOT_STATS(session_data, Value, "ad_rank.ad_dot_counter", #Value, tag, exp_tag, flow_type); \
  } \
} \

#define AD_COUNTER_BY_ITEM(Value) { \
  RANK_DOT_STATS(session_data, Value, "ad_rank.ad_dot_counter_by_item", #Value, tag, exp_tag, flow_type); \
} \

  if (!session_data) return;
  uint32_t outer_total_cnt = 0;
  uint32_t outer_dsp_cnt = 0;
  uint32_t outer_adx_cnt = 0;
  uint32_t outer_dpa_cnt = 0;
  uint32_t inner_total_cnt = 0;

  // 队列维度统计
  auto count_by_ad_list = [&] (auto* p_ad) {
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      inner_total_cnt++;
    } else {
      outer_total_cnt++;
      if (p_ad->get_campaign_type() == kuaishou::ad::AdEnum::DPA_CAMPAIGN) {
        outer_dpa_cnt++;
      } else if (p_ad->get_ad_source_type() == kuaishou::ad::ADX) {
        outer_adx_cnt++;
      } else {
        outer_dsp_cnt++;
      }
    }
  };

  uint32_t inner_hard_photo_size = 0;
  uint32_t inner_native_photo_size = 0;
  uint32_t outer_hard_live_size = 0;
  uint32_t outer_hard_photo_size = 0;
  uint32_t outer_native_live_size = 0;
  uint32_t outer_native_photo_size = 0;

  uint32_t inner_native_live_pc_size = 0;
  uint32_t inner_native_live_fans_size = 0;
  uint32_t inner_normal_live_pc_size = 0;
  uint32_t inner_normal_live_fans_size = 0;

  auto count_by_item_type = [&] (auto* p_ad) {
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      if (p_ad->Is(AdFlag::is_live_ad)) {
        if (p_ad->Is(AdFlag::IsNativeAd)) {
          if (p_ad->Is(AdFlag::is_fanstop)) {
            ++inner_native_live_fans_size;
          } else {
            ++inner_native_live_pc_size;
          }
        } else {
          if (p_ad->Is(AdFlag::is_fanstop)) {
            ++inner_normal_live_fans_size;
          } else {
            ++inner_normal_live_pc_size;
          }
        }
      } else {
        if (p_ad->Is(AdFlag::IsNativeAd)) {
          ++inner_native_photo_size;
        } else {
          ++inner_hard_photo_size;
        }
      }
    } else {
      if (p_ad->Is(AdFlag::is_live_ad)) {
        if (p_ad->Is(AdFlag::IsNativeAd)) {
          ++outer_native_live_size;
        } else {
          ++outer_hard_live_size;
        }
      } else {
        if (p_ad->Is(AdFlag::IsNativeAd)) {
          ++outer_native_photo_size;
        } else {
          ++outer_hard_photo_size;
        }
      }
    }
  };

  std::vector<AdList*> ad_list_vec = {
    session_data->mutable_ad_list(), session_data->mutable_live_ad_list(),
    session_data->mutable_native_ad_list(), session_data->mutable_fanstop_ad_list()};
  for (const auto* ad_list : ad_list_vec) {
    if (!ad_list) continue;
    for (auto* p_ad : ad_list->Ads()) {
      // rank 内部队列维度
      count_by_ad_list(p_ad);
      // item_type 维度
      count_by_item_type(p_ad);
    }
  }

  PERF_DOT_AD_COUNTER(outer_total_cnt)
  PERF_DOT_AD_COUNTER(outer_dsp_cnt)
  PERF_DOT_AD_COUNTER(outer_adx_cnt)
  PERF_DOT_AD_COUNTER(outer_dpa_cnt)
  PERF_DOT_AD_COUNTER(inner_total_cnt)

  AD_COUNTER_BY_ITEM(inner_hard_photo_size)
  AD_COUNTER_BY_ITEM(inner_native_photo_size)

  AD_COUNTER_BY_ITEM(outer_hard_live_size)
  AD_COUNTER_BY_ITEM(outer_hard_photo_size)
  AD_COUNTER_BY_ITEM(outer_native_live_size)
  AD_COUNTER_BY_ITEM(outer_native_photo_size)

  AD_COUNTER_BY_ITEM(inner_native_live_pc_size)
  AD_COUNTER_BY_ITEM(inner_native_live_fans_size)
  AD_COUNTER_BY_ITEM(inner_normal_live_pc_size)
  AD_COUNTER_BY_ITEM(inner_normal_live_fans_size)
}

float GetRankingHourQuotaFactors(const std::string& ab_str) {
  if (ab_str.empty()) return 1.0;
  static const auto kLocalTimeZone = absl::LocalTimeZone();
  auto now = absl::Now();
  auto bd = now.In(kLocalTimeZone);
  std::vector<absl::string_view> params = absl::StrSplit(ab_str, ",");
  float hour_factor = 1.0;
  for (auto &&s : params) {
    int32_t hour = 0;
    float factor = 1.0;
    std::pair<absl::string_view, absl::string_view> param = absl::StrSplit(s, "=");
    if (absl::SimpleAtoi(param.first, &hour)
          && hour == bd.hour
          && absl::SimpleAtof(param.second, &factor)) {
      hour_factor = factor;
      break;
    }
  }
  return hour_factor;
}

std::string GetFactorTag(int32_t formula_type, int32_t rank_factor_type) {
  return absl::StrCat(formula_type, "_", rank_factor_type);
}

AdList* GetAdListByTable(ContextData* session_data, const std::string& table_name) {
  if (table_name == "ad_list_item_table") {
    return session_data->mutable_ad_list();
  } else if (table_name == "native_ad_list_item_table") {
    return session_data->mutable_native_ad_list();
  } else if (table_name == "fanstop_ad_list_item_table") {
    return session_data->mutable_fanstop_ad_list();
  }
  return nullptr;
}

int GetWeekday() {
  absl::Time time_now = absl::Now();
  absl::Time::Breakdown bd = time_now.In(kLocalTimeZone);
  return bd.weekday;
}

// 迁移自 teams/ad/front_server/util/utility/utils.cc 中的 IsDisableAdMark
bool IsDisableAdMark(ContextData* session_data, AdCommon* ad) {
  if (session_data == nullptr || ad == nullptr) {
    return false;
  }
  bool enable_good_soft_ad_new_standard = SPDM_enable_good_soft_ad_new_standard(session_data->get_spdm_ctx());
  bool enable_good_native_photo_new_standard =
      SPDM_enable_good_native_photo_new_standard(session_data->get_spdm_ctx());
  bool is_public_thanos_flow = SPDM_enable_soft_ad_disable_ad_mark_with_review(session_data->get_spdm_ctx()) &&  // NOLINT
                               (ks::ad_base::IsNebulaExploreRequest(session_data->get_sub_page_id()) ||
                                ks::ad_base::IsSelectedRequest(session_data->get_sub_page_id()));
  if ((is_public_thanos_flow) && ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE &&
      !ad->Is(AdFlag::is_new_inner_fanstop)) {
    if (enable_good_soft_ad_new_standard) {
      auto ptr = RankKconfUtil::softAdDisableAdMarkWithReviewConf();
      if (ptr) {
        const auto& disable_ad_mark_config = ptr->data();
        const auto& general_review_include = disable_ad_mark_config.general_review_include();
        const auto& hot_review_include = disable_ad_mark_config.hot_review_include();
        const auto& topk_review_exclude = disable_ad_mark_config.topk_review_exclude();
        bool general_review_flag =
            std::count(general_review_include.begin(), general_review_include.end(),
                       ad->get_general_review());
        bool hot_review_flag = std::count(hot_review_include.begin(), hot_review_include.end(),
                                          ad->get_hot_review());
        bool topk_review_flag =
            (std::count(topk_review_exclude.begin(), topk_review_exclude.end(),
                        ad->get_topk_review()) == 0);
        if (general_review_flag && hot_review_flag && topk_review_flag) {
          return true;
        }
      }
    }
  } else {
    base::Json* disable_ad_mark_config_json = nullptr;
    auto disable_ad_mark_config = RankKconfUtil::softAdDisableAdMarkWithReviewJson();
    if (disable_ad_mark_config != nullptr && disable_ad_mark_config->data != nullptr) {
      disable_ad_mark_config_json = disable_ad_mark_config->data.get();
    }
    auto general_review_include = disable_ad_mark_config_json->Get("general_review_include");
    auto hot_review_exclude = disable_ad_mark_config_json->Get("hot_review_exclude");
    auto topk_review_exclude = disable_ad_mark_config_json->Get("topk_review_exclude");
    if (general_review_include == nullptr ||
        hot_review_exclude == nullptr ||
        topk_review_exclude == nullptr) {
      return false;
    }
    int64 review_id = -1;
    bool general_review_flag = false;
    for (auto& meta_id : general_review_include->array()) {
      review_id = -1;
      if (meta_id->IntValue(&review_id) &&
          review_id == ad->get_general_review()) {
        general_review_flag = true;
      }
    }
    bool hot_review_flag = true;
    for (auto& meta_id : hot_review_exclude->array()) {
      review_id = -1;
      if (meta_id->IntValue(&review_id) &&
          review_id == ad->get_hot_review()) {
        hot_review_flag = false;
      }
    }

    bool topk_review_flag = true;
    for (auto& meta_id : topk_review_exclude->array()) {
      review_id = -1;
      if (meta_id->IntValue(&review_id) &&
          review_id == ad->get_topk_review()) {
        topk_review_flag = false;
      }
    }
    if (general_review_flag && hot_review_flag && topk_review_flag) {
      return true;
    }
  }
  if (enable_good_native_photo_new_standard &&
      ad->get_ad_queue_type() == kuaishou::ad::AdEnum::SOFT_AD_QUEUE) {
    int32_t native_quality_status = ad->get_native_quality_status();
    session_data->dot_perf->Count(1, "native_quality_status_new", absl::StrCat(native_quality_status));
    if (native_quality_status >= 1) {
      return true;
    }
  }
  return false;
}

int EspMerchatFollowBuyerUTag(ContextData* session_data) {
    auto buyer_effective_type =
        session_data->get_rank_request()->ad_request().ad_user_info().buyer_effective_type();
    if (session_data->get_rank_request()->ad_request().ad_user_info().has_user_level_v2_risk()) {
      buyer_effective_type =
           session_data->get_rank_request()->ad_request().ad_user_info().user_level_v2_risk();
    }
    bool no_user_tag = !session_data->get_rank_request()->ad_request().ad_user_info().has_buyer_effective_type() &&  // NOLINT
      !session_data->get_rank_request()->ad_request().ad_user_info().has_user_level_v2_risk();
    int32_t cur_user = -1;
    if (buyer_effective_type == "u0-potential" || buyer_effective_type == "risk" || no_user_tag) {
      cur_user = 0;
    } else if (buyer_effective_type == "u0-silent" || buyer_effective_type == "U0") {  // NOLINT
      cur_user = 1;
    } else if (buyer_effective_type == "u1" || buyer_effective_type == "U1") {
      cur_user = 2;
     } else if (buyer_effective_type == "u2" || buyer_effective_type == "U2") {
      cur_user = 3;
     } else if (buyer_effective_type == "u3" || buyer_effective_type == "U3") {
      cur_user = 4;
    } else if (buyer_effective_type == "u4" || buyer_effective_type == "U4") {
      cur_user = 5;
    } else if (buyer_effective_type == "u4+" || buyer_effective_type == "U4+") {
      cur_user = 6;
    } else {
      cur_user = 7;
    }
    return cur_user;
}

int EspMerchatFollowMerchantLiveTag(ContextData* session_data) {
  int32_t cur_user = -1;
  if (session_data->get_rank_request()->ad_request().ad_user_info().user_merchant_live_consume_ratio()) {
    const auto& user_merchant_live_consume =
        session_data->get_rank_request()->ad_request().ad_user_info().user_merchant_live_consume_ratio();
    if (user_merchant_live_consume <= 0.02) {
      cur_user = 0;
    } else if (user_merchant_live_consume > 0.02 &&
              user_merchant_live_consume <= 0.05) {
      cur_user = 1;
    } else if (user_merchant_live_consume > 0.05 &&
              user_merchant_live_consume <= 0.1) {
      cur_user = 2;
    } else if (user_merchant_live_consume > 0.1 &&
              user_merchant_live_consume <= 0.2) {
      cur_user = 3;
    } else if (user_merchant_live_consume > 0.2 &&
              user_merchant_live_consume <= 0.3) {
      cur_user = 4;
    } else {
      cur_user = 5;
    }
  }
  return cur_user;
}

int EspMerchatFollowFoNumTag(ContextData* session_data) {
  int64_t follow_num = std::max(
        session_data->get_rank_request()->ad_request().reco_user_info().follow_list().size(),
        session_data->get_rank_request()->ad_request().ad_user_info().follow_user().size());
  int32_t cur_user = -1;
  if (follow_num <= 50) {
    cur_user = 0;
  } else if (follow_num > 50 &&
            follow_num <= 200) {
    cur_user = 1;
  } else if (follow_num > 200 &&
            follow_num <= 500) {
    cur_user = 2;
  } else if (follow_num > 500 &&
            follow_num <= 1000) {
    cur_user = 3;
  } else if (follow_num > 1000 &&
            follow_num <= 2000) {
    cur_user = 4;
  } else {
    cur_user = 5;
  }
  return cur_user;
}

void RewardedCoeffLoadStr(const std::string& config_str,
                                std::unordered_map<int64, double>& config_map) { // NOLINT
  if (config_str == "") {
    return;
  }
  std::vector<absl::string_view> items = absl::StrSplit(config_str, ";");
  for (int i = 0; i < items.size(); i++) {
    const std::vector<std::string> key_val = absl::StrSplit(items[i], ":");
    if (key_val.size() != 2) {
      continue;
    }
    int64 key = 0;
    double value = 0.0;
    if (!absl::SimpleAtoi(key_val[0], &key) || !absl::SimpleAtod(key_val[1], &value)) {
       LOG(ERROR) << "rewarded coffe load error, item=" << key_val[0];
       continue;
    }
    LOG_EVERY_N(INFO, 10000) << "small_game_coeff discount_coeff=" << value
      << ";sub_page_id=" << key;
    config_map.emplace(key, value);
  }
  return;
}

double GetRelativeCaliRatio(const ContextData* session_data,
                            const AdCommon* p_ad,
                            const std::string& exp_tag,
                            const double relative_score) {
  double ratio = 1.0;
  if (!session_data->get_is_explore_feed_inner() ||
      relative_score == 0) {
    return ratio;
  }
  const auto& config = RankKconfUtil::exploreRelativeScoreConfig();
  if (config == nullptr) {
    return ratio;
  }
  auto get_cali_ratio = [&](const kconf::ExploreRelativeScoreConfig::BucketConfig& bucket_config) {
    double cali_ratio = 1.0;
    if (bucket_config.buckets() <= 0) {
      return cali_ratio;
    }
    double step = (bucket_config.max_score() - bucket_config.min_score()) / bucket_config.buckets();
    int32_t index = static_cast<int32_t>((relative_score - bucket_config.min_score()) / step);
    index = std::max(0, std::min(index, bucket_config.buckets() - 1));
    const auto& bucket_coef = bucket_config.bucket_coef();
    auto index_iter = bucket_coef.find(index);
    if (index_iter != bucket_coef.end()) {
      cali_ratio = index_iter->second;
    }
    return cali_ratio;
  };
  const auto& bucket_config_map = config->data().bucket_config();
  const std::string& ad_queue_type = kuaishou::ad::AdEnum::AdQueueType_Name(p_ad->get_ad_queue_type());
  auto item_key = absl::StrCat(exp_tag, "|", ad_queue_type, "|",
                          p_ad->Is(AdFlag::is_inner_loop_ad) ? "inner" : "outer");
  auto item_iter = bucket_config_map.find(item_key);
  if (item_iter != bucket_config_map.end()) {
    ratio = get_cali_ratio(item_iter->second);
  } else {
    auto exp_iter = bucket_config_map.find(exp_tag);
    if (exp_iter != bucket_config_map.end()) {
      ratio = get_cali_ratio(exp_iter->second);
    }
  }
  return ratio;
}

void MulHardSctr(const ContextData* session_data, const AdCommon* p_ad, const bool enable,
                 double* p_auction_bid, const std::string& mark, const bool fix_hard_unify_sctr_click) {
  if (!session_data->get_is_explore_feed_inner()) {
    return;
  }

  double& auction_bid = *p_auction_bid;
  std::string mark_ = absl::StrCat(enable ? "1." : "0.", mark);
  if (enable) {
    if (fix_hard_unify_sctr_click) {
      if (p_ad->get_unify_ctr_info().s_type == kuaishou::ad::AD_DELIVERY) {
        RANK_DOT_STATS(session_data, auction_bid * 1000, "sctr_migrate",
                       absl::StrCat("auction_bid_skip.", mark_));
        return;
      }
      if (p_ad->get_unify_sctr_info().value == 0) {
        return;
      }
    }
    auction_bid *= p_ad->get_unify_sctr_info().value;
  }
  RANK_DOT_STATS(session_data, auction_bid * 1000, "sctr_migrate", absl::StrCat("auction_bid.", mark_));
}

double GetHardSctr1Factor(const ContextData* session_data, const AdCommon* p_ad, const bool enable,
                         const double lower, const double upper, const std::string& mark) {
  double sctr_1 = 1.0;
  double sctr = p_ad->get_unify_sctr_info().value;
  std::string mark_ = absl::StrCat(enable ? "1." : "0.", mark);
  if (enable) {
    if (sctr > 0) {
      sctr_1 = 1 / sctr;
      RANK_DOT_COUNT(
          session_data, 1, "sctr_migrate", absl::StrCat("sctr.", mark_),
          absl::StrCat(p_ad->get_unify_sctr_info().s_type, "-", p_ad->get_unify_sctr_info().e_type));
    } else {
      RANK_DOT_STATS(session_data, sctr * 1000, "sctr_migrate", absl::StrCat("sctr_zero.", mark_),
                     absl::StrCat(p_ad->get_unify_sctr_info().s_type, "-", p_ad->get_unify_sctr_info().e_type,
                                  "-", p_ad->get_ocpx_action_type()));
    }
    RANK_DOT_STATS(session_data, sctr * p_ad->get_unify_ctr_info().value * 1000, "sctr_migrate",
                   absl::StrCat("sctr*ctr.", mark_),
                   absl::StrCat(p_ad->get_unify_ctr_info().s_type, "-", p_ad->get_unify_ctr_info().e_type));
  } else {
    RANK_DOT_STATS(session_data, p_ad->get_unify_ctr_info().value * 1000, "sctr_migrate",
                   absl::StrCat("sctr*ctr.", mark_),
                   absl::StrCat(p_ad->get_unify_ctr_info().s_type, "-", p_ad->get_unify_ctr_info().e_type));
  }
  // 保底
  if (sctr_1 < lower) {
    sctr_1 = lower;
  }
  if (sctr_1 > upper) {
    sctr_1 = upper;
  }
  RANK_DOT_STATS(session_data, sctr_1 * 1000, "sctr_migrate", absl::StrCat("sctr_1.", mark_));
  return sctr_1;
}

int64_t GetSearchStyleItemKeyByHash(int64_t creative_id, int64_t ad_queue_type, int64_t search_style) {
  return ad_base::GetItemKeyByHash(creative_id, search_style * 100 + ad_queue_type);
}
int64_t GetTimestampNoDiff() {
  return FLAGS_enable_no_diff_check ? (base::GetTimestamp() / 3600000000L * 3600000000L) :
      base::GetTimestamp();
}

std::string GetCurrentDateV2() {
  auto t = base::Time::Now();
  std::string time_str;
  t.ToStringInFormat("%Y%m%d%H", &time_str);
  return time_str;
}

bool GetNobidUnifyBound(const std::shared_ptr<std::map<std::string, double>> lower_unify_config,
                        const std::shared_ptr<std::map<std::string, double>> upper_unify_config,
                        const ContextData* session_data, const AdCommon* ad,
                        double* lower_bound, double* upper_bound) {
  int64_t page_id = session_data->get_page_id();
  auto unit_tail = ad->get_unit_id() % 100;
  auto ocpx_action_type = AdActionType_Name(ad->get_ocpx_action_type());
  std::string map_key = absl::Substitute("$0_$1", ocpx_action_type, page_id);
  std::string tail_map_key = absl::Substitute("$0_$1_$2", unit_tail, ocpx_action_type, page_id);
  double l_bound = 0, u_bound = 0;
  bool found_upper_bound = false,  found_lower_bound = false;
  // upper bound
  const auto tail_upper_unify_iter =  upper_unify_config->find(tail_map_key);
  const auto upper_unify_iter =  upper_unify_config->find(map_key);
  if (upper_unify_iter != upper_unify_config->end()) {
    u_bound = upper_unify_iter->second;
    found_upper_bound = true;
  }
  // 分尾号
  if (tail_upper_unify_iter != upper_unify_config->end()) {
    u_bound = tail_upper_unify_iter->second;
    found_upper_bound = true;
  }

  // lower bound
  const auto tail_lower_unify_iter =  lower_unify_config->find(tail_map_key);
  const auto lower_unify_iter =  lower_unify_config->find(map_key);
  if (lower_unify_iter != lower_unify_config->end()) {
    l_bound = lower_unify_iter->second;
    found_lower_bound = true;
  }
  // 分尾号
  if (tail_lower_unify_iter != lower_unify_config->end()) {
    l_bound = tail_lower_unify_iter->second;
    found_lower_bound = true;
  }
  *lower_bound = l_bound;
  *upper_bound = u_bound;
  return found_lower_bound && found_upper_bound;
}
bool IsGameIaaRoi7TargetAccount(const ContextData* session_data,
      const AdCommon& ad) {
  std::shared_ptr<std::set<std::string>> game_iaa_roi7_white_product_set =
        RankKconfUtil::gameIaaRoi7WhiteProductSet();
  bool enable_game_iaa_roi7_bid_control_type =
      SPDM_enable_game_iaa_roi7_bid_control_type(session_data->get_spdm_ctx());
  bool hit_game_roi7_white_product = false;
  bool hit_game_roi7_test_account = false;

  // 白名单产品
  if (!game_iaa_roi7_white_product_set->empty()) {
    auto exp_config_itr = game_iaa_roi7_white_product_set->find(ad.get_product_name());
    if (exp_config_itr != game_iaa_roi7_white_product_set->end()) {
        hit_game_roi7_white_product = true;
    }
  }

  // 账户尾号分流
  std::shared_ptr<::ks::infra::TailNumberV2> game_iaa_roi7_account_tail =
      RankKconfUtil::gameIaaRoi7AccountTail();
  if (game_iaa_roi7_account_tail && game_iaa_roi7_account_tail->IsOnFor(ad.get_account_id())) {
      hit_game_roi7_test_account = true;
  }

  bool enable_game_iaa_roi7_all_product =
    SPDM_enable_game_iaa_roi7_all_product(session_data->get_spdm_ctx());
  // 判断是否满足条件
  if (enable_game_iaa_roi7_bid_control_type
      && (hit_game_roi7_white_product || enable_game_iaa_roi7_all_product)
      && hit_game_roi7_test_account
      && ad.get_ocpx_action_type() == kuaishou::ad::AD_IAA_ROAS) {
      return true;
  }
  return false;
}


void CompareAndReplaceRequest(kuaishou::ad::algorithm::UniversePredictRequest* request_old,
  kuaishou::ad::algorithm::UniversePredictRequest* request_new,
  const std::string& tag,
  bool replace) {
  static constexpr auto enable_debug = false;
  auto start_timestamp = GetTimestampNoDiff();
  auto diff_field = RankKconfUtil::rankPsRouterRequestDiffField();
  auto compare_exclude_field = RankKconfUtil::rankPsRouterRequestCompareExcludeField();
  const google::protobuf::Descriptor* descriptor = request_old->GetDescriptor();
  const google::protobuf::Reflection* reflection = request_old->GetReflection();

  if (diff_field == nullptr || request_old == nullptr || request_new == nullptr
    || descriptor == nullptr|| reflection == nullptr || compare_exclude_field == nullptr) {
    LOG(WARNING) << "[" << tag << "]" << "request is nullptr";
    return;
  }
  auto diff_field_content = *diff_field;

  if (SPDM_enableSearchRankPredictDiffAllField()) {
    for (int i = 0; i < descriptor->field_count(); ++i) {
      auto field = descriptor->field(i);
      diff_field_content.insert(field->name());
    }
  }

  if (SPDM_enableSearchRankPredictReplace()) {
    request_old->CopyFrom(*request_new);
    return;
  }

  auto deduplicate_repeated_field = [](google::protobuf::RepeatedPtrField<std::string>* field) {
    std::set<std::string> unique_elements(field->begin(), field->end());
    field->Clear();
    for (auto& item : unique_elements) { field->Add(item.data()); }
  };

  if (true) {
    std::map<int64_t, ContextInfoCommonAttr*> attr_new, attr_old;
    for (auto& attr : *request_new->mutable_context()->mutable_info_common_attr()) {
      attr_new[attr.name_value()] = &attr;
    }
    for (auto& attr : *request_old->mutable_context()->mutable_info_common_attr()) {
      attr_old[attr.name_value()] = &attr;
    }

    // RANK_CMDS 单独去重后对比
    {
      auto iter_old = attr_old.find(ContextInfoCommonAttr::RANK_CMDS);
      auto iter_new = attr_new.find(ContextInfoCommonAttr::RANK_CMDS);
      int32_t rank_cmds_same = 0;
      if (iter_new != attr_new.end() && iter_old != attr_old.end()) {
        absl::flat_hash_set<uint64_t> old_cmd_set(iter_old->second->uint64_list_value().begin(),
                                                  iter_old->second->uint64_list_value().end());
        absl::flat_hash_set<uint64_t> new_cmd_set(iter_new->second->uint64_list_value().begin(),
                                                  iter_new->second->uint64_list_value().end());
        if (new_cmd_set.size() != old_cmd_set.size()) {
          rank_cmds_same = 1;
        } else {
          for (auto old_cmd : old_cmd_set) {
            if (new_cmd_set.count(old_cmd) == 0) {
              rank_cmds_same = 4;
              break;
            }
          }
        }
        if constexpr (enable_debug) {
          LOG_IF_EVERY_N(INFO, rank_cmds_same > 0, 1000) << "[" << tag << "]"
                                                         << "[jyf_debug]: RANK_CMDS not equal "
                                                         << "| status:" << rank_cmds_same;
        }
        ks::ad_base::AdPerf::IntervalLogStash(100 * static_cast<bool>(!rank_cmds_same), "ad.ad_rank",
                                              "ps_request_field_same_rate", tag, "RANK_CMDS",
                                              std::to_string(rank_cmds_same));
        if (iter_new->second->uint64_list_value_size() != iter_old->second->uint64_list_value_size()) {
          iter_new->second->clear_uint64_list_value();
          iter_new->second->mutable_uint64_list_value()->CopyFrom(iter_old->second->uint64_list_value());
        }
      }
    }


    // 图化后这个特征暂时用 ad 全集构建，后续看所有 context 类的特征能否都把不打分的创意排除掉
    // {
    //   auto iter_new = attr_new.find(ContextInfoCommonAttr::AD_QUEUE_TYPE_V2);
    //   auto iter_old = attr_old.find(ContextInfoCommonAttr::AD_QUEUE_TYPE_V2);
    //   if (iter_new != attr_new.end() && iter_old != attr_old.end()) {
    //     for (auto& [k, v] : iter_new->second->map_unit64_bool_value()) {
    //       if (iter_old->second->map_unit64_bool_value().find(k) ==
    //         iter_old->second->map_unit64_bool_value().end()) {
    //         iter_old->second->mutable_map_unit64_bool_value()->insert({k, v});
    //       }
    //     }
    //   }
    // }

    // 图化后存在的 & 图化前里不存在的特征，不需要对比
    // std::vector<ContextInfoCommonAttr*> unique_attrs;
    // for (const auto& pair : attr_new) {
    //   if (attr_old.find(pair.first) == attr_old.end()) {
    //     unique_attrs.push_back(pair.second);
    //   }
    // }

    // for (auto attr : unique_attrs) {
    //   auto attr_old = request_old->mutable_context()->add_info_common_attr();
    //   attr_old->CopyFrom(*attr);
    // }
  }

  for (auto field_name : diff_field_content) {
    const google::protobuf::FieldDescriptor* field = descriptor->FindFieldByName(field_name);
    if (!field) {
        LOG(WARNING) << "[" << tag << "]" << "Field not found: " << field_name;
        return;
    }

#define GET_OR_REPLACE_POD_TYPE(TYPE)                                                                      \
  if (field->is_repeated()) {                                                                              \
    int32_t repeated_compare_status = 0;                                                                   \
    auto old_field_size = reflection->FieldSize(*request_old, field);                                      \
    auto new_field_size = reflection->FieldSize(*request_new, field);                                      \
    std::string output;                                                                                    \
    if (old_field_size != new_field_size && field_name != "cmd") {                                         \
      repeated_compare_status = 1;                                                                         \
      if (old_field_size == 0) {                                                                           \
        repeated_compare_status = 2;                                                                       \
      }                                                                                                    \
      if (new_field_size == 0) {                                                                           \
        repeated_compare_status = 3;                                                                       \
      }                                                                                                    \
    } else {                                                                                               \
      if (compare_exclude_field->count(field_name) == 0) {                                                 \
        google::protobuf::util::MessageDifferencer differencer;                                            \
        differencer.set_message_field_comparison(                                                          \
            google::protobuf::util::MessageDifferencer::MessageFieldComparison::EQUIVALENT);               \
        differencer.set_repeated_field_comparison(google::protobuf::util::MessageDifferencer::AS_SET);     \
        differencer.set_report_moves(false);                                                               \
        if (field_name == "cmd") {                                                                         \
          auto* mutable_old =                                                                              \
              request_old->GetReflection()->MutableRepeatedPtrField<std::string>(request_old, field);      \
          auto* mutable_new =                                                                              \
              request_new->GetReflection()->MutableRepeatedPtrField<std::string>(request_new, field);      \
          deduplicate_repeated_field(mutable_old);                                                         \
          deduplicate_repeated_field(mutable_new);                                                         \
        }                                                                                                  \
        if constexpr (enable_debug) {                                                                      \
          differencer.ReportDifferencesToString(&output);                                                  \
        }                                                                                                  \
        if (!differencer.CompareWithFields(*request_old, *request_new, {field}, {field})) {                \
          repeated_compare_status = 4;                                                                     \
        }                                                                                                  \
      }                                                                                                    \
    }                                                                                                      \
    ks::ad_base::AdPerf::IntervalLogStash(100 * static_cast<bool>(!repeated_compare_status), "ad.ad_rank", \
                                          "ps_request_field_same_rate", tag, field_name,                   \
                                          std::to_string(repeated_compare_status));                        \
    if constexpr (enable_debug) {                                                                          \
      if (repeated_compare_status > 0) {                                                                   \
        LOG_EVERY_N(INFO, 1000) << "[" << tag << "]"                                                       \
                                << "[jyf_debug]: " << output << "| field:" << field_name                   \
                                << "| status:" << repeated_compare_status;                                 \
      }                                                                                                    \
    }                                                                                                      \
    if (replace) {                                                                                         \
      reflection->ClearField(request_old, field);                                                          \
      auto size = reflection->FieldSize(*request_new, field);                                              \
      for (int64_t i = 0; i < size; ++i) {                                                                 \
        reflection->Add##TYPE(request_old, field, reflection->GetRepeated##TYPE(*request_new, field, i));  \
      }                                                                                                    \
    }                                                                                                      \
  } else {                                                                                                 \
    auto old_value = reflection->Get##TYPE(*request_old, field);                                           \
    auto new_value = reflection->Get##TYPE(*request_new, field);                                           \
    bool is_equal = (old_value == new_value);                                                              \
    ks::ad_base::AdPerf::IntervalLogStash(100 * is_equal, "ad.ad_rank", "ps_request_field_same_rate", tag, \
                                          field_name);                                                     \
    if constexpr (enable_debug) {                                                                          \
      if (!is_equal) {                                                                                     \
        LOG_EVERY_N(INFO, 1000) << "[" << tag << "]"                                                       \
                                << "[jyf_debug]field " << field_name << ", old value: " << old_value       \
                                << ", new value: " << new_value;                                           \
      }                                                                                                    \
    }                                                                                                      \
    if (replace) {                                                                                         \
      reflection->Set##TYPE(request_old, field, new_value);                                                \
    }                                                                                                      \
  }

    if (field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_MESSAGE) {
      if (field->is_repeated()) {
        int32_t repeated_compare_status = 0;
        auto old_field_size = reflection->FieldSize(*request_old, field);
        auto new_field_size = reflection->FieldSize(*request_new, field);
        std::string output;
        if (old_field_size != new_field_size) {
          repeated_compare_status = 1;
          if (old_field_size == 0) {
            repeated_compare_status = 2;
          }
          if (new_field_size == 0) {
            repeated_compare_status = 3;
          }
        } else {
          if (compare_exclude_field->count(field_name) == 0) {
            google::protobuf::util::MessageDifferencer differencer;
            differencer.TreatAsMap(
                kuaishou::ad::algorithm::UniversePredictRequest::descriptor()->FindFieldByName(
                    "cmd_item_mapping"),
                kuaishou::ad::algorithm::CmdItemMapping::descriptor()->FindFieldByName("cmdkey"));
            differencer.TreatAsMap(
                kuaishou::ad::algorithm::UniversePredictRequest::descriptor()->FindFieldByName(
                    "item_context"),
                kuaishou::ad::algorithm::ItemContext::descriptor()->FindFieldByName("item_id"));
            differencer.set_message_field_comparison(
                google::protobuf::util::MessageDifferencer::MessageFieldComparison::EQUIVALENT);
            differencer.set_repeated_field_comparison(google::protobuf::util::MessageDifferencer::AS_SET);
            if constexpr (enable_debug) {
              differencer.ReportDifferencesToString(&output);
            }
            if (!differencer.CompareWithFields(*request_old, *request_new, {field}, {field})) {
              repeated_compare_status = 4;
            }
          }
        }

        ks::ad_base::AdPerf::IntervalLogStash(100 * static_cast<bool>(!repeated_compare_status), "ad.ad_rank",
                                              "ps_request_field_same_rate", tag, field_name,
                                              std::to_string(repeated_compare_status));
        if constexpr (enable_debug) {
          if (repeated_compare_status > 0) {
            LOG_EVERY_N(INFO, 1000) << "[" << tag << "]"
                                    << "[jyf_debug]report: " << output << "| field:" << field_name
                                    << "| status:" << repeated_compare_status;
          }
        }
        if (replace) {
          reflection->ClearField(request_old, field);
          auto size = reflection->FieldSize(*request_new, field);
          for (int64_t i = 0; i < size; ++i) {
            google::protobuf::Message* sub_message_old = reflection->AddMessage(request_old, field);
            const google::protobuf::Message& sub_message_new =
                reflection->GetRepeatedMessage(*request_new, field, i);
            sub_message_old->CopyFrom(sub_message_new);
          }
        }
      } else {
        google::protobuf::Message* sub_message_old = reflection->MutableMessage(request_old, field);
        const google::protobuf::Message& sub_message_new = reflection->GetMessage(*request_new, field);
        // protobuf 的 pb diff 工具时间复杂度为 O(n^2logn), 对性能折损很大
        if (compare_exclude_field->count(field_name) == 0) {
          google::protobuf::util::MessageDifferencer differencer;
          std::string output;
          if constexpr (enable_debug) {
            differencer.ReportDifferencesToString(&output);
          }
          differencer.TreatAsMap(
              kuaishou::ad::algorithm::Context::descriptor()->FindFieldByName("info_common_attr"),
              kuaishou::ad::ContextInfoCommonAttr::descriptor()->FindFieldByName("name_value"));
          differencer.set_report_moves(false);
          bool is_equal = differencer.Compare(sub_message_new, *sub_message_old);
          ks::ad_base::AdPerf::IntervalLogStash(100 * is_equal, "ad.ad_rank", "ps_request_field_same_rate",
                                                tag, field_name);
          if constexpr (enable_debug) {
            if (!is_equal) {
              auto pb_2_json = [](const google::protobuf::Message& msg) {
                std::string json_output;
                google::protobuf::util::JsonPrintOptions options;
                options.add_whitespace = true;
                google::protobuf::util::MessageToJsonString(msg, &json_output, options);
                return json_output;
              };
              LOG_EVERY_N(INFO, 1000)
                  << "[" << tag << "]"
                  << "[jyf_debug]report:" << output << ",field" << field_name << ","
                  << "base msg:" << pb_2_json(*sub_message_old) << ",exp msg:" << pb_2_json(sub_message_new);
            }
          }
        }
        if (replace) {
          sub_message_old->CopyFrom(sub_message_new);
        }
      }
    } else if (field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_UINT64) {
      GET_OR_REPLACE_POD_TYPE(UInt64);
    } else if (field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_UINT32) {
      GET_OR_REPLACE_POD_TYPE(UInt32);
    } else if (field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_BOOL) {
      GET_OR_REPLACE_POD_TYPE(Bool);
    } else if (field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_STRING) {
      GET_OR_REPLACE_POD_TYPE(String);
    } else if (field->cpp_type() == google::protobuf::FieldDescriptor::CPPTYPE_ENUM) {
      GET_OR_REPLACE_POD_TYPE(EnumValue)
    } else {
      LOG_EVERY_N(INFO, 100000) << "field " << field_name << " has bad message type " << field->cpp_type();
    }
  }
  ks::ad_base::AdPerf::IntervalLogStash(GetTimestampNoDiff() - start_timestamp, "ad.ad_rank",
                                        "ps_request_diff_timeout", tag);
#undef GET_OR_REPLACE_POD_TYPE
}

ContextData* GetContextData(ks::platform::AddibleRecoContextInterface* context) {
  auto ps_context_wrapper = context->GetMutablePtrCommonAttr<AdContextWrapper>("ad_context_wrapper");
  if (!ps_context_wrapper || !ps_context_wrapper->Get()) {
    return nullptr;
  }
  auto ps_context = ps_context_wrapper->Get();
  return ps_context->GetMutableContextData<ContextData>();
}

bool IsTableNameValid(const std::string& table_name) {
  return table_name == "ad_list_item_table" ||
         table_name == "native_ad_list_item_table" ||
         table_name == "fanstop_ad_list_item_table";
}

void PhotoRerankScoreGetter::Init(ContextData* session_data) {
  if (nullptr == session_data) return;
  session_data_ = session_data;
  const auto& spdm_ctx = session_data_->get_spdm_ctx();
  client_ai_score_type = spdm_ctx.TryGetInteger("client_ai_score_type", 0);
  unify_cpm_inner_weight = spdm_ctx.TryGetDouble("client_ai_pk_score_unify_cpm_inner_weight", 0);
  unify_cpm_outer_weight = spdm_ctx.TryGetDouble("client_ai_pk_score_unify_cpm_outer_weight", 0);
  unify_bonus_inner_weight = spdm_ctx.TryGetDouble("client_ai_pk_score_unify_bonus_inner_weight", 0);
  unify_bonus_outer_weight = spdm_ctx.TryGetDouble("client_ai_pk_score_unify_bonus_outer_weight", 0);
}
int64_t PhotoRerankScoreGetter::GetScore(const AdCommon* p_ad) {
  if (nullptr == session_data_ || nullptr == p_ad) { return 0; }
  int64_t score = p_ad->get_rank_benifit();
  if (1 == client_ai_score_type) {
    score = p_ad->get_cpm();
  } else if (3 == client_ai_score_type) {
    auto unify_cpm = p_ad->get_cpm();
    auto unify_bonus = p_ad->get_rank_benifit() - unify_cpm;
    if (p_ad->Is(AdFlag::is_inner_loop_ad)) {
      score = unify_cpm_inner_weight * unify_cpm + unify_bonus_inner_weight * unify_bonus;
    } else {
      score = unify_cpm_outer_weight * unify_cpm + unify_bonus_outer_weight * unify_bonus;
    }
  }
  return score;
}
}  // namespace utility
}  // namespace ad_rank
}  // namespace ks
