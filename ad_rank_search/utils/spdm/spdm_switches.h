#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace ad_rank {

// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4
// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.


// 以下为 kconf 开关声明.
DECLARE_SPDM_KCONF_BOOL(trans_corporation_name);  // [liyueyang05] 传 corporation_name 字段
DECLARE_SPDM_KCONF_BOOL(enableDedupCidInList);  // [zhoushuaiyin] 重复创意检测打点
DECLARE_SPDM_KCONF_BOOL(enableDupFactorCheck);  // [zhoushuaiyin] 重复创意检测打点
DECLARE_SPDM_KCONF_BOOL(enableNewItemKey);
DECLARE_SPDM_KCONF_BOOL(enableFixIndexDefaultValue);
DECLARE_SPDM_KCONF_BOOL(enableMoveDot);
DECLARE_SPDM_KCONF_BOOL(enablePlayetBoostKconfReplace);
DECLARE_SPDM_KCONF_BOOL(enableNodeTimeCost);
DECLARE_SPDM_KCONF_BOOL(enablePidServiceBonusConf);  // [weizhiyong03] bonus 开关
DECLARE_SPDM_KCONF_BOOL(enableCmdKeyRecord);
DECLARE_SPDM_KCONF_BOOL(enableRecoRetargetLog2);
DECLARE_SPDM_KCONF_BOOL(enableSwitchCartoonKconf);
DECLARE_SPDM_KCONF_BOOL(enableHiddenCostRecord);
DECLARE_SPDM_KCONF_BOOL(enableAggrFailReasonRecord);
DECLARE_SPDM_KCONF_BOOL(enablePredictScoreStandby);  // [zhoushuaiyin] 预估值兜底替换开关
DECLARE_SPDM_KCONF_BOOL(enablePecMergeStyle);  // [zhouting03] pec 融合样式
DECLARE_SPDM_KCONF_BOOL(disableAllPerf);  // [heqian] perf 总开关
DECLARE_SPDM_KCONF_BOOL(disableBgTaskStop);  // [wangjiabin05] 是否停止 bg task sleep
DECLARE_SPDM_KCONF_BOOL(enableFillAuthorForPredict);  // [wangjiabin05] 模型预估是否填充 authorid
DECLARE_SPDM_KCONF_BOOL(enableOuterNativePhotoDcafSample);  // [wangyangrui] 外循环短视频软广, 写 dcaf 样本
DECLARE_SPDM_KCONF_BOOL(switchCommonAttrOfFanstopSessionData);  // [zhangguanglei] 替换字段的使用
DECLARE_SPDM_KCONF_BOOL(enable_reco_frrank_ue_kconf);  // [guohongkuan]  请求 uescore 的 kconf 开关
DECLARE_SPDM_KCONF_BOOL(enableRewardedConversionCalibrate);  // [yangyanxi] 激励激活模型校准
DECLARE_SPDM_KCONF_BOOL(enableHardFastopNoLive);  // [wangjiabin05] 支持非直播粉条硬广
DECLARE_SPDM_KCONF_BOOL(enableSearchDiversionSourceBoost);  // [zhaocuncheng] 搜索导流 query 来源调价 ab 开关
DECLARE_SPDM_ABTEST_BOOL(enable_recommend_query_low_cpm);  // [niejinlong] 搜索导流召回降低阈值
DECLARE_SPDM_ABTEST_BOOL(drop_down_invert_ab);
DECLARE_SPDM_ABTEST_BOOL(enable_force_ocpx_name);
DECLARE_SPDM_KCONF_BOOL(enableSearchLiveSingleBoost);  // [wangning14] 搜索广告直播合并转化类型 boost
DECLARE_SPDM_KCONF_BOOL(enableSearchLiveAccountBoost);  // [linwei06] 搜索广告直播账户维度 boost 统一复用
DECLARE_SPDM_KCONF_BOOL(enableEcpcConvPredict);  // [songxu] ecpc 模型预估
DECLARE_SPDM_KCONF_BOOL(
    enableOuterLoopNativeCallAdFeature);  // [zhangruikang] 开启 ad_list 外循环原生广告请求策略正排审核字段
DECLARE_SPDM_KCONF_BOOL(enableFeedAdxServerClientCtrDefault);  // [wangjiabin05] 双列 adx 使用默认系数
DECLARE_SPDM_KCONF_BOOL(enableRankBiznameControl);  // [weiyilong] rank 识别压测标记
DECLARE_SPDM_KCONF_BOOL(enableSearchNativeStrictStatus);  // [weiyilong] 搜索获取窄口径原生标识
DECLARE_SPDM_KCONF_BOOL(enableDSLClearUnloginUserId);  // [weiyilong] DSL 清理 unlogin user id
DECLARE_SPDM_KCONF_BOOL(enablePostSeparateDataProcCb);  // [weiyilong] Post 分离 DataProc 回调
DECLARE_SPDM_KCONF_BOOL(enableSearchCommonAttr);  // [weiyilong] 搜索特有 CommonAttr
DECLARE_SPDM_KCONF_BOOL(enableSearchInterveneSortFix);  // [weiyilong] 搜索干预排序修复
DECLARE_SPDM_KCONF_BOOL(enableInnerStreamPosDiscountConfig);  // [niejinlong] 搜索内流独立分位置打折配置
DECLARE_SPDM_KCONF_BOOL(enablePrintEcpcRatio);  //  [xiaoyuhao] ecpc ratio 分布打点开关
DECLARE_SPDM_KCONF_BOOL(enableFixRequestCmdSeq);  //  [xiaoyuhao] 修复 ps request cmd 顺序
DECLARE_SPDM_KCONF_BOOL(enableCheckCmdConflictBefore);  //  [xiaoyuhao] cmd 冲突检测前置
DECLARE_SPDM_KCONF_BOOL(enablePredictValueWatcher);  //  [xiaoyuhao] 预估值监控开关
DECLARE_SPDM_KCONF_BOOL(enableRankSearchCrossFeature);  //  [zhaodi] 搜索精排交叉特征开关
DECLARE_SPDM_KCONF_BOOL(enableRankSplashGenAck);  //  [zhaodi] 开屏样本生成逻辑上移
DECLARE_SPDM_KCONF_BOOL(searchFeatureIndexFillRatePerf);  //  [wanglei10] 搜索策略正排数据覆盖率打点
DECLARE_SPDM_KCONF_BOOL(enableT7ROISkipBillingSeparate);  // [wangtao21] roi7 跳过计费分离
DECLARE_SPDM_KCONF_BOOL(enableNewProductCategoryForModel)  // [liliangmin] 开关透传
DECLARE_SPDM_KCONF_BOOL(enableItemCardAdmitFix);  // [weiyilong] 商品卡准入修复
DECLARE_SPDM_KCONF_BOOL(enableLiveCandidateSkipCheck);  // [wangyangrui]
DECLARE_SPDM_KCONF_BOOL(enableMovePredictFeature);  // [xiaoyuhao] 迁移新特征开关
DECLARE_SPDM_KCONF_BOOL(enableRtaSendFeatureId);  // [xiaoyuhao] Rta ad 层级下发 featureId
DECLARE_SPDM_KCONF_BOOL(enableFillAggBiddingRelease);
DECLARE_SPDM_KCONF_BOOL(jixuMixRank);
DECLARE_SPDM_KCONF_BOOL(enableTransferInnerLiveRoas);  //  [xiaoyuchao] 直播 roi 模型 pgmv 值后传开关
DECLARE_SPDM_KCONF_BOOL(enableBrandFanstopCvrPerf);  // [jiayalong] 品牌助推异常转化率打点
DECLARE_SPDM_KCONF_BOOL(enableSearchRankBuildPredictReqNew);  // [zhangzhicong] 搜索精排请求图化开关
DECLARE_SPDM_KCONF_BOOL(enableNotFirstScreenFilter);  // [zhangchaoyi03] 搜索抢首屏过滤

// ---------------------- kconf int32 参数声明 -------------------
DECLARE_SPDM_KCONF_INT32(archimedes_max_cache_num);  // [mateng05] 内粉独立部署服务高峰期降级时 rank 返回结果条数（用于缓存） // NOLINT
DECLARE_SPDM_KCONF_INT32(max_author_cnt_for_splash);
DECLARE_SPDM_KCONF_INT32(adxForcePageInterval);
DECLARE_SPDM_ABTEST_DOUBLE(drop_down_invert_ratio);
DECLARE_SPDM_KCONF_INT32(universeLogInfoFreq);  // [zhangyunhao03] 联盟日志打印频率
DECLARE_SPDM_KCONF_INT32(check_time_interval_for_splash_for_splash);
DECLARE_SPDM_KCONF_INT32(perfNodeCostLatency);  //  [zhangguanglei] perf 打点记录长尾 node 耗时
DECLARE_SPDM_KCONF_INT32(outlierWatcherSampleInterval);  //  [xiaoyuhao] outlier_watcher 采样率
DECLARE_SPDM_KCONF_INT32(switchPosManagerInit);  // [zhangguanglei] 调试 pos_manager 初始化方式
DECLARE_SPDM_KCONF_INT32(reco_frrank_ue_shard_num);  // [guohongkuan]  请求 uescore 的 shard_num
DECLARE_SPDM_KCONF_INT32(reco_frrank_ue_time_out);  // [guohongkuan]  请求 uescore 的 time_out
DECLARE_SPDM_KCONF_INT32(second_stage_predict_time_out);  // [guohongkuan]  二阶段请求 rokect 模型的 time_out
DECLARE_SPDM_KCONF_STRING(reco_live_ue_kess_name)  // [guohongkuan]  请求 直播 uescore 的 kess_name
// ---------------------- kconf int64 参数声明 -------------------
// ---------------------- kconf double 参数声明 -------------------
DECLARE_SPDM_KCONF_DOUBLE(fanstopCpmThreshold);  // [gaoyuan09] ?
DECLARE_SPDM_KCONF_DOUBLE(archimedesOcpmMaxEcpm);  // [mateng05] 内粉 Ocpm 订单 ecpm 保护上限
DECLARE_SPDM_KCONF_DOUBLE(adxFeedServerClientCtrDefaultRatio);  //  [wangjiabin05] 双列 adx 默认系数
DECLARE_SPDM_KCONF_DOUBLE(cplMinCostRatio);  // [wangjiabin05] cpl 成本率最低值
DECLARE_SPDM_KCONF_DOUBLE(mixBidUpliftThr);  //  [shoulifu03] 混排报价 uplift 兜底门槛

DECLARE_SPDM_KCONF_BOOL(enableSecondStagePredictV2);  // 请求二阶段模型总开关
DECLARE_SPDM_KCONF_BOOL(rankKconfDebugFlag);
DECLARE_SPDM_KCONF_BOOL(enableSendOriginalUnifyCxr);
DECLARE_SPDM_ABTEST_BOOL(enable_second_stage_predict);  // 请求二阶段模型;
DECLARE_SPDM_KCONF_INT64(rankDirectPredictFlag);
DECLARE_SPDM_ABTEST_INT64(rank_direct_predict_flag);
DECLARE_SPDM_KCONF_BOOL(disableParseTimeoutResponse);
DECLARE_SPDM_ABTEST_STRING(rank_direct_predict_cmdkey_group);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_matrix_unlogin);   // rank 矩阵流量预估允许未登录用户
DECLARE_SPDM_KCONF_BOOL(enableOuterMatrixUnlogin);       // rank 矩阵流量预估允许未登录用户
DECLARE_SPDM_ABTEST_BOOL(enable_skip_cmd_conflict_item);   // 跳过预估冲突 item
DECLARE_SPDM_ABTEST_STRING(rank_exp_cost_time_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_trans_predict_meta_to_pack);   // 透传预估信息到 adpack
DECLARE_SPDM_KCONF_BOOL(enableFixDupPhotoId);
DECLARE_SPDM_ABTEST_BOOL(enable_copy_embedding_attr);
DECLARE_SPDM_ABTEST_BOOL(enable_clean_inefficient_cmdkey);

DECLARE_SPDM_KCONF_DOUBLE(livePosNReleThreshold);  // [gaokaiming]
DECLARE_SPDM_KCONF_DOUBLE(livePosOneCTCVRRatio);   // [gaokaiming]
DECLARE_SPDM_KCONF_DOUBLE(livePosTwoCTCVRRatio);   // [gaokaiming]
DECLARE_SPDM_KCONF_DOUBLE(livePosFiveCTCVRRatio);  // [gaokaiming]

DECLARE_SPDM_ABTEST_INT64(search_linear_program_cpm_threshold);    // [gaokaiming]

DECLARE_SPDM_ABTEST_DOUBLE(search_ads_top_position_relevance_threshold_for_adx);   // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(search_ads_all_position_relevance_threshold_for_adx);   // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(search_ads_mid_position_relevance_threshold_for_adx);   // [gaokaiming]

DECLARE_SPDM_ABTEST_DOUBLE(search_ads_top_position_relevance_threshold_ratio_for_atlas);  // [gaokaiming]

DECLARE_SPDM_ABTEST_DOUBLE(search_ads_top_position_relevance_threshold_ratio_for_native);  // [gaokaiming]

DECLARE_SPDM_KCONF_DOUBLE(searchRelevanceBadcaseConstraint);       // [gaokaiming]
DECLARE_SPDM_KCONF_DOUBLE(searchRelevanceLinearProgramLambda);     // [gaokaiming]
DECLARE_SPDM_KCONF_DOUBLE(searchRelevanceTopPhotoThreshold);       // [gaokaiming]

DECLARE_SPDM_ABTEST_STRING(search_ads_age_cpm_threshold);   // [gaokaiming]

DECLARE_SPDM_KCONF_STRING(searchAdxDebugQuery)              // [gaokaiming]


DECLARE_SPDM_KCONF_BOOL(enableDiffCompareAd);  // [wanglei39] diff 比较
DECLARE_SPDM_KCONF_BOOL(enableReqIndexUsingNewConfig)  // [wanglei39] 用新配置请求正排
DECLARE_SPDM_KCONF_BOOL(enableReqIndexByTail)  // [wanglei39] 通过实例尾号用新配置请求正排
DECLARE_SPDM_KCONF_BOOL(enableMaxQponUpliftCxrRatio)  // [jiangyuzhen03] 启用 qcpx uplift ratio 上限兜底
DECLARE_SPDM_KCONF_BOOL(enableFixItemAttrDiff);  // [wanglei39] 修复 diff
DECLARE_SPDM_KCONF_BOOL(enableFixItemAttrDiffV2);  // [wanglei39] 修复 diff
DECLARE_SPDM_KCONF_DOUBLE(searchRankHcMaxScore);  // [dangpingbo] 排序 hc 上界分数

// ---------------------- kconf string 参数声明 -------------------
DECLARE_SPDM_KCONF_STRING(nebula_frrank_xtr_grpc_name_outerctr)              // [yuanyue03]
DECLARE_SPDM_KCONF_STRING(gamora_frrank_xtr_grpc_name_outerctr)              // [yuanyue03]

DECLARE_SPDM_ABTEST_BOOL(enhance_force_reco);
DECLARE_SPDM_ABTEST_STRING(game_similar_product_exp);  // [jiangnan07] 游戏相似品人群精细产品实验组
DECLARE_SPDM_ABTEST_STRING(soft_hard_union_exp_tag);  // [wanglei39] 软硬广融合实验组
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_adjust_suppress);  // [yushengkai] 喜番调整虚拟金打压
DECLARE_SPDM_ABTEST_DOUBLE(xifan_suppress_adjust_ratio);  //  [yushengkai] 喜番虚拟金打压系数
DECLARE_SPDM_ABTEST_BOOL(enable_fill_item_attr_by_index);  // [wanglei39] 字段填充 by 正排
DECLARE_SPDM_ABTEST_BOOL(enable_req_index_using_new_config);  // [wanglei39] 用新配置请求正排
DECLARE_SPDM_ABTEST_BOOL(enable_fill_inner_loop_item_attr_by_index);  // [wanglei39] 字段填充 by 正排  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_not_fill_item_attr_from_target);  // [wanglei39] 不填充来自 target 传递的某些字段  //  NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_pid_service_ee_seach_budget);
DECLARE_SPDM_ABTEST_BOOL(enable_search_ads_pos_ecpm_relevance_mix);
DECLARE_SPDM_ABTEST_BOOL(enable_pos2_rele_thres_pid_adjust);  // [tiantian06] 搜索相关性阈值调控系数
DECLARE_SPDM_ABTEST_BOOL(disable_search_goods_tab_dedup_isolate);  // [zhangchaoyi03] 搜索商品 tab 去重
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_coin_set_fix_num);
DECLARE_SPDM_ABTEST_BOOL(enable_gross_fix_with_deep_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_scsr_inspire);
DECLARE_SPDM_ABTEST_DOUBLE(fix_target_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_coin_multi_key);
DECLARE_SPDM_ABTEST_BOOL(enable_close_retention_retarget_ecpc);
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_impression_price_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_cpm_thr_new_score);  // [liubing05] cpm 门槛使用 rk
DECLARE_SPDM_ABTEST_BOOL(enable_ecpc_click_2_purchase_exp);  // [songxu] 点击付费 ecpc
DECLARE_SPDM_ABTEST_BOOL(enable_user_expore_auto_param);  // [yuchengyuan] 行业优化重定向自动调参开关
DECLARE_SPDM_ABTEST_BOOL(enable_use_credit_cvr);  // [yuchengyuan] 金融行业用信率优化开关
DECLARE_SPDM_ABTEST_BOOL(enable_fin_credit_roi_ecpc);  // [liuxinyi08] 金融行业小贷 ROI 优化开关
DECLARE_SPDM_ABTEST_BOOL(enable_use_jinjian_credit_cvr);  // [yuchengyuan] 金融行业完件授信率模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_use_jinjian_credit_filter);  // [yuchengyuan] 金融行业完件授信率策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_model_score_product_conf);  // [yuchengyuan] 行业模型产品配置开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecpc_add_ctr_model);  // [yishijie] ecpc 增加优化目标
DECLARE_SPDM_ABTEST_BOOL(enable_account_jinjian_credit_filter);  // [yuchengyuan] 完件授信率账户策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_ind_deep_optimize);  // [yuchengyuan] 金教行业深度优化开关
DECLARE_SPDM_ABTEST_BOOL(enable_ind_deep_ecpc_optimize);  // [yuchengyuan] 金教行业深度 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_coeff_credit);  // [liuxinyi08] 金融授信单修改新预估值打压系数
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_outer_ctr_industry_cali);  // [dongyao03] 外循环短剧行业 ctr 分数校准
DECLARE_SPDM_ABTEST_BOOL(enable_edu_lps_deep_ecpc);  // [linyuhao03] 教育行业深度 ecpc 开关 正价课目标
DECLARE_SPDM_ABTEST_BOOL(enable_edu_leads_submit_deep_ecpc);  // [linyuhao03] 教育行业深度 ecpc 开关 正价课目标 支持私信  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_edu_lps_deep_ecpc_other_ind);  // [linyuhao03] 教育行业深度 ecpc 开关 正价课目标是否扩展到其他行业  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_edu_deep_ecpc_control_bound);  // [linyuhao03] 教育行业深度 ecpc 开关 控制界
DECLARE_SPDM_ABTEST_BOOL(enable_edu_deep_ecpc_coef);  // [linyuhao03] 教育行业深度 ecpc 开关 控制系数
DECLARE_SPDM_ABTEST_BOOL(enable_conv_nextstay_ecpc);  // [yishijie] 次留 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_ind_use_credit_optimize);  // [yuchengyuan] 金教行业用信调价开关
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_search_discount);  // [yushengkai] 搜索适配短剧折扣
DECLARE_SPDM_ABTEST_BOOL(enable_ind_user_expore_hc_limit);  // [yuchengyuan] 金教行业深度 hc 限制上下界
DECLARE_SPDM_ABTEST_BOOL(enable_ind_rta_hc);  // [yuchengyuan] 金教行业深度 hc 新增 RTA 逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_use_supplier_v2);  // [yuchengyuan] 金教行业深度优化  PB  更新
DECLARE_SPDM_ABTEST_DOUBLE(finance_roi_ecpc_coef);  // [yuchengyuan] 金融 roi 调控系数
DECLARE_SPDM_ABTEST_BOOL(enable_jiaotong_ind_user_hc_limit);  // [zhaijianwei] 交通行业深度 hc 限制上下界
DECLARE_SPDM_ABTEST_BOOL(enable_game_request_imp_conv);  //  [guoqi] 请求付费模型
DECLARE_SPDM_ABTEST_BOOL(close_billing_separate);  // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(gfp_skip_owe_control);  // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_divide_flow_explore_page);    // [nizhihao] 发现页分页面 cpm 门槛
DECLARE_SPDM_ABTEST_BOOL(enable_tube_inner_loop_by_campaign_type);  // [yushengkai] 短剧内循环化切换口径
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_mcb_roi_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_click_after_recommend);  // [duantao] 点后推
DECLARE_SPDM_ABTEST_DOUBLE(final_cpm_thr_ratio_soft);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_q_ecpc_splash);  // [songux] 质量分 ecpc 开屏
DECLARE_SPDM_ABTEST_BOOL(enable_adx_server_client_show_rate_factor);  // [heqian] adx server client show 因子迁移 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_purchase_ratio);  // [zhangzhaoyu] 短剧付费曝光打平实验开关
DECLARE_SPDM_ABTEST_STRING(product_ecpc_exp);
DECLARE_SPDM_ABTEST_DOUBLE(reward_game_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(reward_game_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_inner_live_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_inner_live_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_inner_live_ecpc_boost_ratio);
DECLARE_SPDM_ABTEST_STRING(reward_game_ecpc_exp);
DECLARE_SPDM_ABTEST_STRING(ind_credit_roi_exp_id);
DECLARE_SPDM_ABTEST_DOUBLE(wegame_returntime_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(wegame_returntime_ecpc_lower);
DECLARE_SPDM_ABTEST_BOOL(enable_kgame_duration_ecpc);
DECLARE_SPDM_ABTEST_BOOL(enable_kai_game_iaap_ecpc);
DECLARE_SPDM_ABTEST_BOOL(enable_game_conversion_ecpc);
DECLARE_SPDM_ABTEST_DOUBLE(kgame_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(kgame_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(kgame_duration_time_lower);
DECLARE_SPDM_ABTEST_DOUBLE(kgame_duration_time_upper);
DECLARE_SPDM_ABTEST_DOUBLE(kgame_duration_ecpc_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(game_conversion_ecpc_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(game_pay_ecpc_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_search_rank_hc_model);  // [dangpingbo] 排序 hc nodel 生效
DECLARE_SPDM_ABTEST_DOUBLE(search_rank_hc_ratio);  // [dangpingbo] 排序 hc 超参系数
DECLARE_SPDM_ABTEST_DOUBLE(search_rank_hc_model_ratio);  // [dangpingbo] 排序 hc 超参系数

// [linwei06]
DECLARE_SPDM_ABTEST_BOOL(close_c1_order_paid_search_goods);

DECLARE_SPDM_ABTEST_DOUBLE(ecpm_threshold_explore_inner);
DECLARE_SPDM_ABTEST_DOUBLE(amd_live_ecpm_threshold_explore_inner);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_threshold_ratio);
// [jiyang]
DECLARE_SPDM_ABTEST_BOOL(enable_photo_pvtr_filter_sample);
DECLARE_SPDM_ABTEST_STRING(live_bigr_lift_hidden_cost_exptag);
DECLARE_SPDM_ABTEST_BOOL(enable_roas_twin_game_ad_mcb_node);  // [zhangtuchao] ROI 双 走 mcb node
DECLARE_SPDM_ABTEST_BOOL(disable_roas_twin_game_ecpc);  // [zhangtuchao] ROI 双 迁移屏蔽部分 ecpc
DECLARE_SPDM_ABTEST_BOOL(enable_long_ratio_roas_twin_game_ad);  // [zhangtuchao] ROI 双长线优化策略
//  [zengzhengda] 联盟排序计费打平系数透传
DECLARE_SPDM_ABTEST_BOOL(enable_universe_billing_separate_even_opt2);
DECLARE_SPDM_ABTEST_BOOL(enable_jicheng_account_event_order_paied);    //  [luwei] 订单支付账号继承策略
DECLARE_SPDM_ABTEST_BOOL(enable_jicheng_account_ad_merchant_roas);    //  [luwei] roas 账号继承策略
DECLARE_SPDM_ABTEST_BOOL(enable_jicheng_account_only_for_cold);    //  [luwei] 账号继承策略只对冷启动生效
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_s2h_explore_cali);    //  [luwei] 账号继承策略只对冷启动生效
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_s2h_non_explore_cali);    //  [luwei] 账号继承策略只对冷启动生效
DECLARE_SPDM_ABTEST_BOOL(enable_search_pos_n_no_ad);  // [yaolei] 搜索广告前 n 位不放广告
DECLARE_SPDM_ABTEST_BOOL(enable_search_bidword_micro_boost);  // [yaolei] 搜索明投细粒度调价开关
DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_auto_boost);  // [zhangchaoyi03] 搜索快投细粒度自动调价
// [niejinlong] 搜索快投细粒度自动调价 v2
DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_auto_boost_v2_complete);
// [niejinlong] 搜索快投细粒度自动调价 v2
DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_auto_boost_v2_abtest);
DECLARE_SPDM_ABTEST_BOOL(
    enable_search_app_card_ocpx_type_boost);  // [wangshaoxiao] 搜索下载强样式分 ocpx 调价
DECLARE_SPDM_ABTEST_BOOL(enable_search_list_ecpm_best_pos);  // [wangning14] 搜索序列最优
DECLARE_SPDM_ABTEST_BOOL(enable_search_inner_stream_list_ecpm_best_pos);  // [niejinlong] 搜索内流序列最优
DECLARE_SPDM_ABTEST_DOUBLE(search_inner_cpm_threshold_ratio);  // [zhangchaoyi03] 内循环 cpm 阈值系数
DECLARE_SPDM_ABTEST_DOUBLE(search_midnight_cpm_ratio);  // [zhaoyilin05] 搜索广告凌晨放量 cpm 阈值系数
DECLARE_SPDM_ABTEST_DOUBLE(
    search_midnight_top_rele_ratio);  // [zhaoyilin05] 搜索广告凌晨放量 top4 相关性阈值系数
DECLARE_SPDM_ABTEST_BOOL(enable_goods_kbox_ecpm_thres);  // [niejinlong] 搜索商品 kbox 独立 cpm 阈值
DECLARE_SPDM_ABTEST_BOOL(enable_quick_search_product_bonus_discount);
// [wangning14] 搜索广告快投放 bonus 打折
DECLARE_SPDM_ABTEST_BOOL(enable_industry_diversity_hc);  // [liuxiaoyan] 二级行业多样性 hc
DECLARE_SPDM_ABTEST_BOOL(enable_industry_diversity_hc_native);  // [liuxiaoyan] 二级行业 hc
DECLARE_SPDM_ABTEST_BOOL(enable_second_industry_v5);  // [liuxiaoyan] 二级行业升级 5.0
DECLARE_SPDM_ABTEST_BOOL(enable_soft_freq_hc_native);  // [liuxiaoyan] 精排软频控 hc
DECLARE_SPDM_ABTEST_BOOL(enable_soft_freq_hc_hard);  // [liuxiaoyan] 精排软频控 hc

DECLARE_SPDM_ABTEST_BOOL(enable_search_native_boost_ratio);  // [wangning14]
DECLARE_SPDM_ABTEST_BOOL(enable_search_jinniu_moble_roi_model);  // [huangwei06] 搜索 roi 模型统一
DECLARE_SPDM_ABTEST_BOOL(is_search_moble_rank_info);  // [huangwei06] 搜索下发磁力金牛移动版下发物料流
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_inner_skip_rs);
DECLARE_SPDM_ABTEST_BOOL(enable_search_ad_inner_stream_fix_interval);
DECLARE_SPDM_ABTEST_BOOL(enable_erase_normal_boost);  // [huangwei06] 去除强卡的 boost



DECLARE_SPDM_ABTEST_BOOL(enable_search_rank_predict_router_v3);  // [jiangyifan] 搜索精排请求 v2 切 v3
DECLARE_SPDM_ABTEST_BOOL(enable_search_rank_predict_router_v3_new);  // [jiangyifan] 搜索自身 router 请求 v2 切 v3  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_rank_build_predict_resp_new);  // [jiangyifan] 搜索精排解析返回图化开关
DECLARE_SPDM_KCONF_BOOL(enableSearchRankRewritePredictReqContext);  // [jiangyifan] context 特征用图化后请求覆盖 // NOLINT
DECLARE_SPDM_KCONF_BOOL(enableSearchRankPredictDiffAllField);  // [jiangyifan] 对比精排请求 pb 的全部字段
DECLARE_SPDM_KCONF_BOOL(enableSearchRankPredictReplace);  // [jiangyifan] 用图化后的精排请求替换原请求

DECLARE_SPDM_KCONF_BOOL(enableSearchRouterRankBuildPredictReqNew);  // [zhangzhicong] 搜索 router 请求图化
DECLARE_SPDM_ABTEST_BOOL(enable_search_router_rank_build_predict_req_new);  // [zhangzhicong] 搜索 router 请求图化 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_router_rank_build_predict_resp_new);  // [zhangzhicong] 搜索 router 解析图化 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_router_rank_predict_direct);  // [zhangzhicong] 搜索独有 router 模型直连 // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_refactor_splash_boost_coef_prepare);  // [zhangzhicong] splash_boost_coef 逻辑拆出 // NOLINT

// [jianghao07] 优质素材价值验证
DECLARE_SPDM_ABTEST_BOOL(enable_remove_high_quality);
DECLARE_SPDM_ABTEST_BOOL(enable_remove_nogap);
DECLARE_SPDM_ABTEST_BOOL(enable_remove_inner_nogap);
DECLARE_SPDM_ABTEST_BOOL(enable_remove_outer_nogap);

DECLARE_SPDM_ABTEST_BOOL(enable_search_no_feed_ps);  // [huangwei06] 搜索不访问信息流 router
DECLARE_SPDM_ABTEST_BOOL(enable_search_price_protect);  // [zhouxuan06]
DECLARE_SPDM_ABTEST_BOOL(enable_search_global_dynamic_interval);  // [huangwei06] 动态位置调整
DECLARE_SPDM_ABTEST_BOOL(enable_search_bigv_card_in_shop_tab);  // [zhangchaoyi03] 商品 tab 大卡
DECLARE_SPDM_ABTEST_BOOL(enable_search_bigv_card_in_live_tab);  // [zhangchaoyi03] 直播 tab 大卡
DECLARE_SPDM_ABTEST_BOOL(enable_diversity_filter_in_merge);  // [zhangchaoyi03] 队列合并多样性去重
DECLARE_SPDM_ABTEST_BOOL(enable_normal_ad_list_function);  // [zhangchaoyi03] 普通队列广告选取函数
DECLARE_SPDM_ABTEST_BOOL(enable_search_native_simple_ad_gap);  // [zhangchaoyi03] 原生广告简单 ad_gap
DECLARE_SPDM_ABTEST_BOOL(enable_search_native_simple_inner_ad_gap_consider_list);
// [zhangchaoyi03] 原生广告简单 ad_gap 考虑前面 ad
DECLARE_SPDM_ABTEST_BOOL(enable_native_ads_adgap);  // [zhangchaoyi03] 原生广告 ad_gap
DECLARE_SPDM_ABTEST_BOOL(enable_other_stream_native_ads_adgap);  // [zhangchaoyi03] 原生广告 ad_gap
DECLARE_SPDM_ABTEST_BOOL(enable_allow_other_stream_more_ad);  // [zhangchaoyi03] 原生广告实验多传输 ad


DECLARE_SPDM_ABTEST_BOOL(enable_strong_card_queue_select_ads);  // [zhangchaoyi03] 强样式队列
DECLARE_SPDM_ABTEST_BOOL(enable_search_select_strong_uplift);  // [zhangchaoyi03] 强样式按 uplift 选取

DECLARE_SPDM_ABTEST_BOOL(enable_exact_app_skip_base_cpm_thres);
// [zhangchaoyi03] 强样式精确跳过 base cpm
DECLARE_SPDM_ABTEST_BOOL(enable_exact_form_skip_base_cpm_thres);
DECLARE_SPDM_ABTEST_BOOL(enable_bigv_skip_base_cpm_thres);  // [zhangchaoyi03] 强样式跳过 base cpm
DECLARE_SPDM_ABTEST_BOOL(enable_series_skip_base_cpm_thres);  // [zhangchaoyi03] 强样式跳过 base cpm
DECLARE_SPDM_ABTEST_BOOL(enable_kbox_skip_base_cpm_thres);  // [zhangchaoyi03] 强样式跳过 base cpm
DECLARE_SPDM_ABTEST_BOOL(enable_exact_strong_style_skip_cpm_thres);  // [zhangchaoyi03] 强样式跳过 cpm
DECLARE_SPDM_ABTEST_BOOL(enable_bidword_support);  // [zhangchaoyi03] 搜索直投扶持
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_strong_style_skip_cpm_thres);  // [zhangchaoyi03] 强样式跳过 cpm 阈值
DECLARE_SPDM_ABTEST_BOOL(enable_search_high_value_down_pos);  // [wangning14] 高价值位置下移开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_e2e_twin_tower_down_pos);  // [wangning14] 高价值位置下移开关
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_bid_ratio);      // [wantao21] 内循环 ecpc
DECLARE_SPDM_ABTEST_STRING(inner_high_value_visitor_tag);          // [lihantong] 高价人群摸底
DECLARE_SPDM_ABTEST_BOOL(enable_consum_power_price_discount);      // [lihantong] 高价人群摸底
DECLARE_SPDM_ABTEST_BOOL(enable_high_value_u_level);      // [lihantong] 高价人群摸底
DECLARE_SPDM_ABTEST_DOUBLE(high_value_atv_thresh);     // [lihantong] 高价人群摸底
DECLARE_SPDM_ABTEST_DOUBLE(inner_live_default_gpm);     // [lihantong] 高价人群摸底
DECLARE_SPDM_ABTEST_BOOL(enable_inner_industry_frue);      // [lihantong] 高价人群摸底
DECLARE_SPDM_ABTEST_BOOL(enable_search_personalization_down_pos);  // [yangjunyao] 个性化召回不出在前四位开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_midnight_expand);  // [zhaoyilin05] 搜索广告非一线城市凌晨放量开关
DECLARE_SPDM_ABTEST_BOOL(search_author_shop_intent_compete);  // [zhangchaoyi03] 主播店铺竞品
DECLARE_SPDM_ABTEST_BOOL(enable_listwise_diversity_for_combo);  // [niejinlong] 分队列多样性
DECLARE_SPDM_ABTEST_BOOL(enable_search_delete_unuse_point);  // [zhangchaoyi03] 无用 point 去除
DECLARE_SPDM_ABTEST_BOOL(disable_fanstop_diversity_for_combo);  // [niejinlong] 粉条多样性
DECLARE_SPDM_ABTEST_BOOL(enable_listwise_diversity_dedup);  // [niejinlong] 分队列去重多样性
// 搜索二价计费
DECLARE_SPDM_ABTEST_BOOL(enable_search_gsp_price);      // [niejinlong] 搜索二价计费
DECLARE_SPDM_ABTEST_BOOL(enable_search_gsp_price_protect);  // [niejinlong] 计费比保护
DECLARE_SPDM_ABTEST_BOOL(enable_search_normal_region_gsp);  // [niejinlong] 普通样式队列 GSP
DECLARE_SPDM_ABTEST_BOOL(enable_search_all_region_gsp);     // [niejinlong] 所有广告 GSP
DECLARE_SPDM_ABTEST_BOOL(disable_search_mcb_gsp);           // [niejinlong] mcb 关闭 GSP
DECLARE_SPDM_ABTEST_BOOL(enable_normal_list_next_benefit_only);  // [niejinlong] 仅普通样式队列记录 next benefit // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(search_price_protect_lower_bound);    // [niejinlong] 计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(search_price_ocpm_gsp_boost);  // [niejinlong] 非 mcb 计费比 boost
DECLARE_SPDM_ABTEST_DOUBLE(search_price_mcb_gsp_boost);   // [niejinlong] mcb 计费比 boost
DECLARE_SPDM_ABTEST_INT64(top_ctcvr_num);
DECLARE_SPDM_ABTEST_BOOL(enable_fill_is_potential_nc);
DECLARE_SPDM_ABTEST_INT64(nc_prerank_online_model_boost_num);
DECLARE_SPDM_ABTEST_INT64(ac_prerank_online_model_boost_num);
DECLARE_SPDM_ABTEST_BOOL(enable_mt_lps_launch);  // [yaokangping] mcda 表单推全开关
DECLARE_SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix);  // [zengzhengda] 字段 ad_bid_server_group_tag 修复开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix2);  // [zengzhengda] 字段 ad_bid_server_group_tag 修复开关 2  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_amd_live_celebrity_bonus);  // [wangning14] 搜索广告大 V 卡 bonus 开关
// [fengyajuan] 短视频订单按照指定 key boost 实验
DECLARE_SPDM_ABTEST_BOOL(enable_conv_retention_macda_up_calibration);  // [yaokangping] 激活次留双出价 mcda up 助攻开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_conv_retention_macda_up_calibration_by_exp_tag);  // [yaokangping] 激活次留双出价 mcda up 助攻 exp tag 开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_conv_retention_macda_up_calibration_launch);  // [yaokangping] 激活次留双出价 mcda up 助攻推全开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_mt_purchase_launch);  // [yaokangping] mcda 付费推全开关
DECLARE_SPDM_ABTEST_BOOL(enable_mt_shouxin_launch);  // [yaokangping] mcda 授信推全开关
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_merchant_retention_live_ad);    // [liubing05] 激励直播保送
DECLARE_SPDM_ABTEST_BOOL(enable_universe_indusytle_cem_alg);  //  [xiemiao] 联盟媒体行业 CEM 探索开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_native_ad_billing_seperate);        // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_close_native_skip_ab);
DECLARE_SPDM_ABTEST_BOOL(enable_native_stats_auto_param);                // [guoyuan03] 软广统计调参
DECLARE_SPDM_ABTEST_BOOL(enable_native_auto_param_follow);                // [guoyuan03] 软广统计调参
DECLARE_SPDM_ABTEST_BOOL(enable_native_auto_param_inspire);                // [guoyuan03] 软广统计调参
DECLARE_SPDM_ABTEST_BOOL(enable_deduplicate_by_creative_id);
DECLARE_SPDM_ABTEST_STRING(native_stats_auto_param_expid_group);          // [guoyuan03] 软广统计调参
DECLARE_SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_nebula);   // [guoyuan03] 软广统计调参
DECLARE_SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_follow);          // [jinhui05] 软广统计调参
DECLARE_SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_nebula_follow);   // [jinhui05] 软广统计调参
DECLARE_SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_inspire);     // [jinhui05] 软广统计调参
DECLARE_SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_nebula_inspire);  // [jinhui05] 软广统计调参
DECLARE_SPDM_ABTEST_BOOL(enable_native_adx_biding_exp);        // [songxu] adx 软广 竞价
DECLARE_SPDM_ABTEST_BOOL(enable_cid_ad_bid_mcb);               // [songxu] cid roi

DECLARE_SPDM_ABTEST_BOOL(enable_pre_mt_purchase);        // [guoqi03] pre-ecpc 开关
// [liuguoyu] newlands 素材扶持开关


// [luowenjuan] 激励广告 金币长期价值探索
DECLARE_SPDM_ABTEST_BOOL(enable_coin_long_term_value);  // 探索长期价值开关
DECLARE_SPDM_ABTEST_DOUBLE(icentive_coin_long_term_eprice_lower);  // 策略最低 eprice
DECLARE_SPDM_ABTEST_DOUBLE(icentive_coin_long_term_eprice_upper);  // 策略最高 eprice
DECLARE_SPDM_ABTEST_DOUBLE(icentive_coin_long_term_adjust_ratio);  // 金币调整系数
DECLARE_SPDM_ABTEST_DOUBLE(icentive_coin_long_term_upper);  // 最高 coin
DECLARE_SPDM_ABTEST_DOUBLE(icentive_coin_long_term_lower);  // 最低 coin

// [zhangyuyang05] 激励广告 重构金币策略
DECLARE_SPDM_ABTEST_BOOL(enable_only_unify_normal_calc_coin);  // 关闭老硬广策略
DECLARE_SPDM_ABTEST_BOOL(enable_only_unify_native_calc_coin);  // 关闭老软广策略
DECLARE_SPDM_ABTEST_DOUBLE(unify_calc_coin_default_roi);  //  默认策略的 ROI
DECLARE_SPDM_ABTEST_DOUBLE(incntv_coin_roi_control_coef);  // 激励金币 ROI 调控系数
DECLARE_SPDM_ABTEST_DOUBLE(incntv_cpm2value_coef);  // 激励广告 cpm 到预期价值的折算系数
DECLARE_SPDM_ABTEST_BOOL(enable_ucc_mdp_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(ucc_mdp_ltv_coef);
DECLARE_SPDM_ABTEST_DOUBLE(ucc_mdp_ltv_upper_coef);
DECLARE_SPDM_ABTEST_STRING(ucc_mdp_ltv_exp_key);
DECLARE_SPDM_ABTEST_INT64(incentive_ad_shallow_treatment_max_num);  // 最多 treat 数量
DECLARE_SPDM_ABTEST_STRING(incntv_ad_monitor_tag);  // 监控 tag
DECLARE_SPDM_ABTEST_DOUBLE(unify_coin_upper);
DECLARE_SPDM_ABTEST_DOUBLE(unify_coin_lower);
DECLARE_SPDM_ABTEST_DOUBLE(unify_calc_coin_explore_traffic_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_unify_calc_coin_percent_trt);
DECLARE_SPDM_ABTEST_STRING(unify_calc_coin_percent_trt_str);
DECLARE_SPDM_ABTEST_BOOL(unify_calc_coin_trt_use_percent);
DECLARE_SPDM_ABTEST_DOUBLE(unify_calc_coin_task_cnt_control_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_has_more);
DECLARE_SPDM_ABTEST_STRING(unify_calc_coin_upper_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_refactor_ucc_ad_trt);
DECLARE_SPDM_ABTEST_BOOL(enable_budget_allocation_user_roi_alpha_bias);
DECLARE_SPDM_ABTEST_DOUBLE(rank_has_more_thres);

// [xuxu] 激励营销 PEC
DECLARE_SPDM_ABTEST_BOOL(enable_close_invoke_app_control);
DECLARE_SPDM_ABTEST_BOOL(enable_dsp_invoked_order_paid_logic_move);     // 信息流拉活 订单支付 准入逻辑迁移
DECLARE_SPDM_ABTEST_BOOL(enable_pec_sensitive_user);  // pec 金币不敏感用户准入
DECLARE_SPDM_ABTEST_BOOL(enable_pec_coin_switch);  // pec 平台测广告开启
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_coin_pec);  // 激励视频接入金币 pec
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_coin_pec_whitelist);  // 激励视频接入金币 pec 开启白名单
DECLARE_SPDM_ABTEST_BOOL(enable_pec_support_for_live);
DECLARE_SPDM_ABTEST_DOUBLE(outside_loop_server_client_show_rate_nebula);
DECLARE_SPDM_ABTEST_DOUBLE(outside_loop_server_client_show_rate_thanos);
DECLARE_SPDM_ABTEST_INT64(max_invoked_task_compelete_count_num);
DECLARE_SPDM_ABTEST_INT64(factor_info_max_size);
DECLARE_SPDM_ABTEST_INT64(max_conversion_task_compelete_count_num);
DECLARE_SPDM_ABTEST_BOOL(enable_award_active_app_new);
DECLARE_SPDM_ABTEST_INT64(inspire_live_random_num);
DECLARE_SPDM_ABTEST_INT64(inspire_live_random_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_plus_account_bonus);
DECLARE_SPDM_ABTEST_INT64(pec_coupon_specific_template_id);
DECLARE_SPDM_ABTEST_INT64(pec_coupon_max_discount_value);
DECLARE_SPDM_ABTEST_INT64(coupon_threshold_add_value);
DECLARE_SPDM_ABTEST_STRING(pec_coupon_strategy_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_support_landingpage);
DECLARE_SPDM_ABTEST_INT64(pec_coupon_cpa_bid_filter_low_thr);
DECLARE_SPDM_ABTEST_INT64(pec_coupon_cpa_bid_filter_high_thr);
DECLARE_SPDM_ABTEST_INT64(pec_coupon_set_fix_price);
DECLARE_SPDM_ABTEST_INT64(max_invoked_task_compelete_count_num_merchant);
DECLARE_SPDM_ABTEST_INT64(inspire_invoked_award_interval_second_merchant);
DECLARE_SPDM_ABTEST_INT64(live_pec_coupon_set_fix_price);
DECLARE_SPDM_ABTEST_INT64(live_pec_coupon_cpa_bid_filter_low_thr);
DECLARE_SPDM_ABTEST_INT64(live_pec_coupon_cpa_bid_filter_high_thr);
DECLARE_SPDM_ABTEST_INT64(coupon_threshold_discount_max_ratio);
DECLARE_SPDM_ABTEST_INT64(live_coupon_threshold_discount_max_ratio);
DECLARE_SPDM_ABTEST_INT64(coupon_high_threshold_filter_add_value);
DECLARE_SPDM_ABTEST_INT64(ecom_sensitive_user_fix_discount);

// [gaozepeng] 下单激励
DECLARE_SPDM_ABTEST_BOOL(enable_specific_deep_rewarded_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_fixed_order_paid_deep_coin);
DECLARE_SPDM_ABTEST_STRING(fixed_order_paid_deep_coin_str);
DECLARE_SPDM_ABTEST_BOOL(enable_coin_order_paied_style);
DECLARE_SPDM_ABTEST_INT64(coin_order_paied_style_start_view);
DECLARE_SPDM_ABTEST_INT64(coin_order_paied_award_interval_second);
DECLARE_SPDM_ABTEST_INT64(coin_order_paied_task_finish_upper);
DECLARE_SPDM_ABTEST_STRING(deep_rewarded_exp_tag);
DECLARE_SPDM_ABTEST_STRING(deep_rewarded_coin_list_str);
DECLARE_SPDM_ABTEST_STRING(rewarded_deep_coin_exp_tag);
DECLARE_SPDM_ABTEST_STRING(order_paied_filter_media_app_version);
DECLARE_SPDM_ABTEST_BOOL(enable_item_price_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_random_deep_rewarded_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_optimal_deep_rewarded_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_pass_calc_deep_rewarded_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_deep_rewarded_coin_gross);
DECLARE_SPDM_ABTEST_BOOL(enable_unify_calc_deep_rewarded_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_platform_profit_maximum);
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_order_deep_rewarded_fix_coin);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_ecpc);
DECLARE_SPDM_ABTEST_INT64(inspire_order_deep_rewarded_fix_coin);
DECLARE_SPDM_ABTEST_INT64(order_paied_item_price_theshold);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_order_deep_rewarded_fix_discount_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_order_deep_rewarded_lambda);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_order_deep_rewarded_lambda_main);
DECLARE_SPDM_ABTEST_DOUBLE(deep_rewarded_ecpc_max_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(deep_rewarded_rate_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(cpm_adjust_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_rewarded_use_min_price);
DECLARE_SPDM_ABTEST_BOOL(enable_order_rewarded_black_author);
DECLARE_SPDM_ABTEST_BOOL(enable_holdout_pec_coin_main);
DECLARE_SPDM_ABTEST_BOOL(enable_holdout_pec_coin_nebula);

// 搜索广告激励任务
DECLARE_SPDM_ABTEST_DOUBLE(search_inspire_ad_box_fix_target_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_task_progress_info);
DECLARE_SPDM_ABTEST_DOUBLE(search_inspire_ad_box_single_col_order_paied_coin_discount);
DECLARE_SPDM_ABTEST_DOUBLE(search_inspire_ad_box_cpm_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(search_inspire_ad_box_single_col_deep_optimization_coef);
DECLARE_SPDM_ABTEST_INT64(search_inspire_ad_box_order_paied_item_price_theshold);
DECLARE_SPDM_ABTEST_STRING(search_inspire_ad_box_exp_tag);
DECLARE_SPDM_ABTEST_STRING(search_inspire_ad_box_single_col_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_duanju_thanos_nebula);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_duanju_thanos_main);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_fanstop_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_single_col_fanstop_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_boost_optimize);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_single_col_boost_optimize);
DECLARE_SPDM_ABTEST_BOOL(enable_search_inspire_duanju_boost_optimize);

// [yuancuili] 货架直播卡订单调 pltv 模型
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_live_order_pltv_model_exp);
// [yuancuili] 货架直播卡 pgpm leverage score
DECLARE_SPDM_ABTEST_BOOL(enable_bh_pgpm_leverage_score);  //实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_mall_pgpm_leverage_score);  //实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_pgpm_leverage_score);  //实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_dsp_micro_video_deep_reward);
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_pec_mingtou);
// [xuxu] 激励补贴
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_bcb_bonus);
// [gaozepeng] 激励 ecpc 相关策略
DECLARE_SPDM_ABTEST_DOUBLE(inspire_video_ecpc_ratio);
// [xuxu] 激励直播增补
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_new_user_calc_coin);
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_new_user_cpm_thr);
DECLARE_SPDM_ABTEST_STRING(inspire_live_new_user_coin_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_billing_separate_rewarded_video);
//  [moqi] 硬广引流走直播 ecpc 策略
DECLARE_SPDM_ABTEST_BOOL(enable_hard_queue_p2l_use_live_ecpc_strategy);

// [wangyang10]
DECLARE_SPDM_ABTEST_BOOL(enable_esp_mobile_live_to_hard);  // 移动版直播投硬广开关
DECLARE_SPDM_ABTEST_INT64(esp_mobile_live_to_hard_quota);  // 移动版直播投硬广 quota
DECLARE_SPDM_ABTEST_BOOL(enable_reco_rank_wtr);  // 短视频涨粉使用 reco 精排模型
DECLARE_SPDM_ABTEST_INT64(reco_rank_wtr_timeout);  // 短视频涨粉使用 reco 精排模型耗时
DECLARE_SPDM_ABTEST_DOUBLE(t7_roi_zhongcao_boot_ratio_hourly);

// [luwei]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_bonus_add_soft_queue);  // 内循环软广支持账户补贴

// [jianghao07]
DECLARE_SPDM_ABTEST_BOOL(enable_new_native_quality_status);  // 优质原生 nogap 新链路
DECLARE_SPDM_ABTEST_BOOL(enable_new_native_quality_status_v2);
DECLARE_SPDM_ABTEST_INT64(trigger_relative_score_request_times_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_inner_explore_relative_hc);
// [guoyuan03]
DECLARE_SPDM_ABTEST_BOOL(close_minbid_filter);
// [jinhui05]
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_filter_pxr);
DECLARE_SPDM_ABTEST_INT64(side_window_filter_pxr_mode);
DECLARE_SPDM_ABTEST_DOUBLE(side_window_filter_pxr);
DECLARE_SPDM_ABTEST_DOUBLE(side_window_cpm_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_main_side_window_cpm_thr);
DECLARE_SPDM_ABTEST_DOUBLE(main_side_window_cpm_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_profile_side_window_cpm_thr);
DECLARE_SPDM_ABTEST_DOUBLE(profile_side_window_cpm_thr);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_ltv_low_bound);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_ltv_up_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_main_side_window_pos);
DECLARE_SPDM_ABTEST_STRING(main_side_window_pos_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_profile_side_window_pos);
DECLARE_SPDM_ABTEST_STRING(profile_side_window_pos_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_fanstop_sctr_package);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_cpm_thr_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(inner_explore_cpm_thr_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_ltv_default_value);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_mcb);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_live);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_photo);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_amd_live);
DECLARE_SPDM_ABTEST_BOOL(enable_explore_feed_hard_unify_server_show_rate);
DECLARE_SPDM_ABTEST_DOUBLE(thanos_mix_rank_quota_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(explore_rank_quota_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(thanos_mix_rank_quota_ratio_v2);
DECLARE_SPDM_ABTEST_DOUBLE(explore_rank_quota_ratio_v2);
DECLARE_SPDM_ABTEST_DOUBLE(follow_rank_quota_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_set_prerank_trigger_relative_score);  // [duanxinning] trigger 相关性得分
DECLARE_SPDM_ABTEST_BOOL(enable_fill_inner_trigger_hetu_emb);  // [zhaokun03] 内流 trigger emb 特征接入
DECLARE_SPDM_ABTEST_DOUBLE(wanhe_action_type_adjust_ratio);  // [zhaokun03] 万合出价目标维度成本率校准系数
DECLARE_SPDM_ABTEST_BOOL(enable_invo_traffic_predict);  // [zhaokun03] 创新流量是否生效流量预估
DECLARE_SPDM_ABTEST_BOOL(enable_invo_traffic_cali_top_layer_emb_predict);  // [wangzixu05] 创新流量校准，串行模型生效开关 //NOLINT
DECLARE_SPDM_ABTEST_INT64(invo_traffic_cali_model_emb_size);  // [wangzixu05] 创新流量校准，串行模型 emb 大小 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(invo_traffic_cali_model_cvr_max_diff_percent);  // [wangzixu05] 创新流量校准，cali cvr 和 原始 cvr 最大差值 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_feed_explore_rank_quota_opt);  // 发现页 quota 优化
DECLARE_SPDM_ABTEST_INT64(pv_filter_soft_quota);
DECLARE_SPDM_ABTEST_INT64(pv_filter_hard_quota);
DECLARE_SPDM_ABTEST_STRING(wanhe_profile_boost_opt_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_threshold_opt);  // [zhaokun03] 万合统一门槛
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_threshold_opt_v2);  // [zhaokun03] 万合统一门槛优化
DECLARE_SPDM_ABTEST_DOUBLE(main_side_window_min_bid_ratio);  // [zhaokun03] 侧滑小窗 min_bid 门槛优化系数
DECLARE_SPDM_ABTEST_DOUBLE(profile_side_window_min_bid_ratio);  // [zhaokun03] P 页内流 min_bid 门槛优化系数
DECLARE_SPDM_ABTEST_DOUBLE(profile_skin_min_bid_ratio);  // [zhaokun03] P 页皮肤 min_bid 门槛优化系数
DECLARE_SPDM_ABTEST_BOOL(enable_wanhe_inner_p2l_charge_type);  // [zhaokun03]  万合新接入短引生效曝光计费
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_playlet_name_adload_control);  // [zhaokun03] 支持强出框架配置短剧剧名维度生效  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_playlet_name_conf_time_limit);  // [zhaokun03] 支持强出框架配置生效时间  // NOLINT
// [panshunda] 跳过校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_explore_skip_calibrate_old);
DECLARE_SPDM_ABTEST_BOOL(enable_record_calibrate_info);
// [zhaokun03] 万合曝光计费调整 cpm 门槛系数配置
DECLARE_SPDM_ABTEST_STRING(wanhe_charge_action_type_adjust_cpm_thr_tag);
DECLARE_SPDM_ABTEST_DOUBLE(main_side_window_soft_cpm_ratio);  // [zhaokun03] 万合侧滑小窗软广 cpm 门槛系数
DECLARE_SPDM_ABTEST_DOUBLE(profile_side_window_soft_cpm_ratio);  // [zhaokun03] 万合 P 页内流软广 cpm 门槛系数
DECLARE_SPDM_ABTEST_DOUBLE(profile_skin_soft_cpm_ratio);  // [zhaokun03] 万合 P 页皮肤软广 cpm 门槛系数
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_boost_opt);
DECLARE_SPDM_ABTEST_STRING(side_window_boost_opt_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_sctr_opt);
DECLARE_SPDM_ABTEST_STRING(side_window_sctr_opt_tag);
DECLARE_SPDM_ABTEST_INT64(side_window_cross_section_pid_tag);
DECLARE_SPDM_ABTEST_STRING(wanhe_matching_efficiency_opt_exp_tag);  // [zhaokun03] 万合匹配效率优化分组标识
DECLARE_SPDM_ABTEST_DOUBLE(matching_efficiency_ratio);  // [zhaokun03] 万合匹配效率优化系数
DECLARE_SPDM_ABTEST_INT64(front_force_reco_avoid_same_second_industry_topn);  // [zhaokun03] 短剧强出避让 topn
DECLARE_SPDM_ABTEST_BOOL(splash_eyemax_discount_immg);  // [lizemin] eyemax discount 迁移
DECLARE_SPDM_ABTEST_DOUBLE(splash_eyemax_discount_ratio_value);  // [zhaokun03] 开屏 eyemax 样式折扣系数值

// [wengrunze] 私信落地页优选
DECLARE_SPDM_ABTEST_BOOL(enable_new_form_pm_integration);
DECLARE_SPDM_ABTEST_BOOL(enable_form_pm_integration_single_model);
DECLARE_SPDM_ABTEST_DOUBLE(pm_integration_single_prob_quota);
// [wengrunze] 超深度企微退出开关
DECLARE_SPDM_ABTEST_DOUBLE(pm_integration_leads_prob_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(pm_integration_lps_prob_alpha);
DECLARE_SPDM_ABTEST_BOOL(enable_real_deep_wechat_connected_lps_cvr_set);  // 企微退出开关
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_min_coupon_amount_yuan);
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_bound_rate_coupon_model_right);
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_bound_rate_coupon_model_left);
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_thre_ratio_when_delivery);
DECLARE_SPDM_ABTEST_INT64(value_qcpx_freq_control_pay_nd_author_spu);
DECLARE_SPDM_ABTEST_INT64(qcpx_amount_expand_coupon_expand_ratio);



DECLARE_SPDM_ABTEST_INT64(health_industry_retarget_path);
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_speed_test);
DECLARE_SPDM_ABTEST_INT64(item_card_speed_test_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant);
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_mall);
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_zhuanqian);
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_buyer);
DECLARE_SPDM_ABTEST_BOOL(enable_split_shelf_merchant_guess);
DECLARE_SPDM_ABTEST_BOOL(enable_bh_leverage_score);
DECLARE_SPDM_ABTEST_DOUBLE(bh_live_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(bh_item_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(bh_leverage_score_upper_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_mall_leverage_score);
DECLARE_SPDM_ABTEST_DOUBLE(mall_live_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mall_item_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(live_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(item_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mall_leverage_score_upper_ratio);
DECLARE_SPDM_ABTEST_INT64(mall_high_value_pv_cpm_thr);  // 商城高价值 pv cpm 阈值
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_leverage_score);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_gpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_leverage_score_upper_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_bh_opm);
DECLARE_SPDM_ABTEST_DOUBLE(bh_live_card_opm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(bh_item_card_opm_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_mall_opm);
DECLARE_SPDM_ABTEST_DOUBLE(mall_live_card_opm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mall_item_card_opm_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_opm);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_card_opm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_card_opm_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_gpm_log);
DECLARE_SPDM_ABTEST_INT64(gyl_high_value_pv_cpm_thr);

// [chenziping]
DECLARE_SPDM_ABTEST_STRING(shelf_cmd_page_cali_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_order_paied_cvr_pos_cmd_for_gyl);
DECLARE_SPDM_ABTEST_BOOL(enable_order_paied_cvr_pos_cmd_for_bh);
DECLARE_SPDM_ABTEST_BOOL(enable_order_paied_cvr_pos_cmd_for_mall);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_merchant_cpm_pos_for_gyl);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_merchant_cpm_pos_for_bh);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_merchant_cpm_pos_for_mall);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_merchant_pos_value_log);
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_photo_cmd_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_feed_offline_cali);
DECLARE_SPDM_ABTEST_INT64(qcpx_live_min_coupon_amount_yuan)   // 金牛 qcpx 直播 rct 发券门槛上限
DECLARE_SPDM_ABTEST_INT64(qcpx_live_max_coupon_amount_yuan)   // 金牛 qcpx 直播 rct 发券门槛下限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_price_coupon_ratio)   // 金牛 qcpx 直播 rct 发券门槛折扣系数
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_model_flow_percent);   // qcpx 直播多面额生效 rct 流量比例
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_live_bspline_model);   // qcpx live b-spline 样条回归实验
DECLARE_SPDM_ABTEST_INT64(shelf_qcpx_photo_fixed_discount);   // qcpx photo 固定发x折优惠券  // NOLINT
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_coupon_thre_type);   // qcpx live 券门槛形式
DECLARE_SPDM_ABTEST_INT64(value_p2l_thre_ratio_when_delivery);   // qcpx p2l 单独设置下发时券门槛系数
DECLARE_SPDM_ABTEST_INT64(value_live_thre_ratio_when_delivery);   // qcpx live 单独设置下发时券门槛系数

// [xiaoyuhao]
DECLARE_SPDM_ABTEST_BOOL(rank_abtest_debug_flag);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_undefine_ltv_info);
DECLARE_SPDM_ABTEST_BOOL(enable_opt_reco_handler_order);  // 调整 reco handler 先发后收

// [zhangruyuan]
DECLARE_SPDM_ABTEST_BOOL(enable_target_request_pid_server);  // [zhangruyuan] Target 请求 PIDServer RANK 不再请求 // NOLINT

// [zhangzhao06]
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_inovked_predict);  // [zhangzhao06] 短剧默认走唤端开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_inovked_predict_new);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_invoked_skip_mcb);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_click_app_invoked);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_imp2invoked_predict);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_conversion_predict);  // [yushengkai] 短剧默认走激活开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_conv_invoked_pay_online);  // [zhangzhao06] 短剧实时付费模型开关 //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_request_industry_pay_ltv);
DECLARE_SPDM_ABTEST_BOOL(disable_request_industry_pay_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(industry_pay_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_request_game_industry_pay_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_request_minigame_industry_pay_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_pay_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(minigame_industry_pay_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_pay_ltv_reweight_weight);
DECLARE_SPDM_ABTEST_DOUBLE(minigame_industry_pay_ltv_reweight_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_game_industry_pay_ltv_out);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_ensemble_pv_filter_lower_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_ensemble_pv_filter_upper_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_game_bagging_ltv);

DECLARE_SPDM_ABTEST_BOOL(enable_request_game_industry_pay_ltv_fliter);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_pv_filter_ceiling);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_pv_filter_bottom);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_drop_bottom_weight);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_drop_ceiling_weight);
DECLARE_SPDM_ABTEST_DOUBLE(game_industry_drop_normal_weight);

// [tiangeng]
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_clk2purchase_predict);  // [tiangeng] 短剧点击付费模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_clk2purchase_roas_predict);  // [tiangeng] 短剧ROAS点击付费模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_industry_purchase_predict);  // [tiangeng] 短剧唤端付费模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_industry_purchase_predict_new);  // [tiangeng] 分流量调价短剧唤端付费模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_industry_invoked_purchase_ltv_predict);  // [tiangeng] 短剧唤端付费作用ROAS模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_industry_invoked_purchase_ltv_predict_new);  // [tiangeng] 分流量调价短剧唤端付费作用ROAS模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_industry_conv_purchase_ltv_predict);  // [tiangeng] 短剧激活付费作用ROAS模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_industry_server_show_cvr_predict);  // [tiangeng] 短剧曝光二跳模型开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_industry_clk2purchase_mix);  // [tiangeng] 短剧点击付费混合预估 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(industry_clk2purchase_ratio);  // [tiangeng] 短剧点击付费混合系数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(industry_clk2purchase_other_ratio);  // [tiangeng] 短剧点击付费其他混合系数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_industry_clk2purchase);  // [tiangeng] 短剧点击付费不影响内流 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_industry_clk2purchase_hard);  // [tiangeng] 短剧点击付费不影响硬广 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(industry_clk2purchase_ratio_soft);  // [tiangeng] 短剧点击付费软广混合系数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(industry_clk2purchase_other_ratio_soft);  // [tiangeng] 短剧点击付费软广其他混合系数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_purchase_roi);  // [tiangeng] 短剧付费ROI双出价对齐链路开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_playlet_pay_panel_purchase);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_playlet_conversion_unify);
DECLARE_SPDM_ABTEST_BOOL(enable_request_industry_pay_ltv_new);
DECLARE_SPDM_ABTEST_BOOL(enable_request_playlet_pay_panel_purchase);
DECLARE_SPDM_ABTEST_DOUBLE(industry_pay_ltv_ensemble_weight_new);
DECLARE_SPDM_ABTEST_DOUBLE(industry_invoked_pay_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(industry_invoked_pay_ensemble_weight_new);
DECLARE_SPDM_ABTEST_DOUBLE(industry_playlet_iaa_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(industry_playlet_iaa_ltv_ensemble_weight_new);
DECLARE_SPDM_ABTEST_BOOL(enable_use_iaa_new_playlet_es_weight);
DECLARE_SPDM_ABTEST_DOUBLE(iaa_new_playlet_es_weight);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_con_pay_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_con_invoked_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_con_invoked_ltv_ratio)
DECLARE_SPDM_ABTEST_DOUBLE(playlet_con_invoked_ltv_ratio2)
DECLARE_SPDM_ABTEST_DOUBLE(playlet_hard_queue_imp_invoked_ratio)
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_close_invoked_link)
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_realtime_feature);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_smart_offer_roi);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_smart_offer_roi_thres);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_pay_realtime_model);  // [zhangmengxin] 付费小说唤端付费实时模型
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_ltv_realtime_model);  // [zhangmengxin] 付费小说付费 ltv 实时模型
DECLARE_SPDM_ABTEST_BOOL(enable_fill_fiction_roas_pay_ltv);  // [jiangjinling] 付费小说付费 ltv 预估

DECLARE_SPDM_ABTEST_DOUBLE(fiction_invoked_pay_ensemble_weight);  // [zhangmengxin] 付费小说唤端付费实时模型 ensemble ratio //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(fiction_pay_ltv_ensemble_weight);  // [zhangmengxin] 付费小说付费 ltv 实时模型 ensemble ratio //NOLINT

// [wanbgbin]
DECLARE_SPDM_ABTEST_DOUBLE(upper_ad_roas_long_ratio_pay_amount_7d);        // adROAS 目标长线实验上限

DECLARE_SPDM_ABTEST_BOOL(enable_game_bonus_control);  // [yaokangping] 开启游戏行业补贴上限控制
DECLARE_SPDM_ABTEST_BOOL(enable_splash_inner_loop_support_hc);
DECLARE_SPDM_ABTEST_BOOL(enable_long_ratio_ad_roas);  //  [zhangtuchao] 首日 ROI 长线实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_long_ratio_ad_roas_splash_2_7_amt_);  //  [zhangtuchao] 2_7 ltv 预估在开屏生效
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_explore_skip_soft_cpm_thr);  // [rentingyu] 软广人群探索跳过门槛开关
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_explore_just_allow_mix_request);  // [rentingyu] 人群探索跳过门槛限制流量
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_explore_skip_cpm_thr);  // [rentingyu] 硬广人群探索跳过门槛开关
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_explore_skip_cpm_thr_for_dac);  // [rentingyu] dac 高 pvr 跳过门槛
DECLARE_SPDM_ABTEST_INT64(non_conv_user_skip_cpmthr_tag);  // [rentingyu] 探索策略圈人群 tag
DECLARE_SPDM_ABTEST_INT64(non_conv_user_cvr_skip_thr_tag);  // [rentingyu] 探索策略圈人群 tag cvr 实验单独配置
DECLARE_SPDM_ABTEST_DOUBLE(cvr_explore_cvr_min_thd);  // [rentingyu] 探索策略 cvr 提权下限门槛
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_explore_event);  // [rentingyu] 探索策略 cvr 提权面向转化事件优化 //NOLINT
DECLARE_SPDM_ABTEST_INT64(explore_skip_cpm_thr_topn_num);  // [rentingyu] 探索策略 cvr 提权广告个数
DECLARE_SPDM_ABTEST_BOOL(enable_brand_level_boost_explore);  // [rentingyu] 商家优质品牌 boost
DECLARE_SPDM_ABTEST_BOOL(enable_conv_boost_roas_request_order_paid);  // [rentingyu] 商家优质品牌 boost
DECLARE_SPDM_ABTEST_BOOL(enable_uax_bonus);  // [zhaoziyou03] 外循环 UAX 素材 bonus
DECLARE_SPDM_ABTEST_BOOL(enable_caopan_bonus);  // [zhaoziyou03] 外循环操盘素材 bonus
DECLARE_SPDM_ABTEST_BOOL(enable_esp_aigc_order_paid_cali);  // [wangxiaoyi03] ESP AIGC 订单支付校准
DECLARE_SPDM_ABTEST_DOUBLE(esp_inner_aigc_order_paid_cali_rate);  // [wangxiaoyi03] ESP AIGC 订单支付校准系数
DECLARE_SPDM_ABTEST_BOOL(enable_esp_aigc_roas_cali);  // [wangxiaoyi03] ESP AIGC ROAS 校准
DECLARE_SPDM_ABTEST_DOUBLE(esp_inner_aigc_roas_cali_rate);  // [wangxiaoyi03] ESP AIGC ROAS 校准系数
DECLARE_SPDM_ABTEST_BOOL(enable_soft_brand_level_boost_explore);  // [rentingyu] 商家优质品牌 boost 软广
DECLARE_SPDM_ABTEST_BOOL(enable_brand_level_boost_explore_score_hard);  // [rentingyu] 商家优质品牌 boost 硬广单独系数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(brand_level_boost_explore_score);  // [rentingyu] 商家优质品牌 boost 固定值
DECLARE_SPDM_ABTEST_DOUBLE(outer_high_cost_hc_score);  // [guojiangwei] 外循环素材跑量 - 高消耗创意 hc
DECLARE_SPDM_ABTEST_DOUBLE(uax_ratio);  // [zhaoziyou03] 外循环 UAX 素材 bonus ratio
DECLARE_SPDM_ABTEST_DOUBLE(caopan_ratio);  // [zhaoziyou03] 外循环操盘素材 bonus ratio
DECLARE_SPDM_ABTEST_DOUBLE(brand_level_boost_explore_score_hard);  // [rentingyu] 商家优质品牌 boost 硬广
DECLARE_SPDM_ABTEST_BOOL(enable_brand_level_boost_user_tag);  // [rentingyu] 商家优质品牌 boost 圈人群
DECLARE_SPDM_ABTEST_INT64(ctcvr_moving_avg_req_threshold);  // [rentingyu] 滑动平均 ctcvr thr
DECLARE_SPDM_ABTEST_INT64(high_pvr_skip_cpm_thr_outer_topn_num);  // [rentingyu] 高 pvr 跳过门槛外循环 topn //NOLINT
DECLARE_SPDM_ABTEST_INT64(high_pvr_skip_cpm_thr_inner_topn_num);  // [rentingyu] 高 pvr 跳过门槛内循环 topn //NOLINT
DECLARE_SPDM_ABTEST_INT64(non_conv_user_tag_for_quality_score_boost);  // [rentingyu] 质量分 hc boost 圈人群 tag //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_boost_max_ctcvr_skip_thr);  // [rentingyu] ctcvr boost
DECLARE_SPDM_ABTEST_INT64(non_conv_user_tag_for_force_reco);  // [rentingyu] high pvr 认知实验未转化人群 tag
DECLARE_SPDM_ABTEST_INT64(high_pvr_force_reco_topn);  // [rentingyu] high pvr force topn
DECLARE_SPDM_ABTEST_BOOL(enable_fill_user_value_group);  // [hehandong] 优质人群 cpm 门槛开关
DECLARE_SPDM_ABTEST_BOOL(enable_set_explore_feed_high_quality_threshold);  // [hehandong] 软广优质门槛打折策略
DECLARE_SPDM_ABTEST_BOOL(enable_use_auto_params_ps);    // [baizongyao] 是否启用 AutoParamsPredict 节点
DECLARE_SPDM_ABTEST_BOOL(enable_use_stats_auto_param_plugin);    // [liubing05] 代码重构，切换到插件方式
DECLARE_SPDM_ABTEST_BOOL(disable_hard_auto_param);
DECLARE_SPDM_ABTEST_BOOL(enable_user_group_dim_stats_param);
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_novel_calibration);  // [linyuhao03] SDPA 小说正采样客户预估校准开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_maintower_ensemble_imp_lps);   // [yuanyue03] 表单主塔 ensemble 预估  //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_car_ensemble_imp_lps);   // [zhangyiwei03] 交通行业表单走 ensemble 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ensemble_imp_lps_fix);   // [zhangyiwei03] 行业表单走 ensemble 预估 fix  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_car_ensemble_imp_lps_record);   // [zhangyiwei03] 交通行业表单走 ensemble 预估落表  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_jiaotong_low_pay_cvr_filter);  // [zhaijianwei] 交通低转化人群 drop 策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_jiaotong_dynamic_low_pay_filter);  // [zhaijianwei] 交通低转化人群 drop 动态权重策略开关  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_native_jiaotong_dynamic_low_pay_filter);  // [zhaijianwei] 软广交通低转化人群 drop 动态权重策略开关  //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_close_7day_ecpc);  // [yishijie] 关闭长留增长工具  //NOLINT

// [huwenkang03]
DECLARE_SPDM_ABTEST_BOOL(enable_car_purchase_single_cmd);   // [zhaijianwei] 交通行业付费单出价走 行业模型 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_car_conv_purchase_cmd);   // [huangxin07] 交通行业激活付费双出价走 行业模型 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_car_purchase_single_ensemble);   // [huwenkang03] 交通行业付费单是否 ensemble 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_car_conv_purchase_ensemble);   // [huwenkang03] 交通行业激活付费双是否 ensemble 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_clue_ensemble_imp_lps);   // [huwenkang03] 线索表单 ensemble 开关  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_product_simple_promotion_ensemble);   // [huwenkang03] 线索表单商品速推 ensemble 开关  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_merge_clue_and_edu);   // [huwenkang03] 线索表单合并教育开关  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_lps_clue_not_ecom_coef);   // [huwenkang03] 线索表单曝光校准  //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_account_billing_skip_bs);   // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_industry_live_skip_account_bidding);
DECLARE_SPDM_ABTEST_BOOL(enable_white_account_skip_account_bidding);
DECLARE_SPDM_ABTEST_BOOL(enable_bs_sort_weight_symmetry);   // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_INT64(sensitive_user_cpmthr_tag);  // [rentingyu] 时长圈人群 tag
DECLARE_SPDM_ABTEST_BOOL(enable_bs_inner_loop);   // [tanweihan] 计费分离
DECLARE_SPDM_ABTEST_BOOL(enable_retarget_ecpc_abtest);  // [yuchengyuan] 重定向 ecpc 实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_deep_middle_model_exp);  // [limiao03] 深度中台模型实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_order_submit_cold_split_cmd);  // [zhangyiwei03] 外循环订单新账户单拆 cmdkey
DECLARE_SPDM_ABTEST_BOOL(enable_order_submit_gpm_use_ltv_model);  // [zengdi] 外循环订单 gpm 请求 ltv 模型
DECLARE_SPDM_ABTEST_DOUBLE(cid_order_gpm_adjust_ratio);  // [zengdi] 外循环订单 gpm 打压系数
DECLARE_SPDM_ABTEST_BOOL(enable_deep_middle_model_convpay_exp);  // [limiao03] 深度中台模型激活付费实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_deep_middle_model_kac_exp);  // [limiao03] 深度中台模型关键行为实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_deep_middle_model_nd_exp);  // [limiao03] 深度中台模型次留实验开关
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_calibration_game_upper);  // [yangxinyong] ROAS 游戏启动自动校准上限
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_calibration_game_lower);  // [yangxinyong] ROAS 游戏启动自动校准下限
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_calibration_mini_game_upper);  // [yangxinyong] ROAS 小游戏启动自动校准上限
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_calibration_mini_game_lower);  // [yangxinyong] ROAS 小游戏启动自动校准下限
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_calibration_upper);  // [zhangzhao06] ROAS 启动自动校准上限
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_calibration_lower);  // [zhangzhao06] ROAS 启动自动校准下限
DECLARE_SPDM_ABTEST_BOOL(enable_roi_auto_calibrate);  // [zhangzhao06] ROAS 启动自动校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_roi_auto_calibrate_realtime);  // [zhangzhao06] ROAS 启动自动校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_conv_roi_auto_calibrate);  // [zhangzhao06] ROAS 启动自动校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_conv_roi_auto_calibrate_mini_app);  // [zhangzhao06] ROAS 启动自动校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_deep_cvr_shelve);  // [zhangzhao06] 深转模型下线测试开关
DECLARE_SPDM_ABTEST_BOOL(enable_std_cvr_ltv_coef);  // [yangxinyong] 浅带深系数调权实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_std_cvr_ltv_coef_iaa);  // [wushanshan03] 浅带深系数调权实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_std_cvr_ltv_coef_duanju);  // [yangxinyong] 浅带深系数调权实验短剧开关
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_model_backup);  // [wushanshan03] C 补联合建模短剧备用开关
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_model);  // [yangxinyong] C 补联合建模短剧开关
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_multi_head_model);  // [chenzhengqi] C 补多头联合建模短剧开关
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_multi_head_model_hy_rank);  // [chenzhengqi] C 补策略-行业
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_multi_head_model_mid_rank);  // [chenzhengqi] C 补策略-中台
DECLARE_SPDM_ABTEST_BOOL(enable_c_no_subsidy_duanju_multi_head_model);  // [chenzhengqi] C 补策略--无 C 补赋值
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_origin_ltv_ratio);  // [chenzhengqi] 无 C 补校准系数
DECLARE_SPDM_ABTEST_DOUBLE(roi_iap_mid_origin_playlet_ltv_ratio);  // [chenzhengqi] 无 C 补短剧校准系数
DECLARE_SPDM_ABTEST_DOUBLE(roi_iap_mid_origin_game_ltv_ratio);  // [chenzhengqi] 无 C 补大游校准系数
DECLARE_SPDM_ABTEST_DOUBLE(roi_iap_mid_origin_mini_game_ltv_ratio);  // [chenzhengqi] 无 C 补微小校准系数
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_uplift_ltv_ratio);  // [chenzhengqi] 有 C 补校准系数
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_dnc_ltv_ratio);  // [chenzhengqi] nc 校准系数
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_doc_ltv_ratio);  // [chenzhengqi] oc 校准系数
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_head_ltv_luopan);  // [chenzhengqi] 短剧 C 补预估值落盘
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ensemble_weight_hold);  // [chenzhengqi] es iap 开关
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ensemble_weight_hold);  // [chenzhengqi] es iap 系数
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iaa_ltv_ensemble_weight_hold);  // [chenzhengqi] es iaa 开关
DECLARE_SPDM_ABTEST_DOUBLE(playlet_iaa_ltv_ensemble_weight_hold);  // [chenzhengqi] es iaa 系数
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_novel_model);  // [yangxinyong] C 补联合建模小说开关
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_game_model);  // [yangxinyong] C 补联合建模游戏开关
DECLARE_SPDM_ABTEST_STRING(std_ad_roas_exp_tag);  // [yangxinyong] 浅带深系数调权实验 tag
DECLARE_SPDM_ABTEST_STRING(std_ad_iaa_exp_tag);  // [wushanshan03] 浅带深系数调权实验 tag
DECLARE_SPDM_ABTEST_DOUBLE(iaa_conv_ltv_ratio);  //  [yangxinyong] iaa ratio
DECLARE_SPDM_ABTEST_DOUBLE(iaa_serial_conv_ltv_ratio);  //  [chenzhengqi] iaa_serial ratio
DECLARE_SPDM_ABTEST_DOUBLE(iaap_conv_ltv_ratio);  //  [chenzhengqi] iaap_iaa ratio
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_conv_ltv_weight);  //  [yangxinyong] iaa ratio 开关
DECLARE_SPDM_ABTEST_BOOL(enable_multi_retrieval_cxr_adj_exp);  // [guojiangwei] 召回通路 cxr 校准实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_zp_reward_cvr_revise_exp);  // [songxu] 激励视频 cvr 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_only_insert_high_ue_in_short_seq);  // [qiaolin] 仅高价值人群生效开关
DECLARE_SPDM_ABTEST_INT64(high_ue_short_seq_len);  // [rentingyu] 队列长度门槛
DECLARE_SPDM_ABTEST_BOOL(enable_reco_frrank_ue_nolimit);  // [qiaolin] 请求精排模型开关

DECLARE_SPDM_ABTEST_BOOL(enable_clean_hidden_cost);  // [gaokaiming]

DECLARE_SPDM_ABTEST_DOUBLE(search_live_tab_pos_1_relevance_threshold_v3);      // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(search_live_tab_pos_2_relevance_threshold_v3);      // [gaokaiming]

DECLARE_SPDM_ABTEST_DOUBLE(search_shop_tab_pos_1_relevance_threshold_v4);      // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(search_shop_tab_pos_2_relevance_threshold_v4);      // [gaokaiming]

DECLARE_SPDM_ABTEST_DOUBLE(search_ads_postition_1_rele_threshold_double_v1);      // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(reco_after_search_rele_threshold);      // [zhaoyilin05]
DECLARE_SPDM_ABTEST_DOUBLE(reward_cvr_revise_bound_up);    // [songxu] 激励视频 cvr 校准开关
DECLARE_SPDM_ABTEST_DOUBLE(reward_cvr_revise_bound_down);  // [songxu] 激励视频 cvr 校准开关

DECLARE_SPDM_ABTEST_BOOL(enable_ocpm_inner_max_ecpm_yuan);  // [yesiqi] 内循环软广 ocpm ecpm 上限开关
DECLARE_SPDM_ABTEST_BOOL(enable_non_explore_inner_max_ecpm_yuan);  // [yesiqi] 内循环软广非发现页 ocpm ecpm 上限开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ocpm_follow_max_ecpm_yuan);  // [yesiqi] 关注页软广 ocpm ecpm 上限开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_max_ecpm_yuan_protect);  // [yesiqi] 内循环软广ocpm ecpm 上限总开关  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(incentive_soft_live_auction_upper_bound);  // [gaozepeng] 激励软广直播 auction_bound 上限  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(incentive_hard_live_auction_upper_bound);  // [gaozepeng] 激励硬广直播 auction_bound 上限  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_adrank_filter_dup_photo);  // [guoyuan03] 1
DECLARE_SPDM_ABTEST_BOOL(enable_adrank_filter_dup_cover);  // [guoyuan03] 1
DECLARE_SPDM_ABTEST_BOOL(enable_adrank_filter_product);  // [guoyuan03] 1
DECLARE_SPDM_ABTEST_DOUBLE(ocpm_inner_max_ecpm_yuan);  // [yesiqi] 内循环软广 ocpm ecpm 上限
DECLARE_SPDM_ABTEST_DOUBLE(ocpm_non_explore_inner_max_ecpm_yuan);  // [yesiqi] 内循环软广非发现页 ocpm ecpm 上限  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ocpm_follow_max_ecpm_yuan);  // [yesiqi] 关注页软广 ocpm ecpm 上限
DECLARE_SPDM_KCONF_DOUBLE(nearbyLocalSoftEcpmThreshold);
DECLARE_SPDM_KCONF_DOUBLE(nearbyLocalHardEcpmThreshold);

DECLARE_SPDM_KCONF_DOUBLE(valueQcpxLiveWhiteboxRoiAdjust);  // 直播白盒 roi 调控系数
DECLARE_SPDM_KCONF_DOUBLE(valueQcpxPhotoWhiteboxRoiAdjust);  // 短带白盒 roi 调控系数
DECLARE_SPDM_KCONF_DOUBLE(valueQcpxOtherAuthorRoiAdjustCoef);  // 业务客户 roi 调控系数
DECLARE_SPDM_ABTEST_INT64(value_qcpx_default_coupon_threshold);  // 券默认门槛
DECLARE_SPDM_ABTEST_INT64(value_qcpx_default_coupon_amount);  // 券默认金额
DECLARE_SPDM_ABTEST_INT64(value_qcpx_whitebox_no_bid_ratio_percent);  // 尾号比例 白盒计费无后置项
DECLARE_SPDM_ABTEST_INT64(value_qcpx_whitebox_no_bid_ratio_seed);  // 种子 白盒计费无后置项
DECLARE_SPDM_ABTEST_INT64(value_qcpx_whitebox_not_minus_coupon_percent);  // 尾号比例 白盒排序不减券成本
DECLARE_SPDM_ABTEST_INT64(value_qcpx_whitebox_not_minus_coupon_seed);  // 种子 白盒排序不减券成本
DECLARE_SPDM_KCONF_DOUBLE(valueQcpxVauthorRoiBound);  // roi bound
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_rct_flow_percent);  // rct 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_dark_percent);  // 暗测比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_dark_seed);  // 暗测尾号取模基数
DECLARE_SPDM_ABTEST_INT64(shelf_qcpx_photo_price_coupon_ratio);  // 货架商品价格券面额系数
DECLARE_SPDM_ABTEST_INT64(shelf_qcpx_photo_max_coupon_amount_yuan);  // 货架券面额上限
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_photo_paid_elastic_piecewise_model);  // 订单流量分段线性预估
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_photo_roas_elastic_piecewise_model);  // roas 流量分段线性预估
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_rct_flow_percent);  // rct 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_model_flow_percent);  // model 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_price_coupon_ratio);  // 商品价格券面额系数
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_max_coupon_amount_yuan);  // 券面额上限
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_shelf_auction_operator);  // 总开关
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_shelf_auction_operator_hold);  // 总开关

DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_rct_amount_control_flow_percent);  // 满减券 rct 对照流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_rct_amount_treatment_flow_percent);  // 满减券 rct 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_rct_rate_control_flow_percent);  // 折扣券 rct 对照流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_rct_rate_treatment_flow_percent);  // 折扣券 rct 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_specific_amount_coupon_id);  // v3 特定券 id
// live v3 [yanqi08]
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_live_strategy_v3);  // 券链路三期 策略 开关
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_rct_amount_control_flow_percent);  // 满减券 rct 对照流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_rct_amount_treatment_flow_percent);  // 满减券 rct 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_rct_rate_control_flow_percent);  // 折扣券 rct 对照流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_rct_rate_treatment_flow_percent);  // 折扣券 rct 实验流量比例
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_specific_amount_coupon_id);  // v3 特定券 id
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_rct_right);   // 直播折扣券 rct 下界 950
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_rct_left);   // 直播折扣券 rct 上界 600
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_model_right);   // 直播折扣券 rct 下界 950
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_model_left);   // 直播折扣券 rct 上界 600
// [wanpengcheng]门槛价优化
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_photo_threshold_price_strategy);  // 是否开启门槛价优化
DECLARE_SPDM_ABTEST_INT64(value_qcpx_photo_history_paid_count_thres);  // 历史单数阈值
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_photo_threshold_mode_item_price);  // 是否开启 mode_item_price 作为门槛价策略  // NOLINT
// [wanpengcheng] 折扣券多头预估模型
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_live_cvr_disc_roas_multi_head_model);  // 折扣券 roas 多头预估模型开关
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_low_ori_cvr_cpa_bid_n);  // 计算优惠券面额参数 n
// [wanpengcheng] 满减券全链路模型
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_live_full_stage_model);  // 满减券全链路模型
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_thres_lower);  // a 标签发券门槛下限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_thres_upper);  // a 标签发券门槛上限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_ori_q);  // a 标签原始券金
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_new_q);  // a 标签调整后券金
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_thres_lower);  // b 标签发券门槛下限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_thres_upper);  // b 标签发券门槛上限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_ori_q);  // b 标签原始券金
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_new_q);  // b 标签调整后券金
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_thres_lower);  // c 标签发券门槛下限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_thres_upper);  // c 标签发券门槛上限
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_ori_q);  // c 标签原始券金
DECLARE_SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_new_q);  // c 标签调整后券金
DECLARE_SPDM_ABTEST_INT64(random_coloring_flow_percent);    // 染色流量比例
// bagging cvr log
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_before_bagging_cvr_log);

// [fandi]
DECLARE_SPDM_ABTEST_BOOL(enableSoftPhotoToLivePecTopBar);  // QCPX 直播 短引软广样式组件信息
DECLARE_SPDM_ABTEST_INT64(value_photo_qcpx_freq_control_pay_nd);   // QCPX 下单频控历史天数 - photo
DECLARE_SPDM_ABTEST_INT64(value_live_qcpx_freq_control_pay_nd);   // QCPX 下单频控历史天数 - live

// [zhangyiwei03] cid qcpx 开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_qcpx_strategy_run);  // cid qcpx 总开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_qcpx_cmd);  // cid qcpx 模型开关

// [yishijie]
DECLARE_SPDM_ABTEST_BOOL(enable_common_offline_calibrate);  // [yishijie] 通用离线校准小流量开关
DECLARE_SPDM_ABTEST_STRING(tag_name);  // [yishijie] 升级版联合建模小流量标识
DECLARE_SPDM_ABTEST_BOOL(enable_wangfu_purchase_cmd);   // [yishijie] 网服行业付费出价走行业模型预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_conv_ensemble);   // [yishijie] 电商平台支持行业模型 ensemble  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_move_ensemble_after_bound);   // [yishijie] 电商平台支持行业模型 ensemble  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_conv_log_ensemble);   // [zhangrui30] 电商平台支持行业模型对数 ensemble  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_conv_calibration);   // [zhangrui30] 电商平台支持打分校准   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_conv_soft_calibration);   // [zhangrui30] 电商平台支持软广打分校准   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_use_ecom_conv_bucket_weight);  // [zhangrui30] 电商平台支持行业模型分桶权重  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_conv_soft_weight);  // [zhangrui30] 电商平台支持行业模型软广权重  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(weight_ecom_item_imp_conv_ensemble);  // [yishijie] 电商平台激活模型 ensemble 权重
DECLARE_SPDM_ABTEST_DOUBLE(soft_weight_ecom_item_imp_conv_ensemble);  // [zhangrui30] 电商激活模型软广融合权重  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ecom_conv_calibration_value);   // [zhangrui30] 电商激活打分校准值
DECLARE_SPDM_ABTEST_DOUBLE(ecom_conv_soft_calibration_value);   // [zhangrui30] 电商激活软广打分校准值
DECLARE_SPDM_ABTEST_BOOL(enable_wangfu_purchase_ensemble);   // [yishijie] 网服行业付费出价是否 ensemble 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_use_x18_purchase_model);   // [yishijie] 网服行业付费出价大促模型开关  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(wangfu_purchase_ensemble_alpha);  // [yishijie] 网服行业付费出价 ensemble 权重
DECLARE_SPDM_ABTEST_BOOL(enable_purchase_ecom_cmdkey);
DECLARE_SPDM_ABTEST_BOOL(enable_use_purchase_promotion_model);  // [zhangrui30] 调用电商付费大促模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_use_purchase_exp_model);  // [zhangrui30] 调用综平付费交叉实验模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_purchase_ee_cmdkey);    // [yanghang06] 传媒咨询及游戏行业模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_ee_purchase_cvr_tags);    // [yanghang06] ee 行业模型字段落盘开关
DECLARE_SPDM_ABTEST_BOOL(enable_ctr_manual_cali);  // [yishijie] 人工 ctr 纠偏开关
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_manual_cali);  // [yishijie] 人工 cvr 纠偏开关
DECLARE_SPDM_ABTEST_BOOL(enable_dcvr_manual_cali);  // [yishijie] 人工 cvr 纠偏开关
DECLARE_SPDM_ABTEST_BOOL(enable_ltv_manual_cali);  // [yishijie] 人工 ltv 纠偏开关
DECLARE_SPDM_ABTEST_BOOL(enable_manual_cali_add_creativetype);  // [yishijie] 合并纠偏代码进一个函数
DECLARE_SPDM_ABTEST_BOOL(enable_manual_cali_itemtype);  // [yishijie] 合并纠偏增加维度
DECLARE_SPDM_ABTEST_BOOL(enable_manual_cali_remove_unvalid_tag);  // [yishijie] 纠偏修改
DECLARE_SPDM_ABTEST_BOOL(enable_splash_rta_second_predict);  // [zhangzhicong] 开屏 RTB 二次请求 rta
DECLARE_SPDM_ABTEST_BOOL(enable_adx_thanos_cvr_multihead);  // [yishijie] 是否增加 adx cpc 模型预估值
DECLARE_SPDM_ABTEST_BOOL(enable_fill_label_info_attr);  // [yishijie] 是否将模型打分透传到日志
DECLARE_SPDM_ABTEST_BOOL(enable_fill_ad_source_type);  // [yishijie] 是否将 ad_source_type 透传到日志
DECLARE_SPDM_ABTEST_BOOL(enable_fill_lps_label_info_attr);  // [linyuhao03] 是否将中台表单模型打分透传到日志
DECLARE_SPDM_ABTEST_BOOL(enable_adxcpc_miss_ctr_manual_calibrate);  // [yishijie] adxcpc 跳过 ctr 人工纠偏开关
DECLARE_SPDM_ABTEST_BOOL(enable_adxcpc_model_cali);  // [yishijie] adxcpc 软硬广调整参数开关
DECLARE_SPDM_ABTEST_BOOL(enable_add_dnc_crm_center);  // [yishijie] 增加 DNC hc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_q3_hc_crm_center);  // [yishijie] 新虚拟业务中心逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_qiwei_online_judge);  // [yishijie] 增加企微在线判断
DECLARE_SPDM_ABTEST_BOOL(enable_move_crm_center_code);  // [yishijie] 迁移业务中心代码位置
DECLARE_SPDM_ABTEST_BOOL(enable_click_app_invoked_ecom_cmd);  // [yishijie] 调用电商唤端模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_click_app_invoked_ecom_ensemble);  // [yishijie] 电商唤端模型融合开关
DECLARE_SPDM_ABTEST_BOOL(enable_click_app_invoked_ecom_exp_cmd);  // [zhangrui30] 调用电商唤端旁路模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_use_invoke_promotion_model);  // [zhangrui30] 调用电商唤端大促模型开关
DECLARE_SPDM_ABTEST_DOUBLE(click_app_invoked_ecom_ensemble_industry_weight);  // [yishijie] 电商唤端行业权重
DECLARE_SPDM_ABTEST_DOUBLE(click_app_invoked_ecom_ensemble_center_weight);  // [yishijie] 电商唤端中台权重


// [liumingzong] 发现页 cvr 校准参数
DECLARE_SPDM_ABTEST_BOOL(enable_feed_explore_cvr_calibrate);  // [liumingzong] 发现页 cvr 校准开关
DECLARE_SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_upper_bound);  // [liumingzong] 发现页 cvr 校准上限
DECLARE_SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_lower_bound);  // [liumingzong] 发现页 cvr 校准下限
DECLARE_SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_alpha);  // [liumingzong] 发现页 cvr 校准 alpha
DECLARE_SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_beta);  // [liumingzong] 发现页 cvr 校准 beta
DECLARE_SPDM_ABTEST_BOOL(enable_cvr_sigmoid_calibrate_method);  // [liumingzong] 发现页 cvr 校准方法
DECLARE_SPDM_ABTEST_STRING(feed_explore_cvr_cali_exp_tag);  // [liumingzong] 发现页 cvr 校准 tag

// [xutaotao03]
DECLARE_SPDM_ABTEST_BOOL(enable_acquisition_twinbid_cold_start);  // 有效获客双出价冷启动策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_lps_account_bonus);  // 表单辅助时间回传 bonus
DECLARE_SPDM_ABTEST_BOOL(enable_clue_message_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_clue_wechat_bonus);  // 线索企微后链路回传补贴
DECLARE_SPDM_ABTEST_STRING(enable_lpsdeep_support_decay);  // 线索有效获客扶持退坡
DECLARE_SPDM_ABTEST_BOOL(enable_lps_acquisition_generalization);  // 有效获客泛化模型
DECLARE_SPDM_ABTEST_INT64(acquisition_generalization_upper_bucket);  // 有效获客泛化 ecpc 上限桶
DECLARE_SPDM_ABTEST_INT64(acquisition_generalization_lower_bucket);  // 有效获客泛化 ecpc 下限桶
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_acquisition_generalization);  // IAA 跨产品泛化准入
DECLARE_SPDM_ABTEST_INT64(iaa_acquisition_generalization_upper_bucket);  // IAA 跨产品泛化准入 ecpc 上限桶
DECLARE_SPDM_ABTEST_INT64(iaa_acquisition_generalization_lower_bucket);  // IAA 跨产品泛化准入 ecpc 下限桶
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_acq_gen_score_tags);  //  IAA 跨产品泛化新增字段
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_acq_gen_ecpc_on_skip_sheild_budget);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_lps_acquisition_prediction_default);  // 有效获客模型都抵制修复开关
DECLARE_SPDM_ABTEST_BOOL(enable_customer_acquisition_cvr_request);  // 有效获客出价 cvr 请求开关
DECLARE_SPDM_ABTEST_DOUBLE(poi_distance_adjust_min_dis);  // 商家距离扰动最小距离
DECLARE_SPDM_ABTEST_BOOL(enable_lps_acquisition_support_exp_decay);  // 有效获客 2025 计费打折退坡
DECLARE_SPDM_ABTEST_BOOL(enable_message_twinbid_cold_start);  // 私信双出价冷启动策略开关
DECLARE_SPDM_ABTEST_INT64(pm_fwu_ee_group);  // 私信服务号人群 ee 参数组
DECLARE_SPDM_ABTEST_INT64(pm_fwu_ee_account_cost_threshold);  // 私信服务号账户消耗阈值
DECLARE_SPDM_ABTEST_BOOL(enable_pm_fwh_para_transinfo);

DECLARE_SPDM_ABTEST_DOUBLE(drop_ratio);
DECLARE_SPDM_ABTEST_INT64(drop_tx_cost_thre);
DECLARE_SPDM_ABTEST_INT64(drop_ty_cost_thre);
DECLARE_SPDM_ABTEST_INT64(drop_ts_thre);

DECLARE_SPDM_ABTEST_BOOL(enable_mix_rank_input_hist_page_info_fea);

// [dengjiaxing]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_merge_all_cvr_ctcvr_multi);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_merge_all_ltv_multi);

// [wangyangrui]
DECLARE_SPDM_ABTEST_BOOL(enable_dcaf_sample_delete_native_live);
DECLARE_SPDM_ABTEST_BOOL(enable_build_new_creative_used_item);
DECLARE_SPDM_ABTEST_BOOL(enable_build_used_item_to_adserver);
DECLARE_SPDM_ABTEST_BOOL(enable_build_ad_rank_infos_to_adserver);
DECLARE_SPDM_ABTEST_BOOL(enable_normal_handler_support_native);
DECLARE_SPDM_ABTEST_BOOL(fill_filter_node)
DECLARE_SPDM_ABTEST_BOOL(enable_new_bonus_diff_compare)
DECLARE_SPDM_ABTEST_BOOL(disable_adx_for_ctr)
DECLARE_SPDM_ABTEST_INT64(inner_normal_live_size)


// [wangyang10] s 级活动超级绿通额外 quota, 平时不生效
DECLARE_SPDM_ABTEST_INT64(inner_super_green_live_quota)
DECLARE_SPDM_ABTEST_BOOL(enable_innerloop_bonus_update)
DECLARE_SPDM_ABTEST_BOOL(enable_live_calc_vtr_ueq)
DECLARE_SPDM_ABTEST_INT64(adjust_quota_dsp_ad_list);
DECLARE_SPDM_ABTEST_INT64(adjust_quota_merchant_ad_list);
DECLARE_SPDM_ABTEST_INT64(adjust_quota_live_ad_list);
DECLARE_SPDM_ABTEST_INT64(adjust_quota_native_ad_list);
DECLARE_SPDM_ABTEST_INT64(adjust_quota_fanstop_ad_list);
DECLARE_SPDM_ABTEST_INT64(inner_native_live_pc_quota);
DECLARE_SPDM_ABTEST_STRING(rank_dragon_update_exp_tag);
DECLARE_SPDM_ABTEST_INT64(outer_soft_rank_base_line_num_v2);  // [zhangmengxin] 外循环软广精排 quota v2
DECLARE_SPDM_ABTEST_INT64(outer_soft_rank_base_line_num_v3);
DECLARE_SPDM_ABTEST_INT64(outer_live_rank_num);  // [zhangmengxin] 外循环软广直播 quota
DECLARE_SPDM_ABTEST_BOOL(enable_outer_soft_quota_v3);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_to_dp_server);  // [liuxiaoyan] 请求 dpserver 开关
DECLARE_SPDM_ABTEST_INT64(inner_normal_live_rank_quota)
DECLARE_SPDM_ABTEST_INT64(inner_native_live_rank_quota)
DECLARE_SPDM_ABTEST_INT64(inner_native_live_rank_quota_inspire)

DECLARE_SPDM_ABTEST_INT64(author_cache_hours_for_splash);  // [liubing05] author 缓存周期
DECLARE_SPDM_ABTEST_BOOL(enable_individual_quota_for_inspire_merchant);  //  [liubng05] 激励电商独立 quota
DECLARE_SPDM_ABTEST_BOOL(enable_mcda_up_purchase_third_party_ecpc_exp);  // [guoqi03] 小流量产品开关
DECLARE_SPDM_ABTEST_BOOL(enable_mcda_up_purchase_third_party_ecpc_launch);  // [guoqi03] 推全产品开关
DECLARE_SPDM_ABTEST_BOOL(enable_conv_quality_ecpc);  // [dinghe] 转化质量分 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_conv_quality_delivery_score_exp);  // [dinghe] 转化质量分 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_conv_quality_ecpc_by_kconf);       // [songxu] 转化质量分 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_conv_quality_ecpc_drop_and_take_exp);  // [dinghe] 转化质量分 ecpc 开关

DECLARE_SPDM_ABTEST_BOOL(enable_prophet_user_bonus);  // [lining] 先知补贴开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_duanju_bonus);  // [lining] 新剧补贴开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_duanju_bonus_use_name);  // [guanpingyin] 新剧补贴剧名
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_new_name_log);  // [guanpingyin] 新剧剧名 log
DECLARE_SPDM_ABTEST_BOOL(enable_ecology_new);  // [wanghongfei] 商家生态策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_ecology_v2_native);  // [wanghongfei] 商家生态策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_ecology_live);  // [wanghongfei] 商家生态策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_ecology_live_native);  // [wanghongfei] 商家生态策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_dup_new);  // [wanghongfei] 升级 dup photo
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_thanos)  // [nizhihao]  相关旧开关迁移 spdm
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_search)  // [nizhihao]  相关旧开关迁移 spdm
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_nebula)  // [nizhihao]  相关旧开关迁移 spdm
DECLARE_SPDM_ABTEST_BOOL(enable_rank_benefit_filter);   // [guoyuan03] rank benefit 门槛
DECLARE_SPDM_ABTEST_DOUBLE(default_rank_benefit_thresh);   // [guoyuan03] rank benefit 门槛
DECLARE_SPDM_ABTEST_BOOL(enable_adload_sample_collect);    //  [nizhihao] adload 样本采集开关
// [dinyiming05] 虚拟金实验
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_suppress);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_suppress_whitelist);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_adjust_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_cpm_upper);
DECLARE_SPDM_ABTEST_DOUBLE(inner_account_cpm_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_ecpm_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_account_max_ecpm_yuan);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_holdout_new);
DECLARE_SPDM_ABTEST_BOOL(enable_model_based_adload_control);
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_server_show_rate);    //  [luwei] 打开猜喜 server_show_rate
DECLARE_SPDM_ABTEST_BOOL(enable_gyl_cpm_remove_ctr);    //  [luwei] 打开猜喜 cpm 排序去掉 ctr
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_impression_charge);    //  [luwei] 猜喜 曝光计费
DECLARE_SPDM_ABTEST_BOOL(enable_sctr_default_mobile_guess_you_like);    //  [luwei] 移动端猜喜默认 ctr
DECLARE_SPDM_ABTEST_STRING(mobile_guess_you_like_sctr_exp_tag);    //  [luwei] 移动端猜喜 ctr exp
DECLARE_SPDM_ABTEST_BOOL(enable_guess_you_like_max_rank_quota);    //  [luwei] 调整猜喜 rank quota
DECLARE_SPDM_ABTEST_INT64(guess_you_like_max_rank_quota);    //  [luwei] 猜喜 rank quota
DECLARE_SPDM_ABTEST_DOUBLE(inner_account_suppress_rate);
DECLARE_SPDM_ABTEST_DOUBLE(unify_server_show_rate);
DECLARE_SPDM_ABTEST_DOUBLE(nebula_unify_server_show_rate);
DECLARE_SPDM_ABTEST_BOOL(enable_server_show_rate_with_ratio_sample);  // [xiongyajiao] 样本采集提权开关
DECLARE_SPDM_ABTEST_DOUBLE(gyl_server_show_rate);  // [luwei] 猜喜 server_show_rate
DECLARE_SPDM_ABTEST_DOUBLE(splash_bonus_upper);
DECLARE_SPDM_ABTEST_BOOL(enable_white_project_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(white_project_bonus_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_bid_roas_by_roi_roas);             // [songxu] cid roi 出价 使用系统出价

// [tengwei]
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_new_product_mini_app_live_bonus_ratio);  // 粉条新产品小程序扶持系数
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_show_author_playtime_thr);   // 粉条秀场主播直播预估时长过滤阈值
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_show_author_wtr_thr);        // 粉条秀场主播直播预估互动过滤阈值
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_game_author_playtime_thr);   // 粉条游戏主播直播预估时长过滤阈值
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_game_author_wtr_thr);        // 粉条游戏主播直播预估互动过滤阈值
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_recruit_live_hc);        // 粉条招工 live hc
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_recruit_photo_hc);       // 粉条招工 photo hc
DECLARE_SPDM_ABTEST_INT64(reco_lsp_live_rank_xtr_timeout);              // 调用本地直播模型 timeout
DECLARE_SPDM_ABTEST_INT64(lsp_avoidance_first_outer_index_threshold);   // 本地内部订单软避让 V2 首个外广排名阈值 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(lsp_avoidance_in_out_ecpm_ratio_threshold);  // 本地内部订单软避让 V2 内广与首个外广 ECPM 比例阈值 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_locallife_live_geo_distance_ecpc);      // 本地推基于 Geo ecpc 开关
DECLARE_SPDM_ABTEST_DOUBLE(locallife_live_geo_distance_ecpc_ratio);     // 本地推基于 Geo ecpc 系数

DECLARE_SPDM_ABTEST_BOOL(enable_ss_pcoc_data);  // 内循环自助 pcoc 校准
DECLARE_SPDM_ABTEST_BOOL(enable_inner_ss_calibration);  // 内循环自助 pcoc 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_population_cali);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_population_cali_refine);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_population_cali_wpage);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_population_cali_two);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_population_cali_three);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_remove_follow_page);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_remove_page_two);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_multi_bound);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_multi_bound_refine);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_multi_bound_wpage);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_effective_play_drop_request);  // 内循环人群校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_ss_pcoc_cali_via_esp_author_fans_pcoc_ratio);  // 内循环校准使用 esp_author_fans_pcoc_ratio // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_photo_order_paied_model);  // 本地短视频订单模型 cmd 单拆 // NOLINT


// [yangfukang03]
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_brand_initial_sctr);
DECLARE_SPDM_ABTEST_STRING(po_quan_ee_pid_exp_group);  // ee 破圈实验分组
DECLARE_SPDM_ABTEST_BOOL(enable_po_quan_ee_pid_strategy);  // ee 请求 pid server
DECLARE_SPDM_ABTEST_BOOL(enable_dnc_ltv_analysis);  //  dnc 粗排数据落表

// [tangsiyuan]
DECLARE_SPDM_ABTEST_BOOL(disable_fanstop_temu_quota);
DECLARE_SPDM_ABTEST_INT64(fanstop_leads_new_account_threshold);  // 粉条 leads 新账户 threshold
DECLARE_SPDM_ABTEST_INT64(fanstop_prms_new_account_threshold);  // 粉条 prms 新账户 threshold


DECLARE_SPDM_ABTEST_BOOL(enable_trans_creative_build_type);   // 传 creative_build_type 到 front
DECLARE_SPDM_ABTEST_DOUBLE(gyl_item_sctr);  // 猜喜商品卡 sctr
DECLARE_SPDM_ABTEST_DOUBLE(gyl_live_sctr);  // 猜喜直播卡 sctr
DECLARE_SPDM_ABTEST_DOUBLE(mall_item_sctr);  // 商城商品卡 sctr
DECLARE_SPDM_ABTEST_DOUBLE(mall_live_sctr);  // 商城直播卡 sctr
DECLARE_SPDM_ABTEST_DOUBLE(bh_item_sctr);  // 买首商品卡 sctr
DECLARE_SPDM_ABTEST_DOUBLE(bh_live_sctr);  // 买首直播卡 sctr
DECLARE_SPDM_ABTEST_BOOL(enable_use_shelf_gpm);   // 使用货架 gpm
DECLARE_SPDM_ABTEST_BOOL(enable_shelf_express_cali_ratio);   // 货架明投继承 ratio
DECLARE_SPDM_ABTEST_DOUBLE(shelf_express_cali_ratio);  // 货架明投继承 ratio

DECLARE_SPDM_ABTEST_INT64(live_calib_coef);  // 强化学习校准
DECLARE_SPDM_ABTEST_INT64(coldstart_b_control_ab_range);  // 冷启动 b 端视角解法流量范围
DECLARE_SPDM_ABTEST_INT64(coldstart_b_topk_hc_adjust_pow);  //冷启动 b 端视角 hc 预算平滑分配调整系数幂
DECLARE_SPDM_ABTEST_BOOL(enable_up_items_ue_cpm_test);  //潜力素材更换测试




// [guochangyu]
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_live_roas_ensemble_ratio);  // 本地推全站 roas 下单模型融合系数
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_p2l_roas_ensemble_ratio);  // 本地推全站 roas 引流直播下单模型融合系数 // NOLINT
DECLARE_SPDM_ABTEST_INT64(inner_product_ee_order_cnt_7days_thresh);  // 内循环商品探索近一周订单阈值
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_storewide_pcoc_cali);  // 本地全站分主播 pcoc 校准
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_pcoc_cali_upper_bound);  // 本地全站分主播 pcoc 校准上界
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_pcoc_cali_lower_bound);  // 本地全站分主播 pcoc 校准下界
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_storewide_pcoc_cali_exclude_search);  // 本地推全站 pcoc 校准排除搜索
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_storewide_search_pcoc_cali);  // 本地推全站搜索 pcoc 校准
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_search_pcoc_cali_upper_bound);  // 本地全站搜索分主播 pcoc 校准上界
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_search_pcoc_cali_lower_bound);  // 本地全站搜索分主播 pcoc 校准下界
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_storewide_flow_gmv_model_ensemble);  // 本地全站流化 GMV 模型融合订单支付模型 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_flow_gmv_model_ensemble_ratio);  // 本地全站流化 GMV 模型融合订单支付模型系数 // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(lsp_storewide_search_gmv_model_ensemble_ratio);  // 本地全站搜索 GMV 模型融合订单支付模型系数 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_resolve_local_life_mark);  // 解析本地复记字段标识
DECLARE_SPDM_ABTEST_INT64(outloop_recruit_user_package_hc_group);    // 外循环招工 HC 实验组
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_ecpc_dot);  // [yangfukang03] 减少打点
DECLARE_SPDM_ABTEST_INT64(outerloop_dnc_cem_group_num);    // nc cem 探索组数目
DECLARE_SPDM_ABTEST_INT64(outerloop_dnc_cem_exploit_group);    // nc cem 利用参数版本
DECLARE_SPDM_ABTEST_BOOL(enable_rank_load_nc_model_prod_list);
DECLARE_SPDM_ABTEST_STRING(fanstop_photo_ecpc_exp_prefix);  // ecpc 实验前缀
DECLARE_SPDM_ABTEST_STRING(fanstop_ecpc_score_aggregation);  // ecpc 合并方式
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_distillation_reco_like_photo_ecpc);


DECLARE_SPDM_ABTEST_BOOL(enable_dsp_pec_second_deep_reward);     // 信息流 pec 重激励
DECLARE_SPDM_ABTEST_BOOL(enable_dsp_live_deep_reward);  // [lizemin] 信息流直播激励

// [yaokangping] 微信小游戏 drop 策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_feature_index_req_all_photo);  // [lizemin] 策略正排 photo 表全量请求开关
DECLARE_SPDM_ABTEST_INT64(photo_cpm_ue_tag);  // [lizemin] 风管 cpm 低质量 photo 治理 tag

DECLARE_SPDM_ABTEST_BOOL(enable_merchant_video_auc_cross_test);  // [chencongzheng] 订单 cvr 旁路 auc

// [zengdi] 短视频涨粉旁路 auc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_reco_follow_use_auc_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_fans_follow_use_auc);
// [zengdi] 短视频电商激活 or 唤端旁路 auc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecom_conv_cross_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_invoke_cross_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_atlas_account_bonus);  // 图集素材 bonus 开关
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_hidden_binding_bonus);  // spda 暗绑补贴
DECLARE_SPDM_ABTEST_BOOL(enable_fill_rtabid_label_info_attr);  // 落盘 rtabid 出价
DECLARE_SPDM_ABTEST_INT64(inspire_conv_award_interval_second_merchant);  // 激励场景电商激活激励样式频控时间
DECLARE_SPDM_ABTEST_DOUBLE(cid_roas_reset_cvr);  // cid_roas reset_cvr 的值
DECLARE_SPDM_ABTEST_BOOL(enable_cid_use_goods_type_support);  // cid 扶持切换 商品状态池粒度
DECLARE_SPDM_ABTEST_BOOL(enable_cid_goods_potenial_select);  // cid 高潜力品实验

// [liuxingchen07]
DECLARE_SPDM_ABTEST_BOOL(enable_cid_use_goods_type_new_support);  // cid 扶持按新的商品 id 统计
DECLARE_SPDM_ABTEST_BOOL(enable_cid_roi_imp_gmv_cmd_fix_auc_exp);  // cid roi 出价交叉 auc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_remove_stage_none);  // cid 关闭 stage 为 none 时不进入 补贴
DECLARE_SPDM_ABTEST_BOOL(enable_cid_uplift_log);  // cid cvr 软硬广分头交叉实验
DECLARE_SPDM_ABTEST_BOOL(enable_live_item_emb);  // live 开启请求浩强嵌入
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_sid);  // live 开启请求 sid
DECLARE_SPDM_ABTEST_BOOL(enable_live_rl_ecpc);  // live 开启强化学习 ecpc
// [zhangyiwei03] cid 订单旁路 auc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_order_cross_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_order_live_cross_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_order_live_cmdkey_v2);
// [xiongyajiao]
DECLARE_SPDM_ABTEST_BOOL(enable_cancel_leverage_score_use_post_gmv_v2);
// [fukunyang]
DECLARE_SPDM_ABTEST_BOOL(fix_inner_loop_min_price);  // [wangyang10] 内循环概率计费  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_roas_live_high_atv);  // [fukunyang] 内循环直播 roas 高客单价使用 atv 方式  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_esp_live_inspire_gmv);  // [fukunyang] 内循环直播激励 GPM 门槛调用 roas  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(esp_live_costume_cost_threshold);  // [fukunyang] 服饰行业星火商家校准消耗门槛  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_author_pcoc_data);  // [fukunyang] 获取作者粉丝维度校准数据
DECLARE_SPDM_ABTEST_BOOL(enable_author_pcoc_cali);  // [fukunyang] 作者粉丝维度校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_promotion_pcoc_cali_tool);  // [fukunyang] 大促期间校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_esp_eop_is_pay);  // [fukunyang] 订单支付切换为支付人次实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_eop_inspire_use_gmv);  // [fukunyang] 订单支付激励使用 GMV
DECLARE_SPDM_ABTEST_BOOL(enable_eop_inspire_align_ltv);  // [fukunyang] 订单支付激励对齐 ROAS
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_eop_split_item_type);  // [fukunyang] 订单支付激励对齐 ROAS
DECLARE_SPDM_ABTEST_DOUBLE(inspire_eop_ecpm_weight);  // [fukunyang] 激励上订单加权系数
DECLARE_SPDM_ABTEST_DOUBLE(inspire_eop_gpm_weight);  // [fukunyang] 激励上订单加权系数
DECLARE_SPDM_ABTEST_DOUBLE(inspire_eop_ecpm_weight_p2l);  // [fukunyang] 激励上订单加权系数
DECLARE_SPDM_ABTEST_DOUBLE(inspire_eop_gpm_weight_p2l);  // [fukunyang] 激励上订单加权系数
DECLARE_SPDM_ABTEST_DOUBLE(esp_live_ensemble_roas_ratio);  // [fukunyang] ROAS 模型聚合系数
DECLARE_SPDM_ABTEST_DOUBLE(esp_p2l_ensemble_roas_ratio);  // [fukunyang] ROAS 模型引流聚合系数
DECLARE_SPDM_ABTEST_DOUBLE(esp_live_ensemble_roas_upper_bound_ratio);  // [fukunyang] ROAS 模型限制上限系数
DECLARE_SPDM_ABTEST_DOUBLE(esp_live_ensemble_roas_lower_bound_ratio);  // [fukunyang] ROAS 模型限制下限系数
DECLARE_SPDM_ABTEST_DOUBLE(esp_p2l_ensemble_roas_upper_bound_ratio);  // [fukunyang] ROAS 模型引流上限系数
DECLARE_SPDM_ABTEST_DOUBLE(esp_p2l_ensemble_roas_lower_bound_ratio);  // [fukunyang] ROAS 模型引流下限系数
DECLARE_SPDM_ABTEST_BOOL(enable_ensemble_unify_ltv);  // [fukunyang] ROAS 模型融合结果落表开关
DECLARE_SPDM_ABTEST_BOOL(enable_esp_live_roas_ensemble2);  // [fukunyang] ROAS 模型融合实验流量开关
DECLARE_SPDM_ABTEST_BOOL(disable_esp_live_roas_ensemble);  // [fukunyang] ROAS 模型融合实验流量开关
DECLARE_SPDM_ABTEST_BOOL(enable_esp_roas_ensemble_author_pcoc);  // [fukunyang] ROAS 模型融合实验个性化融合系数开关  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_live_storewide_roas_ensemble);  // [fukunyang] 全站 ROAS 模型融合实验流量开关


// [wangyuan11]
DECLARE_SPDM_ABTEST_DOUBLE(esp_photo_effective_play_cali_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_live_hosting_photo_to_redis);

// [wanglianhai03]
DECLARE_SPDM_ABTEST_BOOL(enable_live_ltv_use_auc);

// [luchi]
DECLARE_SPDM_ABTEST_BOOL(enable_uplift_order_paied);
DECLARE_SPDM_ABTEST_BOOL(enable_native_uplift_order_paied);
DECLARE_SPDM_ABTEST_BOOL(enable_uplift_valid_page);
DECLARE_SPDM_ABTEST_DOUBLE(uplift_order_high_bound);
DECLARE_SPDM_ABTEST_DOUBLE(uplift_order_low_bound);
DECLARE_SPDM_ABTEST_DOUBLE(uplift_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_sv_uplift_hc);
DECLARE_SPDM_ABTEST_DOUBLE(inner_sv_uplift_hc_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_sv_uplift_hc_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_native_inner_sv_uplift_hc);
DECLARE_SPDM_ABTEST_DOUBLE(inner_native_sv_uplift_hc_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(inner_native_sv_uplift_hc_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_sv_roas_gpm_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_search_roas_one_stage);
DECLARE_SPDM_ABTEST_BOOL(enable_sv_rank_candidante);

DECLARE_SPDM_ABTEST_BOOL(enable_roas_multi_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_live_p2l_thanos_split);
DECLARE_SPDM_ABTEST_BOOL(enable_roas_gpm_cmd);

// [litao24]
DECLARE_SPDM_ABTEST_BOOL(enable_p2l_use_auc);

// [lizhenmao]
DECLARE_SPDM_ABTEST_BOOL(enable_roas_7days_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_roas_7days_0_2h_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_roas_7days_2h_3d_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_roas_7days_3d_7d_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_t7_use_t0_model);
DECLARE_SPDM_ABTEST_BOOL(enable_store_t7_use_convert);

// [xiaoyuchao]
DECLARE_SPDM_ABTEST_BOOL(enable_roas_7days_relative_predict_cmd);  // [xiaoyuchao] T7 相对预估；

// [tiantian06]
DECLARE_SPDM_ABTEST_BOOL(enable_search_live_roas_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_search_sigle);
DECLARE_SPDM_ABTEST_DOUBLE(live_smart_bidding_min_rate);
DECLARE_SPDM_ABTEST_DOUBLE(live_smart_bidding_max_rate);
DECLARE_SPDM_ABTEST_DOUBLE(live_exception_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(live_exception_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(photo_smart_bidding_min_rate);
DECLARE_SPDM_ABTEST_DOUBLE(photo_smart_bidding_max_rate);
DECLARE_SPDM_ABTEST_DOUBLE(photo_exception_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(photo_exception_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_calibration_search_isperf);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_refund_merge_queue);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_cid_model);
// wangzhiping03
DECLARE_SPDM_ABTEST_BOOL(enable_card_live_T7);
DECLARE_SPDM_ABTEST_BOOL(enable_goods_dsp_boost);

// dongyan06
DECLARE_SPDM_ABTEST_BOOL(enable_p2l_split_ocpc_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_live_mobile_ocpc_boost_2);
DECLARE_SPDM_ABTEST_BOOL(enable_search_qcpx_live_roi_uplift);
DECLARE_SPDM_ABTEST_BOOL(enable_search_p2l_to_live_ecpm);
DECLARE_SPDM_ABTEST_BOOL(enable_live_pay_rate_rank);

// [anchaojie]
DECLARE_SPDM_ABTEST_BOOL(enable_live_audience_single_use_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_live_audience_feed_use_auc);

// [liuxiaofan05]
DECLARE_SPDM_ABTEST_BOOL(enable_live_pay_use_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_context_info_completion);
DECLARE_SPDM_ABTEST_BOOL(enable_live_pay_follow_tab_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_p2l_pay_follow_tab_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_feed_ctr_fanstop_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_qcpx_order);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_qcpx_roas);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_qcpx_t7roas);

// [zhongyiming]
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_roas_use_auc);
DECLARE_SPDM_ABTEST_BOOL(enable_merchant_t7_roi_use_auc);

// [liuyi]
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_live_pay_follow_tab_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_p2l_pay_follow_tab_exp);

// [wangjian27]
DECLARE_SPDM_ABTEST_BOOL(enable_live_pay_single_tab);
DECLARE_SPDM_ABTEST_BOOL(enable_live_pay_p2l_single_tab);

// [gaoqian03]
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_pred);

// [tanweihan] hidden cost
DECLARE_SPDM_ABTEST_BOOL(enable_game_shoufa_for_all_inds);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_hard_gpm_score);
DECLARE_SPDM_ABTEST_BOOL(enable_hc_inner_live_independent_gmv);
DECLARE_SPDM_ABTEST_BOOL(enable_hc_gpm_bound_by_cpm);
DECLARE_SPDM_ABTEST_BOOL(enable_hc_experience_score);
DECLARE_SPDM_ABTEST_BOOL(enable_hc_gpm_order_pay_independent);
DECLARE_SPDM_ABTEST_BOOL(hc_experience_pxr_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_gpm_p2l_live_audience);
DECLARE_SPDM_ABTEST_BOOL(enable_unify_adload_plugin);
DECLARE_SPDM_ABTEST_BOOL(enable_industry_orientation_adload_control);
DECLARE_SPDM_ABTEST_BOOL(enable_industry_live_force_reco_tag);
DECLARE_SPDM_ABTEST_STRING(outer_live_force_tag);  // [zhangmengxin] 外循环行业直播强出工具  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_orientation_tool_ecpc);  // [zhangmengxin] 外循环行业直播 boost 工具  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_live_force_use_game_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_game_shoufa_force_adload_control);  // [zhangmengxin] 外循环游戏首发工具  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad);
DECLARE_SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad_outer_only);
DECLARE_SPDM_ABTEST_BOOL(enable_game_interest_retarget_force);  // [zhangmengxin] 游戏兴趣重定向强出  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_game_reco_interest_force);  // [zhangmengxin] 游戏 reco 兴趣重定向强出  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_polaris_account);
DECLARE_SPDM_ABTEST_DOUBLE(min_rta_quality_score);
DECLARE_SPDM_ABTEST_DOUBLE(max_rta_quality_score);
DECLARE_SPDM_ABTEST_DOUBLE(max_rta_quality_hc_ratio);

// [lihantong]
DECLARE_SPDM_ABTEST_BOOL(enable_balance_gpm_score_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_balance_gpm_score_ratio_c);
DECLARE_SPDM_ABTEST_DOUBLE(balance_gpm_score_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(balance_gpm_score_ratio_c);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_nobid_calibration);
DECLARE_SPDM_ABTEST_BOOL(enable_nobid_age_gender_cali);
DECLARE_SPDM_ABTEST_BOOL(enable_nobid_age_gender_cali_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_only_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_nobid_pcvr_cali);
DECLARE_SPDM_ABTEST_BOOL(enable_nobid_pcvr_cali_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_smb_industry_cali);
DECLARE_SPDM_ABTEST_STRING(enable_smb_industry_cvr_exp_name);
DECLARE_SPDM_ABTEST_BOOL(enable_smb_industry_cvr_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_smb_industry_cvr_exp_patch);
DECLARE_SPDM_ABTEST_INT64(first_n_ecpc_thresh);

DECLARE_SPDM_ABTEST_BOOL(enable_juxing_use_min_ecpm);  // [yangyiming] 聚星最小 ECPM 阈值
DECLARE_SPDM_ABTEST_BOOL(enable_juxing_forbid_canjing);  // [yangyiming] 聚星参竞开关
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_live_parse_info);  // [yangyiming] 品牌助推直播是否解析粉条字段开关
// [tengwei] fanstop new inner ecommerce order pay ecpc
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_photo_medium);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_photo_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_live_medium);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_live_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_p2l_medium);
DECLARE_SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_p2l_alpha);

DECLARE_SPDM_ABTEST_DOUBLE(search_e2e_top4_rele_thre);  // [zhaoyilin05] 搜索双塔召回 top4 相关性阈值
DECLARE_SPDM_ABTEST_DOUBLE(enable_search_e2e_ecpm_ratio);  // [yangjunyao] 搜索双塔召回 ECPM 阈值
DECLARE_SPDM_ABTEST_DOUBLE(enable_search_e2e_live_ecpm_ratio);  // [yangjunyao] 搜索双塔直播召回 ECPM 阈值
DECLARE_SPDM_ABTEST_DOUBLE(enable_search_e2e_live_ecpm_boost);  // [yangjunyao] 搜索双塔直播召回 ECPM boost
DECLARE_SPDM_ABTEST_BOOL(enable_search_tag_cpm_ratio_conf);  // [yangjunyao] 搜索召回 tag ECPM ratio

// [liuyi]

// [caikehe]
DECLARE_SPDM_ABTEST_BOOL(enable_mix_rank_is_live_fea);

// [chencongzheng] 主播分段打标
DECLARE_SPDM_ABTEST_BOOL(enable_author_tier_fill);
// [qipeng] 进人分层实验 tag
DECLARE_SPDM_ABTEST_STRING(polaris_exp_params_exp_tag);
DECLARE_SPDM_ABTEST_STRING(auto_purchase_roi_coef_exp_tag);
DECLARE_SPDM_ABTEST_STRING(polaris_user_tag_score_hc_exp_tag);  // [dengrujia03] 北极星人群分层实验
DECLARE_SPDM_ABTEST_STRING(native_quality_bonus_coef_exp_tag);
DECLARE_SPDM_ABTEST_STRING(user_tag_source_list_str);
// [huangzhaokai] 内循环移动端硬广短视频 ROAS 投放 auto_cpa_bid 计算
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_photo_roas_calc_auto_cpa_bid_new);
// [huangzhaokai] 内循环移动端硬广短视频 ROAS 投放跳过 roi_ratio 检查
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_skip_roi_ratio_check_new);

DECLARE_SPDM_ABTEST_STRING(clue_acquisition_hc_expgroup);

DECLARE_SPDM_ABTEST_BOOL(enable_pred_target_cost);


//  [songxu]  外投顶价开关
DECLARE_SPDM_ABTEST_BOOL(enhance_ecpm_strategy_rank_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_ecpm_strategy_ecpc_mcb_close);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_mcda_boost_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_search_boost_exp);
DECLARE_SPDM_ABTEST_INT64(cid_search_user_label_num);
DECLARE_SPDM_ABTEST_INT64(cid_search_boost_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_mcda_boost_tag_exp);
DECLARE_SPDM_ABTEST_BOOL(disable_cid_hc_gpm_score_by_cpm_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_corp_default_strategy_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_spu_restart_strategy_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_inspire_zk_bound);
DECLARE_SPDM_ABTEST_DOUBLE(cid_corp_default_bid_ratio);
DECLARE_SPDM_ABTEST_INT64(cid_quality_strategy_tag);
DECLARE_SPDM_ABTEST_INT64(cid_spu_strategy_tag);
DECLARE_SPDM_ABTEST_DOUBLE(cid_account_bid_ratio_upper);
DECLARE_SPDM_ABTEST_INT64(cid_mcda_boost_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_global_ratio_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_cid_ab_global_ratio_exp);
DECLARE_SPDM_ABTEST_DOUBLE(cid_ab_global_bid_ratio);

// [xuyanyan03] 有效获客双出价开关

DECLARE_SPDM_ABTEST_BOOL(enable_bs_cover_all_industry_live_type);
DECLARE_SPDM_ABTEST_BOOL(enable_check_cpa_bid_zero);
DECLARE_SPDM_ABTEST_BOOL(enable_bs_live_flow);
DECLARE_SPDM_ABTEST_BOOL(enable_white_product_skip_account_bidding);

//  [shoulifu03]  短视频引流旁路 auc 开关

DECLARE_SPDM_ABTEST_BOOL(enable_innerloop_splash_skip_admit);  // [luwei] 内循环开屏跳过补贴
DECLARE_SPDM_ABTEST_BOOL(enable_innerloop_account_splash_skip);  // [luwei] 内循环开屏跳过账户补贴
// [shoulifu03] 买家首页流量接入
// [mengfangyuan] gpm 门槛过滤
DECLARE_SPDM_ABTEST_BOOL(enable_multi_imp_adjust);     // [mengfangyuan] 商品多次曝光软打压
DECLARE_SPDM_ABTEST_BOOL(enable_diversity_hc);     // [mengfangyuan] 多样性策略
DECLARE_SPDM_ABTEST_BOOL(enable_pec_bonus);  // [xuxu] pec 补贴

DECLARE_SPDM_ABTEST_BOOL(enable_outer_bcb_bonus_v2);  // [lining] 外循环软广，临时开关
DECLARE_SPDM_ABTEST_BOOL(enable_shield_status_hc);   // [luwei] hc 框架原创主页素材
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_order_pay_cmd_for_guess_you_like);   // [houkai03] 商品卡独立精排

//  [jiangfeng06] 体验参数
DECLARE_SPDM_ABTEST_BOOL(soft_ntr_thr_filter);
DECLARE_SPDM_ABTEST_DOUBLE(soft_ntr_thr);

DECLARE_SPDM_ABTEST_STRING(seq_exp_tag);
DECLARE_SPDM_ABTEST_STRING(diverisity_hc_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_guess_like_rank_dup_author);

DECLARE_SPDM_ABTEST_BOOL(enable_search_ads_pos_ecpm_relevance_mix);  // [tiantian06] ecpm + 相关性过滤开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_ads_pos_ecpm_relevance_live);  // [tiantian06] 排除直播流量开关
DECLARE_SPDM_ABTEST_BOOL(disable_nebula_live_pec_coupon);
DECLARE_SPDM_ABTEST_BOOL(disable_kuaishou_live_pec_coupon);
DECLARE_SPDM_ABTEST_BOOL(enable_multi_level_gpm_thres);
DECLARE_SPDM_ABTEST_BOOL(enable_gpm_thres_for_guess_like);
DECLARE_SPDM_ABTEST_BOOL(enable_merge_to_adlist_for_guess_like);
DECLARE_SPDM_ABTEST_BOOL(enable_dup_adlist_for_guess_like);
DECLARE_SPDM_ABTEST_BOOL(enable_use_sub_page_id_gpm_for_guess_like);
// [shoulifu03] 领券中心开屏导流尾量填充
// [shoulifu03] 买家首页
DECLARE_SPDM_ABTEST_BOOL(enable_buyer_homepage_live_soft_queue);

// [chencongzheng]
DECLARE_SPDM_ABTEST_BOOL(enable_item_card);

DECLARE_SPDM_ABTEST_BOOL(enable_long_ratio_pay_times_2and7);
DECLARE_SPDM_ABTEST_BOOL(enable_split_7day_paytimes_deep_r);
DECLARE_SPDM_ABTEST_BOOL(enable_user_group_w_cpm_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_user_group_w_cpm_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_side_window_user_group_w_cpm_thr);
// ---------------------- abtest integer 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(app_page_form_cpm_thr_exp_tag);  // [sunyuanshuai] cpm 门槛配置
DECLARE_SPDM_ABTEST_INT64(photo_quality_base);  // [liubing05]
DECLARE_SPDM_ABTEST_INT64(splash_purchase_hc_const_value);  // [liubing05]
DECLARE_SPDM_ABTEST_INT64(innerflow_trade_cpm_thr_exp_tag);  // [liubing05] 内流门槛 exptag
DECLARE_SPDM_ABTEST_INT64(min_perfetch_ad_size);  // [liubing05] 预加载最小保留广数量
DECLARE_SPDM_ABTEST_INT64(far_history_ad_window);        // [liuxiaoyan] 二级行业多样性序列窗口较远
DECLARE_SPDM_ABTEST_INT64(recent_history_ad_window);        // [liuxiaoyan] 二级行业多样性序列窗口较近

DECLARE_SPDM_ABTEST_INT64(inspire_live_custom_coin_upper);  // [xuxu] 激励电商个性化金币调整上限
DECLARE_SPDM_ABTEST_INT64(inspire_live_custom_coin_lower);  // [xuxu] 激励电商个性化金币调整下限
DECLARE_SPDM_ABTEST_INT64(merchant_coupon_high_price_threshold); // [xuxu] 售价高于此值 单位厘，优惠力度最大为x%  // NOLINT
DECLARE_SPDM_ABTEST_INT64(merchant_coupon_high_price_discount_value); // [xuxu] 售价高于x值，优惠力度比例最大为此值%  // NOLINT
DECLARE_SPDM_ABTEST_INT64(merchant_coupon_low_price_threshold); // [xuxu] 售价低于此值 单位厘，优惠后实付金额需大于y  // NOLINT
DECLARE_SPDM_ABTEST_INT64(merchant_coupon_low_price_discount_value); // [xuxu] 售价低于x值，实付款补得低于此值,单位厘  // NOLINT
DECLARE_SPDM_ABTEST_INT64(follow_ecpm_boost_tail);        // [guoyuan03] 关注页

DECLARE_SPDM_ABTEST_INT64(inspire_industry_live_cpm_thr_high);

DECLARE_SPDM_ABTEST_INT64(native_ad_bs_price_lower);    // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_INT64(native_ad_bs_price_upper);    // [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_INT64(new_spu_dup_photo_cost_rank_thre);    // [chencongzheng] 新品打标素材消耗排序阈值
DECLARE_SPDM_ABTEST_INT64(hc_experience_pxr_enum);


// [guoyuan03] u4 提价实验
DECLARE_SPDM_ABTEST_BOOL(enable_follow_u4_tijia_jifei);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_u4_tijia_bujifei);
DECLARE_SPDM_ABTEST_INT64(u4_tijia_tail);
DECLARE_SPDM_ABTEST_DOUBLE(follow_u4_tijia_jifei_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(follow_u4_tijia_jifei_bias);
DECLARE_SPDM_ABTEST_DOUBLE(follow_u4_tijia_bujifei_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(follow_u4_tijia_bujifei_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_pk_use_index);
// [guoyuan03] ad load 控制

// [chencongzheng] 激励电商计费打折
DECLARE_SPDM_ABTEST_DOUBLE(reward_merchant_roas_coef);
DECLARE_SPDM_ABTEST_DOUBLE(reward_merchant_orderpay_coef);

// [shoulifu03] 直播 pec 优惠券金额及门槛
DECLARE_SPDM_ABTEST_INT64(live_pec_coupon_discount_amount);
DECLARE_SPDM_ABTEST_INT64(live_pec_coupon_coupon_threshold);
DECLARE_SPDM_ABTEST_INT64(live_pec_coupon_template_id);

// [shoulifu03] 短视频引流 pec 优惠券金额及门槛
DECLARE_SPDM_ABTEST_INT64(p2l_pec_coupon_discount_amount);
DECLARE_SPDM_ABTEST_INT64(p2l_pec_coupon_coupon_threshold);
DECLARE_SPDM_ABTEST_INT64(p2l_pec_coupon_template_id);
//  [shoulifu03] 买家首页 gpm 门槛
DECLARE_SPDM_ABTEST_INT64(merchant_live_gpm_threshold);
DECLARE_SPDM_ABTEST_INT64(first_gpm_thres);
DECLARE_SPDM_ABTEST_INT64(second_gpm_thres);
DECLARE_SPDM_ABTEST_INT64(third_gpm_thres);

DECLARE_SPDM_ABTEST_INT64(first_gpm_thres_for_guess_like);
DECLARE_SPDM_ABTEST_INT64(second_gpm_thres_for_guess_like);
DECLARE_SPDM_ABTEST_INT64(default_guess_like_gpm_threshold);

// [chencongzheng] 新主播冷启动相关
DECLARE_SPDM_ABTEST_INT64(new_live_author_fans_cnt);
DECLARE_SPDM_ABTEST_INT64(new_live_author_last_live);
DECLARE_SPDM_ABTEST_INT64(t0_fans_cnt);
DECLARE_SPDM_ABTEST_INT64(t1_fans_cnt);

//  [shoulifu03] pec 随机发券系数
DECLARE_SPDM_ABTEST_INT64(pec_coupon_random_throttling_factor);
// [shoulifu03] 买家首页直播进精排 quota
// [liubing05] 猜喜 quota
DECLARE_SPDM_ABTEST_INT64(inner_normal_photo_guess_like_quota);
DECLARE_SPDM_ABTEST_INT64(inner_native_photo_guess_like_quota);

DECLARE_SPDM_ABTEST_INT64(backup_ad_quota);     // [zhaoyilin05] 搜索明投兜底广告 quota
// ---------------------- abtest double 参数声明 -------------------
DECLARE_SPDM_ABTEST_DOUBLE(outer_bcb_bonus_adjust_ratio);  // [lining] 软广补贴与硬广打平系数
DECLARE_SPDM_ABTEST_DOUBLE(
                search_ads_pos1_ecpm_relevance_mix_rele_thres_v1);  // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(
                search_ads_pos2_ecpm_relevance_mix_rele_thres_v1);  // [gaokaiming]
DECLARE_SPDM_ABTEST_DOUBLE(
                search_ads_pos1_ecpm_relevance_mix_ecpm_thres);  // [tiantian06] ecpm 过滤 top1 阈值
DECLARE_SPDM_ABTEST_DOUBLE(
                search_ads_pos2_ecpm_relevance_mix_ecpm_thres);  // [tiantian06] ecpm 过滤 top4 阈值

DECLARE_SPDM_ABTEST_DOUBLE(live_merchant_ecology_ratio);  // [wanghongfei] 直播生态打压系数
DECLARE_SPDM_ABTEST_DOUBLE(account_id_cpm_thr_ratio_soft_v2);    //  [nizhihao] 壮阳账户 cpm 门槛系数
DECLARE_SPDM_ABTEST_BOOL(enable_account_id_cpm_thr_v2);   // [nizhihao] 壮阳账户 cpm 门槛开关
DECLARE_SPDM_ABTEST_DOUBLE(server_client_show_rate_soft);    //  [nizhihao] 软广 曝光率系数
DECLARE_SPDM_ABTEST_DOUBLE(adload_admit_thresh_ratio);    //  [nizhihao] cpm 门槛系数
DECLARE_SPDM_ABTEST_BOOL(enable_big_promotion_support_by_author);   // [nizhihao] 大促 author 扶持开关
DECLARE_SPDM_ABTEST_DOUBLE(big_promotion_support_cpm_ratio);    //  [nizhihao] 大促 author 门槛系数
DECLARE_SPDM_ABTEST_BOOL(enable_lt_experience);   // [nizhihao] lt 实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_native_auto_param_admit_all);   // [nizhihao] 自动调参准入
DECLARE_SPDM_ABTEST_BOOL(enable_cpm_thr_ratio_adjust_for_adload);
DECLARE_SPDM_ABTEST_DOUBLE(lt_experience_cpm_ratio);    //  [nizhihao] lt 实验门槛系数
DECLARE_SPDM_ABTEST_DOUBLE(lt_experience_cpm_ratio_nebula);    //  [nizhihao] lt 实验门槛系数极速版
DECLARE_SPDM_ABTEST_DOUBLE(adload_cpm_thr_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(adload_cpm_thr_ratio_nebula);
DECLARE_SPDM_ABTEST_BOOL(enable_bid_uplift_strategy_new);   // [nizhihao] uplift 选择策略
DECLARE_SPDM_ABTEST_BOOL(enable_new_uplift_score_calc);   // [nizhihao] uplift 选择策略
DECLARE_SPDM_ABTEST_BOOL(enable_close_ecpc_max_min);   // [nizhihao] close ecpc
DECLARE_SPDM_ABTEST_BOOL(enable_close_ecpc_mix);   // [dingyiming05] close ecpc 公域单列
DECLARE_SPDM_ABTEST_BOOL(enable_close_ecpc_mix_v2);   // [dingyiming05] close ecpc 公域单列 v2
DECLARE_SPDM_ABTEST_DOUBLE(close_ecpc_mix_coef);
DECLARE_SPDM_ABTEST_INT64(pay_mode_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_account_holdout);
DECLARE_SPDM_ABTEST_DOUBLE(inner_account_holdout_ratio);
DECLARE_SPDM_ABTEST_INT64(sensitive_user_adload_tag);
DECLARE_SPDM_ABTEST_DOUBLE(pay_mode_account_cpm_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_pay_mode_account_cpm_thr);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_default);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w1);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w2);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w3);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w4);
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w5);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w1);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w2);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w3);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w4);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w5);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_default);
DECLARE_SPDM_ABTEST_DOUBLE(hc_max_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(hc_min_cpm_ratio);
DECLARE_SPDM_ABTEST_STRING(model_based_adload_naive_tag);
DECLARE_SPDM_ABTEST_STRING(model_based_adload_industry_tag);
DECLARE_SPDM_ABTEST_STRING(model_based_adload_user_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_user_group_dim_native_stats_param);
DECLARE_SPDM_ABTEST_BOOL(enable_adload_adjust_cpm_thr_fixed);
DECLARE_SPDM_ABTEST_STRING(user_group_level);

DECLARE_SPDM_ABTEST_DOUBLE(splash_purchase_hc_coef);
DECLARE_SPDM_ABTEST_DOUBLE(nobid_fast_cpm_threshold);  // [tangweiqi] 移动端极速进人 nobid 激励门槛
DECLARE_SPDM_ABTEST_DOUBLE(native_bs_auto_bid_weight);         //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_DOUBLE(native_bs_auto_bid_weight_inspire_live);         //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_lower);    //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_upper);    //  [guoyuan03] 软广计费分离
DECLARE_SPDM_ABTEST_DOUBLE(native_ad_gmv_default_val);    //  [guoyuan03] 软广 gpm 默认值
DECLARE_SPDM_ABTEST_DOUBLE(hc_gpm_min_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(hc_gpm_max_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(hc_experience_avg);
DECLARE_SPDM_ABTEST_DOUBLE(hc_experience_cpm_trans);
DECLARE_SPDM_ABTEST_DOUBLE(hc_experience_min_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(hc_experience_max_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(hc_experience_pxr_min);
DECLARE_SPDM_ABTEST_DOUBLE(hc_experience_pxr_max);
DECLARE_SPDM_ABTEST_INT64(traffic_hc_record_pp);  // [dingyiming05] 流量 hc 落表控制 pp
DECLARE_SPDM_ABTEST_DOUBLE(storewide_roi_hc_ratio);   // [shengmingyang] 全店 ROI hc 系数
DECLARE_SPDM_ABTEST_DOUBLE(storewide_roi_hc_ratio_qhc);   // [shengmingyang] 全店 ROI hc 系数全互斥



DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_avg_cpm); // [xuxu] 激励电商个性化金币调整参照值  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_base_coin); // [xuxu] 激励电商个性化金币基准金币数  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_bias_coin); // [xuxu] 激励电商个性化金币 bias 金币数  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_alpha); // [xuxu] 激励电商个性化金币调整生效程度  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(guess_you_like_cpm_threshold_yuan);  // [yesiqi] 猜你喜欢软广 cpm 门槛

DECLARE_SPDM_ABTEST_DOUBLE(merchant_roas_coef_double_col);  //  [chencongzheng] 双列短视频 roas cvr 系数
DECLARE_SPDM_ABTEST_DOUBLE(self_service_bonus_ratio);   // [tangsiyuan] 自助平台补贴系数
DECLARE_SPDM_ABTEST_DOUBLE(gamora_ueq_by_vtr_weight);  // [wangyang10] 用 vtr 算 ueq 精选页权重    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(nebula_ueq_by_vtr_weight);  // [wangyang10] 用 vtr 算 ueq 极速发现页权重    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(gamara_live_vtr_model_ueq_weight);  // [wangyang10] 用 vtr 算 ueq 精选页权重    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(nebula_live_vtr_model_ueq_weight);  // [wangyang10] 用 vtr 算 ueq 极速版权重    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(gamara_total_ueq_weight);  // [jiangfeng06] ueq 精选页权重    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(nebula_total_ueq_weight);  // [jiangfeng06] ueq 极速版权重    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(leverage_gmv_take_rate);  // [shengmingyang] 撬动 gmv 货币化率
DECLARE_SPDM_ABTEST_DOUBLE(pk_hard_cpm_adjust_ratio);  // [shengmingyang] 软硬广 pk 硬广 cpm 调整比例
DECLARE_SPDM_ABTEST_DOUBLE(live_rerank_fixed_sctr);  // [bianfeifei] 直播 rerank 固定 sctr


DECLARE_SPDM_ABTEST_INT64(ad_user_experience_hc_pxr_enum);                 // [wangzixu05] ad_user_experience hc pxr 预估值来源    // NOLINT
DECLARE_SPDM_ABTEST_INT64(other_hc_ntr_source_enum);                        // [wangzixu05] other hc 新增 ntr 项 ntr 预估值来源【reco / ad】     // NOLINT

DECLARE_SPDM_ABTEST_INT64(live_other_hc_ntr_source_enum);                   // [wangzixu05] other hc 新增 ntr 项 ntr 直播 预估值来源【reco / ad】     // NOLINT


DECLARE_SPDM_ABTEST_INT64(ntr_hc_source_enum);                              // [wangzixu05] traffic hc 新增 ntr 项 ntr 预估值来源【reco / ad】     // NOLINT
DECLARE_SPDM_ABTEST_INT64(live_ntr_hc_source_enum);                         // [wangzixu05] traffic hc 新增 ntr 项 ntr 直播 预估值来源【reco / ad】     // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_reco_live_ue_for_ntr_hc);                   // [wangzixu05] reco 直播 ue 模型调用开关 for ntr hc      // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_filter_dup_live_stream_id);                 // [wangzixu05] reco 直播 ue 模型调用，live_stream_id 去重      // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_reco_live_ue_shard);                        // [wangzixu05] reco 直播 ue 模型调用，拆包开关      // NOLINT
DECLARE_SPDM_ABTEST_INT64(reco_live_ue_shard_num);                          // [wangzixu05] reco 直播 ue 模型调用，拆包大小      // NOLINT


DECLARE_SPDM_ABTEST_BOOL(enable_ad_sensitive_rct_hc_thanos_gamora);                        // [wangzixu05]  广告敏感人群 rct hc 主站精选页 实验开关   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_sensitive_rct_hc_thanos_nebula);                        // [wangzixu05]  广告敏感人群 rct hc 极速版发现页 实验开关   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_sensitive_rct_hc_quantile_by_request_cpm);             // [wangzixu05]  广告敏感人群 根据请求内 cpm 分位数调整 hc  // NOLINT

DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_fixed_score_thanos_gamora);                // [wangzixu05]  广告敏感人群 rct hc 主站精选页 固定 hc 大小   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_fixed_score_thanos_nebula);                // [wangzixu05]  广告敏感人群 rct hc 极速版发现页 固定 hc 大小   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_request_cpm_quantile_thanos_gamora);          // [wangzixu05]  广告敏感人群 rct hc 主站精选页 by request cpm 分位数   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_request_cpm_quantile_thanos_nebula);          // [wangzixu05]  广告敏感人群 rct hc 极速版发现页 by request cpm 分位数   // NOLINT

DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_thanos_gamora);                 // [wangzixu05]  广告敏感人群 rct hc 主站精选页 hc weight   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_thanos_nebula);                 // [wangzixu05]  广告敏感人群 rct hc 极速版发现页 hc weight   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_hard);                          // [wangzixu05]  广告敏感人群 rct hc 硬广 hc weight   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_soft);                          // [wangzixu05]  广告敏感人群 rct hc 软广 hc weight   // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_fanstop);                       // [wangzixu05]  广告敏感人群 rct hc 粉条 hc weight   // NOLINT



DECLARE_SPDM_ABTEST_BOOL(enable_crc_ue_low_nogap);            // [qiaolin] 劣质标签不出
DECLARE_SPDM_ABTEST_BOOL(enable_reco_pxtr_model_all_pages);               // [wangzixu05] reco pxtr模型请求，全页面生效  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_reco_pxtr_model_inner_explore);               // [wangzixu05] reco pxtr模型请求，不在发现页内流生效  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_reco_pxtr_model_feed_explore);               // [wangzixu05] reco pxtr模型请求，不在发现页外流生效  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(disable_reco_pxtr_model_other_pages);            // [wangzixu05] reco pxtr模型请求，不在其他小页面生效  // NOLINT
DECLARE_SPDM_ABTEST_STRING(inner_explore_reco_prerank_xtr_grpc_name);   // [wangzixu05] reco pxtr模型，主站双列发现页内流    // NOLINT
DECLARE_SPDM_ABTEST_STRING(feed_explore_reco_prerank_xtr_grpc_name);    // [wangzixu05] reco pxtr模型，主站双列发现页内流    // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_reco_pxtr_model_grpc_map);   // [wangzixu05] reco pxtr模型，<sub_page_id, kess_name> map    // NOLINT

DECLARE_SPDM_ABTEST_INT64(hc_jump_out_rate_source_enum);                 // [wangzixu05] 外跳率 hc 预估值来源  // NOLINT
DECLARE_SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_age_filter);                 // [panshunda] 外跳率生效年龄阈值  // NOLINT
DECLARE_SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_request_times_suppress);                 // [panshunda] 外跳率额外打压当天刷次  // NOLINT
DECLARE_SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_refresh_times_suppress);                 // [panshunda] 外跳率额外打压当前session刷次  // NOLINT
DECLARE_SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_times_suppress_weight);                 // [panshunda] 外跳率额外打压权重  // NOLINT
DECLARE_SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_request_times_filter);                 // [panshunda] 外跳率生效当天请求次数阈值  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_incentive_deep_quit_rate_pred);              // [gaozepeng] 激励请求深度退出率模型  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(reco_htr_whitelist_use_sub_page_id);               // [wangzixu05] reco htr 作为 ntr 过滤预估值白名单页面，使用sub_page_id  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ntr_filter_by_reco_htr);               // [wangzixu05] 软广使用 reco htr 作为 ntr 过滤预估值  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ntr_filter_by_reco_htr_live);               // [qiaolin] 软广使用 reco htr 作为 ntr 过滤预估值直播开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_hard_ntr_filter_by_reco_htr);               // [wangzixu05] 硬广使用 reco htr 作为 ntr 过滤预估值  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_hard_ntr_filter_by_reco_htr_live);               // [qiaolin] 硬广使用 reco htr 作为 ntr 过滤预估值直播开关  // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_longvalue_auto_param_hc);  // [qiaolin] 长期价值调参准入开关
DECLARE_SPDM_ABTEST_INT64(ctcvr_moving_avg_req_threshold2);  // [qiaolin] 滑动平均 ctcvr thr








DECLARE_SPDM_ABTEST_INT64(hard_nebula_user_mobile_price_upper_bound);                          // [wangzixu05] 圈人群时长提升 硬广 极速版 手机价格上限       // NOLINT


DECLARE_SPDM_ABTEST_INT64(hard_nebula_user_active_decrease_days);                              // [wangzixu05] 圈人群时长提升 硬广 极速版 降频用户 两周活跃天数差值       // NOLINT
DECLARE_SPDM_ABTEST_INT64(hard_explore_user_mobile_price_upper_bound);                          // [yesiqi] 发现页双列 圈人群时长提升 硬广 极速版 手机价格上限       // NOLINT
DECLARE_SPDM_ABTEST_INT64(hard_explore_inner_user_mobile_price_upper_bound);                          // [yesiqi] 发现页内流 圈人群时长提升 硬广 极速版 手机价格上限       // NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_ueq_by_pxtr_w_level);  // [wangzixu05] 分W人群用 pxtr 计算 ueq 开关     // NOLINT
//  [gaowei03]  联盟短视频 cvr ltv 门槛
DECLARE_SPDM_ABTEST_DOUBLE(plagiarism_heritage_upper_bound);    // [chencongzheng] 抄袭继承上界
DECLARE_SPDM_ABTEST_DOUBLE(buyer_homepage_cpm_threshold);  // [shoulifu03] 买家首页 cpm 门槛
DECLARE_SPDM_ABTEST_DOUBLE(vtr_ueq_upper_bound);  // [wangyang10] vtr 计算 ueq 上限, yuan
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_live_cpm_thr);  //  [hehandong] 发现页双列直播 cpm 门槛值
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_soft_cpm_threshold_yuan);  // [hehandong] 发现页双列软广 cpm 门槛值
DECLARE_SPDM_ABTEST_DOUBLE(explore_feed_soft_live_cpm_thr);  // [hehandong] 发现页双列软广直播门槛
DECLARE_SPDM_ABTEST_DOUBLE(outer_native_juxing_app_advance_discount);  // [zhangmengxin] 外循环软广聚星唤端打折系数    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(outer_native_dsp_app_advance_discount);  // [zhangmengxin]  外循环软广 dsp 原生唤端打折系数    // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(speed_fanstop_cpm_max_ratio);  // [yangfukang03] 新内粉 hidden cost
DECLARE_SPDM_ABTEST_DOUBLE(follow_page_rank_ratio);  // [wangyang10] 关注页竞价计费调节欠成本现象

DECLARE_SPDM_ABTEST_DOUBLE(hc_gpm_default);
DECLARE_SPDM_ABTEST_DOUBLE(hc_gpm_take_rate);
DECLARE_SPDM_ABTEST_DOUBLE(guess_you_like_rank_gmv_ratio);  // [huangzhaokai] 猜你喜欢 gmv 因子
DECLARE_SPDM_ABTEST_DOUBLE(guess_you_like_rank_gmv_ratio_for_hard);  // [liubing05] 猜你喜欢硬广 gmv 因子
DECLARE_SPDM_ABTEST_DOUBLE(hc_gpm_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(hc_gpm_avg);

DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_twin_bid_flow);
DECLARE_SPDM_ABTEST_BOOL(button_click_simple_room_begin);
DECLARE_SPDM_ABTEST_BOOL(enable_button_click_twin_bid_v1);
DECLARE_SPDM_ABTEST_BOOL(enable_button_click_twin_bid_v2);
DECLARE_SPDM_ABTEST_DOUBLE(button_click_cpa_price_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(button_click_twin_bid_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(button_click_twin_bid_beta);
DECLARE_SPDM_ABTEST_DOUBLE(button_click_twin_bid_hour);
DECLARE_SPDM_ABTEST_DOUBLE(button_click_twin_bid_alpha_max);
DECLARE_SPDM_ABTEST_DOUBLE(button_click_twin_bid_alpha_min);

// [zhaoqilong] 行业直播-组件点击
DECLARE_SPDM_ABTEST_BOOL(enable_button_click_twin_bid_strategy_with_param_kconf)
DECLARE_SPDM_ABTEST_STRING(button_click_twin_bid_exp_name)

DECLARE_SPDM_ABTEST_BOOL(enable_button_click_twin_bid_strategy_with_action_kconf)
DECLARE_SPDM_ABTEST_STRING(button_click_twin_bid_action_exp_name)

DECLARE_SPDM_ABTEST_BOOL(enable_button_click_twin_bid_mcb_strategy)

DECLARE_SPDM_ABTEST_DOUBLE(coldstart_hc_u4_coef);    // [chencongzheng] 高 u 人群冷启动系数
DECLARE_SPDM_ABTEST_DOUBLE(coldstart_hc_u3_coef);    // [chencongzheng] 高 u 人群冷启动系数
DECLARE_SPDM_ABTEST_DOUBLE(coldstart_hc_u0_coef);    // [chencongzheng] 高 u 人群冷启动系数

DECLARE_SPDM_ABTEST_DOUBLE(non_mix_thanos_native_cpm_threshold_yuan);  // [shengmingyang] 软广非混排单列门槛
DECLARE_SPDM_ABTEST_DOUBLE(merchant_rank_gmv_ratio);  //  [shoulifu03] 电商流量 gmv 因子
// [liuxiaoyan]  二级行业多样性 hc 系数
DECLARE_SPDM_ABTEST_DOUBLE(recent_ratio_neg);
DECLARE_SPDM_ABTEST_DOUBLE(far_ratio_neg);
DECLARE_SPDM_ABTEST_DOUBLE(ratio_positive);
DECLARE_SPDM_ABTEST_DOUBLE(soft_freq_hc_ratio_upper);
DECLARE_SPDM_ABTEST_DOUBLE(soft_freq_hc_ratio_lower);
DECLARE_SPDM_ABTEST_DOUBLE(soft_freq_hc_coef);
DECLARE_SPDM_ABTEST_DOUBLE(soft_freq_hc_coef_native);
DECLARE_SPDM_ABTEST_STRING(freq_hc_exp_tag);
DECLARE_SPDM_ABTEST_DOUBLE(ad_direct_merchant_gpm_hc_min_ratio);  // [songxu] 商家 hc cpm 下界
DECLARE_SPDM_ABTEST_DOUBLE(ad_direct_merchant_gpm_hc_max_ratio);  // [songxu] 商家 hc cpm 上界
DECLARE_SPDM_ABTEST_BOOL(enable_native_jump_ratio_cpm_thre);
DECLARE_SPDM_ABTEST_INT64(jump_ratio_cvr_num_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(jump_decay_weight);
DECLARE_SPDM_ABTEST_DOUBLE(jump_out_cpm_max_thre);
DECLARE_SPDM_ABTEST_DOUBLE(jump_out_cpm_min_thre);
DECLARE_SPDM_ABTEST_DOUBLE(jump_ratio_total_cvr);
DECLARE_SPDM_ABTEST_BOOL(enable_jump_out_ads_fliter);
DECLARE_SPDM_ABTEST_DOUBLE(jump_ratio_thr);
DECLARE_SPDM_ABTEST_DOUBLE(max_jump_ratio_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_low_quality_photo_fliter);
DECLARE_SPDM_ABTEST_BOOL(enable_melt_exp_fliter);
DECLARE_SPDM_ABTEST_DOUBLE(random_drop_out_ads);
DECLARE_SPDM_ABTEST_DOUBLE(reco_vtr_thre);
DECLARE_SPDM_ABTEST_INT64(first_industry_id_skip);
// [liuxiaoyan] 双端导流
DECLARE_SPDM_ABTEST_BOOL(enable_native_double_end_hc);
DECLARE_SPDM_ABTEST_BOOL(enable_double_end_hc);
DECLARE_SPDM_ABTEST_DOUBLE(double_end_fix_cpm_thre);

// [shoulifu03] 买家首页软广队列 cpm 门槛
DECLARE_SPDM_ABTEST_DOUBLE(buyer_homepage_soft_queue_cpm_thr);
// [shoulifu03] 直播 gpm 兜底值
DECLARE_SPDM_ABTEST_DOUBLE(live_default_gpm);
// [shoulifu03] 软广 hidden cost 上下界
DECLARE_SPDM_ABTEST_DOUBLE(native_hc_max_cpm_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(native_hc_min_cpm_ratio);
// 付费次数参数
DECLARE_SPDM_ABTEST_DOUBLE(pay_times_weight_1d);
DECLARE_SPDM_ABTEST_DOUBLE(pay_times_weight_2and7);
// 付费金额拆分
DECLARE_SPDM_ABTEST_DOUBLE(pay_amount_weight_1d);
DECLARE_SPDM_ABTEST_DOUBLE(pay_amount_weight_2and7);
DECLARE_SPDM_ABTEST_BOOL(enable_pay_amount_2and7);
DECLARE_SPDM_ABTEST_BOOL(enable_game_30d_ltv_load);
DECLARE_SPDM_ABTEST_BOOL(enable_long_ratio_seven_day_roas);
DECLARE_SPDM_ABTEST_DOUBLE(upper_origin_pay_amount_7d);
// 长线纠偏系数
DECLARE_SPDM_ABTEST_BOOL(enable_seven_day_longratio_cali_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(seven_day_longratio_cali_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_roas_longratio_cali_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(ad_roas_longratio_cali_ratio);
// 混排 gpm 迁移开关
DECLARE_SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_live);
DECLARE_SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_photo);

DECLARE_SPDM_ABTEST_DOUBLE(upper_r_long_ratio_7d_repurchase_param);

DECLARE_SPDM_ABTEST_STRING(sdpa_item_explore_tag);  // [wuwei03] 商品库广告商品探索策略
// [nizhihao] adload
DECLARE_SPDM_ABTEST_STRING(multi_level_exp_group_tag);
DECLARE_SPDM_ABTEST_STRING(adload_sample_group_tag);
DECLARE_SPDM_ABTEST_INT64(adload_sample_pv_bucket);
DECLARE_SPDM_ABTEST_INT64(game_appoint_req_threshold);
DECLARE_SPDM_ABTEST_INT64(live_vtr_req_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_fix);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_rta_cpm_log);
DECLARE_SPDM_ABTEST_INT64(game_shoufa_req_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_game_rnd_explore_outer);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_subsidy_log_record);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_different_weight);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_purchase_ratio_for_iaa);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_purchase_ratio_for_iap);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_conv_ratio_for_iaa);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_conv_ratio_for_iap);
DECLARE_SPDM_ABTEST_DOUBLE(no_subsidy_calibrate_factor);
DECLARE_SPDM_ABTEST_DOUBLE(subsidy_calibrate_factor);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_reset_subsidy_unify_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_reset_subsidy_unify_ltv);
DECLARE_SPDM_ABTEST_INT64(outer_hc_allocate_req_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_game_predict_fill);
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_novel_predict_fill);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_reset_subsidy_unify_ltv_iaap);
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv);
DECLARE_SPDM_ABTEST_DOUBLE(iaap_iaa_ltv_bagging_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_playtimes_strategy);
DECLARE_SPDM_ABTEST_STRING(mini_game_playtimes_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_billing_seperate);
DECLARE_SPDM_ABTEST_STRING(iap_game_twin_bid_group_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_7d_value_allocate);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_twin_bid_divide_ocpc);
DECLARE_SPDM_ABTEST_DOUBLE(iap_twin_bid_default_long_value_iap_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(iap_twin_bid_default_long_value_iaap_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(iap_twin_bid_default_long_value_boost_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(iap_game_long_value_ratio_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(iap_game_long_value_ratio_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(iap_twin_bid_decay_weight);
DECLARE_SPDM_ABTEST_INT64(iap_twin_bid_req_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_twin_bid_unify_post_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(default_moving_iap_game_long_value_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_7d_value_twin_bid);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_7d_value_twin_bid_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_7r_allocate);
DECLARE_SPDM_ABTEST_DOUBLE(final_iap_twin_bid_ratio_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(final_iap_twin_bid_ratio_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(final_iap_twin_bid_boost_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_billing_seperate);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_billing_seperate_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(iap_billing_seperate_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_player_only_first_charge_admit);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_iaap_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_iaap_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(big_r_explore_factor);
DECLARE_SPDM_ABTEST_INT64(big_r_explore_ad_num);
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv7);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_7d_value_allocate_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_7d_use_auto_roas);
DECLARE_SPDM_ABTEST_DOUBLE(iap_long_value_target_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(final_iap_7r_boost_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_request_new_7r);
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_7r_request_iaa_ltv7);
DECLARE_SPDM_ABTEST_BOOL(enable_iap_payment_window_30d);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_use_long_value);
DECLARE_SPDM_ABTEST_DOUBLE(default_iap_payment_constraint);
DECLARE_SPDM_ABTEST_DOUBLE(default_iap_long_value_constraint);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_fill_iaa_ltv7_field);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_params_parse_rank);
DECLARE_SPDM_ABTEST_STRING(enable_mini_game_big_r_strategy_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_30d);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_24h);
DECLARE_SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_24h_thrd);
DECLARE_SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_30d_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_explore_force);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_uv_monitor);
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv_admit_all);
DECLARE_SPDM_ABTEST_BOOL(enable_big_game_fill_industry_7r);
DECLARE_SPDM_ABTEST_BOOL(enable_big_game_add_sdk_admit);
DECLARE_SPDM_ABTEST_INT64(long_value_req_threshold);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_7r_fill_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_fill_7r_judge);
DECLARE_SPDM_ABTEST_STRING(enable_big_game_7r_admit_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_7r_fill_admit_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_user_update);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_big_r_explore_end);
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_reward_skip_force);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_update_rank_constraint);
DECLARE_SPDM_ABTEST_DOUBLE(default_iap_payment_constraint_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_big_r_monitor_update);
// [rentingyu] explore
DECLARE_SPDM_ABTEST_STRING(dnc_exp_tag);

DECLARE_SPDM_ABTEST_STRING(coldstart_tiered_strategy_exp_tag);  // [chencongzheng] 冷启动分级实验 tag
DECLARE_SPDM_ABTEST_STRING(coldstart_model_p2l_bonus_exp_tag);  // [yuanli03] 冷启动模型 tag
DECLARE_SPDM_ABTEST_STRING(coldstart_model_photo_bonus_exp_tag);  // [yuanli03] 冷启动模型 tag
DECLARE_SPDM_ABTEST_STRING(coldstart_hc_strategy_exp_tag);  // [chencongzheng] 冷启动分级实验 tag

DECLARE_SPDM_ABTEST_STRING(fanstop_inner_region_org_exp_types);  // [luoqiang] 跳过内粉 ecpm 过滤的组织 id
DECLARE_SPDM_ABTEST_STRING(inspire_live_bias_ratio_config);  //  [shoulifu03] 激励直播额外金币系数配置
DECLARE_SPDM_ABTEST_STRING(
    inspire_live_piecewise_linear_config);  //  [shoulifu03] 激励直播金币个性化下发线性分段配置  //NOLINT
DECLARE_SPDM_ABTEST_STRING(inspire_live_aucbid_ratio_by_ocpx);  //  [gaozepeng] 激励直播分优化目标配置金币 bias 项 auction_bid 系数  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_native_coin_adjust_ratio);  //  [gaozepeng] 激励直播金币调节系数  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_live_roi_control);  //  [gaozepeng] 激励直播金币做 roi 限制  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_price_ratio);  //  [gaozepeng] 激励直播广告价格系数  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_roi_lower);  //  [gaozepeng] 激励直播 roi 门槛  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_adless_add_pop_window);  //  [zhangxingyu03] 免广告样式是否允许展示弹窗
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_unify_coin_and_style);  //  [zhangxingyu03] 深度激励金币与样式统一 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_coin_pec_for_joint_draw);  // [zhangxingyu03] 币 pec for 共建 draw 流  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_joint_draw_pec_app_advance_coin_num);  // [zhangxingyu03] 币 pec for 共建 draw 流拉活金币值  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_joint_draw_pec_app_coin_num);  // [zhangxingyu03] 币 pec for 共建 draw 流激活金币值  //NOLINT
DECLARE_SPDM_ABTEST_STRING(invoked_uplift_cvr_ratio_str);  //  [zhangxingyu03] 唤端 uplift model base cvr ratio //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_iaa_adless_style_delivery_gap_s_new);  // [zhangxingyu03] IAA 免广告新下发间隔单位秒  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_adless_joint_freq_control_use_ms);  // [zhangxingyu03] 免广告联合频控使用毫秒
DECLARE_SPDM_ABTEST_DOUBLE(invoked_uplift_cvr_ratio);  //  [zhangxingyu03] 拉活激励 uplift cvr ratio  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(invoked_treatment_coin_ratio);  //  [zhangxingyu03] 拉活激励 invoked treatment coin ratio  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_invoke_fix_deep_coin_v3);  // [zhangxingyu03] 拉活激励 rct v3
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_order_admit_sub_page_id);  // [zhangxingyu03]  下单激励准入 sub_page_id  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_order_skip_roas);  // [zhangxingyu03]  下单激励跳过 roas 优化目标  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(invoked_optimal_uplift_cvr_ratio);  // [zhangxingyu03] 唤端最优 uplift clip ratio  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_total_deep_task_delivery_time_gap_s); // [zhangxingyu03] 免广告联合金币深度激励时间频控  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_uax_aigc_bid_calibration);  // [lining]
DECLARE_SPDM_ABTEST_BOOL(disable_mcda_calibration);  // [zhaoziyou]
DECLARE_SPDM_ABTEST_BOOL(enable_native_deep_reward_effect_cpm);  // [zhangxingyu03]
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_reward_no_erase_style);  // [zhangxingyu03]
DECLARE_SPDM_ABTEST_DOUBLE(invoked_fix_deep_style_ratio);  // [zhangxingyu03] 拉活深度激励样式固定比率
DECLARE_SPDM_ABTEST_STRING(invoked_fix_deep_coin_num_str);  // [zhangxingyu03] 拉活激励固定金币数值
DECLARE_SPDM_ABTEST_BOOL(enable_deep_coin_adjust_ratio);  // [zhangxingyu03] 深度金币调整 ratio
DECLARE_SPDM_ABTEST_DOUBLE(deep_coin_adjust_ratio);  // [zhangxingyu03]
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_adless_style);  // [zhangxingyu03] 免广告激励
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_adless_page_id_admit);  // [zhangxingyu03] 免广告激励
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_adless_sub_page_id_admit);  // [zhangxingyu03] 免广告激励
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_adless_pos_id_admit);  // [zhangxingyu03] 免广告激励
DECLARE_SPDM_ABTEST_INT64(incentive_adless_product_delivery_gap_s);  // [zhangxingyu03] 免广告激励产品名下发间隔单位秒  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_adless_style_delivery_gap_s);  // [zhangxingyu03] 免广告样式下发间隔单位秒  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_iaa_adless_style_delivery_gap_s);  // [zhangxingyu03] IAA 免广告下发间隔单位秒  //NOLINT
DECLARE_SPDM_ABTEST_STRING(deep_coin_ratio_str);  // [zhangxingyu03] 深度金币分档位 ratio
DECLARE_SPDM_ABTEST_BOOL(enable_deep_coin_adjust_by_different_ratio);  // [zhangxingyu03] 深度金币分档位调控  //NOLINT
DECLARE_SPDM_ABTEST_INT64(deep_coin_range_per_level_for_adjust); // [zhangxingyu03] 分档位调控深度金币时每个档位的金币值  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_deep_coin_adjust_by_ocpc_action_type);  // [zhangxingyu03] 深度金币分优化目标调控  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_order_uplift_deep_coin_decision);  // [zhangxingyu03] 下单激励使用 uplift cvr 决策  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_order_rct_fix_coin);  // [zhangxingyu03]  下单深度激励 rct 实验
DECLARE_SPDM_ABTEST_DOUBLE(order_fix_deep_style_ratio);  //  [zhangxingyu03] 下单激励 rct ratio
DECLARE_SPDM_ABTEST_DOUBLE(inspire_live_feed_sctr);  //  [gaozepeng] 激励直播双列 sctr
DECLARE_SPDM_ABTEST_DOUBLE(inspire_mix_sctr);  //  [gaozepeng] 激励混合 sctr
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_feed_soft_ad_new_sctr);  //  [gaozepeng] 激励双列软广生效新的 sctr
DECLARE_SPDM_ABTEST_BOOL(enable_inspire_feed_soft_ad_fixed_sctr);  //  [gaozepeng] 激励双列软广生效固定 sctr
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_auto_dark_btr_tag);  // [gaozepeng] 插入 INCENTIVE_AUTO_DARK_BTR_TAG //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_rewarded_soft_ad);  // [gaozepeng] 粉条短视频接入激励
DECLARE_SPDM_ABTEST_BOOL(enable_soft_photo_for_inspire);  // [gaozepeng] 软广短视频接入激励
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_ecpc);  // [gaozepeng] 激励视频优化目标 ecpc 开关  //NOLINT

// [zhangxingyu03] 深浅联合决策
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_coin_joint_decision);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_rct_migrate);

// [zhangxingyu03] 深度激励整体
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_deep_incentive_skip_incentive_flow_judge);
DECLARE_SPDM_ABTEST_DOUBLE(deep_coin_adjust_ratio_for_live_order_roi)
DECLARE_SPDM_ABTEST_STRING(calc_view_coin_consider_deep_incentive_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_calc_view_coin_use_cpm_before_deep_incentive_new);
DECLARE_SPDM_ABTEST_DOUBLE(calc_view_coin_use_cpm_before_deep_incentive_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_rct_add_native);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_add_dish_bugfix);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_add_dish);
DECLARE_SPDM_ABTEST_BOOL(enable_iaa_deep_coin_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_d_i_specific_coin_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_calc_adx_deep_incentive_coin_by_cpm);
DECLARE_SPDM_ABTEST_DOUBLE(adx_deep_incentive_coin_roi_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_calc_view_coin_use_gross_after_deep_incentive);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_record_rewarded_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_total_holdout);  // [zhangxingyu03]  深度激励整体 holdout 实验  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_gross_max_by_model);  // [zhangxingyu03]  深度激励毛利排序  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(deep_incentive_gross_max_coef);  //  [zhangxingyu03] 深度激励毛利排序系数
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_global_rct_style_judge);

// [zhangxingyu03] 拉活激励
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_d_i_uplift_blacklist);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_d_i_specific_time_gap_config);
DECLARE_SPDM_ABTEST_INT64(invoked_d_i_specific_time_gap_s);
DECLARE_SPDM_ABTEST_DOUBLE(invoked_d_i_ob_data_record_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(invoked_iaa_deep_coin_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_d_i_global_rct);
DECLARE_SPDM_ABTEST_DOUBLE(invoked_d_i_global_rct_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_d_i_iaa_use_uplift_model);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_d_i_specific_config);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_advertiser_blacklist);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_new_admit_config);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_rct_switch_split);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_holdout_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_add_cid);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_uplift_cvr_clip);
DECLARE_SPDM_ABTEST_DOUBLE(invoked_uplift_cvr_clip_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_marginal_roi_constrain);  // [zhangxingyu03] 拉活边际 roi 约束  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(invoked_deep_incentive_marginal_roi_constrain_coef);  //  [zhangxingyu03] 拉活边际 roi 约束系数  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_roi_constrain);  // [zhangxingyu03] 拉活生效 roi 约束  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(invoked_deep_incentive_roi_constrain_coef);  //  [zhangxingyu03] 拉活 roi 约束系数
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_auto_cpa_bid);  // [zhangxingyu03] 拉活使用 auto cpa bid  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(invoked_deep_incentive_coin_coef);  //  [zhangxingyu03] 拉活深度金币系数
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_adjust_cvr);  // [zhangxingyu03] 拉活激励整体 cvr 校准  //NOLINT
DECLARE_SPDM_ABTEST_STRING(invoked_deep_incentive_adjust_cvr_ratio_str);  // [zhangxingyu03] 拉活激励整体 cvr 调整系数 //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_invoked_max_mission_num);  // [zhangxingyu03] 拉活唤端最大任务次数  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_invoked_delivery_time_gap);  // [zhangxingyu03] 拉活唤端下发时间间隔  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_rewarded_ratio);  // [zhangxingyu03]  拉活激励使用领奖率系数  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_holdout);  //  [zhangxingyu03] 拉活激励 holdout

// [zhangxingyu03] 激活激励
DECLARE_SPDM_ABTEST_BOOL(enable_award_active_app_new_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_coin_decision_for_promotion);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_uplift_model_add_site_page_for_promotion);
DECLARE_SPDM_ABTEST_DOUBLE(conv_iaa_deep_coin_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_global_rct);
DECLARE_SPDM_ABTEST_DOUBLE(conv_d_i_global_rct_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_uplift_site_page_product_name_whitelist);
DECLARE_SPDM_ABTEST_INT64(enable_conv_d_i_style_delivery_time_gap_s);
DECLARE_SPDM_ABTEST_INT64(enable_conv_d_i_style_max_mission_num);
DECLARE_SPDM_ABTEST_DOUBLE(conv_d_i_ob_data_record_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_ratio_control_config_bugfix);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_iaa_use_uplift_model);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_uplift_model_add_site_page);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_d_i_specific_config);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_new_admit_config);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_rct_switch_split);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_skip_deep_bid_twin);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_rct_request_uplift_model);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_uplift_model_black_list);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_uplift_model_white_list);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_add_cid);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_uplift_cvr_clip);
DECLARE_SPDM_ABTEST_DOUBLE(conv_uplift_cvr_clip_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_roi_constrain);  // [zhangxingyu03] 激活生效 roi 约束  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(conv_deep_incentive_roi_constrain_coef);  //  [zhangxingyu03] 激活 roi 约束系数
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_auto_cpa_bid);  // [zhangxingyu03] 激活使用 auto cpa bid  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(conv_deep_incentive_coin_coef);  //  [zhangxingyu03] 拉活深度金币系数
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_adjust_cvr);  // [zhangxingyu03] 激活激励整体 cvr 校准  //NOLINT
DECLARE_SPDM_ABTEST_STRING(conv_deep_incentive_adjust_cvr_ratio_str);  // [zhangxingyu03] 激活激励整体 cvr 调整系数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_conv_install_judge);  // [zhangxingyu03]  激活激励判断是否安装  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_conv_product_id_control);  // [zhangxingyu03]  激活激励 product id 频控  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_conv_max_mission_num);  // [zhangxingyu03] 激活激励最大任务次数  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_conv_delivery_time_gap);  // [zhangxingyu03] 激活激励下发时间间隔  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_rewarded_ratio);  // [zhangxingyu03]  激活激励使用领奖率系数  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_conv_uplift_model);  // [zhangxingyu03]  激活深度激励使用 uplift model  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_conv_model_decision_skip_rct);  // [zhangxingyu03] 模型决策跳过 rct  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_conv_uplift_cvr_adjust);  // [zhangxingyu03] cvr 调整系数  //NOLINT
DECLARE_SPDM_ABTEST_STRING(conv_uplift_cvr_ratio_str);  // [zhangxingyu03] cvr 调整系数
DECLARE_SPDM_ABTEST_DOUBLE(conv_treatment_coin_ratio);  //  [zhangxingyu03] coin ratio
DECLARE_SPDM_ABTEST_DOUBLE(conv_optimal_uplift_cvr_ratio);  //  [zhangxingyu03] uplift cvr ratio
DECLARE_SPDM_ABTEST_BOOL(enable_conv_uplift_deep_coin_decision);  // [zhangxingyu03] 激活激励使用 uplift cvr 决策  //NOLINT
DECLARE_SPDM_ABTEST_STRING(conv_uplift_model_exp_tag);  // [zhangxingyu03] 激活深度激励 rct 金币档位
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_holdout);  //  [zhangxingyu03] 激活激励 holdout
DECLARE_SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_marginal_roi_constrain);
DECLARE_SPDM_ABTEST_DOUBLE(conv_deep_incentive_marginal_roi_constrain_coef);

// [zhangxingyu03] 下单激励
DECLARE_SPDM_ABTEST_BOOL(enable_order_native_deep_incentive_new);
DECLARE_SPDM_ABTEST_BOOL(enable_order_d_i_global_rct);
DECLARE_SPDM_ABTEST_DOUBLE(order_d_i_global_rct_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_use_uplift_logits);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_incentive_price_adjust_for_sv_order);
DECLARE_SPDM_ABTEST_STRING(deep_incentive_price_adjust_str_for_sv_order);
DECLARE_SPDM_ABTEST_BOOL(enable_deep_coin_adjust_ratio_for_sv_order_roi);
DECLARE_SPDM_ABTEST_DOUBLE(deep_coin_adjust_ratio_for_sv_order_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_rct_switch_split);
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_clip_list);
DECLARE_SPDM_ABTEST_STRING(order_deep_incentive_clip_ratio_str);
DECLARE_SPDM_ABTEST_BOOL(enable_order_rct_request_uplift_model);
DECLARE_SPDM_ABTEST_BOOL(enable_order_uplift_cvr_clip);
DECLARE_SPDM_ABTEST_DOUBLE(order_d_i_ob_data_record_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(order_uplift_cvr_clip_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_roi_constrain);  // [zhangxingyu03] 下单生效 roi 约束  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(order_deep_incentive_roi_constrain_coef);  //  [zhangxingyu03] 下单 roi 约束系数
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_use_auto_cpa_bid);  // [zhangxingyu03] 下单使用 auto cpa bid  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_order_native_deep_incentive);  // [zhangxingyu03] 深度激励下单接软广  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(order_deep_incentive_coin_coef);  //  [zhangxingyu03] 拉活深度金币系数
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_adjust_cvr);  // [zhangxingyu03] 下单激励整体 cvr 校准  //NOLINT
DECLARE_SPDM_ABTEST_STRING(order_deep_incentive_adjust_cvr_ratio_str);  // [zhangxingyu03] 下单激励整体 cvr 调整系数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_effect_cpm);  // [zhangxingyu03] 下单激励影响 cpm  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_order_max_mission_num);  // [zhangxingyu03] 下单激励最大任务次数  //NOLINT
DECLARE_SPDM_ABTEST_INT64(incentive_order_delivery_time_gap);  // [zhangxingyu03] 下单激励下发时间间隔  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_order_uplift_model);  // [zhangxingyu03] 下单激励 uplift model  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_model_decision_skip_rct);  // [zhangxingyu03] 模型决策跳过 rct  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_order_uplift_cvr_adjust);  // [zhangxingyu03] cvr 调整系数  //NOLINT
DECLARE_SPDM_ABTEST_STRING(order_uplift_cvr_ratio_str);  // [zhangxingyu03] cvr 调整系数
DECLARE_SPDM_ABTEST_DOUBLE(order_treatment_coin_ratio);  //  [zhangxingyu03] coin ratio
DECLARE_SPDM_ABTEST_DOUBLE(order_optimal_uplift_cvr_ratio);  //  [zhangxingyu03] uplift cvr ratio
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_holdout);  //  [zhangxingyu03] 下单激励 holdout
DECLARE_SPDM_ABTEST_BOOL(enable_order_deep_incentive_use_marginal_roi_constrain);
DECLARE_SPDM_ABTEST_DOUBLE(order_deep_incentive_marginal_roi_constrain_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_order_d_i_rct_add_item_price_judge);
DECLARE_SPDM_ABTEST_DOUBLE(order_d_i_global_rct_coin_ratio_upper);

// [zhangxingyu03] 直播订单激励
DECLARE_SPDM_ABTEST_DOUBLE(live_order_d_i_discount_coef);
DECLARE_SPDM_ABTEST_DOUBLE(live_order_d_i_coin_upper_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_d_i_cvr_adjust);
DECLARE_SPDM_ABTEST_STRING(live_order_d_i_cvr_adjust_str);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_d_i_use_new_treatment);
DECLARE_SPDM_ABTEST_BOOL(enable_d_i_rct_tag_change);
DECLARE_SPDM_ABTEST_DOUBLE(live_order_d_i_ob_data_record_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(live_order_treatment_coin_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(live_order_optimal_uplift_cvr_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_pay_uplift_decision);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_pay_uplift_init);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_pay_deep_incentive_rct);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_rct_fix_coin);
DECLARE_SPDM_ABTEST_DOUBLE(live_order_fix_deep_style_ratio);
DECLARE_SPDM_ABTEST_STRING(live_order_uplift_model_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_pay_deep_incentive_holdout)  // [renruimin]

// [zhangxingyu03] 直播 ROAS 激励
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_d_i_use_discount_ratio_smooth);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_d_i_use_discount_ratio_smooth_ratio_k);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_d_i_use_discount_ratio_smooth_ratio_b);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_d_i_discount_coef);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_d_i_coin_upper_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_d_i_discount_consider_gmv);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_discount_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_d_i_cvr_adjust);
DECLARE_SPDM_ABTEST_STRING(live_roas_d_i_cvr_adjust_str);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_d_i_use_new_treatment);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_d_i_add_t7_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_d_i_add_storewide);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_uplift_add_clear);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_treatment_coin_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_optimal_uplift_cvr_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_uplift_decision);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_uplift_init);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_deep_incentive_rct);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_rct_fix_coin);
DECLARE_SPDM_ABTEST_DOUBLE(live_roas_fix_deep_style_ratio);
DECLARE_SPDM_ABTEST_STRING(live_roas_uplift_model_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_deep_incentive_holdout)  // [renruimin]

// [zhangxingyu03] draw 流
DECLARE_SPDM_ABTEST_DOUBLE(incentive_draw_flow_coin_roi_coef);  //  [zhangxingyu03] draw 流金币 roi 系数  //NOLINT

DECLARE_SPDM_ABTEST_STRING(revert_bonus_exp_tag);  // [chencongzheng] 补贴框架不生效 bonus_tag

DECLARE_SPDM_ABTEST_STRING(live_gpm_unified_exp_tag);
DECLARE_SPDM_ABTEST_STRING(unify_adload_total_tag);
DECLARE_SPDM_ABTEST_STRING(unify_adload_ft_tag);
DECLARE_SPDM_ABTEST_STRING(unify_adload_ad_tag);
DECLARE_SPDM_ABTEST_STRING(unify_adload_industry_ori_tag);
DECLARE_SPDM_ABTEST_STRING(user_group_w_cpm_thr_exp_tag);
DECLARE_SPDM_ABTEST_STRING(follow_user_group_w_cpm_thr_exp_tag);
DECLARE_SPDM_ABTEST_STRING(side_window_user_group_w_cpm_thr_exp_tag);
DECLARE_SPDM_ABTEST_STRING(game_shoufa_force_exp_tag);  // [zhangmengxin] 游戏首发配置 tag
// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------
DECLARE_SPDM_ABTEST_BOOL(enable_model_calibrate);  // [haomingyang] rank 模型校准功能//NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_rank_high_priority_quota);  // 粉条精排保量 quota 开关
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_cpm_new_threshold_yuan);  // 外粉新 cpm 阈值开关
DECLARE_SPDM_ABTEST_BOOL(enable_rta_second_predict_use_new_paid);  // rta 激励视频下发 xaid 中的 paid
DECLARE_SPDM_ABTEST_BOOL(enable_rta_sta_tag_refill);  // rta_sta_tag 下发修正
DECLARE_SPDM_ABTEST_INT64(outerloop_nc_max_force_reco_topn);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_load_outerloop_interest_industry);
DECLARE_SPDM_ABTEST_INT64(outerloop_nc_max_goal_ocpx);
DECLARE_SPDM_ABTEST_INT64(outerloop_nc_max_goal_industry);
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_mark);
DECLARE_SPDM_ABTEST_BOOL(enable_brand_fanstop_r3_skip_cpmbound);
DECLARE_SPDM_ABTEST_BOOL(enable_all_brand_fanstop_skip_cpmbound);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_load_outerloop_ac_industry);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_load_outerloop_ac_frequence);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_content_consumption_force_reco);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_cc_kgame_force_reco);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_cc_playlet_force_reco);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_cc_fiction_force_reco);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_search_leads_industry_inner_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_search_serial_iaa_boost_total);
DECLARE_SPDM_ABTEST_BOOL(enable_search_serial_roas_boost_total);
DECLARE_SPDM_ABTEST_BOOL(enable_search_adbox_order_pay_model);
DECLARE_SPDM_ABTEST_BOOL(enable_style_distinguish_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_style_pos_event_logging);
DECLARE_SPDM_ABTEST_BOOL(enable_live_roas_style_distinguish_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_live_order_style_distinguish_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_search_industry_live_lps_bigcard_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_search_industry_live_lps_form_card_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_search_series_iaa_conv);
DECLARE_SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_mark_update);
DECLARE_SPDM_ABTEST_INT64(outerloop_cc_nc_max_topn);
// ---------------------- abtest int64 参数声明 -------------------
DECLARE_SPDM_ABTEST_INT64(fanstop_rank_high_priority_photo_quota);  // 粉条精排 photo 保量 quota
DECLARE_SPDM_ABTEST_INT64(fanstop_rank_high_priority_live_quota);  // 粉条精排 live 保量 quota
DECLARE_SPDM_ABTEST_INT64(outerloop_nc_max_overimpression_threshold);  // 外循环 DNC 同品曝光次数阈值
DECLARE_SPDM_ABTEST_INT64(outerloop_ac_retrieval_diversity_threshold);
DECLARE_SPDM_ABTEST_INT64(outerloop_low_active_ac_thresh);
// ---------------------- abtest double 参数声明 -------------------
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_cpm_new_threshold_yuan);  // 外粉 cpm 新阈值
DECLARE_SPDM_ABTEST_DOUBLE(brand_fanstop_cpm_new_threshold_yuan);  // 外粉品牌 cpm 新阈值
DECLARE_SPDM_ABTEST_DOUBLE(feed_brand_fanstop_cpm_new_threshold_yuan);  // 外粉品牌 cpm 新阈值
DECLARE_SPDM_ABTEST_DOUBLE(speed_fanstop_cpm_new_threshold_yuan);  // 外粉速推 cpm 新阈值
DECLARE_SPDM_ABTEST_DOUBLE(feed_speed_fanstop_cpm_new_threshold_yuan);  // 外粉速推 cpm 新阈值
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_cvr_threshold_ratio_order_paied);
DECLARE_SPDM_ABTEST_DOUBLE(fanstop_cvr_threshold_ratio_leads_submit);
// ---------------------- kconf bool 参数声明 -------------------

// ---------------------- kconf int64 参数声明 -------------------
// ---------------------- kconf double 参数声明 -------------------


DECLARE_SPDM_ABTEST_BOOL(enbale_online_calibration_with_cmd);    // [yeshuxiong] 实时校准开关
DECLARE_SPDM_ABTEST_BOOL(enbale_online_calibration_with_cmd_order_paied);
DECLARE_SPDM_ABTEST_BOOL(enbale_online_calibration_cmd_native);    // [panjianfei03] 软广实时校准开关
DECLARE_SPDM_ABTEST_BOOL(enbale_online_calibration_conv_native);   // [zhangmengxin] 软广实时校准激活开关
DECLARE_SPDM_ABTEST_BOOL(enable_award_play_and_invoked_new);   // [wangjiabin05] 更新完成激励拉活判断逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_rank_price_record);
DECLARE_SPDM_ABTEST_INT64(enhance_ecpm_strategy_default_tag);   // [songxu] 商家计费客户侧实验  // NOLINT
DECLARE_SPDM_ABTEST_INT64(enhance_ecpm_strategy_default_tag);   // [songxu] 商家计费客户侧实验  // NOLINT
// [wuyinhao] 端智能使用 cpm 还是 rank_benefit 作为门槛 0 表示 rank_benefit ，1 表示 cpm
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_use_cpm_or_rankbenifit);
// [wuyinhao] 端智能是否使用 cpm 或 rank_benefit 的门槛值
DECLARE_SPDM_ABTEST_INT64(client_ai_high_value_use_threshold_value);
DECLARE_SPDM_ABTEST_BOOL(enable_calc_client_ai_rerank_score_adrank);
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_u_level_calibration);  // [linbowei] 直播全站分 u 校准策略
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_model_cmd);  // [linbowei] 直播全站请求增量建模
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_calibration);  // [linbowei] 直播全站增量建模 校准
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_block_lower_u);  // [linbowei] 直播全站增量建模 低 u 策略准入  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_page_limit);  // [linbowei] 直播全站增量建模 分页面策略准入  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_r);  // [linbowei] 直播全站增量建模 r 模型
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_q);  // [linbowei] 直播全站增量建模 q 模型
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_fix_cali_v2);  // [linbowei] 直播全站增量建模 固定系数校准实验 v2  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_page_calibration);  // [linbowei] 直播全站增量建模 分页面校准实验  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_esp_calibration);  // [linbowei] 直播全站增量建模 分客户类型校准实验  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_fix_cvr_upper_bound);  // [linbowei] 直播全站增量建模 校准兼容上限  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_cali_ltv);  // [linbowei] 直播全站增量建模 roas 切 ltv 校准  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_bound);  // [linbowei] 直播全站增量建模 上下界限制  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_storewide_live_uplift_author_exp_tag);  // [linbowei] 直播全站增量建模 客户尾号实验打标  // NOLINT
DECLARE_SPDM_ABTEST_STRING(storewide_live_uplift_exp_tag);  // [linbowei] 直播全站增量建模 流量实验 tag  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(storewide_live_uplift_p2_weight);  // [linbowei] 直播全站增量建模 p2 权重
DECLARE_SPDM_ABTEST_INT64(storewide_write_request_spuid_to_redis_expire);  // [zhaochen11] 直播全站同品打压实验写redis过期时间  // NOLINT
DECLARE_SPDM_KCONF_INT64(minPriceBound);  // [heqian] min_max price ratio bound
DECLARE_SPDM_KCONF_INT64(maxPriceBound);  // [heqian] min_max price ratio bound
DECLARE_SPDM_ABTEST_INT64(follow_max_price);
DECLARE_SPDM_ABTEST_INT64(follow_fanstop_v2_max_price);
DECLARE_SPDM_ABTEST_INT64(wanhe_max_price);  // [zhaokun03] 万合最大计费
DECLARE_SPDM_ABTEST_INT64(wanhe_fanstop_v2_max_price);
DECLARE_SPDM_ABTEST_INT64(max_amd_photo_ranking_list_idx);          // [huangzhaokai] 硬广精排列表选取的最大个数  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(max_amd_photo_ranking_list_size);         // [huangzhaokai] 硬广精排列表缓存最大长度
DECLARE_SPDM_ABTEST_INT64(invo_traffic_rank_redis_nums);  //  [zhaokun03] 创新流量参竞信息写入 redis 单次数量
DECLARE_SPDM_ABTEST_INT64(max_native_ranking_list_idx);             // [huangzhaokai] 软广精排列表选取的最大个数  //  NOLINT
DECLARE_SPDM_ABTEST_INT64(max_native_ranking_list_size);            // [huangzhaokai] 软广精排列表缓存最大长度
DECLARE_SPDM_ABTEST_INT64(max_hard_ad_force_reco_tag_size);  // [wuyonghong] 有强插 tag 硬广数量上限
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_sort_by_tag);
DECLARE_SPDM_ABTEST_DOUBLE(force_reco_rb_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_rb_thrd);
DECLARE_SPDM_ABTEST_BOOL(enable_feed_explore_sctr_calibrate);  // [duanxinning] 发现页外流 sctr 校准
DECLARE_SPDM_ABTEST_STRING(feed_explore_sctr_calibrate_tag);   // [duanxinning] 发现页外流 sctr 校准 tag
DECLARE_SPDM_ABTEST_DOUBLE(feed_sctr_cali_upper_bound);   // [duanxinning] 发现页外流 sctr 校准系数上界
DECLARE_SPDM_ABTEST_DOUBLE(feed_sctr_cali_lower_bound);   // [duanxinning] 发现页外流 sctr 校准系数下界
DECLARE_SPDM_ABTEST_BOOL(enable_log_inner_explore_overlap_ratio);  // [duanxinning] 发现页内流逃逸用户系数落日志  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_same_second_industry);
DECLARE_SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_update);  // [zhaokun03] 短剧强出避让策略升级
DECLARE_SPDM_ABTEST_INT64(max_native_ad_force_reco_tag_size);  // [wuyonghong] 有强插 tag 软广数量上限
DECLARE_SPDM_ABTEST_BOOL(enable_qcpx_p2p_switch);  // [wangjiabin05] 是否切换 发券 p2p
DECLARE_SPDM_ABTEST_INT64(normal_size_for_move_auction);    // [wangjiabin05] 计费下移入口硬广队列大小
DECLARE_SPDM_ABTEST_BOOL(enable_front_auction_move_rank);  // [wangjiabin05] front 竞价下移至 rank
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_mark_unify);  // [wangjiabin05] front 强出收敛
DECLARE_SPDM_ABTEST_BOOL(enable_rank_quota_logic_split);  // [wangjiabin05] rank quota 优选独立
DECLARE_SPDM_ABTEST_BOOL(enable_unify_rank_size);  // [wangjiabin05] rank 统一队列
DECLARE_SPDM_ABTEST_INT64(ad_rank_unify_result_size);    // [wangjiabin05] rank 出口统一队列大小
DECLARE_SPDM_ABTEST_BOOL(enable_search_cpm_thres_with_rele);  // [niejinlong] 搜索带相关性判断的 cpm 阈值
DECLARE_SPDM_ABTEST_BOOL(enable_diversion_source_cpm_config);  // [niejinlong] 搜索广告导流 cpm 阈值配置
DECLARE_SPDM_ABTEST_BOOL(enable_search_inner_stream_top4_rele_thres);  // [niejinlong] 搜索广告内流 top4
DECLARE_SPDM_ABTEST_BOOL(enable_search_inner_stream_first_ad_pos1);  // [niejinlong] 搜索广告内流 top1
DECLARE_SPDM_ABTEST_BOOL(enable_outer_loop_candidate_sample);  // [chenqi07] 外循环下发率流采样实验
DECLARE_SPDM_ABTEST_BOOL(enable_sample_outer_soft);  // [wangzhiqiang03] 外循环软广流采样实验
DECLARE_SPDM_ABTEST_BOOL(enable_sample_outer_soft_v3);  // [wangzhiqiang03] 外循环软广流采样实验
DECLARE_SPDM_ABTEST_BOOL(enable_rank_industry_explore);  // [guanpingyin] 行业人群探索
DECLARE_SPDM_ABTEST_BOOL(enable_rank_industry_explore_v2);  // [yuchengyuan] 金教独立开关人群探索
DECLARE_SPDM_ABTEST_BOOL(enable_industry_user_explore);  // [yuchengyuan] 行业垂类分层人群探索
DECLARE_SPDM_ABTEST_BOOL(enable_finance_deep_bonus);   // [yuchengyuan] 金融行业深度补贴  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_finance_deep_bonus_by_project);   // [yuchengyuan] 金融行业深度补贴个性化  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_edu_ensemble_imp_lps);   // [linyuhao03] 教育表单走 ensemble 预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_edu_ensemble_imp_lps_block_corp);   // [linyuhao03] 教育表单屏蔽异常营业执照  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_ecom_conv_main_pred);   // [linyuhao03] SDPA 电商激活使用中台模型预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_c2_lps);   // [zhaozhuang] c2表单  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_c2_lps_ownctr);   // [zhaozhuang] c2表单 单独ctr  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_wechat_connected_imp_lps);  // [yemengqing03] 企微新链路独立模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_industry_user_explore_duanju_constant);  // [guanpingyin] 行业探索短剧常数
DECLARE_SPDM_ABTEST_BOOL(enable_industry_user_explore_duanju_model_delta);  // [guanpingyin] 行业探索短剧常数
DECLARE_SPDM_ABTEST_BOOL(enable_industry_user_explore_jiaotong);  // [zhaijianwei] 交通人群探索
DECLARE_SPDM_ABTEST_BOOL(enable_industry_user_explore_realestate);  // [xuyanyan03] 房地产行业人群探索开关
DECLARE_SPDM_ABTEST_STRING(realestate_deepopt_expid);  // [xuyanyan03] 房地产行业联合建模深度优化策略配置组
DECLARE_SPDM_ABTEST_DOUBLE(realestate_hc_cpm_ratio);  // [xuyanyan03] 房地产行业人群探索 hc 系数
DECLARE_SPDM_ABTEST_INT64(realestate_hc_uservalue);  // [xuyanyan03] 房地产行业 hc 用户 tag

DECLARE_SPDM_ABTEST_BOOL(enable_clue_acquisition_hc);  // [xuyanyan03] 线索有效获客 hc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_clue_acquisition_hc_ratio);  // [xuyanyan03] 线索有效获客 hc_ratio 开关
DECLARE_SPDM_ABTEST_BOOL(enable_lpsdeep_support);  // [xuyanyan03] 线索有效获客扶持开关
DECLARE_SPDM_ABTEST_BOOL(enable_edu_lps_deep_support);  // [linyuhao03] 教育正价课扶持开关
DECLARE_SPDM_ABTEST_DOUBLE(clue_acquisition_hc_ratio_single);  // [xuyanyan03] 线索有效获客单出价 hc_ratio
DECLARE_SPDM_ABTEST_DOUBLE(clue_acquisition_hc_ratio_twin);  // [xuyanyan03] 线索有效获客双出价 hc_ratio
DECLARE_SPDM_ABTEST_DOUBLE(effective_acquisition_coef_single_bid);  // [xuyanyan03] 有效获客单出价校准系数 dcvr  //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(effective_acquisition_coef_twin_bid);  // [xuyanyan03] 有效获客双出价校准系数 dcvr
DECLARE_SPDM_ABTEST_DOUBLE(clue_support_hc_w);  // [xuyanyan03] 线索行业反哺计划 hc 系数
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_item_explore);  // [wuwei03] 商品库广告商品探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_item_explore);  // [wuwei03] 商品库广告商品探索开关
DECLARE_SPDM_ABTEST_BOOL(enable_cid_hc_cpm_default_exp);  // [songxu] cid hc 默认
DECLARE_SPDM_ABTEST_DOUBLE(cid_hc_cpm_default_exp_ratio);  // [songxu] cid hc 默认
DECLARE_SPDM_ABTEST_DOUBLE(paied_course_order_paied_coeff);  // [wuwei03] 付费课堂订单支付 cvr 调整系数
DECLARE_SPDM_ABTEST_BOOL(enable_paid_course_bonus);  // [wuwei03] 付费课堂 bonus 开关

DECLARE_SPDM_ABTEST_BOOL(enable_leads_unify_cvr)
// 外循环行业直播相关参数
DECLARE_SPDM_ABTEST_BOOL(enable_request_direct_live_lps_realtime); // [yangkebin] 外循环行业直播-直播直投-表单提交实时模型 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(direct_live_lps_realtime_ensemble_weight); // [yangkebin] 外循环行业直播-直播直投-表单提交实时模型权重 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(direct_live_lps_base_ensemble_weight); // [yangkebin] 外循环行业直播-直播直投-表单提交base模型权重 //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_dsplive_direct_conv_bypass_auc); // [zhangxin29] 外循环行业直播-直播直投-激活模型 旁路auc使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dsplive_direct_lps_bypass_auc); // [zhangxin29] 外循环行业直播-直播直投-表单模型 旁路auc使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dsplive_ctr_bypass_auc); // [zhangxin29] 外循环行业直播-ctr单列 旁路auc使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dsplive_p2l_conv_bypass_auc); // [zhangxin29] 外循环行业直播-作品引流-激活模型 旁路auc使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dsplive_p2l_lps_bypass_auc); // [zhangxin29] 外循环行业直播-作品引流-表单模型 旁路auc使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_dsplive_p2l_ctr_bypass_auc); // [zhangxin29] 外循环行业直播-作品引流-ctr 旁路auc使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_atomization_order); // [zhangxin29] 内循环直播原子化项目-订单出价-使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_atomization_roas); // [zhangxin29] 内循环直播原子化项目-ROI出价-使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_atomization_t7roas); // [zhangxin29] 内循环直播原子化项目-7日ROI出价-使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_atomization_switch); // [lining10] ROI出价请求由订单模型切换到原子化pay_cnt模型,务必在enable_inner_live_atomization_order 和enable_inner_live_atomization_order同时打开的基础上使用-使能开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_atomization_t7_switch); // [lining10] T7ROI出价请求由订单模型切换到原子化pay_cnt模型,务必在enable_inner_live_atomization_order 和enable_inner_live_atomization_order同时打开的基础上使用-使能开关 //NOLINT

DECLARE_SPDM_ABTEST_BOOL(enable_key_action2_dsp);  // [zhouqifei] 关键行为 2.0 支持 dsp
DECLARE_SPDM_ABTEST_BOOL(enable_local_life_kbox_list);  // [zhaodi] 本地生活 kbox 拆队列开关
// [zhaodi] 开屏 rank_result 填充 ad_queue_type 开关
DECLARE_SPDM_ABTEST_BOOL(enable_splash_rank_result_ad_queue_type);
DECLARE_SPDM_ABTEST_BOOL(enable_search_candidate_ack_v1);   // [zhaoyilin05] 搜索样本流构建 V1 版本开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_candidate_ack_live);   // [zhaoyilin05] 搜索样本流 V1 版本是否包含直播
DECLARE_SPDM_ABTEST_BOOL(enable_ad_force_reco_tag);   // [wuyonghong] 是否使用强插 tag
DECLARE_SPDM_ABTEST_BOOL(disable_incentive_force);   // [wuyonghong] 激励强出屏蔽
DECLARE_SPDM_ABTEST_BOOL(disable_incentive_fanstop_force);   // [wuyonghong] 激励粉条强出屏蔽
DECLARE_SPDM_ABTEST_BOOL(disable_force_reco_inner_v2);   // [dingyiming05] 内循环强出屏蔽
DECLARE_SPDM_ABTEST_BOOL(disable_force_reco_outer_v2);   // [dingyiming05] 外循环强出屏蔽
DECLARE_SPDM_ABTEST_BOOL(enable_dup_hc_v2);  // [wanghongfei] 素材跳过 hc 开关
DECLARE_SPDM_ABTEST_DOUBLE(dup_exp_hc);  // [wanghongfei] 素材跳过 hc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_original_photo_hc);  // [wanghongfei] 素材原创 hc 开关
DECLARE_SPDM_ABTEST_DOUBLE(dup_skip_hc_originality_thresh);  // [wanghongfei] 素材阈值
DECLARE_SPDM_ABTEST_DOUBLE(dup_skip_hc_bound_thresh);  // [wanghongfei] 素材阈值
DECLARE_SPDM_ABTEST_DOUBLE(original_hc_ratio);  // [wanghongfei] 素材 hc ratio
DECLARE_SPDM_ABTEST_DOUBLE(original_shield_hc_ratio);  // [luwei] 主页素材 hc ratio
DECLARE_SPDM_ABTEST_DOUBLE(original_exp_ratio);  // [wanghongfei] 素材 hc ratio
DECLARE_SPDM_ABTEST_DOUBLE(dup_bound_hc_ratio);  // [wanghongfei] 素材 hc ratio
DECLARE_SPDM_ABTEST_DOUBLE(outer_live_cpm_thr);
// [lihantong] 商品力
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_pid_service);
DECLARE_SPDM_ABTEST_DOUBLE(smb_billing_hard_lower_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(smb_billing_hard_upper_ratio);
DECLARE_SPDM_ABTEST_STRING(competing_item_hc_exp_tag);
DECLARE_SPDM_ABTEST_STRING(smb_hc_exp_tag);
DECLARE_SPDM_ABTEST_DOUBLE(merchant_gpm_l0_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(merchant_gpm_l1_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(merchant_gpm_l2_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(merchant_gpm_l3_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(merchant_gpm_l4_ratio);

DECLARE_SPDM_ABTEST_DOUBLE(game_high_value_explore_bonus_ratio);  // [zhangmengxin] 游戏高价值用户探索 bonus 系数 //NOLINT
DECLARE_SPDM_ABTEST_INT64(game_high_ltv_bonus_effect_tag);  // [zhangmengxin] 游戏 ltv 补贴 tag //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_game_high_ltv_bonus);  // [zhangmengxin] 游戏 ltv 补贴 tag 开关 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(game_high_ltv_bonus_effect_ratio);  // [zhangmengxin] 游戏 ltv 补贴 tag 系数 //NOLINT
DECLARE_SPDM_ABTEST_INT64(kminigame_high_ltv_bonus_effect_tag);  // [jiangpeng07] 快小游 ltv 补贴 tag //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_high_ltv_bonus_effect_ratio);  // [jiangpeng07] 快小游 ltv 补贴 tag 系数 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(wegame_nc_explore_bonus_ratio);  // [jiangpeng07] 微小游爆品探索 bonus 系数 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_game_long_value_explore);   // [guoqi] 游戏长期价值探索
DECLARE_SPDM_ABTEST_BOOL(enable_bonus_after_process_immg);  // [lizemin] bonus 迁移后置转换
DECLARE_SPDM_ABTEST_BOOL(enable_zero_bonus_tag_setting);  // [lizemin] bonus_tag set 0 when bonus_cpm=0
DECLARE_SPDM_ABTEST_BOOL(enable_game_long_value_explore_launch);    // [guoqi03] 游戏长期价值探索-单独推全开关 配合白名单食用 //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(game_long_value_explore_constant_hard);
DECLARE_SPDM_ABTEST_DOUBLE(game_long_value_explore_constant_soft);
DECLARE_SPDM_ABTEST_DOUBLE(game_long_value_cpm_ratio);      // [guoqi03] 长期公式 cpm 参数
DECLARE_SPDM_ABTEST_DOUBLE(game_conversion_rate_alpha);     // [guoqi03] cvr 超参
DECLARE_SPDM_ABTEST_DOUBLE(game_ltv_beta);                  // [guoqi03] ltv 超参
DECLARE_SPDM_ABTEST_DOUBLE(game_long_value_ensemble_a);
DECLARE_SPDM_ABTEST_DOUBLE(game_long_value_ensemble_c);
// 游戏 30 日 LTV 参数
DECLARE_SPDM_ABTEST_BOOL(enable_request_game_30d_ltv_hc);
DECLARE_SPDM_ABTEST_BOOL(enable_game_30d_ltv_hc);
DECLARE_SPDM_ABTEST_DOUBLE(game_30d_ltv_global_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(game_30d_ltv_decay_weight);
DECLARE_SPDM_ABTEST_INT64(game_30d_ltv_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(game_30d_ltv_cpm_exp_ratio);
// 游戏首充党 ecpc 策略参数
DECLARE_SPDM_ABTEST_BOOL(enable_request_game_player_only_first_charge_ecpc);
DECLARE_SPDM_ABTEST_BOOL(enable_game_player_only_first_charge_ecpc);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_decay_weight);
DECLARE_SPDM_ABTEST_INT64(game_player_only_first_charge_ecpc_req_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_coeff);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_upper_bound);
DECLARE_SPDM_ABTEST_STRING(game_player_only_first_charge_ecpc_exp_name);
DECLARE_SPDM_ABTEST_INT64(game_player_only_first_charge_ecpc_key_type);
DECLARE_SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_fan_force);  // [zhangmengxin] 外循环直播粉丝强出策略开关
DECLARE_SPDM_ABTEST_INT64(outer_live_game_conv_ecpc_req_threshold);

DECLARE_SPDM_ABTEST_BOOL(enable_product_model_lab_score);

DECLARE_SPDM_ABTEST_DOUBLE(init_account_purchase_roi_coef);   // [lining] 行业 roi_hc
DECLARE_SPDM_ABTEST_DOUBLE(drop_account_purchase_roi_coef);   // [lining] 行业 roi_hc
DECLARE_SPDM_ABTEST_DOUBLE(drop_min_account_purchase_roi_coef);   // [lining] 行业 roi_hc
DECLARE_SPDM_ABTEST_DOUBLE(drop_adload_admit_thresh_ratio);   // [lining] 行业 roi_hc
DECLARE_SPDM_ABTEST_DOUBLE(hc_update_bound);   // [lining] 行业 hc_update_bound
DECLARE_SPDM_ABTEST_DOUBLE(hc_update_coef);   // [lining] 行业 hc_update_coef
DECLARE_SPDM_ABTEST_DOUBLE(industry_default_target_roi);   // [lining] 行业 industry_default_target_roi
DECLARE_SPDM_ABTEST_DOUBLE(industry_default_ltv_hc_factor);   // [lining] 行业 default_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(industry_purchase_ltv_hc_factor);   // [lining] 行业 purchase_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(industry_purchase_roi_hc_factor);   // [lining] 行业 purchase_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(native_purchase_ltv_hc_coef);   // [lining] 行业 default_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(native_purchase_roi_hc_coef);   // [lining] 行业 purchase_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(native_roi_hc_coef);   // [lining] 行业 purchase_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(industry_roi_hc_factor);   // [lining] 行业 purchase_ltv_factor
DECLARE_SPDM_ABTEST_DOUBLE(industry_default_ltv_hc_filter_factor);
DECLARE_SPDM_ABTEST_DOUBLE(follow_cpm_threshold);           // [jinhui05] 关注页 CPM 门槛，元
DECLARE_SPDM_ABTEST_DOUBLE(sctr_into_cpm_discount_ratio_follow);   // [yuanli03] 关注页 cpm 打折 ratio
DECLARE_SPDM_ABTEST_BOOL(enable_use_gpm_for_guess_you_like_hard);   // [huangzhaokai] 猜你喜欢 rank_benefit 中增加 gmv //NOLINT
// [guoqi03] 游戏请求付费预估模型
DECLARE_SPDM_ABTEST_BOOL(enable_game_request_7r_conv_ltv);
DECLARE_SPDM_ABTEST_STRING(admit_7r_conv_ltv_ocpcs);
DECLARE_SPDM_ABTEST_STRING(admit_2_7d_pay_ltv_ocpcs);
DECLARE_SPDM_ABTEST_BOOL(enable_game_roi_request_ltv27);
DECLARE_SPDM_ABTEST_BOOL(enable_duanju_close_polaris);
DECLARE_SPDM_ABTEST_STRING(sub_page_id_gmv_ratio_exp_tag_for_guess_like);
DECLARE_SPDM_ABTEST_BOOL(enable_industry_hc_pid_splittest_exp);    // [songxu] hc 行业 pid splittest 实验
DECLARE_SPDM_ABTEST_INT64(industry_hc_pid_splittest_exp_tag);      // [songxu] hc 行业 pid splittest 实验 tag
DECLARE_SPDM_ABTEST_STRING(admit_pay_times_ocpcs);

DECLARE_SPDM_ABTEST_BOOL(enable_duanju_relative_hc);
DECLARE_SPDM_ABTEST_DOUBLE(duanju_relative_hc_alpha);

DECLARE_SPDM_ABTEST_BOOL(enable_initial_release_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_log_game_polaris_hc_pacing_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(initial_release_base_prob);
DECLARE_SPDM_ABTEST_DOUBLE(initial_release_prob_max);
DECLARE_SPDM_ABTEST_DOUBLE(initial_release_prob_min);
DECLARE_SPDM_ABTEST_BOOL(enable_retarget_force_tag);

DECLARE_SPDM_ABTEST_BOOL(enable_po_quan_bcee_strategy);

DECLARE_SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank);  // [guoqi03] 天花板精排
// [wuyonghong] 天花板精排换到新的强出 tag
DECLARE_SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank_force_tag);

DECLARE_SPDM_ABTEST_DOUBLE(outer_native_gpm_cpm_ratio);  // [zhangmengxin] 外循环软广 gpm 折算 cpm ratio

DECLARE_SPDM_ABTEST_BOOL(enable_mobile_photo_soft_model);  // [lihantong] 移动端短视频模型
DECLARE_SPDM_ABTEST_BOOL(enable_mobile_soft_to_hard_auction_bound);  // [lihantong] 移动端短视频模型
DECLARE_SPDM_ABTEST_DOUBLE(mobile_soft2hard_max_auciton);  // [lihantong] 移动端短视频模型

DECLARE_SPDM_ABTEST_BOOL(enable_model_explore);  // [fanzhongxiang] 模型主动探索
DECLARE_SPDM_ABTEST_DOUBLE(model_explore_thr);  // [fanzhongxiang] 模型主动探索
DECLARE_SPDM_ABTEST_DOUBLE(model_explore_hc_ratio);  // [fanzhongxiang] 模型主动探索
DECLARE_SPDM_ABTEST_INT64(model_explore_filter_size);  // [fanzhongxiang] 模型主动探索
DECLARE_SPDM_ABTEST_BOOL(enable_age_hc);  // [fanzhongxiang] 模型主动探索

DECLARE_SPDM_ABTEST_DOUBLE(account_bidding_stable_hc_ratio_hard);  // [liyueyang05] 账户稳定性硬广 hc 系数
DECLARE_SPDM_ABTEST_DOUBLE(account_bidding_stable_hc_ratio_soft);  // [liyueyang05] 账户稳定性软广 hc 系数
DECLARE_SPDM_ABTEST_DOUBLE(playlet_account_bidding_stable_hc_ratio_hard);  // [liyueyang05] 付费短剧账户稳定性硬广 hc 系数  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(playlet_account_bidding_stable_hc_ratio_soft);  // [liyueyang05] 付费短剧账户稳定性软广 hc 系数  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(tongxin_expore_hc_limit);  // [libingjie03] 通信开卡率探索 hc
DECLARE_SPDM_ABTEST_DOUBLE(tongxin_expore_hc_alpha);  // [libingjie03] 通信开卡率探索 hc
// [wangyunli] 外循环下发率数据流 cpm fix 只用 server show
DECLARE_SPDM_ABTEST_BOOL(enable_outer_delivery_sample_cpm_fix_with_server_show);
// [wangyunli] 外循环下发率数据流相关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_sampler_judge_by_target_ad_num);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_sampler_pre_filter);
DECLARE_SPDM_ABTEST_BOOL(enable_sample_creatives_replace_rank);

DECLARE_SPDM_ABTEST_BOOL(enable_disable_soft_user_group_cpm_thr);  //  [shoulifu03] 跳过旧的保 adload 框架
DECLARE_SPDM_ABTEST_BOOL(enbale_online_calibration_with_cmd_roas);  // [yeshuxiong] roas 校准开关 cmdid 过滤
DECLARE_SPDM_ABTEST_BOOL(enbale_gyl_calibration);  // [luwei] 商品卡校准过滤
DECLARE_SPDM_ABTEST_BOOL(enable_inner_native_order_pay_calibration);  // [yuanwei09] order 软广校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_order_pay_cali_ad_queue_type);  // [yuanwei09] order 校准请求区分软硬广
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_jichen);  // [luwei] 商品卡明投继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_jichen_add_ocpc_action_type);  // [luwei] 商品卡明投继承增加优化目标
DECLARE_SPDM_ABTEST_BOOL(disable_item_card_jichen_roas);  // [luwei] 关闭商品卡 roas 明投继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_spu_jichen);  // [luwei] 商品卡明投 spu 继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_soft_queue_jichen);  // [luwei] 商品卡明投软广继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_author_spu_jichen);  // [luwei] 商品卡明投 author spu 继承
DECLARE_SPDM_ABTEST_DOUBLE(item_card_jichen_rate_delta);  // [luwei] 商品卡明投继承增量
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_not_jichen_cali);  // [luwei] 商品卡明投非继承生效纠偏
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_not_jichen_cali2);  // [luwei] 商品卡明投非继承生效纠偏
DECLARE_SPDM_ABTEST_DOUBLE(item_card_not_jichen_cali_rate);  // [luwei] 商品卡明投非继承纠偏系数

DECLARE_SPDM_ABTEST_BOOL(enable_item_card_merchant_product_id_hard_jichen);  // [luwei] 商品卡明投商品硬广继承
DECLARE_SPDM_ABTEST_BOOL(disable_spu_0_jichen);  // [luwei] 商品卡明投 spu 0 取消继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_roas_item_id_hard_jichen);  // [luwei] 商品卡明投商品 roas 硬广继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_roas_item_id_soft_jichen);  // [luwei] 商品卡明投商品 roas 软广继承
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_roas_jichen_fix);  // [luwei] 商品卡明投商品 roas 继承修复
// [luwei] 商品卡明投 merchant_product_id 0 取消继承
DECLARE_SPDM_ABTEST_BOOL(disable_merchant_product_id_0_jichen);
DECLARE_SPDM_ABTEST_BOOL(enable_jichen_cvr_upper_bound);  // [luwei] 配置商品卡明投 cvr 继承上限
DECLARE_SPDM_ABTEST_DOUBLE(item_card_jichen_cvr_upper_bound);  // [luwei] 商品卡明投 cvr 继承上限

DECLARE_SPDM_ABTEST_BOOL(enable_inner_roas_cali_ad_queue_type);  // [yuanwei09] roas 校准请求区分软硬广
DECLARE_SPDM_ABTEST_BOOL(enable_inner_native_roas_calibration);  // [yuanwei09] roas 软广校准开关
DECLARE_SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_min_rate);  // [yeshuxiong] roas 校准上界
DECLARE_SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_max_rate);  // [yeshuxiong] roas 校准下界
DECLARE_SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_exception_upper_bound);  // [yeshuxiong] roas 校准上界
DECLARE_SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_exception_lower_bound);  // [yeshuxiong] roas 校准下界
DECLARE_SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_min_rate);  // [yuanyue03] 表单校准上界
DECLARE_SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_max_rate);  // [yuanyue03] 表单校准下界
DECLARE_SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_exception_upper_bound);  // [yuanyue03] 表单校准上界
DECLARE_SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_exception_lower_bound);  // [yuanyue03] 表单校准下界

DECLARE_SPDM_ABTEST_DOUBLE(ecpm_threshold_explore_inner_native);   // [yuanli03] 关注页内流独立 cpm 门槛
DECLARE_SPDM_ABTEST_BOOL(enable_explore_inner_uplift_score);   // [hehandong] 发现页内流 uplift 开关
DECLARE_SPDM_ABTEST_BOOL(enable_explore_inner_user_value_hard_ad_adjust);  // [liubing] 发现页内流硬广人群价值调整开关   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_explore_inner_user_value_soft_ad_adjust);  // [liubing] 发现页内流软广广人群价值调整开关   //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_skip_soft_uplift_score);   // [liubing] 发现页内流 uplift 跳过软广开关
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_uplift_score_thr);   // [hehandong] 发现页内流 uplift 门槛
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_uplift_score_ratio);   // [hehandong] 发现页内流 uplift 系数

// [jiyang] 关注页复原 CPM 门槛
DECLARE_SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag);
DECLARE_SPDM_ABTEST_DOUBLE(follow_ecpm_threshold_init);
DECLARE_SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag_3);
// [jiyang] 短剧行业外传内补贴 bonus
DECLARE_SPDM_ABTEST_BOOL(enable_payskit_outer_bonus_to_inner);
DECLARE_SPDM_ABTEST_STRING(payskit_outer_bonus_to_inner_exptag);
// [jiyang] 重构优质软广判定逻辑
DECLARE_SPDM_ABTEST_BOOL(enable_refactor_nogap);
DECLARE_SPDM_ABTEST_BOOL(enable_refactor_nogap_newlink);
DECLARE_SPDM_ABTEST_BOOL(enable_refactor_nogap_newlink_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retcs);
DECLARE_SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retdyddcs);
DECLARE_SPDM_ABTEST_BOOL(enable_tapeout_follow_hc);
DECLARE_SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retcs_2);
DECLARE_SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retdyddcs_2);
DECLARE_SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_2);
DECLARE_SPDM_ABTEST_BOOL(disable_incentive_not_costly_coin_rb);  // [gapzepeng] 激励无成本不使用 coin rb
DECLARE_SPDM_ABTEST_DOUBLE(incentive_unify_rb_coin_ratio);  // [gapzepeng] 激励统一 rb 金币系数
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_exploit_use_session_expected_value);  // [gapzepeng] 激励金币决策使用 session_expected_value  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_incentive_sctr_migrate);  // [gapzepeng] 激励迁移 sctr
DECLARE_SPDM_ABTEST_DOUBLE(incentive_sctr_migrate_upper);  // [gapzepeng] 激励 1 / sctr 上限
// [jiyang] 关注页精排重构
DECLARE_SPDM_ABTEST_BOOL(enable_follow_rank_refactor_dot);
DECLARE_SPDM_ABTEST_BOOL(enable_add_chuangxin_model_feature);
// [jinhui05] 内流 trigger item 对应的河图标签
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_wanhe);
DECLARE_SPDM_ABTEST_INT64(inner_trigger_item_strategy_mode);
DECLARE_SPDM_ABTEST_STRING(inner_trigger_item_strategy_tag);
DECLARE_SPDM_ABTEST_DOUBLE(inner_trigger_item_same_tag_factor);
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_path_boost_inner_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_path_boost_wanhe);
DECLARE_SPDM_ABTEST_DOUBLE(inner_trigger_item_path_boost);
// [jinhui05] 主站发现页内流曝光系数移除
DECLARE_SPDM_ABTEST_BOOL(enable_explore_inner_sctr_migrate);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_sctr_migrate_lower);
DECLARE_SPDM_ABTEST_DOUBLE(explore_inner_sctr_migrate_upper);
// [jinhui05] 硬广 unify_sctr 优化
DECLARE_SPDM_ABTEST_BOOL(fix_hard_unify_sctr_click);
DECLARE_SPDM_ABTEST_BOOL(fix_hard_unify_sctr);



DECLARE_SPDM_ABTEST_BOOL(enable_roas_native_use_imp_ltv_switch_new);  //  [yeshuxiong] roas 一段式开关
DECLARE_SPDM_ABTEST_DOUBLE(outer_native_game_gpm_cpm_ratio);  // [zhangmengxin] 外循环游戏行业软广补齐 gpm 系数   //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(outer_hard_game_gpm_cpm_ratio);  // [zhangmengxin] 外循环游戏行业硬广补齐 gpm 系数   //NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(game_ecpc_decay_weight);  // [jiangnan07] 游戏滑窗均值的 decay 系数
DECLARE_SPDM_ABTEST_INT64(game_ecpc_threshold);   // [jiangnan07] 游戏滑窗均值的次数下限
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_native_ueq);  //  [zhangmengxin] 外循环直播时长计算 ueq
DECLARE_SPDM_ABTEST_DOUBLE(outer_live_native_ueq_weight);  // [zhangmengxin] 外循环直播时长 ueq 权重
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower);  // [dingyiming05] 计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper);  // [dingyiming05] 计费比上界
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower);  // [dingyiming05] 软广计费比下界
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper);  // [dingyiming05] 软广计费比上界
// [dingyiming05] 计费摸底实验
DECLARE_SPDM_ABTEST_BOOL(enable_rank_bs_outer_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_bs_outer_ratio_native);
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower_outer);
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper_outer);
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower_outer);
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper_outer);
DECLARE_SPDM_ABTEST_DOUBLE(native_bs_auto_bid_weight_outer);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_bs_exp);
DECLARE_SPDM_ABTEST_BOOL(disable_bs_outer_soft_photo);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_bs_exp_inner_roi);
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower_v2);
DECLARE_SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper_v2);
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower_v2);
DECLARE_SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper_v2);
DECLARE_SPDM_ABTEST_DOUBLE(separate_billing_auto_bid_weight_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_experience_thr);  // [dingyiming05] 体验因子门槛过滤
DECLARE_SPDM_ABTEST_DOUBLE(experience_thr);  // [dingyiming05] 体验因子门槛
DECLARE_SPDM_ABTEST_BOOL(enable_close_bonus);  // [dingyiming05] bonus 留反实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_close_bonus_mix_inner);  // [dingyiming05] 单列 bonus 留反实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_close_bonus_mix_outer);  // [dingyiming05] 单列 bonus 留反实验开关
DECLARE_SPDM_ABTEST_BOOL(disable_bonus_feed_explorer);
DECLARE_SPDM_ABTEST_BOOL(disable_bonus_inner_explorer);
DECLARE_SPDM_ABTEST_BOOL(disable_bonus_follow);
DECLARE_SPDM_ABTEST_BOOL(disable_bonus_all);
DECLARE_SPDM_ABTEST_BOOL(enable_reco_live_ue);  // [dingyiming05] reco 直播 ue 模型调用开关
DECLARE_SPDM_ABTEST_INT64(reco_live_ue_timeout);  // [dingyiming05] reco 直播 ue 模型调用耗时
DECLARE_SPDM_ABTEST_STRING(reco_live_ue_grpc_name);  // [dingyiming05] reco 直播 ue 模型调用 name
DECLARE_SPDM_ABTEST_BOOL(enable_constrain_high_quality_soft);  // [qiaolin] ue_score 开关
DECLARE_SPDM_ABTEST_BOOL(enable_constrain_soft);  // [qiaolin] ue_score 开关
DECLARE_SPDM_ABTEST_BOOL(enable_no_constrain);  // [qiaolin] ue_score 开关
DECLARE_SPDM_ABTEST_DOUBLE(high_ue_force_thr);  // [qiaolin] ue_score 探索门槛
DECLARE_SPDM_ABTEST_BOOL(enable_high_ue_explore_force);  // [qiaolin] ue_score 开关
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_force_series_id);

DECLARE_SPDM_ABTEST_BOOL(enable_outer_game_invoke_link);  // [lizhuo03] 外循环小游戏唤端链路实验开关
DECLARE_SPDM_ABTEST_BOOL(enable_invoke_splash_cross_auc);  // [zengdi] 外循环唤端开屏 使用旁路 auc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_playlet_purchase_ltv);  // [zhangzhao06] 外循环短剧 LTV 模型预估  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_button_click);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_button_click_test);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_playtime);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_lps_playtime);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_playtime_strategy);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_id_tail_number);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_live_ocpc_conf);
DECLARE_SPDM_ABTEST_STRING(outer_live_playtime_strategy_exp_name);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_wx);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_wx2);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_ad_live_no_simplified_live_room);

DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_lps_photo_to_live);   //  [zhaoqilong] 外循环直播 表单 短引 开关
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_live_lps_direct_live);   //  [zhaoqilong] 外循环直播 表单 直投 开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_u_cvr);  // [lizhuo03] 外循环统一 cvr 模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_u_cvr_bound);  // [lizhuo03] 外循环统一 cvr 模型 bound 开关
DECLARE_SPDM_ABTEST_DOUBLE(enable_outer_u_cvr_bound_ratio);  // [lizhuo03] 外循环统一 cvr 模型 bound 值
DECLARE_SPDM_ABTEST_BOOL(enable_outer_u_cvr_ensemble);  // [yujinbei] 外循环统一 cvr 模型 ensemble 开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_u_cvr_old_request_close);  // [yujinbei] 外循环统一 cvr 模型 老链路请求 开关   // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_explore_feed_soft_cpm_threshold);  //  [hehandong] 发现页双列软广 cpm 门槛开关
DECLARE_SPDM_ABTEST_BOOL(enable_explore_feed_soft_live_cpm_thr);  //  [hehandong] 发现页双列软广直播门槛开关
DECLARE_SPDM_ABTEST_BOOL(enable_explore_feed_live_cpm_thr);  //  [hehandong] 发现页双列直播 cpm 门槛开关
DECLARE_SPDM_ABTEST_STRING(spu_weight_exp_tag_for_splash_inner_ad);  //  [liubing05] 开屏 SPUID 实验 tag
DECLARE_SPDM_ABTEST_BOOL(enable_outer_u_ctr_soft_queue_cali);  // [zhouman] 统一 ctr 软广队列校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_u_ctr_hard_queue_cali);  // [zhouman] 统一 ctr 硬广队列校准开关
DECLARE_SPDM_ABTEST_DOUBLE(outer_u_ctr_soft_queue_cali_ratio);  // [zhouman] 统一 ctr 软广队列校准系数
DECLARE_SPDM_ABTEST_DOUBLE(outer_u_ctr_hard_queue_cali_ratio);  // [zhouman] 统一 ctr 硬广队列校准系数
DECLARE_SPDM_ABTEST_BOOL(enable_close_manual_calibration);  // [zhouman]
DECLARE_SPDM_ABTEST_STRING(big_promotion_gpm_hc_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_week_retention_ecpc);  //  [niehui] 七留双出价格次留 ecpc

DECLARE_SPDM_ABTEST_BOOL(enable_lps_cvr_soft_queue_cali);  // [tanyijia] 外循环表单 软广队列校准开关
DECLARE_SPDM_ABTEST_DOUBLE(lps_not_ecom_coeff_soft_col);  // [tanyijia] 外循环表单 软广队列校准系数



DECLARE_SPDM_ABTEST_BOOL(enable_print_factor_info);  //  [sunkang] 多因子框架白盒打点开关
DECLARE_SPDM_ABTEST_BOOL(enable_ecpc_whitelist_opt);  //  [sunkang]  ecpc 白名单前置优化
DECLARE_SPDM_ABTEST_BOOL(enable_soft_and_hard_pk);  //  [sunkang] 软硬 pk 开关
DECLARE_SPDM_ABTEST_INT64(soft_and_hard_pk_type);  //  [sunkang] 软硬广 pk 方式：1 rb 大小 2 cpm 大小
DECLARE_SPDM_ABTEST_BOOL(enable_ecpc_max_min);
DECLARE_SPDM_ABTEST_BOOL(fix_ecpc_max_min);  //  [dingyiming05] ecpc fix
DECLARE_SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_mix);  //  [dingyiming05] ecpc 公域单列调控系数
DECLARE_SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_jili);  //  [dingyiming05] ecpc 激励调控系数
DECLARE_SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_feed);  //  [dingyiming05] ecpc 双列调控系数
DECLARE_SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_follow);  //  [dingyiming05] ecpc 关注页调控系数
DECLARE_SPDM_ABTEST_BOOL(enable_update_rta_second_predict_source_type_list)  //  [sunkang] rta 二次请求控制配置化 // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_follow_fanstop_feed_model_sctr);
DECLARE_SPDM_ABTEST_BOOL(enable_follow_fanstop_thanos_model_sctr);
DECLARE_SPDM_ABTEST_BOOL(disable_follow_cost_ratio_account_black_list);
DECLARE_SPDM_ABTEST_BOOL(fix_follow_cost_ratio_account_black_list);
DECLARE_SPDM_ABTEST_STRING(new_mix_rank_bid_exp_tag);

DECLARE_SPDM_ABTEST_DOUBLE(search_adx_adjust_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_auc_sample_opt);
DECLARE_SPDM_ABTEST_BOOL(enable_product_ltr_write_samples);
DECLARE_SPDM_ABTEST_BOOL(enable_mix_unify_gpm_pass);   // [xiaoyuhao] 透传混排 gpm 字段
DECLARE_SPDM_ABTEST_BOOL(enable_rta_send_feature_id);   // [xiaoyuhao] Rta ad 层级下发 featureId
DECLARE_SPDM_ABTEST_INT64(search_item_card_bonus_combo_search);  // [weiyilong] 搜索无素材商品卡 bonus - 综搜
DECLARE_SPDM_ABTEST_INT64(search_item_card_bonus_goods_tab);  // [weiyilong] 搜索无素材商品卡 bonus - 垂搜
DECLARE_SPDM_ABTEST_BOOL(
    enable_combo_search_page0_kbox_item_boost);  // [weiyilong] 综搜首页 商品 kbox 队列生效 boost
DECLARE_SPDM_ABTEST_BOOL(enable_search_multi_logic_table);  // [weiyilong] 搜索分队列逻辑表
DECLARE_SPDM_ABTEST_BOOL(enable_search_posterior_async_fix);  // [weiyilong] 搜索后验异步接口修复
DECLARE_SPDM_ABTEST_BOOL(enable_search_strong_boost_plugin);  // [weiyilong] 搜索强卡 boost 单独 plugin
// [weiyilong] 外流使用上页 page_size 计算非首页的首坑
DECLARE_SPDM_ABTEST_BOOL(enable_search_last_page_size_for_first_pos_v2);
// [weiyilong] 内流使用上页 page_size 计算非首页的首坑
DECLARE_SPDM_ABTEST_BOOL(enable_search_last_page_size_for_first_pos);
// [weiyilong] 用于计算 bonus 的 CPM 含有 boost
DECLARE_SPDM_ABTEST_BOOL(enable_search_cpm_for_bonus_contain_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_search_cpc_boost_align);  // [weiyilong] CPC boost 逻辑对齐
DECLARE_SPDM_ABTEST_BOOL(enable_search_mcb_boost_align);  // [weiyilong] MCB boost 逻辑对齐
DECLARE_SPDM_ABTEST_DOUBLE(search_modify_ratio_lower_bound);  // [weiyilong] 搜索 modifyRatio 下界
DECLARE_SPDM_ABTEST_DOUBLE(search_modify_ratio_upper_bound);  // [weiyilong] 搜索 modifyRatio 上界
DECLARE_SPDM_ABTEST_DOUBLE(rank_benifit_strong_sctr_bound);  // [zhangchaoyi03] 强样式 sctr 上界
DECLARE_SPDM_ABTEST_BOOL(disable_search_boost_ctr);  // [weiyilong] 搜索 boost 不在 ctr 上生效
DECLARE_SPDM_ABTEST_BOOL(enable_search_regulate_boost_scope);  // [weiyilong] 搜索 boost 生效范围调整
// [weiyilong] 搜索分样式 modifyRatio 重构
DECLARE_SPDM_ABTEST_BOOL(enable_search_modify_ratio_boost_style_only);
DECLARE_SPDM_ABTEST_BOOL(enable_fanstop_dup_live_stream_id_fix);  // [weiyilong] 修复粉条队列合并后 live 去重
DECLARE_SPDM_ABTEST_BOOL(enable_search_first_page_relevance_filter);  // [weiyilong] 综搜首页双列相关性过滤
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_strong_card_choose_merge);  // [zhangchaoyi03] 动态强样式混合选择
DECLARE_SPDM_ABTEST_DOUBLE(search_first_page_photo_rele_thr);  // [weiyilong] 综搜首页双列相关性阈值 photo
DECLARE_SPDM_ABTEST_DOUBLE(search_first_page_live_rele_thr);  // [weiyilong] 综搜首页双列相关性阈值 live
DECLARE_SPDM_ABTEST_BOOL(disable_search_multi_list_inner_sort);  // [weiyilong] 搜索多队列选取 内流不 sort
DECLARE_SPDM_ABTEST_BOOL(enable_search_fanstop_bid_boost_guard);  // [weiyilong] 粉条 search_bid_boost 兜底
DECLARE_SPDM_ABTEST_DOUBLE(
    search_item_card_support_cpa_bid_ratio);  // [weiyilong] 搜索无素材商品卡换序扶持 cpa_bid 比较系数
DECLARE_SPDM_ABTEST_DOUBLE(
    search_item_card_support_mcb_cpa_bid_ratio);  // [weiyilong] 搜索无素材商品卡换序扶持 mcb 出价比较系数
// [zhengchaofan] 搜索商品卡、kbox CTR 置为 1
DECLARE_SPDM_ABTEST_BOOL(enable_search_prerank_hard_sample);  // [zhengchaofan] 搜索粗排精排不一致难样本采样
DECLARE_SPDM_ABTEST_BOOL(enable_search_goods_and_kbox_remove_ctr);
DECLARE_SPDM_ABTEST_BOOL(enable_splash_boost_coef_immg);  // [lizemin] 开屏 boost 迁图
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_ecpc);  // [rongyu03] 游戏重定向 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_ecpc_switch);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_multi_opt_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_multi_opt_boost_ltv1);
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_multi_opt_boost_nodiff);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_ratio_max);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_ratio_min);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_purchase_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(mini_game_retarget_conv_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(game_retarget_ecpc_boost_const);  // [rongyu03] 游戏重定向 ecpc 扶持参数
DECLARE_SPDM_ABTEST_DOUBLE(game_retarget_ecpc_discount_const);  // [rongyu03] 游戏重定向 ecpc 折扣参数
DECLARE_SPDM_ABTEST_DOUBLE(game_retarget_ecpc_avg_value_const);  // [rongyu03] 游戏重定向 ecpc 均值参数
DECLARE_SPDM_ABTEST_DOUBLE(game_retarget_ecpc_boost_max_ratio);  // [rongyu03] 游戏重定向 ecpc 上限参数
DECLARE_SPDM_ABTEST_DOUBLE(game_retarget_ecpc_discount_min_ratio);  // [rongyu03] 游戏重定向 ecpc 下限参数
DECLARE_SPDM_ABTEST_DOUBLE(uaa_ratio);  // [rongyu03] UAA bonus 系数
DECLARE_SPDM_ABTEST_DOUBLE(ual_ratio);  // [rongyu03] UAL bonus 系数
DECLARE_SPDM_ABTEST_BOOL(enable_game_retarget_ecpc_boost_multitag);
// [rongyu03] 游戏重定向 ecpc 通路粒度扶持开关
DECLARE_SPDM_ABTEST_DOUBLE(game_retarget_ecpc_boost_multitag_const);
// [zhengchaofan] 搜索短剧付费隔离信息流 cmd_key
DECLARE_SPDM_ABTEST_BOOL(enable_search_duanju_independent);

DECLARE_SPDM_ABTEST_BOOL(enable_search_sctr_all_pos);  // [zhengchaofan] 搜索广告分位置预估优化 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_sctr_all_pos_calc_benefit);  // [zhengchaofan] 搜索广告全位置预估，计算benefit适配 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_sctr_pos0);  // [zhengchaofan] 搜索广告全位置预估，强样式SCTR适配 //NOLINT
DECLARE_SPDM_ABTEST_STRING(search_diversion_bid_boost_exp);  // [zhaocuncheng]  搜索商业化导流来源调价配置
DECLARE_SPDM_ABTEST_BOOL(enable_search_pos_ctr_v1);  // [wangbin24] 搜索广告分位置预估 ctr 开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_goods_search_gmv_callback);  // [wangbin24] 垂搜 GMV 回捞开关 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_search_qcpx_live_uplift_model);  // [wangbin24] 搜素直播 qcpx uplit 模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_qcpx_rearward);  // [wangbin24] 搜素 qcpx 后移
DECLARE_SPDM_ABTEST_BOOL(disable_search_goods_live_ecpm);  // [wangbin24] 搜素商品 tab 关闭独立 ecpm
DECLARE_SPDM_ABTEST_BOOL(enable_search_goods_tab_fix_bug);
DECLARE_SPDM_ABTEST_BOOL(enable_goods_p2l_to_direct);  // [wangbin24] 搜素商品 tab 支持短引转直投
DECLARE_SPDM_ABTEST_BOOL(enable_search_inner_cid);  // [wangbin24] 搜索支持内循环 CID

DECLARE_SPDM_ABTEST_BOOL(enable_fix_ad_list_copy);  // [zhangguanglei] rank post proc 阶段 ad 复制问题修复
DECLARE_SPDM_ABTEST_BOOL(enable_revenue_optimise_switch);  // [nizhihao] 现金率 ecpc 开关
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_avoid_same_second_industry);  // [xiedong] 允许强出避让同行业
DECLARE_SPDM_ABTEST_BOOL(enable_force_reco_cpm_ratio_threshold);  // [xiedong] 允许强出门槛设置

DECLARE_SPDM_ABTEST_BOOL(enable_good_soft_ad_new_standard);  // [shoulifu03] 优质软广去标新标准
DECLARE_SPDM_ABTEST_BOOL(enable_good_native_photo_new_standard);  //  [shoulifu03]  优质原生素材新标准实验
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review);  // [yupeng05] 是否基于审核数据去软广标
// [liyichen05] playlet exp params
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_impression_discount_incentive);  // 喜番激励激励计费打折实验
DECLARE_SPDM_ABTEST_BOOL(enable_xifan_impression_discount_feed);  // 喜番激励信息流计费打折实验
DECLARE_SPDM_ABTEST_STRING(smart_offer_exp_tag);  // C 补的 rank 设置 从 front 迁移来的
DECLARE_SPDM_ABTEST_STRING(normal_smart_offer_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_baokuanju_dynamic_coef_adjustment);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_coef_adjustment_with_t12);
DECLARE_SPDM_ABTEST_BOOL(enable_dynamic_coef_adjustment_with_cvr);
DECLARE_SPDM_ABTEST_STRING(playlet_yali_cdx_1_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_ecpc_ratio_combine);  // [yangxuan06] 短剧重定向连乘开关
DECLARE_SPDM_ABTEST_DOUBLE(playlet_bonus_amount_ratio);
// [jiyang]  优质软广判定规则调整-小流量实验方案
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_old);  // 生效老规则
DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_new);  // 生效新规则
// [wanke] 下线请求 post-server 的 GetLiveNewTagData 接口, 已确认不再使用
DECLARE_SPDM_KCONF_BOOL(disableGetLiveNewTagFromPostServer);

DECLARE_SPDM_ABTEST_INT64(search_rank_max_rsp_size);  //  [danghaodong] 搜索精排返回广告数缩减

DECLARE_SPDM_ABTEST_BOOL(enable_search_celebrity_bonus_normal);  // [zhangchaoyi03] 普通队列直播大卡 bonus
DECLARE_SPDM_ABTEST_BOOL(enable_search_celebrity_bonus_authorname);  // [zhangchaoyi03] 普通队列直播大卡 bonus

DECLARE_SPDM_ABTEST_BOOL(enable_outer_context_aware);  // [zhengchaofan] 搜索外循环精排候选做特征

// [zhaoqilong] 行业 boost 策略
DECLARE_SPDM_ABTEST_BOOL(enable_outer_industry_boost_ranking)
DECLARE_SPDM_ABTEST_BOOL(enable_revenue_splash_modify)
DECLARE_SPDM_ABTEST_BOOL(enable_revenue_splash_sunset)
DECLARE_SPDM_ABTEST_STRING(outer_industry_boost_name)

DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_prerank_add_candidate_feature_v1);  // 直播直投粗排候选特征
// [yangxuan06] 短剧 ecpc
DECLARE_SPDM_ABTEST_DOUBLE(playlet_ecpc_lower_bound);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_ecpc_upper_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_reco_retarget);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_reco_retarget_not_theme);
DECLARE_SPDM_ABTEST_STRING(playlet_ecpc_exp_whitelist_tag);
DECLARE_SPDM_ABTEST_STRING(playlet_dynamic_adjust_exp_tag);  // [jiaqingrui]  剧名动态调价
DECLARE_SPDM_ABTEST_STRING(playlet_reco_retarget_exp_tag);  // [yushengkai]  自然重定向

// [jiangyuzhen03]
DECLARE_SPDM_ABTEST_BOOL(enable_unify_ecpm_bound);  // ecpm 统一 bound
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__1_next_stay_filter);  // 次留 drop 门槛
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__2_zhutui_thres);  // 品牌助推下界
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__3_juxing_thres);  // 聚星 ecpm 下界
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__4_native_ecpm_upper_bound);  // 软广分流量 ecpm 上限
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__5_follow_u4_zoom);  // 关注页 u4 提价
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__6_billing_separate);  // 排序计费分离
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__7_old_ecpm_ratio);  // 旧 ecpm_ratio 算子
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__8_cpm_origin);  // cpm_origin 相关字段
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__9_same_product_penalty);  // 爆品跟品打压
DECLARE_SPDM_ABTEST_BOOL(disable_ecpm_strategy__10_follow_charge_bonus);  // 关注页 bonus 计费
// [jiangyuzhen03] end

DECLARE_SPDM_ABTEST_BOOL(enable_soft_ad_clue_predict_exp);  // [zhaijianwei] 线索软广预估值修复开关
DECLARE_SPDM_ABTEST_BOOL(enable_native_new_key_compatible);  // [shoulifu03] 兼容上游原生审核新键位
DECLARE_SPDM_ABTEST_BOOL(enable_inner_live_follow_rm_invalid_request_2); // [zhangxin29] 开关-移除直播直投涨粉无效请求 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_feature_index_use_dragon_rpc);  // [chenchen13] rank 请求策略正排，切换到 dragon 接口  // NOLINT
DECLARE_SPDM_ABTEST_DOUBLE(key_action_ltv_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(key_action_ltv_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(key_action_ltv_ecpc_gamma);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_win_auction_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_ecpc_upper);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_ecpc_gamma);
DECLARE_SPDM_ABTEST_BOOL(enable_new_product_category_for_model);  // [liliangmin] 直播类目特征切换开关
DECLARE_SPDM_ABTEST_DOUBLE(key_action_ltv_win_auction_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_new_inspire_soft_calibration_roas);  // [cuirunpeng] 激励软广 ROAS 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_new_inspire_soft_calibration_order_paied);  // [cuirunpeng] 激励软广订单校准开关  // NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_check_cmd_conflict_before);    // [xiaoyuhao] cmd 冲突前置
DECLARE_SPDM_ABTEST_BOOL(enable_outer_prerank_add_carm_to_rank);  // [zhangzhen24] 粗排候选特征
DECLARE_SPDM_ABTEST_BOOL(enable_new_inspire_soft_calibration_storewide);  // [cuirunpeng] 激励软广全站校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_outer_prerank_add_feature_to_rank);  // [zhangzhen24] 粗排候选特征
DECLARE_SPDM_ABTEST_BOOL(enable_outer_prerank_add_feature_to_rank_v2);  // [zhangzhen24] 粗排候选特征
DECLARE_SPDM_ABTEST_BOOL(enable_outer_sample_remove_adx_exp);  // [zhangzhen24] 粗排候选特征
DECLARE_SPDM_ABTEST_BOOL(enable_no_ltv_bid_rank);  // [liyu26] 粗排隔离调价实验精排开关
DECLARE_SPDM_ABTEST_BOOL(enable_log_rank_benefit);  // [liyu26] 数据流落表 rank_benefit 开关
DECLARE_SPDM_ABTEST_BOOL(enable_wentou_gimbal_ratio_dynamic)  // [caijiawen] 稳投 gimbal 动态系数标志
DECLARE_SPDM_ABTEST_BOOL(enable_search_iaa_7_day_roas);  // [dangpingbo]  搜索 ROI IAA 7 day 实验开关 new
DECLARE_SPDM_ABTEST_BOOL(enable_search_pay_split_to_impression);  // [dpb] 搜索付费切分点前移
DECLARE_SPDM_ABTEST_BOOL(enable_search_next_day_rs_filter_v2);  // [dangpingbo] 搜索次留相关性干预
DECLARE_SPDM_ABTEST_BOOL(enable_search_outer_cmd_independent);  // [dangpingbo] 搜索外循环模型独立
DECLARE_SPDM_ABTEST_BOOL(enable_search_independent_industry_live);  // [wangbin24] 搜索独立行业直播链路总开关
DECLARE_SPDM_ABTEST_BOOL(enable_search_info_feature);   // [wangbin24] 增加从客户端参数 search info 提取的搜索特征  // NOLINT
// [liyuanqing]
DECLARE_SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict);
DECLARE_SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict_ltv_new_normal);
DECLARE_SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict_ltv_old);
DECLARE_SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict_account);
// [chenxian]
DECLARE_SPDM_ABTEST_BOOL(enable_stop_playlet_origin_industry_model);
DECLARE_SPDM_ABTEST_BOOL(disable_playlet_uplift_cvr_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_model_ensemble_v1);
DECLARE_SPDM_ABTEST_BOOL(disable_playlet_uplift_model_ensemble_v1);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_two_head_cbu_admit_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_c_subsidy_playlet_mid_model);  // cbu 复用中台 C 补联合建模短剧开关
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_use_mid_model);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_use_industry_model);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_industry_conv2ltv_model);

DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_update_unify_ltv_v1);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_update_unify_ltv_v2);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_uplift_admit_threshold_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_uplift_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_uplift_model_origin_ltv_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_uplift_model_uplift_ltv_ratio);
DECLARE_SPDM_ABTEST_STRING(C_activity);
DECLARE_SPDM_ABTEST_STRING(multi_smart_offer_gear_info);
DECLARE_SPDM_ABTEST_STRING(multi_smart_offer_gear_info_dnc);
DECLARE_SPDM_ABTEST_STRING(normal_multi_smart_offer_gear_info_dnc);
DECLARE_SPDM_ABTEST_STRING(multi_smart_offer_gear_info_doc);
DECLARE_SPDM_ABTEST_STRING(normal_multi_smart_offer_gear_info_doc);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_gear_info_by_user_type);
DECLARE_SPDM_ABTEST_STRING(multi_smart_offer_value_ratio);

DECLARE_SPDM_ABTEST_BOOL(enable_write_playlet_multi_smart_offer_2_context);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_uplift_model_origin_ltv_ratio_mcb);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_uplift_model_uplift_ltv_ratio_mcb);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_mcb_independent_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_model_use_one_head_cvr);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_model_use_one_head_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_uplift_model_stop_cbu_ecpc);
// [chenxian random cbu]
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_smart_offer_default_by_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_random_stop_playlet_smart_offer_by_rank);
DECLARE_SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_dnc);
DECLARE_SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_doc);
DECLARE_SPDM_ABTEST_BOOL(enable_random_playlet_smart_offer_by_rank);
DECLARE_SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_dnc);
DECLARE_SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_doc);
// [chenxian multi uplift]
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_cmd_h6);
DECLARE_SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_ltv_cmd_h6);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_cmd_h5);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_cmd_h4);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_cvr_cmd_h6);
DECLARE_SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_cvr_cmd_h6);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_cvr_cmd_h5);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_cvr_cmd_h4);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_model_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_model_rank);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_personal_offer_value);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_roi_threshold);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_origin_ltv_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ltv_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_dnc_ltv_ratio);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_doc_ltv_ratio);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_max_bonus);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_max_roi);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_update_unify_ltv);
DECLARE_SPDM_ABTEST_BOOL(enable_cartoon_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ensemble_weight);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_cartoon_ensemble_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_without_cbu);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_one_head_ltv_without_cbu);
DECLARE_SPDM_ABTEST_INT64(smart_offer_multi_uplift_dnc_max_dangwei_index);
DECLARE_SPDM_ABTEST_INT64(smart_offer_multi_uplift_doc_min_dangwei_index);
DECLARE_SPDM_ABTEST_INT64(smart_offer_multi_uplift_nouse_dangwei_index);
DECLARE_SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_guaranteed);
DECLARE_SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ltv_threshold);
// [chenxian] 短剧 iap 切分点
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iap_cut_point_to_click);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iap_cut_point_to_click_log);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iap_cvr_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_playlet_iap_add_noctcvr);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_rank_playlet_click_pay_purchase);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_click_pay_purchase_upper_bound);

// [liyongchang] 短剧 iaa 实时校准到全归因的变换系数
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_playlet_iaa_ltv_transform);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_iaa_ltv_transform_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(playlet_calibration_ratio_max);

DECLARE_SPDM_ABTEST_STRING(playlet_cbu_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_change_playlet_conversion_purchase_add);
// [cuirunpeng] 短视频内循环新校准开关及参数
DECLARE_SPDM_ABTEST_BOOL(enable_new_online_calibration_with_cmd_order_paied);
DECLARE_SPDM_ABTEST_BOOL(enable_new_inner_native_roas_calibration);
DECLARE_SPDM_ABTEST_BOOL(enable_new_calibration_params_native_roas);
DECLARE_SPDM_ABTEST_DOUBLE(native_roas_new_smart_bidding_min_rate);
DECLARE_SPDM_ABTEST_DOUBLE(native_roas_new_smart_bidding_max_rate);
DECLARE_SPDM_ABTEST_DOUBLE(native_roas_new_exception_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(native_roas_new_exception_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_new_inner_native_order_pay_calibration);
DECLARE_SPDM_ABTEST_BOOL(enable_roas_online_calibration_by_stage_one_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_native_roas_online_calibration_by_stage_one_cmd);
DECLARE_SPDM_ABTEST_BOOL(enable_tube_to_inner_loop_exp);  // [jiangyuzhen03] 短剧切内循环实验
DECLARE_SPDM_ABTEST_BOOL(enable_item_card_order_paied_cali);  // [lizixuan07] 商品卡校准
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_order_paied_cmd_for_mall);  // [lizixuan07] 商城商品卡独立精排
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_order_paied_cmd_for_zhuanqian);
DECLARE_SPDM_ABTEST_BOOL(enable_use_new_order_paied_cmd_for_buyer_home);  // [lizixuan07] 买首商品卡独立精排

// [zhaoyi13]
DECLARE_SPDM_ABTEST_BOOL(enable_ue_check_tag);  // ue 优质广告实验开关
DECLARE_SPDM_ABTEST_INT64(outer_live_start_ecpc_dft_req_threshold);
// 引入看评率
DECLARE_SPDM_ABTEST_BOOL(enable_log_reco_cestr);
DECLARE_SPDM_ABTEST_INT64(outer_reco_cestr_ecpc_dft_req_threshold);
DECLARE_SPDM_ABTEST_INT64(photo_comment_cnt_thr);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iaa_ipu_log);
DECLARE_SPDM_ABTEST_INT64(fiction_iaa_hc_ipu_thr);

// [cuirunpeng] 短视频全站推广校准开关及参数
DECLARE_SPDM_ABTEST_BOOL(enable_inner_storewide_cali_ad_queue_type);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_normal_storewide_calibration);
DECLARE_SPDM_ABTEST_DOUBLE(normal_storewide_smart_bidding_min_rate);
DECLARE_SPDM_ABTEST_DOUBLE(normal_storewide_smart_bidding_max_rate);
DECLARE_SPDM_ABTEST_DOUBLE(normal_storewide_exception_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(normal_storewide_exception_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_inner_native_storewide_calibration);
DECLARE_SPDM_ABTEST_DOUBLE(native_storewide_smart_bidding_min_rate);
DECLARE_SPDM_ABTEST_DOUBLE(native_storewide_smart_bidding_max_rate);
DECLARE_SPDM_ABTEST_DOUBLE(native_storewide_exception_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(native_storewide_exception_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enbale_splash_inner_live_roas_7days);  // [wangzixu05] 开屏直播 ROAS 预估 切换为三段之和 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_inner_explore_sim_rank);  // [duanxinning] 发现页内流精排相似性 cmd
DECLARE_SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore_new);  // [duanxinning] trigger hetu 类目落日志  //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_price_private_message);  // [zhaijianwei] 私信计费打折顶价开关
DECLARE_SPDM_ABTEST_STRING(private_message_support_decay_group);  // [zhaijianwei] 私信扶持 退坡
DECLARE_SPDM_ABTEST_BOOL(enable_white_list_map_for_ecpc);  // [yishijie] 综平 ecpc 策略跳过 rta 限制白名单
DECLARE_SPDM_ABTEST_BOOL(enable_white_list_map_for_rtabid);  // [yishijie] rta 直接出价提价操作
DECLARE_SPDM_ABTEST_DOUBLE(edu_lps_deep_ecpc_lower_bound);  // [linyuhao03] 教育深度 ecpc 下界
DECLARE_SPDM_ABTEST_DOUBLE(edu_lps_deep_ecpc_upper_bound);  // [linyuhao03] 教育深度 ecpc 上界
DECLARE_SPDM_ABTEST_DOUBLE(edu_lps_deep_ecpc_coef);  // [linyuhao03] 教育深度 ecpc 调控系数
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_ensemble_invoked);  // [linyuhao03] SDPA 统一模型唤端目标走融合 ensemble 预估 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_ensemble_conversion);  // [linyuhao03] SDPA 统一模型激活目标走融合 ensemble 预估 //NOLINT
DECLARE_SPDM_ABTEST_BOOL(enable_sdpa_purchase_to_zhongtai);  // [linyuhao03] SDPA 拉活付费切回中台付费模型 //NOLINT

DECLARE_SPDM_ABTEST_BOOL(disable_useless_calibration_strategy);  // [dingxiangkun] 下线无用校准策略

DECLARE_SPDM_ABTEST_BOOL(enable_merchant_photo_roas_cvr_auc_cross_test);  // [cuirunpeng] ROAS cvr 旁路 auc

// [zhangbuang] 外循环 本地线索-跑量扶持

DECLARE_SPDM_ABTEST_BOOL(disable_new_online_calibration_with_cmd_roas);  // [cuirunpeng] 短带 ROAS 离线校准新开关  // NOLINT
// [zhangyuemeng]
DECLARE_SPDM_ABTEST_BOOL(enable_lsp_storewide_skip_bs);
// [yuhanzhang]
DECLARE_SPDM_ABTEST_BOOL(enable_request_industry_game_iaa_ltv);  // [gaoyuan21] iaa 游戏行业 ltv 模型请求开关
DECLARE_SPDM_ABTEST_DOUBLE(industry_game_iaa_ltv_ensemble_weight);

DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_roi7_bid_control_type);
DECLARE_SPDM_ABTEST_INT64(game_iaa_roi7_account_mod);
DECLARE_SPDM_ABTEST_INT64(game_iaa_roi7_account_begin);
DECLARE_SPDM_ABTEST_STRING(minigame_bidding_strategy_type);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_roi_coef);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_roi_online_coef);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_pacing_coef);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_min_shallow_coef);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_min_deep_coef);
DECLARE_SPDM_ABTEST_STRING(game_iaa_rou7_dynamic_adjust_exp_tag);


DECLARE_SPDM_ABTEST_BOOL(enable_game_bidding_formula_new);
DECLARE_SPDM_ABTEST_DOUBLE(minigame_iaa_roi7_ratio_coef);
DECLARE_SPDM_ABTEST_BOOL(enable_request_industry_game_iaa_ltv7);
DECLARE_SPDM_ABTEST_BOOL(enable_minigame_iaa_roi7_test);
DECLARE_SPDM_ABTEST_STRING(minigame_roi7_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_roi7_all_product);
DECLARE_SPDM_ABTEST_BOOL(enable_request_industry_game_iaa_ltv7_all_product);
DECLARE_SPDM_ABTEST_BOOL(enable_white_acount_request_industry_game_iaa_ltv7);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_deep_avg_pacing_alpha);
DECLARE_SPDM_ABTEST_DOUBLE(game_iaa_deep_avg_pacing_beta);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_iaa_7day_roas_predict);



// [gaoyuan21] iaa 游戏行业 ltv 模型加权系数

// [gaoyuan21] iaa 游戏小账户 & 新账户免顶价 ecpc 系数


// [gaoyuan21] iaa 游戏小账户 & 新账户免顶价 ecpc 系数(免游戏)

// [gaoyuan21] iaa 游戏顶价取小账户、ecpc 最小值


// [gaoyuan21] iaa 顶价 hc 标签


DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_ltv_calibrate);
// [gaoyuan21] iaa 首 R ltv 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_7r_ltv_calibrate);
// [gaoyuan21] iaa 7R ltv 校准开关

DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_coin_data);
DECLARE_SPDM_ABTEST_BOOL(enable_kmini_game_iaa_old_version_ecpc);

DECLARE_SPDM_ABTEST_BOOL(enable_game_shoufa_pay_ecpc_boost);
DECLARE_SPDM_ABTEST_BOOL(enable_kminigame_product_cost_decay_ecpc);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_app_ecpc_lower);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_app_ecpc_upper);
DECLARE_SPDM_ABTEST_STRING(kminigame_app_ecpc_exp);
DECLARE_SPDM_ABTEST_BOOL(enable_game_iaa_ipu_log);

DECLARE_SPDM_ABTEST_BOOL(enable_game_shoufa_force_show);
DECLARE_SPDM_ABTEST_BOOL(enable_kminigame_app_avoid_black_account_reward_ecpc);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_industry_boost_ecpc_reserve_bid);
DECLARE_SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_reserve_bid);
DECLARE_SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_reserve_bid_coef);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_app_black_account_upper_bound);
DECLARE_SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_upper_bound);

DECLARE_SPDM_ABTEST_BOOL(enable_all_orientation_industry_boost);

DECLARE_SPDM_ABTEST_BOOL(enable_outer_minigame_explore_ecpc_ranking);
DECLARE_SPDM_ABTEST_STRING(outer_minigame_explore_ecpc_ranking_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_outer_game_explore_ecpc_reserve_bid);
DECLARE_SPDM_ABTEST_DOUBLE(outer_game_explore_ecpc_reserve_bid);
DECLARE_SPDM_ABTEST_DOUBLE(outer_game_explore_ecpc_upper_bound);

DECLARE_SPDM_ABTEST_BOOL(enable_kminigame_default_reward_ecpc);
DECLARE_SPDM_ABTEST_DOUBLE(kminigame_default_reward_ecpc_ratio);




// [yangzhao07] 首发游戏
DECLARE_SPDM_ABTEST_BOOL(enable_request_shoufa_game_ltv);  // 首发 ltv 模型请求开关
DECLARE_SPDM_ABTEST_BOOL(enable_request_shoufa_game_ltv_test);  // 大游戏模型请求开关(测试用)
DECLARE_SPDM_ABTEST_BOOL(enable_request_shoufa_game_ltv_hold);  // 首发 ltv 模型常驻流量
DECLARE_SPDM_ABTEST_DOUBLE(shoufa_game_ltv_ensemble_weight);  // 首发 ltv 模型调参参数
DECLARE_SPDM_ABTEST_BOOL(enable_revert_std_cvr_ltv_coef);  // [yangzhao07] 浅带深系数调权实验 revert 开关
DECLARE_SPDM_ABTEST_DOUBLE(game_iap_adjust_weight);  // 大 R 模型调参参数
DECLARE_SPDM_ABTEST_BOOL(enable_request_wx_minigame_ltv);  // 微小模型请求开关
DECLARE_SPDM_ABTEST_BOOL(enable_request_hardcore_game_ltv);  // 大游模型请求开关
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_iap_calibration_strategy);  // iap ltv 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_iaap_calibration_strategy);  // iaap ltv 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_iaap_calibration_strategy_v2);  // iaap ltv 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_mini_game_seven_day_iaap_calibration_strategy);  // 7 r ltv 校准开关
DECLARE_SPDM_ABTEST_BOOL(enable_game_industry_7r_model);  // 7r
DECLARE_SPDM_ABTEST_BOOL(enable_game_industry_7r_model_serve);  // 7r
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv_change_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv_luopan);

// [lianghaoqiang]
DECLARE_SPDM_ABTEST_BOOL(enable_poi_distance_adjust);
DECLARE_SPDM_ABTEST_DOUBLE(poi_distance_adjust_coef);
DECLARE_SPDM_ABTEST_DOUBLE(poi_distance_adjust_half);

// [luyuanquan]
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_item_imp_lps_message_local);
DECLARE_SPDM_ABTEST_BOOL(enable_ad_dsp_item_imp_lps_leadssubmit_local);

DECLARE_SPDM_ABTEST_BOOL(enable_kmini_game_iaa_new_version_ecpc);
DECLARE_SPDM_ABTEST_INT64(fiction_ecpc_impression_cnt_thresh);
DECLARE_SPDM_ABTEST_BOOL(enable_industry_fiction_iaa_ltv_ensemble);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iap_pay_ltv_multi_head);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iap_pay_ltv_multi_style_head);
DECLARE_SPDM_ABTEST_INT64(fiction_iap_uplift_k_formula_type);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_style_ltv_cal_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_bias);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iaa_ltv_ensemble_weight);
DECLARE_SPDM_KCONF_BOOL(enableReportFictionMultiHeadV2);
DECLARE_SPDM_ABTEST_BOOL(enable_offline_fiction_subsidy_uplift_model);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_reverse_unify_ltv_cal_weight);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_reverse_unify_ltv_cal_bias);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iap_price_multi_head_v2);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_default_max_real_pay_price);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_weight_v2);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_bias_v2);
DECLARE_SPDM_ABTEST_INT64(fcition_iap_uplift_price_formula_type);
DECLARE_SPDM_ABTEST_INT64(fiction_iap_nc_ecpc_impression_cnt_thresh);

DECLARE_SPDM_ABTEST_BOOL(fiction_retarget_subsidy_discount);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_conv_ltv_ensemble_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_conv_ltv_model);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_pay_ltv_model);
DECLARE_SPDM_ABTEST_BOOL(fix_fiction_uplift_model_combine);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_na_fiction_admit);
DECLARE_SPDM_ABTEST_BOOL(enable_industry_ecpc_decrease);
DECLARE_SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_lower_bound);
DECLARE_SPDM_ABTEST_BOOL(enable_orientation_priority);
DECLARE_SPDM_ABTEST_DOUBLE(fiction_iaa_unify_ltv_cal_weight);
DECLARE_SPDM_ABTEST_BOOL(enable_fiction_iaa_ltv_weight);

// [sunzhipeng03]
DECLARE_SPDM_ABTEST_BOOL(enable_fill_short_video_cvr_label_info_attr);  // 短带第一段 unfiy cvr 填充

// [xiatian06] 直播精排 uescore 的落盘开关
DECLARE_SPDM_ABTEST_BOOL(enable_live_frrank_uescore_fill_context_stop);
// [guohongkuan] 请求精排 ue score
DECLARE_SPDM_ABTEST_BOOL(enable_reco_frrank_ue);  // 请求精排模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_reco_live_frue);  // 请求直播精排 uescore 模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_reco_frrank_ue_outside);  // 外循环请求精排模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_reco_frrank_ue_outerprm);  // 外循环请求私信精排模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_reco_frrank_ue_outerprm_normal);  // 外循环请求私信精排模型开关
// 精选  声明一个名为 SPDM_gamora_reco_frrank_xtr_grpc_name 的 Abtest string 类型参数
DECLARE_SPDM_ABTEST_STRING(gamora_reco_frrank_xtr_grpc_name);
// 极速版  声明一个名为 SPDM_nebula_reco_frrank_xtr_grpc_name 的 Abtest string 类型参数
DECLARE_SPDM_ABTEST_STRING(nebula_reco_frrank_xtr_grpc_name);
// 外循环极速版  声明一个名为 SPDM_nebula_reco_frrank_xtr_grpc_name 的 Abtest string 类型参数
DECLARE_SPDM_ABTEST_STRING(gamora_reco_sv_frrank_xtr_grpc_name);
DECLARE_SPDM_ABTEST_STRING(nebula_reco_sv_frrank_xtr_grpc_name);
// [yuanyue03] 外循环 ue 开关 拆 handler 版
DECLARE_SPDM_ABTEST_BOOL(enable_outerctr_reco_frrank_ue);  // 外循环 ctr 请求精排模型开关
DECLARE_SPDM_ABTEST_STRING(gamora_reco_outerctr_frrank_xtr_grpc_name);  // 外循环精排模型名
DECLARE_SPDM_ABTEST_STRING(nebula_reco_outerctr_frrank_xtr_grpc_name);  // 外循环精排模型名

// [libingjie03] uax 表单 & 私信链路优选探索策略开关
DECLARE_SPDM_ABTEST_BOOL(enable_form_pm_integration_explore);
DECLARE_SPDM_ABTEST_BOOL(enable_form_pm_integration);
DECLARE_SPDM_ABTEST_DOUBLE(fixed_form_pm_explore_rate);
DECLARE_SPDM_ABTEST_BOOL(enable_adjust_admit_integration);
DECLARE_SPDM_ABTEST_BOOL(enable_qiwei_admit_integration);
DECLARE_SPDM_ABTEST_DOUBLE(form_pm_integration_explore_rate1);
DECLARE_SPDM_ABTEST_DOUBLE(form_pm_integration_explore_rate2);
DECLARE_SPDM_ABTEST_DOUBLE(form_pm_integration_explore_rate3);
DECLARE_SPDM_ABTEST_BOOL(disable_inner_native_roas_calibration);  // 关闭 ROAS 软广校准开关

DECLARE_SPDM_ABTEST_INT64(rank_cache_min_quota);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_cache_add_log);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_score_cache);
DECLARE_SPDM_ABTEST_STRING(rank_score_cache_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_rank_score_cache_add_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_adlist_cache_inner_soft);
DECLARE_SPDM_ABTEST_BOOL(enable_adlist_cache_decice_by_corpus);
DECLARE_SPDM_ABTEST_STRING(rank_cache_hit_ratio_adjust_exp_tag);
DECLARE_SPDM_ABTEST_STRING(rank_cache_cpm_ttl_bucket_exp_tag);
DECLARE_SPDM_ABTEST_STRING(adlist_cache_quota_adjust_exp_tag);
DECLARE_SPDM_ABTEST_BOOL(enable_fix_cache_size_adjust);

// [dingyiming05] 精排缓存用户价值配置
DECLARE_SPDM_ABTEST_INT64(rank_cache_cpm_topk);

// [menjunyi]
DECLARE_SPDM_ABTEST_BOOL(rta_second_request_add_paid2);
DECLARE_SPDM_KCONF_BOOL(enableDeviceSupportTaid);

DECLARE_SPDM_ABTEST_BOOL(enable_discount_effect_exp);  // [jiangyuzhen03] 计费打折关闭实验

// 搜索 QCPX 相关参数
DECLARE_SPDM_ABTEST_BOOL(enable_search_qcpx_effect);

DECLARE_SPDM_ABTEST_DOUBLE(max_qpon_uplift_cxr_ratio);  // [jiangyuzhen03] qcpx uplift ratio 上限兜底倍数

// ICA 开关
DECLARE_SPDM_ABTEST_BOOL(enable_prerank_rank_ica_live);
DECLARE_SPDM_ABTEST_DOUBLE(ica_rank_input_live_quota_ratio);

// predict cache
DECLARE_SPDM_ABTEST_BOOL(enable_cache_ue_photo_predict);
DECLARE_SPDM_ABTEST_BOOL(enable_cache_ue_p2l_predict);
DECLARE_SPDM_ABTEST_BOOL(enable_skip_ue_cache_key);
DECLARE_SPDM_ABTEST_INT64(ue_cache_photo_predict_ttl);
DECLARE_SPDM_ABTEST_INT64(ue_cache_p2l_predict_ttl);

// [suntianyu06] 订单模型请求 ue score
DECLARE_SPDM_ABTEST_BOOL(enable_inner_order_reco_frrank_ue);  // 请求精排模型开关
DECLARE_SPDM_ABTEST_BOOL(enable_inner_order_sv_reco_frrank_ue);  // 请求精排模型开关

DECLARE_SPDM_ABTEST_BOOL(enable_trace_migration);  // [jiangyuzhen03] 后移 tracelog 发送逻辑

DECLARE_SPDM_ABTEST_INT64(cid_max_item_ranking_list_idx);          // cid 外循环参竞精排列表选取的最大个数
DECLARE_SPDM_ABTEST_INT64(cid_max_item_ranking_list_size);         // cid 外循环参竞精排列表缓存最大长度
// cid 外循环 dup_photo_id 精排打分最大个数
DECLARE_SPDM_ABTEST_INT64(cid_max_dup_photo_id_ranking_score_idx);
// cid 外循环 dup_photo_id 精排打分最大长度
DECLARE_SPDM_ABTEST_INT64(cid_max_dup_photo_id_ranking_score_size);

DECLARE_SPDM_ABTEST_INT64(cid_account_max_item_ranking_list_idx);
DECLARE_SPDM_ABTEST_INT64(cid_account_max_item_ranking_list_size);

// [zhangpuyang]
DECLARE_SPDM_KCONF_BOOL(enable_unit_id_in_second_bid_request);  // 二次请求增加 unit_id
DECLARE_SPDM_ABTEST_BOOL(enable_fill_bid_move_back);  // fill_bid 逻辑后移
DECLARE_SPDM_KCONF_BOOL(enable_CalcOriginPrice_Compute_Dot_in_preOnline);  // 打点在 pre_online 环境生效


// [cuirunpeng]
DECLARE_SPDM_ABTEST_BOOL(enable_inner_merchant_roas_param_tuning);  // 内循环短带 roas 两段模型参数联调开关

// [shizujun]
DECLARE_SPDM_ABTEST_BOOL(enable_discount_roi_opt);  // 打折优化开关
DECLARE_SPDM_ABTEST_BOOL(enable_discount_roi_opt_author);  // 打折优化加行业开关
DECLARE_SPDM_ABTEST_BOOL(enable_discount_roi_opt_bid);  // 打折优化提价改进开关
DECLARE_SPDM_ABTEST_INT64(discount_roi_opt_quartile);  // 分位数
DECLARE_SPDM_ABTEST_DOUBLE(makeup_roi_opt_ratio);  // 加价系数

// [liyitian]
DECLARE_SPDM_ABTEST_BOOL(enable_outer_ecpc_close);  // ecpc 关闭实验
}  // namespace ad_rank
}  // namespace ks
