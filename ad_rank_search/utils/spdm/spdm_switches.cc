#include "teams/ad/ad_rank_search/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"
namespace ks {
namespace ad_rank {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, ks::AbtestBiz);

// 以下为 kconf 开关定义.
SPDM_KCONF_BOOL(ad.adRank, trans_corporation_name);
SPDM_KCONF_BOOL(ad.adRank, enableRewardedConversionCalibrate);
SPDM_KCONF_BOOL(ad.adRank, enableDedupCidInList);

SPDM_KCONF_BOOL(ad.adRank, enableDupFactorCheck);  // [zhoushuaiyin] 重复创意检测打点
SPDM_KCONF_BOOL(ad.adRank, enableHiddenCostRecord);
SPDM_KCONF_BOOL(ad.adRank, enableAggrFailReasonRecord);
SPDM_KCONF_BOOL(ad.adRank, enableNodeTimeCost);
SPDM_KCONF_BOOL(ad.adRank, enablePidServiceBonusConf);
SPDM_KCONF_BOOL(ad.adRank2, enablePlayetBoostKconfReplace);
SPDM_KCONF_BOOL(ad.adRank2, enableFixIndexDefaultValue);
SPDM_KCONF_BOOL(ad.adRank3, enableSwitchCartoonKconf);
SPDM_KCONF_BOOL(ad.adRank2, enableMoveDot);
SPDM_KCONF_BOOL(ad.adRank2, enableFillAggBiddingRelease);
SPDM_KCONF_BOOL(ad.adRank2, enableRecoRetargetLog2);
SPDM_KCONF_BOOL(ad.adFrontDiffSwitches, enableCmdKeyRecord);
SPDM_KCONF_BOOL(ad.adRank, enablePredictScoreStandby);
SPDM_KCONF_BOOL(ad.adRank2, enableHardFastopNoLive);
SPDM_KCONF_BOOL(ad.adRank, enablePecMergeStyle);
SPDM_KCONF_BOOL(ad.adRank, disableGetLiveNewTagFromPostServer);
SPDM_KCONF_BOOL(ad.adRank, disableAllPerf);
SPDM_KCONF_BOOL(ad.adRank3, disableBgTaskStop);
SPDM_KCONF_BOOL(ad.adRank3, enableFillAuthorForPredict);
SPDM_KCONF_BOOL(ad.adRank3, enableReportFictionMultiHeadV2);
SPDM_KCONF_BOOL(ad.adRank3, searchFeatureIndexFillRatePerf);

SPDM_KCONF_BOOL(ad.adRank, enableSearchLiveSingleBoost);
SPDM_KCONF_BOOL(ad.adRank2, enableSearchLiveAccountBoost);
SPDM_KCONF_BOOL(ad.adRank, enableSearchDiversionSourceBoost);
SPDM_KCONF_BOOL(ad.adRank, enableEcpcConvPredict);
SPDM_KCONF_BOOL(ad.adRank2, enableDiffCompareAd);
SPDM_KCONF_BOOL(ad.adRank2, enableReqIndexUsingNewConfig);
SPDM_KCONF_BOOL(ad.adRank2, enableReqIndexByTail);
SPDM_KCONF_BOOL(ad.adRank2, enableFixItemAttrDiff);
SPDM_KCONF_BOOL(ad.adRank2, enableFixItemAttrDiffV2);

SPDM_KCONF_BOOL(ad.adRank2, enableFeedAdxServerClientCtrDefault);
SPDM_KCONF_BOOL(ad.adRank2, enableNewItemKey);
SPDM_KCONF_BOOL(ad.adRank2, enableRankBiznameControl);
SPDM_KCONF_BOOL(ad.adRank2, enableSearchNativeStrictStatus);
SPDM_KCONF_BOOL(ad.adRank2, enableDSLClearUnloginUserId);
SPDM_KCONF_BOOL(ad.adRank2, enablePostSeparateDataProcCb);
SPDM_KCONF_BOOL(ad.adRank2, enableSearchCommonAttr);
SPDM_KCONF_BOOL(ad.adRank2, enableSearchInterveneSortFix);
SPDM_KCONF_BOOL(ad.adRank2, enableInnerStreamPosDiscountConfig);
SPDM_KCONF_BOOL(ad.adRank2, enablePrintEcpcRatio);
SPDM_KCONF_BOOL(ad.adRank2, enableFixRequestCmdSeq);
SPDM_KCONF_BOOL(ad.adRank2, enableCheckCmdConflictBefore);
SPDM_KCONF_BOOL(ad.adRank2, enablePredictValueWatcher);
SPDM_KCONF_BOOL(ad.adRank2, enableRankSearchCrossFeature);
SPDM_KCONF_BOOL(ad.adRank2, enableRankSplashGenAck);
SPDM_KCONF_BOOL(ad.adRank2, enableSplashRankResultAdQueueType);
SPDM_KCONF_BOOL(ad.adRank2, enableT7ROISkipBillingSeparate);
SPDM_KCONF_BOOL(ad.adRank2, enableNewProductCategoryForModel);
SPDM_KCONF_BOOL(ad.adRank2, enableItemCardAdmitFix);
SPDM_KCONF_BOOL(ad.adRank2, enableLiveCandidateSkipCheck);
SPDM_KCONF_BOOL(ad.adRank2, enableMovePredictFeature);
SPDM_KCONF_BOOL(ad.adRank2, enableRtaSendFeatureId);
SPDM_KCONF_BOOL(ad.adRank2, enableOuterNativePhotoDcafSample);
SPDM_KCONF_BOOL(ad.adRank2, switchCommonAttrOfFanstopSessionData);
SPDM_KCONF_BOOL(ad.adRank, jixuMixRank);
SPDM_KCONF_BOOL(ad.adRank, enableTransferInnerLiveRoas);  //  [xiaoyuchao] 直播 roi 模型 pgmv 值后传开关
SPDM_KCONF_BOOL(ad.adRank, enable_reco_frrank_ue_kconf);  // [guohongkuan]  请求精排 uescore 的 kconf 开关定义
SPDM_KCONF_BOOL(ad.adRank3, enableMaxQponUpliftCxrRatio);
SPDM_KCONF_BOOL(ad.adRank3, enableBrandFanstopCvrPerf);  // [jiayalong] 品牌助推异常转化率打点
SPDM_KCONF_BOOL(ad.adRank3, enableSearchRankBuildPredictReqNew);
SPDM_KCONF_BOOL(ad.target_search, enableNotFirstScreenFilter);
// ------------------------------ kconf int32 参数定义 ---------------------------------
SPDM_KCONF_INT32(reco.inner_fanstop, archimedes_max_cache_num, 3);
SPDM_KCONF_INT32(ad.adRank, max_author_cnt_for_splash, 200);
SPDM_KCONF_INT32(ad.adRank, adxForcePageInterval, 1);
SPDM_KCONF_INT32(ad.adRank, universeLogInfoFreq, 100);
SPDM_KCONF_INT32(ad.adRank, check_time_interval_for_splash_for_splash, 7200);
SPDM_KCONF_INT32(ad.adRank, perfNodeCostLatency, 1000);
SPDM_KCONF_INT32(ad.frontserver, outlierWatcherSampleInterval, 10);
SPDM_KCONF_INT32(ad.adRank, switchPosManagerInit, 0);
SPDM_KCONF_INT32(ad.adRank, reco_frrank_ue_shard_num, 128);  // [guohongkuan]  请求 uescore 的 shard_num
SPDM_KCONF_INT32(ad.adRank, reco_frrank_ue_time_out, 100);  // [guohongkuan]  请求 uescore 的 time_out
SPDM_KCONF_INT32(ad.adRank, second_stage_predict_time_out, 100);  // [guohongkuan]  二阶段请求 rokect 模型的 time_out // NOLINT
SPDM_KCONF_STRING(ad.adRank, reco_live_ue_kess_name, "")  // [guohongkuan]  请求 直播 uescore 的 kess_name

// ------------------------------ kconf int64 参数定义 ---------------------------------

// ------------------------------ kconf double 参数定义 ---------------------------------
SPDM_KCONF_DOUBLE(ad.adserver, fanstopCpmThreshold, 6.0);
SPDM_KCONF_DOUBLE(reco.inner_fanstop, archimedesOcpmMaxEcpm, 10000);
SPDM_KCONF_DOUBLE(ad.adRank2, adxFeedServerClientCtrDefaultRatio, 0.5);
SPDM_KCONF_DOUBLE(ad.adRank3, cplMinCostRatio, 0.8);
SPDM_KCONF_DOUBLE(ad.adRank, mixBidUpliftThr, -100.0);

SPDM_KCONF_BOOL(ad.adRank3, enableSecondStagePredictV2);
SPDM_KCONF_BOOL(ad.adRank2, rankKconfDebugFlag);
SPDM_KCONF_BOOL(ad.adRank3, enableSendOriginalUnifyCxr);
SPDM_ABTEST_BOOL(enable_second_stage_predict, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_INT64(ad.adRank3, rankDirectPredictFlag, 0);
SPDM_ABTEST_INT64(rank_direct_predict_flag, ks::AbtestBiz::AD_DSP, 0);
SPDM_KCONF_BOOL(ad.adRank3, disableParseTimeoutResponse);
SPDM_ABTEST_STRING(rank_direct_predict_cmdkey_group, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_outer_matrix_unlogin, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adRank3, enableOuterMatrixUnlogin);
SPDM_ABTEST_BOOL(enable_skip_cmd_conflict_item, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(rank_exp_cost_time_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_trans_predict_meta_to_pack, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adRank3, enableFixDupPhotoId);
SPDM_ABTEST_BOOL(enable_copy_embedding_attr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_clean_inefficient_cmdkey, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_clean_hidden_cost, ks::AbtestBiz::AD_DSP);  // [gaokaiming]

SPDM_KCONF_DOUBLE(ad.adRank, livePosNReleThreshold, 1.8);  // [gaokaiming]
SPDM_KCONF_DOUBLE(ad.adRank, livePosOneCTCVRRatio, 0.6);   // [gaokaiming]
SPDM_KCONF_DOUBLE(ad.adRank, livePosTwoCTCVRRatio, 0.6);   // [gaokaiming]
SPDM_KCONF_DOUBLE(ad.adRank, livePosFiveCTCVRRatio, 0.6);  // [gaokaiming]

SPDM_ABTEST_INT64(search_linear_program_cpm_threshold, ks::AbtestBiz::AD_DSP, 1000000);    // [gaokaiming]

SPDM_ABTEST_BOOL(enable_force_ocpx_name, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_search_discount, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(search_ads_top_position_relevance_threshold_for_adx,
                   ks::AbtestBiz::AD_DSP, 1.8);  // [gaokaiming]
SPDM_ABTEST_DOUBLE(search_ads_mid_position_relevance_threshold_for_adx,
                   ks::AbtestBiz::AD_DSP, 1.0);  // [gaokaiming]
SPDM_ABTEST_DOUBLE(search_ads_all_position_relevance_threshold_for_adx,
                   ks::AbtestBiz::AD_DSP, 0.0);  // [gaokaiming]

SPDM_ABTEST_DOUBLE(search_ads_top_position_relevance_threshold_ratio_for_atlas,
                   ks::AbtestBiz::AD_DSP, 1.0);  // [gaokaiming]

SPDM_ABTEST_DOUBLE(search_ads_top_position_relevance_threshold_ratio_for_native,
                   ks::AbtestBiz::AD_DSP, 1.0);  // [gaokaiming]

SPDM_KCONF_DOUBLE(ad.adRank, searchRelevanceBadcaseConstraint, 0.03);            // [gaokaiming]
SPDM_KCONF_DOUBLE(ad.adRank, searchRelevanceLinearProgramLambda, 170000000);     // [gaokaiming]
SPDM_KCONF_DOUBLE(ad.adRank, searchRelevanceTopPhotoThreshold, 1.6);             // [gaokaiming]

SPDM_ABTEST_STRING(search_ads_age_cpm_threshold, ks::AbtestBiz::AD_DSP, "base");    // [gaokaiming]

SPDM_KCONF_STRING(ad.adRank, searchAdxDebugQuery, "iphone")                         // [gaokaiming]
SPDM_KCONF_DOUBLE(ad.adRank, searchRankHcMaxScore, 300000000);     // [dangpingbo]

// ------------------------------ kconf string 参数定义 ---------------------------------
SPDM_KCONF_STRING(ad.adRank, nebula_frrank_xtr_grpc_name_outerctr, "grpc_mix_ue_score")  // [yuanyue03]
SPDM_KCONF_STRING(ad.adRank, gamora_frrank_xtr_grpc_name_outerctr, "grpc_mix_ue_score")  // [yuanyue03]

// 以下为 abtest 开关定义.
SPDM_ABTEST_BOOL(drop_down_invert_ab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(game_similar_product_exp, ks::AbtestBiz::AD_DSP, "exp1");
SPDM_ABTEST_STRING(soft_hard_union_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_fill_item_attr_by_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_req_index_using_new_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_inner_loop_item_attr_by_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_not_fill_item_attr_from_target, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_bonus_diff_compare, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_auc_sample_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pid_service_ee_seach_budget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_duanju_mcb_roi_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enhance_force_reco, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tube_inner_loop_by_campaign_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_loop_candidate_sample, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sample_outer_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sample_outer_soft_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_bid_server_group_tag_fix2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_coin_set_fix_num, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fix_target_roi, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_gross_fix_with_deep_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_scsr_inspire, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_coin_multi_key, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_twin_game_ad_mcb_node, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_roas_twin_game_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_long_ratio_roas_twin_game_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_expore_auto_param, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bonus_pid_service, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_retention_retarget_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_click_after_recommend, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(close_billing_separate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(gfp_skip_owe_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(product_ecpc_exp, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(reward_game_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(reward_game_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kminigame_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kminigame_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kminigame_inner_live_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kminigame_inner_live_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kminigame_inner_live_ecpc_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(reward_game_ecpc_exp, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(ind_credit_roi_exp_id, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_xifan_impression_discount_feed, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_impression_discount_incentive, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_BOOL(enable_xifan_impression_price_rank, ks::AbtestBiz::XIFAN_PLAY_APPS);
SPDM_ABTEST_DOUBLE(wegame_returntime_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(wegame_returntime_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_cid_ad_bid_mcb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kgame_duration_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kai_game_iaap_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_conversion_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(kgame_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kgame_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kgame_duration_time_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(kgame_duration_time_upper, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(kgame_duration_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_conversion_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_pay_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// [linwei06]
SPDM_ABTEST_BOOL(close_c1_order_paid_search_goods, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(smart_offer_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(normal_smart_offer_exp_tag, ks::AbtestBiz::AD_DSP, "base");
// [jiyang]
SPDM_ABTEST_BOOL(enable_photo_pvtr_filter_sample, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(live_bigr_lift_hidden_cost_exptag, ks::AbtestBiz::AD_DSP, "");
// [nizhihao begin]
SPDM_ABTEST_BOOL(enable_divide_flow_explore_page, ks::AbtestBiz::AD_DSP);  // [nizhihao] 发现页页面拆分 门槛
SPDM_ABTEST_DOUBLE(cpm_threshold_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_close_ecpc_max_min, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_ecpc_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_ecpc_mix_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(close_ecpc_mix_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecpm_threshold_explore_inner, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(amd_live_ecpm_threshold_explore_inner, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(inner_account_suppress_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_adload_sample_collect, ks::AbtestBiz::AD_DSP);  // [nizhihao] adload 样本采集开关
SPDM_ABTEST_BOOL(enable_inner_account_suppress, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_account_suppress_whitelist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_account_adjust_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_account_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_account_cpm_upper, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_account_ecpm_upper_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_account_max_ecpm_yuan, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_inner_account_holdout_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_use_min_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dcaf_sample_delete_native_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_account_cpm_upper, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_BOOL(enable_model_based_adload_control, ks::AbtestBiz::AD_DSP);  // [nizhihao] model based adload
SPDM_ABTEST_DOUBLE(unify_server_show_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_gyl_server_show_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyl_cpm_remove_ctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_server_show_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(nebula_unify_server_show_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_pay_mode_account_cpm_thr, ks::AbtestBiz::AD_DSP);  // [nizhihao] 虚拟金账户开关
SPDM_ABTEST_BOOL(enable_big_promotion_support_by_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lt_experience, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cpm_thr_ratio_adjust_for_adload, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(lt_experience_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(lt_experience_cpm_ratio_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adload_cpm_thr_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adload_cpm_thr_ratio_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_bid_uplift_strategy_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_uplift_score_calc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(big_promotion_support_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_revenue_optimise_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_auto_param_admit_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(game_appoint_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_INT64(live_vtr_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_game_retarget_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_rta_cpm_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(game_shoufa_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_wechat_game_rnd_explore_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_subsidy_log_record, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_different_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_retarget_purchase_ratio_for_iaa, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mini_game_retarget_purchase_ratio_for_iap, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mini_game_retarget_conv_ratio_for_iaa, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mini_game_retarget_conv_ratio_for_iap, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(no_subsidy_calibrate_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(subsidy_calibrate_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mini_game_reset_subsidy_unify_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_reset_subsidy_unify_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outer_hc_allocate_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_c_subsidy_game_predict_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_novel_predict_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_reset_subsidy_unify_ltv_iaap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(iaap_iaa_ltv_bagging_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mini_game_playtimes_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(mini_game_playtimes_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_mini_game_billing_seperate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(iap_game_twin_bid_group_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_iap_7d_value_allocate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_twin_bid_divide_ocpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(iap_twin_bid_default_long_value_iap_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iap_twin_bid_default_long_value_iaap_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iap_twin_bid_default_long_value_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iap_game_long_value_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iap_game_long_value_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iap_twin_bid_decay_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(iap_twin_bid_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_iap_twin_bid_unify_post_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_moving_iap_game_long_value_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_iap_7d_value_twin_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_7d_value_twin_bid_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_7r_allocate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(final_iap_twin_bid_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(final_iap_twin_bid_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(final_iap_twin_bid_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_iap_billing_seperate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_billing_seperate_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iap_billing_seperate_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_iaap_player_only_first_charge_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_iaap_lower_bound, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_iaap_upper_bound, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(big_r_explore_factor, ks::AbtestBiz::AD_DSP, 1.5);
SPDM_ABTEST_INT64(big_r_explore_ad_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv7, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_7d_value_allocate_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_7d_use_auto_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(iap_long_value_target_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(final_iap_7r_boost_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mini_game_request_new_7r, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaap_7r_request_iaa_ltv7, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iap_payment_window_30d, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_use_long_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_iap_payment_constraint, ks::AbtestBiz::AD_DSP, 180.0);
SPDM_ABTEST_DOUBLE(default_iap_long_value_constraint, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_mini_game_fill_iaa_ltv7_field, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_params_parse_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_mini_game_big_r_strategy_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_30d, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_ee_strategy_by_24h, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_24h_thrd, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(mini_game_big_r_ee_strategy_30d_thrd, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_explore_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_uv_monitor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv_admit_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_game_fill_industry_7r, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_game_add_sdk_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(long_value_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_mini_game_7r_fill_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_fill_7r_judge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_big_game_7r_admit_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_mini_game_7r_fill_admit_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_user_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_big_r_explore_end, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_reward_skip_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_big_r_update_rank_constraint, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_iap_payment_constraint_rank, ks::AbtestBiz::AD_DSP, 180.0);
SPDM_ABTEST_BOOL(enable_big_r_monitor_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_conv_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_fiction_conv_ltv_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_pay_ltv_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_fiction_uplift_model_combine, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_na_fiction_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_ecpc_decrease, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_orientation_priority, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_default, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w1, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w3, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w4, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_user_tag_coef_w5, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_uplift_score_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(explore_inner_uplift_score_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w1, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w3, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w4, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_w5, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_inner_user_value_coef_default, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(pay_mode_account_cpm_thr, ks::AbtestBiz::AD_DSP, 50.0);
SPDM_ABTEST_INT64(pay_mode_tag, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_inner_account_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_account_holdout_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(adload_sample_group_tag, ks::AbtestBiz::AD_DSP, "exp1");
SPDM_ABTEST_INT64(sensitive_user_adload_tag, ks::AbtestBiz::AD_DSP, 16);
SPDM_ABTEST_INT64(adload_sample_pv_bucket, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(drop_down_invert_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_account_id_cpm_thr_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(model_based_adload_naive_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(model_based_adload_industry_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(model_based_adload_user_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(playlet_ltv_low_bound, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(playlet_ltv_up_bound, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(final_cpm_thr_ratio_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_ltv_default_value, ks::AbtestBiz::AD_DSP, 30.0);
SPDM_ABTEST_STRING(user_group_level, ks::AbtestBiz::AD_DSP, "0");
SPDM_ABTEST_BOOL(enable_user_group_dim_native_stats_param, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_mcb, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_live, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_photo, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(explore_feed_hard_server_show_rate_amd_live, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(enable_explore_feed_hard_unify_server_show_rate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(thanos_mix_rank_quota_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_rank_quota_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(thanos_mix_rank_quota_ratio_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_rank_quota_ratio_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(follow_rank_quota_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fill_inner_trigger_hetu_emb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_prerank_trigger_relative_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_merchant_retention_live_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mt_lps_launch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_amd_live_celebrity_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_recommend_query_low_cpm, ks::AbtestBiz::AD_DSP);  // [niejinlong] 搜索导流召回降低阈值
SPDM_ABTEST_BOOL(enable_search_pos_n_no_ad, ks::AbtestBiz::AD_DSP);  // [yaolei] 搜索广告前 n 位不放广告
SPDM_ABTEST_BOOL(enable_search_cpm_thres_with_rele, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_universe_billing_separate_even_opt2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_diversion_source_cpm_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inner_stream_top4_rele_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inner_stream_first_ad_pos1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_bidword_micro_boost,
                 ks::AbtestBiz::AD_DSP);  // [yaolei] 搜索明投细粒度调价开关
SPDM_ABTEST_BOOL(enable_quick_search_auto_boost,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 搜索快投细粒度自动调价
SPDM_ABTEST_BOOL(enable_quick_search_auto_boost_v2_complete,
                 ks::AbtestBiz::AD_DSP);  // [niejinlong] 搜索快投细粒度自动调价 v2
SPDM_ABTEST_BOOL(enable_quick_search_auto_boost_v2_abtest,
                 ks::AbtestBiz::AD_DSP);  // [niejinlong] 搜索快投细粒度自动调价 v2
SPDM_ABTEST_BOOL(enable_search_app_card_ocpx_type_boost,
                 ks::AbtestBiz::AD_DSP);  // [wangshaoxiao] 搜索下载强样式分 ocpx 调价
SPDM_ABTEST_DOUBLE(search_inner_cpm_threshold_ratio,
                 ks::AbtestBiz::AD_DSP, 1.0);  // [zhangchaoyi03] 内循环 cpm 阈值系数
SPDM_ABTEST_DOUBLE(search_midnight_cpm_ratio,
                 ks::AbtestBiz::AD_DSP, 0.2);  // [zhaoyilin05] 搜索广告凌晨放量 cpm 阈值系数
SPDM_ABTEST_DOUBLE(search_midnight_top_rele_ratio,
                 ks::AbtestBiz::AD_DSP, 0.2);  // [zhaoyilin05] 搜索广告凌晨放量 top4 相关性阈值系数
SPDM_ABTEST_BOOL(enable_goods_kbox_ecpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [niejinlong] 搜索商品 kbox 独立 cpm 阈值
SPDM_ABTEST_BOOL(enable_search_list_ecpm_best_pos, ks::AbtestBiz::AD_DSP);  // [wangning14] 搜索序列最优
SPDM_ABTEST_BOOL(enable_search_inner_stream_list_ecpm_best_pos,
                 ks::AbtestBiz::AD_DSP);  // [niejinlong] 搜索内流序列最优
SPDM_ABTEST_BOOL(enable_quick_search_product_bonus_discount,
    ks::AbtestBiz::AD_DSP);  // [wangning14] 搜索广告快投放 bonus



SPDM_ABTEST_BOOL(enable_search_rank_predict_router_v3_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_rank_predict_router_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_rank_build_predict_resp_new, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adRank3, enableSearchRankRewritePredictReqContext);
SPDM_KCONF_BOOL(ad.adRank3, enableSearchRankPredictDiffAllField);
SPDM_KCONF_BOOL(ad.adRank3, enableSearchRankPredictReplace);

SPDM_KCONF_BOOL(ad.adRank3, enableSearchRouterRankBuildPredictReqNew);
SPDM_ABTEST_BOOL(enable_search_router_rank_build_predict_req_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_router_rank_build_predict_resp_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_router_rank_predict_direct, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_refactor_splash_boost_coef_prepare, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_search_native_boost_ratio, ks::AbtestBiz::AD_DSP);  // [wangning14]
SPDM_ABTEST_BOOL(enable_search_jinniu_moble_roi_model, ks::AbtestBiz::AD_DSP);  // [huangwei06] 搜索模型独立
SPDM_ABTEST_BOOL(enable_search_ad_inner_skip_rs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ad_inner_stream_fix_interval, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(is_search_moble_rank_info, ks::AbtestBiz::AD_DSP);  // [huangwei06]
SPDM_ABTEST_BOOL(enable_erase_normal_boost, ks::AbtestBiz::AD_DSP);  // [huangwei06]
SPDM_ABTEST_BOOL(enable_search_no_feed_ps, ks::AbtestBiz::AD_DSP);  // [huangwei06]
SPDM_ABTEST_BOOL(enable_search_price_protect, ks::AbtestBiz::AD_DSP);  // [zhouxuan06]
SPDM_ABTEST_BOOL(enable_search_global_dynamic_interval, ks::AbtestBiz::AD_DSP);  // [huangwei06]
SPDM_ABTEST_BOOL(enable_search_bigv_card_in_shop_tab, ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03]
SPDM_ABTEST_BOOL(enable_search_bigv_card_in_live_tab, ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03]
SPDM_ABTEST_BOOL(enable_diversity_filter_in_merge, ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03]
SPDM_ABTEST_BOOL(enable_game_request_imp_conv, ks::AbtestBiz::AD_DSP);  // [guoqi]
// [zhangchaoyi03] 强样式队列
SPDM_ABTEST_BOOL(enable_strong_card_queue_select_ads, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 强样式队列选广告
SPDM_ABTEST_BOOL(enable_search_select_strong_uplift, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 强样式 uplift 选广告
SPDM_ABTEST_BOOL(enable_normal_ad_list_function, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 普通队列广告选取函数
SPDM_ABTEST_BOOL(enable_search_native_simple_ad_gap, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 原生广告简单 ad_gap
SPDM_ABTEST_BOOL(enable_search_native_simple_inner_ad_gap_consider_list, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 原生广告简单 ad_gap 考虑前面 ad
SPDM_ABTEST_BOOL(enable_native_ads_adgap, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 原生广告 ad_gap
SPDM_ABTEST_BOOL(enable_other_stream_native_ads_adgap, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 原生广告实验多传输 ad
SPDM_ABTEST_BOOL(enable_allow_other_stream_more_ad, ks::AbtestBiz::AD_DSP);
// [zhangchaoyi03] 原生广告实验多传输 ad
SPDM_ABTEST_BOOL(enable_exact_app_skip_base_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 精确强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_exact_form_skip_base_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 精确强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_bigv_skip_base_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_series_skip_base_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_kbox_skip_base_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_exact_strong_style_skip_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_bidword_support,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 搜索直投扶持
SPDM_ABTEST_BOOL(enable_fanstop_strong_style_skip_cpm_thres,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 强样式跳过 cpm 阈值
SPDM_ABTEST_BOOL(enable_search_high_value_down_pos,
                 ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_e2e_twin_tower_down_pos,
                 ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_personalization_down_pos,
                 ks::AbtestBiz::AD_DSP);  // [yangjunyao] 个性化召回不出在前四位
SPDM_ABTEST_BOOL(enable_search_midnight_expand,
                 ks::AbtestBiz::AD_DSP);  // [zhaoyilin05] 搜索广告非一线城市凌晨放量开关
SPDM_ABTEST_BOOL(search_author_shop_intent_compete,
                 ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 主播店铺竞品
SPDM_ABTEST_BOOL(enable_listwise_diversity_for_combo, ks::AbtestBiz::AD_DSP);  // [niejinlong] 分队列多样性
SPDM_ABTEST_BOOL(enable_search_delete_unuse_point, ks::AbtestBiz::AD_DSP);  // [zhangchaoyi03] 无用 point 去除
SPDM_ABTEST_BOOL(disable_fanstop_diversity_for_combo, ks::AbtestBiz::AD_DSP);  // [niejinlong] 粉条多样性
SPDM_ABTEST_BOOL(enable_listwise_diversity_dedup, ks::AbtestBiz::AD_DSP);  // [niejinlong] 分队列去重多样性
// 搜索二价计费
SPDM_ABTEST_BOOL(enable_search_gsp_price, ks::AbtestBiz::AD_DSP);      // [niejinlong] 搜索二价计费
SPDM_ABTEST_BOOL(enable_search_gsp_price_protect, ks::AbtestBiz::AD_DSP);  // [niejinlong] 计费比保护
SPDM_ABTEST_BOOL(enable_search_normal_region_gsp, ks::AbtestBiz::AD_DSP);  // [niejinlong] 普通样式队列 GSP
SPDM_ABTEST_BOOL(enable_search_all_region_gsp, ks::AbtestBiz::AD_DSP);     // [niejinlong] 所有广告 GSP
SPDM_ABTEST_BOOL(disable_search_mcb_gsp, ks::AbtestBiz::AD_DSP);           // [niejinlong] mcb 关闭 GSP
SPDM_ABTEST_BOOL(enable_normal_list_next_benefit_only, ks::AbtestBiz::AD_DSP);     // [niejinlong] 仅普通样式队列记录 next benefit // NOLINT
SPDM_ABTEST_DOUBLE(search_price_protect_lower_bound, ks::AbtestBiz::AD_DSP, 0.66);  // [niejinlong] 计费比下界
SPDM_ABTEST_DOUBLE(search_price_ocpm_gsp_boost, ks::AbtestBiz::AD_DSP, 1.0);  // [niejinlong] 非 mcb 计费比 boost // NOLINT
SPDM_ABTEST_DOUBLE(search_price_mcb_gsp_boost, ks::AbtestBiz::AD_DSP, 1.0);   // [niejinlong] mcb 计费比 boost

SPDM_ABTEST_BOOL(enable_outer_u_ctr_soft_queue_cali, ks::AbtestBiz::AD_DSP);  // [zhouman] 统一 ctr 软广队列校准开关 // NOLINT
SPDM_ABTEST_BOOL(enable_outer_u_ctr_hard_queue_cali, ks::AbtestBiz::AD_DSP);  // [zhouman] 统一 ctr 硬广队列校准开关 // NOLINT
SPDM_ABTEST_DOUBLE(outer_u_ctr_soft_queue_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);  // [zhouman] 统一 ctr 软广队列校准系数 // NOLINT
SPDM_ABTEST_DOUBLE(outer_u_ctr_hard_queue_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);  // [zhouman] 统一 ctr 硬广队列校准系数 // NOLINT
SPDM_ABTEST_BOOL(enable_close_manual_calibration, ks::AbtestBiz::AD_DSP);   // [zhouman]
SPDM_ABTEST_BOOL(enable_explore_skip_calibrate_old, ks::AbtestBiz::AD_DSP);  // [panshunda] 跳过校准开关
SPDM_ABTEST_BOOL(enable_record_calibrate_info, ks::AbtestBiz::AD_DSP);  // [panshunda] 记录校准相关信息
SPDM_ABTEST_BOOL(enable_mcda_up_purchase_third_party_ecpc_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mcda_up_purchase_third_party_ecpc_launch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pre_mt_purchase, ks::AbtestBiz::AD_DSP);     // pre-ecpc purchase
SPDM_ABTEST_INT64(author_cache_hours_for_splash, ks::AbtestBiz::AD_DSP, 48);
// [yesiqi]

SPDM_ABTEST_BOOL(enable_lps_cvr_soft_queue_cali, ks::AbtestBiz::AD_DSP);  // [tanyijia] 外循环表单 软广队列校准开关 // NOLINT
SPDM_ABTEST_DOUBLE(lps_not_ecom_coeff_soft_col, ks::AbtestBiz::AD_DSP, 1.0);  // [tanyijia] 外循环表单 软广队列校准系数 // NOLINT



SPDM_ABTEST_BOOL(enable_adrank_filter_dup_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adrank_filter_dup_cover, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adrank_filter_product, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_DOUBLE(ad.adRank, nearbyLocalHardEcpmThreshold, 3.0);
SPDM_KCONF_DOUBLE(ad.adRank, nearbyLocalSoftEcpmThreshold, 3.0);
SPDM_KCONF_DOUBLE(ad.adRank2, valueQcpxLiveWhiteboxRoiAdjust, 1.0);
SPDM_KCONF_DOUBLE(ad.adRank2, valueQcpxPhotoWhiteboxRoiAdjust, 1.0);
SPDM_KCONF_DOUBLE(ad.adRank2, valueQcpxOtherAuthorRoiAdjustCoef, 1.0);
SPDM_ABTEST_BOOL(enable_qcpx_photo_paid_elastic_piecewise_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_qcpx_photo_roas_elastic_piecewise_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_qcpx_default_coupon_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_default_coupon_amount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_whitebox_no_bid_ratio_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_whitebox_no_bid_ratio_seed, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_whitebox_not_minus_coupon_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_whitebox_not_minus_coupon_seed, ks::AbtestBiz::AD_DSP, 0);
SPDM_KCONF_DOUBLE(ad.adRank2, valueQcpxVauthorRoiBound, 1.0);
SPDM_ABTEST_INT64(value_qcpx_live_rct_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_dark_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_dark_seed, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_qcpx_photo_threshold_price_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_qcpx_photo_threshold_mode_item_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_qcpx_photo_rct_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_photo_model_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_photo_price_coupon_ratio, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(value_qcpx_photo_max_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(shelf_qcpx_photo_price_coupon_ratio, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(shelf_qcpx_photo_max_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 40);
SPDM_ABTEST_INT64(value_qcpx_photo_history_paid_count_thres, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_qcpx_shelf_auction_operator, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_qcpx_shelf_auction_operator_hold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_qcpx_photo_rct_amount_control_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_photo_rct_amount_treatment_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_photo_rct_rate_control_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_photo_rct_rate_treatment_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_photo_specific_amount_coupon_id, ks::AbtestBiz::AD_DSP, 0);
// [yanqi08] live v3
SPDM_ABTEST_BOOL(enable_qcpx_live_strategy_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_qcpx_live_rct_amount_control_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_rct_amount_treatment_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_rct_rate_control_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_rct_rate_treatment_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_specific_amount_coupon_id, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_rct_right, ks::AbtestBiz::AD_DSP, 950);
SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_rct_left, ks::AbtestBiz::AD_DSP, 600);
SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_model_right, ks::AbtestBiz::AD_DSP, 950);
SPDM_ABTEST_INT64(value_qcpx_live_bound_rate_coupon_model_left, ks::AbtestBiz::AD_DSP, 600);
SPDM_ABTEST_BOOL(enable_qcpx_live_cvr_disc_roas_multi_head_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_qcpx_live_low_ori_cvr_cpa_bid_n, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_qcpx_live_full_stage_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_thres_lower, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_thres_upper, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_ori_q, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_a_new_q, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_thres_lower, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_thres_upper, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_ori_q, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_b_new_q, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_thres_lower, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_thres_upper, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_ori_q, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_roi_tag_c_new_q, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(random_coloring_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_qcpx_before_bagging_cvr_log, ks::AbtestBiz::AD_DSP);

// [fandi]
SPDM_ABTEST_BOOL(enableSoftPhotoToLivePecTopBar, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(value_photo_qcpx_freq_control_pay_nd, ks::AbtestBiz::AD_DSP, 7);
SPDM_ABTEST_INT64(value_live_qcpx_freq_control_pay_nd, ks::AbtestBiz::AD_DSP, 7);
// [luwei]
SPDM_ABTEST_BOOL(enable_inner_account_bonus_add_soft_queue, ks::AbtestBiz::AD_DSP);

// [zhangyiwei03] cid qcpx 开关
SPDM_ABTEST_BOOL(enable_cid_qcpx_strategy_run, ks::AbtestBiz::AD_DSP);  // cid qcpx 总开关
SPDM_ABTEST_BOOL(enable_cid_qcpx_cmd, ks::AbtestBiz::AD_DSP);  // cid qcpx 总开关

// [wangyang10]
SPDM_ABTEST_BOOL(enable_esp_mobile_live_to_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(esp_mobile_live_to_hard_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_reco_rank_wtr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(reco_rank_wtr_timeout, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(t7_roi_zhongcao_boot_ratio_hourly, ks::AbtestBiz::AD_DSP, 1.1);

SPDM_ABTEST_INT64(value_qcpx_photo_min_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_INT64(value_qcpx_photo_bound_rate_coupon_model_right, ks::AbtestBiz::AD_DSP, 900);
SPDM_ABTEST_INT64(value_qcpx_photo_bound_rate_coupon_model_left, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_INT64(value_qcpx_photo_thre_ratio_when_delivery, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(value_qcpx_freq_control_pay_nd_author_spu, ks::AbtestBiz::AD_DSP, 7);
SPDM_ABTEST_INT64(qcpx_amount_expand_coupon_expand_ratio, ks::AbtestBiz::AD_DSP, 2);

SPDM_ABTEST_INT64(qcpx_live_min_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(qcpx_live_max_coupon_amount_yuan, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_price_coupon_ratio, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_qcpx_live_model_flow_percent, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_qcpx_live_bspline_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(shelf_qcpx_photo_fixed_discount, ks::AbtestBiz::AD_DSP, 800);

// [heluo]
SPDM_ABTEST_INT64(value_qcpx_live_coupon_thre_type, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(value_p2l_thre_ratio_when_delivery, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(value_live_thre_ratio_when_delivery, ks::AbtestBiz::AD_DSP, 4);


SPDM_ABTEST_DOUBLE(edu_lps_deep_ecpc_lower_bound, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(edu_lps_deep_ecpc_upper_bound, ks::AbtestBiz::AD_DSP, 1.3);
SPDM_ABTEST_DOUBLE(edu_lps_deep_ecpc_coef, ks::AbtestBiz::AD_DSP, 0.99);

SPDM_ABTEST_BOOL(enable_mix_rank_input_hist_page_info_fea, ks::AbtestBiz::AD_DSP);

// [dengjiaxing]
SPDM_ABTEST_BOOL(enable_inner_merge_all_cvr_ctcvr_multi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_merge_all_ltv_multi, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_build_new_creative_used_item, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_build_used_item_to_adserver, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_build_ad_rank_infos_to_adserver, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_handler_support_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fill_filter_node, ks::AbtestBiz::AD_DSP)
SPDM_ABTEST_BOOL(enable_rank_price_record, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_max, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(hybrid_auction_billing_ratio_min, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_INT64(pv_filter_soft_quota, ks::AbtestBiz::AD_DSP, 30);
SPDM_ABTEST_INT64(pv_filter_hard_quota, ks::AbtestBiz::AD_DSP, 2);
SPDM_ABTEST_BOOL(enable_cid_corp_default_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(cid_quality_strategy_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(cid_spu_strategy_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_cid_quality_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_global_ratio_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_ab_global_ratio_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enhance_ecpm_strategy_default_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(disable_adx_for_ctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_model_backup, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_multi_head_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_multi_head_model_hy_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_duanju_multi_head_model_mid_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_no_subsidy_duanju_multi_head_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_playlet_mid_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_novel_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c_subsidy_game_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_normal_live_size, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(inner_super_green_live_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_innerloop_bonus_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_calc_vtr_ueq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(adjust_quota_dsp_ad_list, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adjust_quota_merchant_ad_list, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adjust_quota_live_ad_list, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adjust_quota_native_ad_list, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(adjust_quota_fanstop_ad_list, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inner_native_live_pc_quota, ks::AbtestBiz::AD_DSP, 25);
SPDM_ABTEST_BOOL(enable_rank_to_dp_server, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(rank_dragon_update_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(outer_soft_rank_base_line_num_v2, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outer_soft_rank_base_line_num_v3, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_outer_soft_quota_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outer_live_rank_num, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(inner_normal_live_rank_quota, ks::AbtestBiz::AD_DSP, 94);
SPDM_ABTEST_INT64(inner_native_live_rank_quota, ks::AbtestBiz::AD_DSP, 132);
SPDM_ABTEST_INT64(inner_native_live_rank_quota_inspire, ks::AbtestBiz::AD_DSP, 220);


// [fukunyang]
SPDM_ABTEST_DOUBLE(esp_live_costume_cost_threshold, ks::AbtestBiz::AD_DSP, 50000.0);
SPDM_ABTEST_BOOL(enable_roas_live_high_atv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_live_inspire_gmv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_eop_inspire_align_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_eop_split_item_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_inner_loop_min_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_author_pcoc_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_author_pcoc_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_promotion_pcoc_cali_tool, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_eop_is_pay, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_eop_inspire_use_gmv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_eop_ecpm_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_eop_gpm_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_eop_ecpm_weight_p2l, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_eop_gpm_weight_p2l, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(esp_live_ensemble_roas_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(esp_p2l_ensemble_roas_ratio, ks::AbtestBiz::AD_DSP, 0.6);
SPDM_ABTEST_DOUBLE(esp_live_ensemble_roas_upper_bound_ratio, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(esp_live_ensemble_roas_lower_bound_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(esp_p2l_ensemble_roas_upper_bound_ratio, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(esp_p2l_ensemble_roas_lower_bound_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_ensemble_unify_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_live_roas_ensemble2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_esp_live_roas_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_esp_roas_ensemble_author_pcoc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_storewide_roas_ensemble, ks::AbtestBiz::AD_DSP);

// [wangyuan11]
SPDM_ABTEST_DOUBLE(esp_photo_effective_play_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_live_hosting_photo_to_redis, ks::AbtestBiz::AD_DSP);

// [luchi]
SPDM_ABTEST_DOUBLE(uplift_order_high_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(uplift_order_low_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(uplift_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_uplift_order_paied, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_uplift_order_paied, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_uplift_valid_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sv_roas_gpm_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_roas_one_stage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sv_rank_candidante, ks::AbtestBiz::AD_DSP);

// [wanglianhai03]
SPDM_ABTEST_BOOL(enable_live_ltv_use_auc, ks::AbtestBiz::AD_DSP);
// [lining10]
SPDM_ABTEST_BOOL(enable_roas_multi_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_p2l_thanos_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_gpm_cmd, ks::AbtestBiz::AD_DSP);

// [litao24]
SPDM_ABTEST_BOOL(enable_p2l_use_auc, ks::AbtestBiz::AD_DSP);

// [lizhenmao]
SPDM_ABTEST_BOOL(enable_roas_7days_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_7days_0_2h_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_7days_2h_3d_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_7days_3d_7d_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_t7_use_t0_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_store_t7_use_convert, ks::AbtestBiz::AD_DSP);

// [xiaoyuchao]
SPDM_ABTEST_BOOL(enable_roas_7days_relative_predict_cmd, ks::AbtestBiz::AD_DSP);

// [tiantian06]
SPDM_ABTEST_BOOL(enable_search_live_roas_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_sigle, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 0.4);
SPDM_ABTEST_DOUBLE(live_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 2.5);
SPDM_ABTEST_DOUBLE(live_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(live_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(photo_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 0.4);
SPDM_ABTEST_DOUBLE(photo_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 2.5);
SPDM_ABTEST_DOUBLE(photo_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(photo_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_calibration_search_isperf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_card_live_T7, ks::AbtestBiz::AD_DSP);  // 搜索商品卡直播支持 T7
SPDM_ABTEST_BOOL(enable_goods_dsp_boost, ks::AbtestBiz::AD_DSP);  // 搜索垂搜短视频单独 boost
SPDM_ABTEST_BOOL(enable_p2l_split_ocpc_boost, ks::AbtestBiz::AD_DSP);  // 搜索短引分转化目标 boost
SPDM_ABTEST_BOOL(enable_live_mobile_ocpc_boost_2, ks::AbtestBiz::AD_DSP);  // 移动端直播分转化目标 boost
SPDM_ABTEST_BOOL(enable_search_qcpx_live_roi_uplift, ks::AbtestBiz::AD_DSP);  // 搜索直播 QCPX ROI 目标开关
SPDM_ABTEST_BOOL(enable_search_p2l_to_live_ecpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_search_goods_live_ecpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_goods_tab_fix_bug, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_goods_p2l_to_direct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inner_cid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_pay_rate_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_refund_merge_queue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_cid_model, ks::AbtestBiz::AD_DSP);  // inner cid

// [gonglingyun]
// 搜索广告线索行业内流 boost 开关
SPDM_ABTEST_BOOL(enable_ad_search_leads_industry_inner_boost, ks::AbtestBiz::AD_DSP);
// 搜索广告短剧 IAA 独立激活 CMD 开关
SPDM_ABTEST_BOOL(enable_search_series_iaa_conv, ks::AbtestBiz::AD_DSP);
// 搜索广告短剧 IAA 屏蔽 Kconf Boost 开关
SPDM_ABTEST_BOOL(enable_search_serial_iaa_boost_total, ks::AbtestBiz::AD_DSP);
// 搜索广告短剧 AD_ROAS 屏蔽 Boost 开关
SPDM_ABTEST_BOOL(enable_search_serial_roas_boost_total, ks::AbtestBiz::AD_DSP);
// 搜索广告分样式建模按强卡 eCPM 动态划分强卡队列开关
SPDM_ABTEST_BOOL(enable_style_distinguish_strategy, ks::AbtestBiz::AD_DSP);
// 搜索广告 分样式 分位置 打点开关
SPDM_ABTEST_BOOL(enable_style_pos_event_logging, ks::AbtestBiz::AD_DSP);
// 搜索广告行业直播大卡 Boost 开关
SPDM_ABTEST_BOOL(enable_search_industry_live_lps_bigcard_boost, ks::AbtestBiz::AD_DSP);
// 搜索广告行业直播表单强样式 Boost 开关
SPDM_ABTEST_BOOL(enable_search_industry_live_lps_form_card_boost, ks::AbtestBiz::AD_DSP);
// 搜索广告直播 ROAS 投放目标按强卡 eCPM 动态划分强卡队列开关
SPDM_ABTEST_BOOL(enable_live_roas_style_distinguish_strategy, ks::AbtestBiz::AD_DSP);
// 搜索广告直播订单投放目标按强卡 eCPM 动态划分强卡队列开关
SPDM_ABTEST_BOOL(enable_live_order_style_distinguish_strategy, ks::AbtestBiz::AD_DSP);
// 搜索广告盒子短带商品购买模型开关
SPDM_ABTEST_BOOL(enable_search_adbox_order_pay_model, ks::AbtestBiz::AD_DSP);
// [anchaojie]
SPDM_ABTEST_BOOL(enable_live_audience_single_use_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_audience_feed_use_auc, ks::AbtestBiz::AD_DSP);

// [houkai03]
SPDM_ABTEST_BOOL(enable_use_new_order_pay_cmd_for_guess_you_like, ks::AbtestBiz::AD_DSP);

// [yuancuili]
SPDM_ABTEST_BOOL(enable_shelf_live_order_pltv_model_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bh_pgpm_leverage_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mall_pgpm_leverage_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyl_pgpm_leverage_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(health_industry_retarget_path, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_item_card_speed_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(item_card_speed_test_quota, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(mall_high_value_pv_cpm_thr, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_INT64(gyl_high_value_pv_cpm_thr, ks::AbtestBiz::AD_DSP, 10.0);

// [xiaoyuhao]
SPDM_ABTEST_BOOL(enable_split_shelf_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_zhuanqian, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_buyer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_shelf_merchant_guess, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(rank_abtest_debug_flag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_undefine_ltv_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_opt_reco_handler_order, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_bh_leverage_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bh_live_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 0.6);
SPDM_ABTEST_DOUBLE(bh_item_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_DOUBLE(bh_leverage_score_upper_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(live_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(item_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mall_leverage_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mall_live_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_item_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_leverage_score_upper_ratio, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(enable_gyl_leverage_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_live_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_gpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_leverage_score_upper_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_bh_opm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(bh_live_card_opm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bh_item_card_opm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_mall_opm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mall_live_card_opm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_item_card_opm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_gyl_opm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_live_card_opm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_item_card_opm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_shelf_gpm_log, ks::AbtestBiz::AD_DSP);

// [chenziping]
SPDM_ABTEST_STRING(shelf_cmd_page_cali_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_order_paied_cvr_pos_cmd_for_gyl, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_paied_cvr_pos_cmd_for_bh, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_paied_cvr_pos_cmd_for_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_merchant_cpm_pos_for_gyl, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_merchant_cpm_pos_for_bh, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_merchant_cpm_pos_for_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_merchant_pos_value_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_photo_cmd_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gyl_feed_offline_cali, ks::AbtestBiz::AD_DSP);

// [xutaotao03]
SPDM_ABTEST_BOOL(enable_acquisition_twinbid_cold_start, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lps_account_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_clue_message_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_clue_wechat_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_lpsdeep_support_decay, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_lps_acquisition_generalization, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(acquisition_generalization_upper_bucket, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(acquisition_generalization_lower_bucket, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_iaa_acquisition_generalization, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(iaa_acquisition_generalization_upper_bucket, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(iaa_acquisition_generalization_lower_bucket, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_iaa_acq_gen_score_tags, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_acq_gen_ecpc_on_skip_sheild_budget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_lps_acquisition_prediction_default, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_customer_acquisition_cvr_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(poi_distance_adjust_min_dis, ks::AbtestBiz::AD_DSP, 31.0);
SPDM_ABTEST_BOOL(enable_lps_acquisition_support_exp_decay, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_message_twinbid_cold_start, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(pm_fwu_ee_group, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(pm_fwu_ee_account_cost_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_BOOL(enable_pm_fwh_para_transinfo, ks::AbtestBiz::AD_DSP);



// [zengdi]
SPDM_ABTEST_BOOL(enable_atlas_account_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sdpa_hidden_binding_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_rtabid_label_info_attr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inspire_conv_award_interval_second_merchant, ks::AbtestBiz::AD_DSP, 600);
SPDM_ABTEST_DOUBLE(cid_roas_reset_cvr, ks::AbtestBiz::AD_DSP, 0.0005);
SPDM_ABTEST_BOOL(enable_cid_use_goods_type_support, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_goods_potenial_select, ks::AbtestBiz::AD_DSP);

// [liuxingchen07]
SPDM_ABTEST_BOOL(enable_cid_use_goods_type_new_support, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_roi_imp_gmv_cmd_fix_auc_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_remove_stage_none, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_uplift_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_item_emb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_sid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_rl_ecpc, ks::AbtestBiz::AD_DSP);

// [linyuhao03]
SPDM_ABTEST_BOOL(enable_paid_course_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sdpa_ensemble_conversion, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sdpa_ensemble_invoked, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sdpa_purchase_to_zhongtai, ks::AbtestBiz::AD_DSP);

// [yishijie]
SPDM_ABTEST_BOOL(enable_common_offline_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(tag_name, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_wangfu_purchase_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_conv_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_move_ensemble_after_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_conv_log_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_conv_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_conv_soft_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_ecom_conv_bucket_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_conv_soft_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wangfu_purchase_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_x18_purchase_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(wangfu_purchase_ensemble_alpha, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(weight_ecom_item_imp_conv_ensemble, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(soft_weight_ecom_item_imp_conv_ensemble, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_purchase_ecom_cmdkey, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_purchase_promotion_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_purchase_exp_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_purchase_ee_cmdkey, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ee_purchase_cvr_tags, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ecom_conv_calibration_value, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecom_conv_soft_calibration_value, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ctr_manual_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cvr_manual_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dcvr_manual_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ltv_manual_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_manual_cali_add_creativetype, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_manual_cali_itemtype, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_manual_cali_remove_unvalid_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_rta_second_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adx_thanos_cvr_multihead, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_label_info_attr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_ad_source_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fill_lps_label_info_attr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adxcpc_miss_ctr_manual_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adxcpc_model_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_q3_hc_crm_center, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_dnc_crm_center, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_qiwei_online_judge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_move_crm_center_code, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_click_app_invoked_ecom_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_click_app_invoked_ecom_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_click_app_invoked_ecom_exp_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(click_app_invoked_ecom_ensemble_industry_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(click_app_invoked_ecom_ensemble_center_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_use_invoke_promotion_model, ks::AbtestBiz::AD_DSP);

// [liuxiaofan05]
SPDM_ABTEST_BOOL(enable_context_info_completion, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_pay_use_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_pay_follow_tab_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_p2l_pay_follow_tab_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_feed_ctr_fanstop_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_qcpx_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_qcpx_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_qcpx_t7roas, ks::AbtestBiz::AD_DSP);

// [zhongyiming]
SPDM_ABTEST_BOOL(enable_merchant_roas_use_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_t7_roi_use_auc, ks::AbtestBiz::AD_DSP);

// [songxu]
SPDM_ABTEST_BOOL(enable_zp_reward_cvr_revise_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(reward_cvr_revise_bound_up, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(reward_cvr_revise_bound_down, ks::AbtestBiz::AD_DSP, 0.1);

// [liuyi]
SPDM_ABTEST_BOOL(enable_mobile_live_pay_follow_tab_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_p2l_pay_follow_tab_exp, ks::AbtestBiz::AD_DSP);

// [caikehe]
SPDM_ABTEST_BOOL(enable_mix_rank_is_live_fea, ks::AbtestBiz::AD_DSP);

// [wangjian27]
SPDM_ABTEST_BOOL(enable_live_pay_single_tab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_pay_p2l_single_tab, ks::AbtestBiz::AD_DSP);

// [gaoqian03]
SPDM_ABTEST_BOOL(enable_use_new_pred, ks::AbtestBiz::AD_DSP);

// [wengrunze]
SPDM_ABTEST_BOOL(enable_real_deep_wechat_connected_lps_cvr_set, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_form_pm_integration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_form_pm_integration_single_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(pm_integration_single_prob_quota, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(pm_integration_leads_prob_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(pm_integration_lps_prob_alpha, ks::AbtestBiz::AD_DSP, 1.0);

// [yemengqing03]

SPDM_ABTEST_DOUBLE(search_e2e_top4_rele_thre, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(enable_search_e2e_live_ecpm_boost, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(enable_search_e2e_ecpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(enable_search_e2e_live_ecpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_search_tag_cpm_ratio_conf, ks::AbtestBiz::AD_DSP);

//  [yangzhao07]
SPDM_ABTEST_BOOL(enable_request_shoufa_game_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_shoufa_game_ltv_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_shoufa_game_ltv_hold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(shoufa_game_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_revert_std_cvr_ltv_coef,
                 ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_iap_adjust_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_request_wx_minigame_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_hardcore_game_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_iap_calibration_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_iaap_calibration_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_iaap_calibration_strategy_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mini_game_seven_day_iaap_calibration_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_industry_7r_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_industry_7r_model_serve, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv_change_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaap_request_iaa_ltv_luopan, ks::AbtestBiz::AD_DSP);


// [zhaorongsheng] 搜索广告激励
SPDM_ABTEST_DOUBLE(search_inspire_ad_box_fix_target_roi, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_task_progress_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(search_inspire_ad_box_single_col_order_paied_coin_discount, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(search_inspire_ad_box_cpm_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(search_inspire_ad_box_single_col_deep_optimization_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(search_inspire_ad_box_order_paied_item_price_theshold, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_STRING(search_inspire_ad_box_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(search_inspire_ad_box_single_col_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_search_inspire_duanju_thanos_nebula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inspire_duanju_thanos_main, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_fanstop_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_single_col_fanstop_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inspire_ad_box_boost_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inspire_single_col_boost_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_inspire_duanju_boost_optimize, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_conv_retention_macda_up_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_retention_macda_up_calibration_by_exp_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_retention_macda_up_calibration_launch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mt_purchase_launch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mt_shouxin_launch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_pec_mingtou, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_invoke_app_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_specific_deep_rewarded_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_support_landingpage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ecom_sensitive_user_fix_discount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(pec_coupon_set_fix_price, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(pec_coupon_cpa_bid_filter_low_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(pec_coupon_cpa_bid_filter_high_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(deep_rewarded_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(deep_rewarded_coin_list_str, ks::AbtestBiz::AD_DSP, "13,299,1000,3666");
SPDM_ABTEST_STRING(rewarded_deep_coin_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(order_paied_filter_media_app_version, ks::AbtestBiz::AD_DSP, "11.7.10");
SPDM_ABTEST_BOOL(enable_item_price_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_rewarded_black_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_holdout_pec_coin_main, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_holdout_pec_coin_nebula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_random_deep_rewarded_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_optimal_deep_rewarded_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pass_calc_deep_rewarded_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_deep_rewarded_coin_gross, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unify_calc_deep_rewarded_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_platform_profit_maximum, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_order_deep_rewarded_fix_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_rewarded_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(coupon_high_threshold_filter_add_value, ks::AbtestBiz::AD_DSP, 1000000);
SPDM_ABTEST_INT64(inspire_order_deep_rewarded_fix_coin, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(order_paied_item_price_theshold, ks::AbtestBiz::AD_DSP, 5000);
SPDM_ABTEST_DOUBLE(inspire_order_deep_rewarded_fix_discount_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_order_deep_rewarded_lambda, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_order_deep_rewarded_lambda_main, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(deep_rewarded_ecpc_max_ratio, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(deep_rewarded_rate_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(cpm_adjust_ratio, ks::AbtestBiz::AD_DSP, 0.61);
SPDM_ABTEST_INT64(live_pec_coupon_set_fix_price, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(live_pec_coupon_cpa_bid_filter_low_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(live_pec_coupon_cpa_bid_filter_high_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(coupon_threshold_discount_max_ratio,  ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(live_coupon_threshold_discount_max_ratio, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(pec_coupon_specific_template_id, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(pec_coupon_max_discount_value, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(pec_coupon_strategy_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_INT64(coupon_threshold_add_value, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_BOOL(enable_rewarded_bcb_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_plus_account_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_video_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inspire_live_new_user_calc_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_billing_separate_rewarded_video, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_live_new_user_cpm_thr, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_DOUBLE(inspire_live_feed_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_roas_calibration_game_upper, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(ad_roas_calibration_game_lower, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(ad_roas_calibration_mini_game_upper, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(ad_roas_calibration_mini_game_lower, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(ad_roas_calibration_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_roas_calibration_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_mix_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inspire_feed_soft_ad_new_sctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inspire_feed_soft_ad_fixed_sctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_auto_dark_btr_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inspire_live_new_user_coin_coef, ks::AbtestBiz::AD_DSP, "1:100;2:400");
SPDM_ABTEST_BOOL(enable_log_inner_explore_overlap_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_feed_explore_sctr_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(feed_explore_sctr_calibrate_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(feed_sctr_cali_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(feed_sctr_cali_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_feed_explore_cvr_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_upper_bound, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_lower_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(feed_explore_cvr_cali_exp_tag, ks::AbtestBiz::AD_DSP, "V1");
SPDM_ABTEST_BOOL(enable_cvr_sigmoid_calibrate_method, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_alpha, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(feed_explore_cvr_cali_beta, ks::AbtestBiz::AD_DSP, 0.2);
// [zhangzhao06]
SPDM_ABTEST_BOOL(enable_conv_invoked_pay_online, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_industry_pay_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_request_industry_pay_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(industry_pay_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_deep_cvr_shelve, ks::AbtestBiz::AD_DSP);

// [yangxinyong]
SPDM_ABTEST_BOOL(enable_request_game_industry_pay_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_industry_pay_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_industry_pay_ltv_reweight_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_request_minigame_industry_pay_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(minigame_industry_pay_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(minigame_industry_pay_ltv_reweight_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_game_industry_pay_ltv_out, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_industry_ensemble_pv_filter_lower_ratio, ks::AbtestBiz::AD_DSP, 0.2);
SPDM_ABTEST_DOUBLE(game_industry_ensemble_pv_filter_upper_ratio, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_BOOL(enable_game_bagging_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_game_industry_pay_ltv_fliter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_industry_pv_filter_ceiling, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(game_industry_pv_filter_bottom, ks::AbtestBiz::AD_DSP, 0.2);
SPDM_ABTEST_DOUBLE(game_industry_drop_bottom_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_industry_drop_ceiling_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_industry_drop_normal_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(std_ad_roas_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(std_ad_iaa_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(iaa_conv_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iaa_serial_conv_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(iaap_conv_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_iaa_conv_ltv_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_force_series_id, ks::AbtestBiz::AD_DSP);

// [chenzhengqi]
SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_origin_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi_iap_mid_origin_playlet_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi_iap_mid_origin_game_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roi_iap_mid_origin_mini_game_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_uplift_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_dnc_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_mid_doc_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_head_ltv_luopan, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ensemble_weight_hold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ensemble_weight_hold, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_playlet_iaa_ltv_ensemble_weight_hold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_iaa_ltv_ensemble_weight_hold, ks::AbtestBiz::AD_DSP, 1.0);

// [tiangeng]
SPDM_ABTEST_BOOL(enable_ad_rank_clk2purchase_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_clk2purchase_roas_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_industry_purchase_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_industry_purchase_predict_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_industry_invoked_purchase_ltv_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_industry_invoked_purchase_ltv_predict_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_industry_conv_purchase_ltv_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_inovked_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_inovked_predict_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_invoked_skip_mcb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_click_app_invoked, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_imp2invoked_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_conversion_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_industry_server_show_cvr_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(industry_clk2purchase_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(industry_clk2purchase_other_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_industry_clk2purchase_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_industry_clk2purchase, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_industry_clk2purchase_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(industry_clk2purchase_ratio_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(industry_clk2purchase_other_ratio_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_playlet_purchase_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_industry_pay_ltv_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(industry_pay_ltv_ensemble_weight_new, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(industry_invoked_pay_ensemble_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(industry_invoked_pay_ensemble_weight_new, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_con_pay_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_con_invoked_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_con_invoked_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_con_invoked_ltv_ratio2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_hard_queue_imp_invoked_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_playlet_close_invoked_link, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_realtime_feature, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_smart_offer_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_smart_offer_roi_thres, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ad_rank_playlet_pay_panel_purchase, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_playlet_conversion_unify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_playlet_pay_panel_purchase, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(industry_playlet_iaa_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(industry_playlet_iaa_ltv_ensemble_weight_new, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_use_iaa_new_playlet_es_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(iaa_new_playlet_es_weight, ks::AbtestBiz::AD_DSP, 0.5);


//  [gaoyuan21]
SPDM_ABTEST_BOOL(enable_request_industry_game_iaa_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(industry_game_iaa_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.5);

SPDM_ABTEST_BOOL(enable_game_iaa_ipu_log, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_game_iaa_ltv_calibrate,
ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_iaa_7r_ltv_calibrate,
ks::AbtestBiz::AD_DSP);

// [zhangmengxin]
SPDM_ABTEST_BOOL(enable_fiction_pay_realtime_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_ltv_realtime_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_invoked_pay_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fiction_pay_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_BOOL(enable_fill_fiction_roas_pay_ltv, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(drop_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_INT64(drop_tx_cost_thre, ks::AbtestBiz::AD_DSP, 5000);
SPDM_ABTEST_INT64(drop_ty_cost_thre, ks::AbtestBiz::AD_DSP, 5000);
SPDM_ABTEST_INT64(drop_ts_thre, ks::AbtestBiz::AD_DSP, 3600);



SPDM_ABTEST_BOOL(enable_game_bonus_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ocpm_inner_max_ecpm_yuan, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_non_explore_inner_max_ecpm_yuan, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_max_ecpm_yuan_protect, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ocpm_follow_max_ecpm_yuan, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(incentive_soft_live_auction_upper_bound, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_DOUBLE(incentive_hard_live_auction_upper_bound, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_BOOL(enable_cvr_explore_skip_soft_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cvr_explore_skip_cpm_thr_for_dac, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cvr_explore_just_allow_mix_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cvr_explore_skip_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(non_conv_user_skip_cpmthr_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(non_conv_user_cvr_skip_thr_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(cvr_explore_cvr_min_thd, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_cvr_explore_event, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(explore_skip_cpm_thr_topn_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_brand_level_boost_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_boost_roas_request_order_paid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_uax_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_caopan_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_brand_level_boost_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_brand_level_boost_explore_score_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(brand_level_boost_explore_score, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(outer_high_cost_hc_score, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(uax_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(caopan_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(brand_level_boost_explore_score_hard, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_brand_level_boost_user_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ctcvr_moving_avg_req_threshold, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_INT64(ctcvr_moving_avg_req_threshold2, ks::AbtestBiz::AD_DSP, 1000);

SPDM_ABTEST_INT64(high_pvr_skip_cpm_thr_outer_topn_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(high_pvr_skip_cpm_thr_inner_topn_num, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(high_pvr_force_reco_topn, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(non_conv_user_tag_for_quality_score_boost, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_boost_max_ctcvr_skip_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(non_conv_user_tag_for_force_reco, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fill_user_value_group, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_set_explore_feed_high_quality_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_auto_params_ps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_stats_auto_param_plugin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_hard_auto_param, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_group_dim_stats_param, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_retarget_ecpc_abtest, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_close_7day_ecpc, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_sdpa_novel_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_maintower_ensemble_imp_lps, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_car_ensemble_imp_lps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_car_ensemble_imp_lps_record, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ensemble_imp_lps_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_finance_deep_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_finance_deep_bonus_by_project, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_ensemble_imp_lps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_ensemble_imp_lps_block_corp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sdpa_ecom_conv_main_pred, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c2_lps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_c2_lps_ownctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wechat_connected_imp_lps, ks::AbtestBiz::AD_DSP);

// [wangxiaoyi03]
SPDM_ABTEST_BOOL(enable_esp_aigc_order_paid_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(esp_inner_aigc_order_paid_cali_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_esp_aigc_roas_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(esp_inner_aigc_roas_cali_rate, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_leads_unify_cvr, ks::AbtestBiz::AD_DSP);
// [huwenkang03]
SPDM_ABTEST_BOOL(enable_car_purchase_single_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_car_conv_purchase_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_car_purchase_single_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_car_conv_purchase_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_clue_ensemble_imp_lps, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_product_simple_promotion_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_clue_and_edu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lps_clue_not_ecom_coef, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_conv_quality_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_quality_delivery_score_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_quality_ecpc_by_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_quality_ecpc_drop_and_take_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_prophet_user_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_duanju_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_duanju_bonus_use_name, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_duanju_new_name_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_queue_p2l_use_live_ecpc_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_long_ratio_ad_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_long_ratio_ad_roas_splash_2_7_amt_, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecology_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_ecology_v2_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_ecology_live_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_dup_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_ecology_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_account_billing_skip_bs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_live_skip_account_bidding, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_white_account_skip_account_bidding, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bs_sort_weight_symmetry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ocpm_inner_max_ecpm_yuan, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_DOUBLE(ocpm_non_explore_inner_max_ecpm_yuan, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_DOUBLE(ocpm_follow_max_ecpm_yuan, ks::AbtestBiz::AD_DSP, 100.0);
SPDM_ABTEST_INT64(sensitive_user_cpmthr_tag, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_BOOL(enable_bs_inner_loop, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_spu_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_spu_restart_strategy_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_corp_default_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_cid_inspire_zk_bound, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(cid_account_bid_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(cid_ab_global_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [wanbgbin]
SPDM_ABTEST_DOUBLE(upper_ad_roas_long_ratio_pay_amount_7d, ks::AbtestBiz::AD_DSP, 2000.0);

// [tengwei]
SPDM_ABTEST_DOUBLE(fanstop_new_product_mini_app_live_bonus_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fanstop_show_author_playtime_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fanstop_show_author_wtr_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fanstop_game_author_playtime_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fanstop_game_author_wtr_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fanstop_recruit_live_hc, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fanstop_recruit_photo_hc, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(reco_lsp_live_rank_xtr_timeout, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(lsp_avoidance_first_outer_index_threshold, ks::AbtestBiz::AD_DSP, 16);
SPDM_ABTEST_DOUBLE(lsp_avoidance_in_out_ecpm_ratio_threshold, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(enable_locallife_live_geo_distance_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(locallife_live_geo_distance_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.01);
SPDM_ABTEST_BOOL(enable_ss_pcoc_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_ss_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_population_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_population_cali_refine, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_population_cali_wpage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_population_cali_two, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_population_cali_three, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_remove_follow_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_remove_page_two, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_multi_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_multi_bound_refine, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_multi_bound_wpage, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_effective_play_drop_request, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ss_pcoc_cali_via_esp_author_fans_pcoc_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lsp_photo_order_paied_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_smb_coldstart_hc_exp, ks::AbtestBiz::AD_DSP, "default");

// [yangfukang03]
SPDM_ABTEST_DOUBLE(fanstop_brand_initial_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fanstop_live_parse_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(po_quan_ee_pid_exp_group, ks::AbtestBiz::AD_DSP, "");  // 样例 "exp1,exp2,exp3"
SPDM_ABTEST_BOOL(enable_po_quan_ee_pid_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dnc_ltv_analysis, ks::AbtestBiz::AD_DSP);
// [tangsiyuan]
SPDM_ABTEST_BOOL(disable_fanstop_temu_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fanstop_leads_new_account_threshold, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(fanstop_prms_new_account_threshold, ks::AbtestBiz::AD_DSP, 15);

// [jiayalong]
SPDM_ABTEST_INT64(outloop_recruit_user_package_hc_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outerloop_dnc_cem_group_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(outerloop_dnc_cem_exploit_group, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_brand_fanstop_r3_skip_cpmbound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_all_brand_fanstop_skip_cpmbound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_load_outerloop_ac_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_load_outerloop_ac_frequence, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_content_consumption_force_reco, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_cc_kgame_force_reco, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_cc_playlet_force_reco, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_cc_fiction_force_reco, ks::AbtestBiz::AD_DSP);
// [yanghang06]

SPDM_ABTEST_BOOL(enable_trans_creative_build_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(gyl_item_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gyl_live_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_item_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mall_live_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bh_item_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(bh_live_sctr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_use_shelf_gpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_shelf_express_cali_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(shelf_express_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_INT64(live_calib_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(coldstart_b_control_ab_range, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(coldstart_b_topk_hc_adjust_pow, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_up_items_ue_cpm_test, ks::AbtestBiz::AD_DSP);


// [guochangyu]
SPDM_ABTEST_DOUBLE(lsp_storewide_live_roas_ensemble_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(lsp_storewide_p2l_roas_ensemble_ratio, ks::AbtestBiz::AD_DSP, 0.6);
SPDM_ABTEST_INT64(inner_product_ee_order_cnt_7days_thresh, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_lsp_storewide_pcoc_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(lsp_storewide_pcoc_cali_upper_bound, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(lsp_storewide_pcoc_cali_lower_bound, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_BOOL(enable_lsp_storewide_pcoc_cali_exclude_search, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_lsp_storewide_search_pcoc_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(lsp_storewide_search_pcoc_cali_upper_bound, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(lsp_storewide_search_pcoc_cali_lower_bound, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_BOOL(enable_lsp_storewide_flow_gmv_model_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(lsp_storewide_flow_gmv_model_ensemble_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(lsp_storewide_search_gmv_model_ensemble_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_resolve_local_life_mark, ks::AbtestBiz::AD_DSP);

// hidden cost
SPDM_ABTEST_BOOL(enable_inner_hard_gpm_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_balance_gpm_score_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_balance_gpm_score_ratio_c, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(merchant_gpm_l0_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_gpm_l1_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_gpm_l2_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_gpm_l3_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_gpm_l4_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_shield_status_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(merchant_item_quality_lvl_0_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_item_quality_lvl_1_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_item_quality_lvl_2_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(competing_item_hc_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(smb_hc_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(balance_gpm_score_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(balance_gpm_score_ratio_c, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_hc_inner_live_independent_gmv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hc_gpm_bound_by_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(first_n_ecpc_thresh, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_inner_nobid_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nobid_age_gender_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nobid_age_gender_cali_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_only_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nobid_pcvr_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_nobid_pcvr_cali_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smb_industry_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(enable_smb_industry_cvr_exp_name, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_BOOL(enable_smb_industry_cvr_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smb_industry_cvr_exp_patch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hc_experience_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_bonus_mix_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_bonus_mix_outer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bonus_feed_explorer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bonus_inner_explorer, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bonus_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bonus_all, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_live_ue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(reco_live_ue_timeout, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_STRING(reco_live_ue_grpc_name, ks::AbtestBiz::AD_DSP, "grpc_mixrankLiveInferServerv1alldata");
SPDM_ABTEST_DOUBLE(high_ue_force_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_high_ue_explore_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hc_gpm_order_pay_independent, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(hc_experience_pxr_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fix_gpm_p2l_live_audience, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unify_adload_plugin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_orientation_adload_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_live_force_reco_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(outer_live_force_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_outer_live_orientation_tool_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_force_use_game_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_shoufa_force_adload_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_shoufa_for_all_inds, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_interest_retarget_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_reco_interest_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_sv_uplift_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_sv_uplift_hc_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_sv_uplift_hc_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_sdpa_item_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_item_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_inner_sv_uplift_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_native_sv_uplift_hc_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_native_sv_uplift_hc_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fanstop_distillation_reco_like_photo_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_load_nc_model_prod_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_video_auc_cross_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_middle_model_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_submit_cold_split_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_submit_gpm_use_ltv_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_order_gpm_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_deep_middle_model_convpay_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_middle_model_kac_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_middle_model_nd_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roi_auto_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roi_auto_calibrate_realtime, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_roi_auto_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_roi_auto_calibrate_mini_app, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_std_cvr_ltv_coef, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_std_cvr_ltv_coef_duanju, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_std_cvr_ltv_coef_iaa, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_multi_retrieval_cxr_adj_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_cid_hc_gpm_score_by_cpm_exp, ks::AbtestBiz::AD_DSP);
// merchant_roas config by zed
SPDM_ABTEST_BOOL(enable_cancel_leverage_score_use_post_gmv_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_follow_use_auc_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fans_follow_use_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecom_conv_cross_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoke_cross_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_order_cross_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_order_live_cross_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_order_live_cmdkey_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_ad_billing_seperate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_close_native_skip_ab, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jicheng_account_event_order_paied, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jicheng_account_ad_merchant_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jicheng_account_only_for_cold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_s2h_explore_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_s2h_non_explore_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsp_invoked_order_paid_logic_move, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pec_sensitive_user, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_coin_pec, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_coin_pec_whitelist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pec_coin_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pec_support_for_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_inner_loop_support_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsp_micro_video_deep_reward, ks::AbtestBiz::AD_DSP);

// [luowenjuan] 激励广告 金币长期价值探索
SPDM_ABTEST_BOOL(enable_coin_long_term_value, ks::AbtestBiz::AD_DSP);      // 探索长期价值开关
SPDM_ABTEST_DOUBLE(icentive_coin_long_term_eprice_lower, ks::AbtestBiz::AD_DSP, 0.0);  //策略最低 eprice
SPDM_ABTEST_DOUBLE(icentive_coin_long_term_eprice_upper, ks::AbtestBiz::AD_DSP, 1000.0);  // 策略最高 eprice
SPDM_ABTEST_DOUBLE(icentive_coin_long_term_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);  // 金币调整系数
SPDM_ABTEST_DOUBLE(icentive_coin_long_term_upper, ks::AbtestBiz::AD_DSP, 2000.0);  // 最高 coin
SPDM_ABTEST_DOUBLE(icentive_coin_long_term_lower, ks::AbtestBiz::AD_DSP, 10.0);  // 最低 coin

// [zhangyuyang05] 激励广告 浅度激励 金币策略重构
SPDM_ABTEST_BOOL(enable_only_unify_normal_calc_coin, ks::AbtestBiz::AD_DSP);  // 关闭老硬广策略
SPDM_ABTEST_BOOL(enable_only_unify_native_calc_coin, ks::AbtestBiz::AD_DSP);  // 关闭老软广策略
SPDM_ABTEST_DOUBLE(unify_calc_coin_explore_traffic_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(unify_calc_coin_default_roi, ks::AbtestBiz::AD_DSP, 2.5);           // 默认策略的 ROI
SPDM_ABTEST_DOUBLE(incntv_coin_roi_control_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(incntv_cpm2value_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ucc_mdp_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ucc_mdp_ltv_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ucc_mdp_ltv_upper_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(ucc_mdp_ltv_exp_key, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(incentive_ad_shallow_treatment_max_num, ks::AbtestBiz::AD_DSP, 10);  // 重最多的 treat 数量
SPDM_ABTEST_STRING(incntv_ad_monitor_tag, ks::AbtestBiz::AD_DSP, "");                  // 监控 tag
SPDM_ABTEST_DOUBLE(unify_coin_upper, ks::AbtestBiz::AD_DSP, 1000.0);
SPDM_ABTEST_DOUBLE(unify_coin_lower, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_BOOL(enable_unify_calc_coin_percent_trt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(unify_calc_coin_percent_trt_str, ks::AbtestBiz::AD_DSP, "-20,-10,0,10,20");
SPDM_ABTEST_BOOL(unify_calc_coin_trt_use_percent, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(unify_calc_coin_task_cnt_control_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_rank_has_more, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(unify_calc_coin_upper_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_refactor_ucc_ad_trt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_budget_allocation_user_roi_alpha_bias, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(rank_has_more_thres, ks::AbtestBiz::AD_DSP, 0.0);

// [gaozepeng] 下单激励
SPDM_ABTEST_BOOL(enable_fixed_order_paid_deep_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(fixed_order_paid_deep_coin_str, ks::AbtestBiz::AD_DSP, "600");
SPDM_ABTEST_BOOL(enable_coin_order_paied_style, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(coin_order_paied_style_start_view, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(coin_order_paied_award_interval_second, ks::AbtestBiz::AD_DSP, 600);
SPDM_ABTEST_INT64(coin_order_paied_task_finish_upper, ks::AbtestBiz::AD_DSP, 5);
// [xuxu] 小程序广告
SPDM_ABTEST_BOOL(enable_micro_app_cvr_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_increase_bid_ad_outer_only, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(smb_billing_hard_lower_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smb_billing_hard_upper_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_cid_bid_roas_by_roi_roas, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_polaris_account, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(min_rta_quality_score, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(max_rta_quality_score, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(max_rta_quality_hc_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_native_adx_biding_exp, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(incentive_iaa_adless_style_delivery_gap_s_new, ks::AbtestBiz::AD_DSP, 60);
SPDM_ABTEST_BOOL(enable_deep_incentive_unify_coin_and_style, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_coin_pec_for_joint_draw, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_joint_draw_pec_app_advance_coin_num, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(incentive_joint_draw_pec_app_coin_num, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_BOOL(enable_adless_joint_freq_control_use_ms, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_uplift_deep_coin_decision, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(order_fix_deep_style_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_order_rct_fix_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_optimal_uplift_cvr_ratio, ks::AbtestBiz::AD_DSP, 0.2);
SPDM_ABTEST_BOOL(enable_incentive_order_skip_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_order_admit_sub_page_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adless_add_pop_window, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(invoked_uplift_cvr_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(invoked_uplift_cvr_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(invoked_treatment_coin_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_invoke_fix_deep_coin_v3, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(deep_coin_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_deep_coin_adjust_by_different_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(deep_coin_range_per_level_for_adjust, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_BOOL(enable_deep_coin_adjust_by_ocpc_action_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_uax_aigc_bid_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_mcda_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_deep_reward_effect_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_deep_reward_no_erase_style, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_fix_deep_style_ratio, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_STRING(invoked_fix_deep_coin_num_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_deep_coin_adjust_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(deep_coin_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_incentive_adless_style, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_adless_page_id_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_adless_sub_page_id_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_adless_pos_id_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_adless_product_delivery_gap_s, ks::AbtestBiz::AD_DSP, 600);
SPDM_ABTEST_INT64(incentive_adless_style_delivery_gap_s, ks::AbtestBiz::AD_DSP, 7200);
SPDM_ABTEST_INT64(incentive_iaa_adless_style_delivery_gap_s, ks::AbtestBiz::AD_DSP, 7200);
SPDM_ABTEST_INT64(incentive_total_deep_task_delivery_time_gap_s, ks::AbtestBiz::AD_DSP, 7200);  // NOLINT

// [zhangxingyu03] 深浅联合决策
SPDM_ABTEST_BOOL(enable_incentive_coin_joint_decision, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_rct_migrate, ks::AbtestBiz::AD_DSP);

// [zhangxingyu03] 深度激励整体
SPDM_ABTEST_BOOL(enable_xifan_deep_incentive_skip_incentive_flow_judge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(deep_coin_adjust_ratio_for_live_order_roi, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_calc_view_coin_use_cpm_before_deep_incentive_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(calc_view_coin_use_cpm_before_deep_incentive_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_deep_incentive_rct_add_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_add_dish_bugfix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_add_dish, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_iaa_deep_coin_coef, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_d_i_specific_coin_coef, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calc_adx_deep_incentive_coin_by_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(adx_deep_incentive_coin_roi_ratio, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_BOOL(enable_calc_view_coin_use_gross_after_deep_incentive, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_record_rewarded_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_total_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_gross_max_by_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(deep_incentive_gross_max_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_deep_incentive_global_rct_style_judge, ks::AbtestBiz::AD_DSP);

// [zhangxingyu03] 拉活激励
SPDM_ABTEST_STRING(calc_view_coin_consider_deep_incentive_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_invoked_d_i_uplift_blacklist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_d_i_specific_time_gap_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(invoked_d_i_specific_time_gap_s, ks::AbtestBiz::AD_DSP, 90);  // NOLINT
SPDM_ABTEST_DOUBLE(invoked_iaa_deep_coin_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_invoked_d_i_global_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_d_i_global_rct_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_invoked_d_i_iaa_use_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_d_i_specific_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_new_admit_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_advertiser_blacklist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_d_i_ob_data_record_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_rct_switch_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_holdout_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_add_cid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_uplift_cvr_clip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_uplift_cvr_clip_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_marginal_roi_constrain, ks::AbtestBiz::AD_DSP);  //NOLINT
SPDM_ABTEST_DOUBLE(invoked_deep_incentive_marginal_roi_constrain_coef, ks::AbtestBiz::AD_DSP, 2.5);  //NOLINT
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_roi_constrain, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_deep_incentive_roi_constrain_coef, ks::AbtestBiz::AD_DSP, 2.5);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_auto_cpa_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(invoked_deep_incentive_coin_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_adjust_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(invoked_deep_incentive_adjust_cvr_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(incentive_invoked_max_mission_num, ks::AbtestBiz::AD_DSP, -1);  // NOLINT
SPDM_ABTEST_INT64(incentive_invoked_delivery_time_gap, ks::AbtestBiz::AD_DSP, -1);  // NOLINT
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_use_rewarded_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoked_deep_incentive_holdout, ks::AbtestBiz::AD_DSP);

// [zhangxingyu03] 激活激励
SPDM_ABTEST_BOOL(enable_award_active_app_new_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_deep_coin_decision_for_promotion, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_d_i_uplift_model_add_site_page_for_promotion, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(conv_iaa_deep_coin_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_conv_d_i_global_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(conv_d_i_global_rct_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_conv_d_i_uplift_site_page_product_name_whitelist, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(enable_conv_d_i_style_delivery_time_gap_s, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(enable_conv_d_i_style_max_mission_num, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_BOOL(enable_conv_d_i_ratio_control_config_bugfix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_d_i_iaa_use_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_d_i_uplift_model_add_site_page, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_d_i_specific_config, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(conv_d_i_ob_data_record_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_new_admit_config, ks::AbtestBiz::AD_DSP);  // NOLINT
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_rct_switch_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_skip_deep_bid_twin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_rct_request_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_uplift_model_black_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_uplift_model_white_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_add_cid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_uplift_cvr_clip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(conv_uplift_cvr_clip_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_roi_constrain, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(conv_deep_incentive_roi_constrain_coef, ks::AbtestBiz::AD_DSP, 2.5);
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_auto_cpa_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(conv_deep_incentive_coin_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_adjust_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(conv_deep_incentive_adjust_cvr_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_incentive_conv_install_judge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_conv_product_id_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_conv_max_mission_num, ks::AbtestBiz::AD_DSP, -1);  // NOLINT
SPDM_ABTEST_INT64(incentive_conv_delivery_time_gap, ks::AbtestBiz::AD_DSP, -1);  // NOLINT
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_rewarded_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_conv_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_model_decision_skip_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_uplift_cvr_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(conv_uplift_cvr_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(conv_treatment_coin_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(conv_optimal_uplift_cvr_ratio, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_conv_uplift_deep_coin_decision, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(conv_uplift_model_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_deep_incentive_use_marginal_roi_constrain, ks::AbtestBiz::AD_DSP);  //NOLINT
SPDM_ABTEST_DOUBLE(conv_deep_incentive_marginal_roi_constrain_coef, ks::AbtestBiz::AD_DSP, 2.5);  //NOLINT

// [zhangxingyu03] 下单激励
SPDM_ABTEST_BOOL(enable_order_native_deep_incentive_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_d_i_global_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(order_d_i_global_rct_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_use_uplift_logits, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_deep_incentive_price_adjust_for_sv_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(deep_incentive_price_adjust_str_for_sv_order, ks::AbtestBiz::AD_DSP, "1,1,1,1");
SPDM_ABTEST_BOOL(enable_deep_coin_adjust_ratio_for_sv_order_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(deep_coin_adjust_ratio_for_sv_order_roi, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_rct_switch_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_clip_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(order_deep_incentive_clip_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_order_rct_request_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_uplift_cvr_clip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(order_uplift_cvr_clip_ratio, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_roi_constrain, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(order_d_i_ob_data_record_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(order_deep_incentive_roi_constrain_coef, ks::AbtestBiz::AD_DSP, 2.5);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_use_auto_cpa_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_native_deep_incentive, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(order_deep_incentive_coin_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_adjust_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(order_deep_incentive_adjust_cvr_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_order_deep_incentive_effect_cpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(incentive_order_max_mission_num, ks::AbtestBiz::AD_DSP, -1);  // NOLINT
SPDM_ABTEST_INT64(incentive_order_delivery_time_gap, ks::AbtestBiz::AD_DSP, -1);  // NOLINT
SPDM_ABTEST_BOOL(enable_incentive_order_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_model_decision_skip_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_uplift_cvr_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(order_uplift_cvr_ratio_str, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(order_treatment_coin_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(order_optimal_uplift_cvr_ratio, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_holdout, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_order_deep_incentive_use_marginal_roi_constrain, ks::AbtestBiz::AD_DSP);  //NOLINT
SPDM_ABTEST_DOUBLE(order_deep_incentive_marginal_roi_constrain_coef, ks::AbtestBiz::AD_DSP, 2.5);  //NOLINT
SPDM_ABTEST_BOOL(enable_order_d_i_rct_add_item_price_judge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(order_d_i_global_rct_coin_ratio_upper, ks::AbtestBiz::AD_DSP, 0.5);

// [zhangxingyu03] 直播订单激励
SPDM_ABTEST_DOUBLE(live_order_d_i_discount_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(live_order_d_i_coin_upper_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_live_order_d_i_cvr_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(live_order_d_i_cvr_adjust_str, ks::AbtestBiz::AD_DSP, "1,1,1");
SPDM_ABTEST_BOOL(enable_live_order_d_i_use_new_treatment, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_d_i_rct_tag_change, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_order_pay_uplift_init, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_order_optimal_uplift_cvr_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(live_order_treatment_coin_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(live_order_d_i_ob_data_record_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_live_order_pay_uplift_decision, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_order_pay_deep_incentive_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_order_rct_fix_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_order_fix_deep_style_ratio, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(live_order_uplift_model_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_live_order_pay_deep_incentive_holdout, ks::AbtestBiz::AD_DSP);   // [renruimin]

// [zhangxingyu03] 直播 ROAS 激励
SPDM_ABTEST_BOOL(enable_live_roas_d_i_use_discount_ratio_smooth, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_roas_d_i_use_discount_ratio_smooth_ratio_k, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(live_roas_d_i_use_discount_ratio_smooth_ratio_b, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(live_roas_d_i_discount_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(live_roas_d_i_coin_upper_coef, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_live_roas_d_i_discount_consider_gmv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_roas_discount_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_live_roas_d_i_cvr_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(live_roas_d_i_cvr_adjust_str, ks::AbtestBiz::AD_DSP, "1,1,1");
SPDM_ABTEST_BOOL(enable_live_roas_d_i_use_new_treatment, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_roas_d_i_add_t7_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_roas_d_i_add_storewide, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_roas_uplift_add_clear, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_roas_uplift_init, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_roas_optimal_uplift_cvr_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(live_roas_treatment_coin_ratio, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_live_roas_uplift_decision, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_roas_deep_incentive_rct, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_live_roas_rct_fix_coin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(live_roas_fix_deep_style_ratio, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(live_roas_uplift_model_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_live_roas_deep_incentive_holdout, ks::AbtestBiz::AD_DSP);  // [renruimin]

// [zhangxingyu03] draw 流
SPDM_ABTEST_DOUBLE(incentive_draw_flow_coin_roi_coef, ks::AbtestBiz::AD_DSP, 0);

// [lishaozhe]
SPDM_ABTEST_BOOL(enable_mobile_photo_roas_calc_auto_cpa_bid_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_skip_roi_ratio_check_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_author_tier_fill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(polaris_exp_params_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(auto_purchase_roi_coef_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(polaris_user_tag_score_hc_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(native_quality_bonus_coef_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(user_tag_source_list_str, ks::AbtestBiz::AD_DSP, "0");
SPDM_ABTEST_STRING(fanstop_photo_ecpc_exp_prefix, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(fanstop_ecpc_score_aggregation, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_juxing_use_min_ecpm, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_juxing_forbid_canjing, ks::AbtestBiz::AD_DSP);

// [jiyang] 关注页复原 CPM 门槛
SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(follow_ecpm_threshold_init, ks::AbtestBiz::AD_DSP, 4.0);
SPDM_ABTEST_STRING(follow_ecpm_threshold_init_tag_3, ks::AbtestBiz::AD_DSP, "");
// [jiyang] 短剧行业外传内补贴 bonus
SPDM_ABTEST_BOOL(enable_payskit_outer_bonus_to_inner, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(payskit_outer_bonus_to_inner_exptag, ks::AbtestBiz::AD_DSP, "");
// [jiyang] 重构优质软广判定逻辑
SPDM_ABTEST_BOOL(enable_refactor_nogap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_refactor_nogap_newlink, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_refactor_nogap_newlink_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retcs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retdyddcs, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tapeout_follow_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retcs_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_retdyddcs_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tapeout_follow_hc_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_incentive_not_costly_coin_rb, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(incentive_unify_rb_coin_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_incentive_exploit_use_session_expected_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_sctr_migrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(incentive_sctr_migrate_upper, ks::AbtestBiz::AD_DSP, 100.0);
// [jiyang] 关注页精排重构
SPDM_ABTEST_BOOL(enable_follow_rank_refactor_dot, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_chuangxin_model_feature, ks::AbtestBiz::AD_DSP);
// [jinhui05] 内流 trigger item 对应的河图标签
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_wanhe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(inner_trigger_item_strategy_mode, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(inner_trigger_item_strategy_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(inner_trigger_item_same_tag_factor, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_trigger_item_path_boost_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trigger_item_path_boost_wanhe, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_trigger_item_path_boost, ks::AbtestBiz::AD_DSP, 1.0);
// [jinhui05] 主站发现页内流曝光系数移除
SPDM_ABTEST_BOOL(enable_explore_inner_sctr_migrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(explore_inner_sctr_migrate_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(explore_inner_sctr_migrate_upper, ks::AbtestBiz::AD_DSP, 10.0);
// [jinhui05] 硬广 unify_sctr 优化
SPDM_ABTEST_BOOL(fix_hard_unify_sctr_click, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_hard_unify_sctr, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_upper_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_lower_bound, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_photo_medium, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_photo_alpha, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_live_medium, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_live_alpha, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_p2l_medium, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(new_inner_ecommerce_order_paid_ecpc_p2l_alpha, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(finance_roi_ecpc_coef, ks::AbtestBiz::AD_DSP, 0.99);

SPDM_ABTEST_BOOL(enable_pred_target_cost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_credit_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fin_credit_roi_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_jinjian_credit_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_jinjian_credit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_model_score_product_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecpc_add_ctr_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_cpm_thr_new_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_account_jinjian_credit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ind_deep_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ind_deep_ecpc_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_coeff_credit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_lps_deep_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_leads_submit_deep_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_lps_deep_ecpc_other_ind, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_deep_ecpc_control_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_deep_ecpc_coef, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_nextstay_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ind_use_credit_optimize, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ind_user_expore_hc_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ind_rta_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_supplier_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jiaotong_ind_user_hc_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enhance_ecpm_strategy_rank_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecpm_strategy_ecpc_mcb_close, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(close_minbid_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_side_window_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_side_window_filter_pxr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(side_window_filter_pxr_mode, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(sctr_into_cpm_discount_ratio_follow, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(max_invoked_task_compelete_count_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(factor_info_max_size, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inspire_live_random_num, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inspire_live_random_coin, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(max_conversion_task_compelete_count_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_DOUBLE(side_window_filter_pxr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(side_window_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_main_side_window_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(main_side_window_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_profile_side_window_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(profile_side_window_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_main_side_window_pos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(main_side_window_pos_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_profile_side_window_pos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(profile_side_window_pos_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_follow_fanstop_sctr_package, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_cpm_thr_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inner_explore_cpm_thr_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_duanju_playlet_name_adload_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_duanju_playlet_name_conf_time_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wanhe_threshold_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wanhe_threshold_opt_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(main_side_window_min_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(profile_side_window_min_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(profile_skin_min_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(wanhe_profile_boost_opt_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_wanhe_inner_p2l_charge_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(wanhe_charge_action_type_adjust_cpm_thr_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(wanhe_action_type_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(main_side_window_soft_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(profile_side_window_soft_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(profile_skin_soft_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_side_window_boost_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(side_window_boost_opt_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_side_window_sctr_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(side_window_sctr_opt_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_INT64(side_window_cross_section_pid_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_STRING(wanhe_matching_efficiency_opt_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(matching_efficiency_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(front_force_reco_avoid_same_second_industry_topn, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(splash_eyemax_discount_ratio_value, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(splash_eyemax_discount_immg, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_mcda_boost_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_conv_q_ecpc_splash, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_search_boost_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(cid_search_user_label_num, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_INT64(cid_search_boost_exp_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_adx_server_client_show_rate_factor, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_mcda_boost_tag_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(cid_mcda_boost_exp_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(max_invoked_task_compelete_count_num_merchant, ks::AbtestBiz::AD_DSP, 3);
SPDM_ABTEST_INT64(inspire_invoked_award_interval_second_merchant, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_INT64(trigger_relative_score_request_times_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_mix_inner_explore_relative_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trigger_item_hetu_tag_inner_explore_new, ks::AbtestBiz::AD_DSP);



SPDM_ABTEST_BOOL(enable_individual_quota_for_inspire_merchant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_stats_auto_param, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_auto_param_follow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_auto_param_inspire, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(native_stats_auto_param_expid_group, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_nebula, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_follow, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_nebula_follow, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_inspire, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(native_stats_auto_param_expid_group_nebula_inspire, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_innerloop_account_splash_skip, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_innerloop_splash_skip_admit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_model_calibrate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_multi_imp_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_diversity_hc, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_outer_bcb_bonus_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pec_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(server_client_show_rate_thanos, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(server_client_show_rate_search, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(server_client_show_rate_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_search_ads_pos_ecpm_relevance_mix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_pos2_rele_thres_pid_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_search_goods_tab_dedup_isolate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_ads_pos_ecpm_relevance_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_diversity_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_diversity_hc_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_second_industry_v5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_freq_hc_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_freq_hc_hard, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(soft_ntr_thr_filter, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_deduplicate_by_creative_id, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_bs_cover_all_industry_live_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_check_cpa_bid_zero, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bs_live_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_white_product_skip_account_bidding, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_audience_explore_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(audience_explore_hc_ratio, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_BOOL(enable_item_card, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(disable_nebula_live_pec_coupon, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_kuaishou_live_pec_coupon, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_multi_level_gpm_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_gpm_thres_for_guess_like, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merge_to_adlist_for_guess_like, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dup_adlist_for_guess_like, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_buyer_homepage_live_soft_queue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_guess_like_rank_dup_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(inspire_native_coin_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inspire_live_roi_control, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_soft_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_photo_for_inspire, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_long_ratio_pay_times_2and7, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_split_7day_paytimes_deep_r, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_user_group_w_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_user_group_w_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_side_window_user_group_w_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_incentive_deep_quit_rate_pred, ks::AbtestBiz::AD_DSP);

// ------------------------------ abtest integer 参数定义 ---------------------------------
SPDM_ABTEST_INT64(app_page_form_cpm_thr_exp_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(photo_quality_base, ks::AbtestBiz::AD_DSP, 4);
SPDM_ABTEST_INT64(innerflow_trade_cpm_thr_exp_tag, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(min_perfetch_ad_size, ks::AbtestBiz::AD_DSP, 5);

SPDM_ABTEST_INT64(inspire_live_custom_coin_upper, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(inspire_live_custom_coin_lower, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_INT64(merchant_coupon_low_price_threshold, ks::AbtestBiz::AD_DSP, 20000);
SPDM_ABTEST_INT64(merchant_coupon_high_price_threshold, ks::AbtestBiz::AD_DSP, 20000);
SPDM_ABTEST_INT64(merchant_coupon_high_price_discount_value, ks::AbtestBiz::AD_DSP, 75);
SPDM_ABTEST_INT64(merchant_coupon_low_price_discount_value, ks::AbtestBiz::AD_DSP, 5000);

SPDM_ABTEST_INT64(follow_ecpm_boost_tail, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(far_history_ad_window, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(recent_history_ad_window, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_INT64(inspire_industry_live_cpm_thr_high, ks::AbtestBiz::AD_DSP, 1);

SPDM_ABTEST_INT64(native_ad_bs_price_lower, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(native_ad_bs_price_upper, ks::AbtestBiz::AD_DSP, 100000);

SPDM_ABTEST_INT64(hc_experience_pxr_enum, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(splash_purchase_hc_const_value, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_INT64(new_spu_dup_photo_cost_rank_thre, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_INT64(live_pec_coupon_discount_amount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(live_pec_coupon_coupon_threshold, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(live_pec_coupon_template_id, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(p2l_pec_coupon_discount_amount, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(p2l_pec_coupon_coupon_threshold, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(p2l_pec_coupon_template_id, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_INT64(merchant_live_gpm_threshold, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(first_gpm_thres, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(second_gpm_thres, ks::AbtestBiz::AD_DSP, 20);
SPDM_ABTEST_INT64(third_gpm_thres, ks::AbtestBiz::AD_DSP, 20);

SPDM_ABTEST_INT64(first_gpm_thres_for_guess_like, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(second_gpm_thres_for_guess_like, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(default_guess_like_gpm_threshold, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_INT64(new_live_author_last_live, ks::AbtestBiz::AD_DSP, 14);
SPDM_ABTEST_INT64(new_live_author_fans_cnt, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_INT64(t0_fans_cnt, ks::AbtestBiz::AD_DSP, 10000);
SPDM_ABTEST_INT64(t1_fans_cnt, ks::AbtestBiz::AD_DSP, 100000);

SPDM_ABTEST_INT64(pec_coupon_random_throttling_factor, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(inner_normal_photo_guess_like_quota, ks::AbtestBiz::AD_DSP, 300);
SPDM_ABTEST_INT64(inner_native_photo_guess_like_quota, ks::AbtestBiz::AD_DSP, 300);

SPDM_ABTEST_INT64(backup_ad_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(outer_bcb_bonus_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(search_ads_pos1_ecpm_relevance_mix_rele_thres_v1, ks::AbtestBiz::AD_DSP, 1.8);
SPDM_ABTEST_DOUBLE(search_ads_pos2_ecpm_relevance_mix_rele_thres_v1, ks::AbtestBiz::AD_DSP, 1.6);
SPDM_ABTEST_DOUBLE(search_ads_pos1_ecpm_relevance_mix_ecpm_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(search_ads_pos2_ecpm_relevance_mix_ecpm_thres, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_white_project_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(white_project_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(splash_bonus_upper, ks::AbtestBiz::AD_DSP, 100000000.0);
SPDM_ABTEST_DOUBLE(live_merchant_ecology_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(soft_ntr_thr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_avg_cpm, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_base_coin, ks::AbtestBiz::AD_DSP, 80.0);
SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_bias_coin, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(inspire_live_custom_coin_alpha, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(inspire_live_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inspire_live_roi_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_bs_auto_bid_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_bs_auto_bid_weight_inspire_live, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_ad_bs_price_ratio_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(nobid_fast_cpm_threshold, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(native_ad_gmv_default_val, ks::AbtestBiz::AD_DSP, 20000.0);
SPDM_ABTEST_DOUBLE(hc_gpm_min_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_gpm_max_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(adload_admit_thresh_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(merchant_roas_coef_double_col, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(self_service_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gamora_ueq_by_vtr_weight, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(nebula_ueq_by_vtr_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gamara_live_vtr_model_ueq_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(nebula_live_vtr_model_ueq_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(gamara_total_ueq_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(nebula_total_ueq_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(leverage_gmv_take_rate, ks::AbtestBiz::AD_DSP, 0.05);
SPDM_ABTEST_DOUBLE(pk_hard_cpm_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.25);
SPDM_ABTEST_DOUBLE(live_rerank_fixed_sctr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(plagiarism_heritage_upper_bound, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(buyer_homepage_cpm_threshold, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(vtr_ueq_upper_bound, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(explore_feed_live_cpm_thr, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(outer_native_juxing_app_advance_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_native_dsp_app_advance_discount, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(speed_fanstop_cpm_max_ratio, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(storewide_roi_hc_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(storewide_roi_hc_ratio_qhc, ks::AbtestBiz::AD_DSP, 1.01);
SPDM_ABTEST_DOUBLE(guess_you_like_cpm_threshold_yuan, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_soft_cpm_threshold_yuan, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(explore_feed_soft_live_cpm_thr, ks::AbtestBiz::AD_DSP, 4.0);
SPDM_ABTEST_DOUBLE(follow_page_rank_ratio, ks::AbtestBiz::AD_DSP, 1.0);


SPDM_ABTEST_INT64(ad_user_experience_hc_pxr_enum, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(other_hc_ntr_source_enum, ks::AbtestBiz::AD_DSP, 1);

SPDM_ABTEST_INT64(live_other_hc_ntr_source_enum, ks::AbtestBiz::AD_DSP, 4);


SPDM_ABTEST_INT64(ntr_hc_source_enum, ks::AbtestBiz::AD_DSP, 1);

SPDM_ABTEST_INT64(live_ntr_hc_source_enum, ks::AbtestBiz::AD_DSP, 4);

SPDM_ABTEST_BOOL(enable_reco_live_ue_for_ntr_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_filter_dup_live_stream_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_live_ue_shard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(reco_live_ue_shard_num, ks::AbtestBiz::AD_DSP, 15);


SPDM_ABTEST_BOOL(enable_ad_sensitive_rct_hc_thanos_gamora, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_sensitive_rct_hc_thanos_nebula, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_sensitive_rct_hc_quantile_by_request_cpm, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_fixed_score_thanos_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_fixed_score_thanos_nebula, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_request_cpm_quantile_thanos_gamora, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_request_cpm_quantile_thanos_nebula, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_thanos_gamora, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_thanos_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_hard, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ad_sensitive_rct_hc_score_weight_fanstop, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_crc_ue_low_nogap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_reco_pxtr_model_all_pages, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_reco_pxtr_model_other_pages, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_reco_pxtr_model_inner_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_reco_pxtr_model_feed_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(inner_explore_reco_prerank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "grpc_mergeTowerFountainInferForAd");  // NOLINT
SPDM_ABTEST_STRING(feed_explore_reco_prerank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "grpc_mergeTowerHotInferForAd");  // NOLINT
SPDM_ABTEST_BOOL(enable_reco_pxtr_model_grpc_map, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(hc_jump_out_rate_source_enum, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_age_filter, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_request_times_suppress, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_refresh_times_suppress, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_times_suppress_weight, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(jump_out_rate_ocpc_action_request_times_filter, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_BOOL(reco_htr_whitelist_use_sub_page_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ntr_filter_by_reco_htr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ntr_filter_by_reco_htr_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_ntr_filter_by_reco_htr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_hard_ntr_filter_by_reco_htr_live, ks::AbtestBiz::AD_DSP);






SPDM_ABTEST_BOOL(enable_longvalue_auto_param_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(hard_nebula_user_mobile_price_upper_bound, ks::AbtestBiz::AD_DSP, 2000);

SPDM_ABTEST_INT64(hard_nebula_user_active_decrease_days, ks::AbtestBiz::AD_DSP, 7);




SPDM_ABTEST_DOUBLE(hard_explore_user_age_segment_list_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_DOUBLE(hard_explore_user_active_degree_enum_list_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_INT64(hard_explore_user_mobile_price_upper_bound, ks::AbtestBiz::AD_DSP, 2000);
SPDM_ABTEST_DOUBLE(hard_explore_user_mobile_price_upper_bound_cpm_thr, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_INT64(hard_explore_inner_user_mobile_price_upper_bound, ks::AbtestBiz::AD_DSP, 2000);

SPDM_ABTEST_BOOL(enable_ueq_by_pxtr_w_level, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(hc_gpm_default, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_gpm_take_rate, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(guess_you_like_rank_gmv_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(guess_you_like_rank_gmv_ratio_for_hard, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_gpm_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_gpm_avg, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_experience_avg, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_experience_cpm_trans, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_experience_min_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_experience_max_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(hc_experience_pxr_min, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_experience_pxr_max, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(traffic_hc_record_pp, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(server_client_show_rate_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(hc_max_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(splash_purchase_hc_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(hc_min_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
// industry diveristy
SPDM_ABTEST_DOUBLE(recent_ratio_neg, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(far_ratio_neg, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(ratio_positive, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(soft_freq_hc_ratio_upper, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(soft_freq_hc_ratio_lower, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(soft_freq_hc_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(soft_freq_hc_coef_native, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_STRING(freq_hc_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_native_jump_ratio_cpm_thre, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(jump_out_cpm_max_thre, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(jump_out_cpm_min_thre, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(jump_ratio_cvr_num_threshold, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(jump_decay_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(jump_ratio_total_cvr, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(first_industry_id_skip, ks::AbtestBiz::AD_DSP, -1);
// 外跳屏蔽实验
SPDM_ABTEST_BOOL(enable_jump_out_ads_fliter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(jump_ratio_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(max_jump_ratio_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_low_quality_photo_fliter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_melt_exp_fliter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(random_drop_out_ads, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(reco_vtr_thre, ks::AbtestBiz::AD_DSP, 0.0);
// 双端引流
SPDM_ABTEST_BOOL(enable_native_double_end_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_double_end_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(double_end_fix_cpm_thre, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_outer_live_twin_bid_flow, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(button_click_simple_room_begin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(button_click_cpa_price_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_button_click_twin_bid_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_button_click_twin_bid_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(button_click_twin_bid_alpha, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(button_click_twin_bid_beta, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(button_click_twin_bid_hour, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(button_click_twin_bid_alpha_max, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(button_click_twin_bid_alpha_min, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_BOOL(enable_button_click_twin_bid_strategy_with_param_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(button_click_twin_bid_exp_name, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_button_click_twin_bid_strategy_with_action_kconf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(button_click_twin_bid_action_exp_name, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_button_click_twin_bid_mcb_strategy, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(coldstart_hc_u4_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(coldstart_hc_u3_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(coldstart_hc_u0_coef, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_DOUBLE(non_mix_thanos_native_cpm_threshold_yuan, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(merchant_rank_gmv_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(account_id_cpm_thr_ratio_soft_v2, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_DOUBLE(buyer_homepage_soft_queue_cpm_thr, ks::AbtestBiz::AD_DSP, 3.0);
SPDM_ABTEST_DOUBLE(live_default_gpm, ks::AbtestBiz::AD_DSP, 6.0);
SPDM_ABTEST_DOUBLE(ad_direct_merchant_gpm_hc_min_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(ad_direct_merchant_gpm_hc_max_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_hc_max_cpm_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_hc_min_cpm_ratio, ks::AbtestBiz::AD_DSP, -1.0);

// 付费次数参数
SPDM_ABTEST_DOUBLE(pay_times_weight_1d, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(pay_times_weight_2and7, ks::AbtestBiz::AD_DSP, 1.0);
// 付费金额
SPDM_ABTEST_DOUBLE(pay_amount_weight_1d, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(pay_amount_weight_2and7, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_pay_amount_2and7, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_30d_ltv_load, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_long_ratio_seven_day_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(upper_origin_pay_amount_7d, ks::AbtestBiz::AD_DSP, 2000.0);
// 短剧付费曝光打平
SPDM_ABTEST_BOOL(enable_duanju_purchase_ratio, ks::AbtestBiz::AD_DSP);
// 长线纠偏系数
SPDM_ABTEST_BOOL(enable_seven_day_longratio_cali_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(seven_day_longratio_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ad_roas_longratio_cali_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ad_roas_longratio_cali_ratio, ks::AbtestBiz::AD_DSP, 1.0);
// 混排 gpm 迁移开关
SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_add_multi_head_mix_gpm_photo, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(upper_r_long_ratio_7d_repurchase_param, ks::AbtestBiz::AD_DSP, 30.0);

SPDM_ABTEST_STRING(revert_bonus_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(fanstop_inner_region_org_exp_types, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(inspire_live_bias_ratio_config, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(inspire_live_piecewise_linear_config, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(inspire_live_aucbid_ratio_by_ocpx, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(seq_exp_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(diverisity_hc_exp_tag, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(multi_level_exp_group_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(sdpa_item_explore_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(dnc_exp_tag, ks::AbtestBiz::AD_DSP, "default");
// [zhangruyuan]
SPDM_ABTEST_BOOL(enable_target_request_pid_server, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_STRING(live_gpm_unified_exp_tag, ks::AbtestBiz::AD_DSP, "base");

SPDM_ABTEST_STRING(unify_adload_total_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(unify_adload_ft_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(unify_adload_ad_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(unify_adload_industry_ori_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(game_shoufa_force_exp_tag, ks::AbtestBiz::AD_DSP, "base");

SPDM_ABTEST_STRING(coldstart_tiered_strategy_exp_tag, ks::AbtestBiz::AD_DSP, "exp1");
SPDM_ABTEST_STRING(coldstart_hc_strategy_exp_tag, ks::AbtestBiz::AD_DSP, "exp1");
SPDM_ABTEST_STRING(coldstart_model_p2l_bonus_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(coldstart_model_photo_bonus_exp_tag, ks::AbtestBiz::AD_DSP, "base");

SPDM_ABTEST_STRING(clue_acquisition_hc_expgroup, ks::AbtestBiz::AD_DSP, "default");
SPDM_ABTEST_STRING(user_group_w_cpm_thr_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(follow_user_group_w_cpm_thr_exp_tag, ks::AbtestBiz::AD_DSP, "follow");
SPDM_ABTEST_STRING(side_window_user_group_w_cpm_thr_exp_tag, ks::AbtestBiz::AD_DSP, "side_window");
// ---------------------- 内粉专用 参数声明区域（防 git 冲突） -------------------
// ---------------------- abtest bool 参数声明 -------------------
SPDM_ABTEST_BOOL(enable_fanstop_rank_high_priority_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_cpm_new_threshold_yuan, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_bonus_after_process_immg, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_zero_bonus_tag_setting, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_second_predict_use_new_paid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_sta_tag_refill, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outerloop_nc_max_force_reco_topn, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_rank_load_outerloop_interest_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outerloop_nc_max_goal_ocpx, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(outerloop_nc_max_goal_industry, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_mark, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outerloop_low_active_ac_mark_update, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outerloop_cc_nc_max_topn, ks::AbtestBiz::AD_DSP, 10);
// ---------------------- abtest int64 参数声明 -------------------
SPDM_ABTEST_INT64(fanstop_rank_high_priority_photo_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(fanstop_rank_high_priority_live_quota, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(outerloop_nc_max_overimpression_threshold, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(outerloop_ac_retrieval_diversity_threshold, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(outerloop_low_active_ac_thresh, ks::AbtestBiz::AD_DSP, 30);
// ---------------------- abtest double 参数声明 -------------------
SPDM_ABTEST_DOUBLE(fanstop_cpm_new_threshold_yuan, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(brand_fanstop_cpm_new_threshold_yuan, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(feed_brand_fanstop_cpm_new_threshold_yuan, ks::AbtestBiz::AD_DSP, 4.0);
SPDM_ABTEST_DOUBLE(speed_fanstop_cpm_new_threshold_yuan, ks::AbtestBiz::AD_DSP, 5.0);
SPDM_ABTEST_DOUBLE(feed_speed_fanstop_cpm_new_threshold_yuan, ks::AbtestBiz::AD_DSP, 4.0);
SPDM_ABTEST_DOUBLE(fanstop_cvr_threshold_ratio_order_paied, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fanstop_cvr_threshold_ratio_leads_submit, ks::AbtestBiz::AD_DSP, 1.0);
// ---------------------- kconf bool 参数声明 -------------------

// ---------------------- kconf int 参数声明 -------------------
// ---------------------- kconf double 参数声明 -------------------

// ---------------------- kconf string 参数声明 -------------------



SPDM_ABTEST_BOOL(enable_dsp_pec_second_deep_reward, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsp_live_deep_reward, ks::AbtestBiz::AD_DSP);  // [lizemin] 信息流直播激励
SPDM_ABTEST_BOOL(enbale_online_calibration_with_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_online_calibration_with_cmd_order_paied, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_award_play_and_invoked_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_award_active_app_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_calc_client_ai_rerank_score_adrank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rewarded_next_n_ad, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(rewarded_next_n_ad_price_thr, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(rewarded_next_n_ad_roi_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(client_ai_high_value_use_cpm_or_rankbenifit, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(client_ai_high_value_use_threshold_value, ks::AbtestBiz::AD_DSP, 1000000);
SPDM_ABTEST_BOOL(enable_storewide_live_u_level_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_model_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_block_lower_u, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_page_limit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_r, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_q, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_fix_cali_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_page_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_esp_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_fix_cvr_upper_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_cali_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_storewide_live_uplift_author_exp_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(storewide_live_uplift_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(storewide_live_uplift_p2_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(storewide_write_request_spuid_to_redis_expire, ks::AbtestBiz::AD_DSP, 300);
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, minPriceBound, 0);
SPDM_KCONF_INT64(ad.adFrontDiffSwitches, maxPriceBound, 1000000);
SPDM_ABTEST_INT64(follow_max_price, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_INT64(follow_fanstop_v2_max_price, ks::AbtestBiz::AD_DSP, 8000);
SPDM_ABTEST_INT64(wanhe_max_price, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_INT64(wanhe_fanstop_v2_max_price, ks::AbtestBiz::AD_DSP, 8000);
SPDM_ABTEST_INT64(max_amd_photo_ranking_list_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_amd_photo_ranking_list_size, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(invo_traffic_rank_redis_nums, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(max_native_ranking_list_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(max_native_ranking_list_size, ks::AbtestBiz::AD_DSP, 200);
SPDM_ABTEST_INT64(max_hard_ad_force_reco_tag_size, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_force_reco_sort_by_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(force_reco_rb_thrd, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_BOOL(enable_force_reco_rb_thrd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_front_duanju_force_reco_avoid_same_second_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(max_native_ad_force_reco_tag_size, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_qcpx_p2p_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_quota_logic_split, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_reco_mark_unify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_constrain_high_quality_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_constrain_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_no_constrain, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_unify_rank_size, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_feed_explore_rank_quota_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ad_rank_unify_result_size, ks::AbtestBiz::AD_DSP, 70);
SPDM_ABTEST_INT64(normal_size_for_move_auction, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_BOOL(enable_front_auction_move_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_online_calibration_cmd_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_online_calibration_conv_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cid_hc_cpm_default_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(cid_hc_cpm_default_exp_ratio, ks::AbtestBiz::AD_DSP, 0.1);

// 外循环行业直播相关参数
SPDM_ABTEST_BOOL(enable_request_direct_live_lps_realtime, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(direct_live_lps_realtime_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(direct_live_lps_base_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.5);

SPDM_ABTEST_BOOL(enable_dsplive_direct_conv_bypass_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsplive_direct_lps_bypass_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsplive_ctr_bypass_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsplive_p2l_conv_bypass_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsplive_p2l_lps_bypass_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dsplive_p2l_ctr_bypass_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_atomization_order, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_atomization_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_atomization_t7roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_atomization_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_atomization_t7_switch, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_key_action2_dsp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_local_life_kbox_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_rank_result_ad_queue_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_candidate_ack_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_candidate_ack_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_force_reco_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_incentive_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_incentive_fanstop_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_force_reco_inner_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_force_reco_outer_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_industry_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_industry_explore_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_user_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_user_explore_duanju_constant, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_user_explore_duanju_model_delta, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_industry_user_explore_jiaotong, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jiaotong_low_pay_cvr_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jiaotong_dynamic_low_pay_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_jiaotong_dynamic_low_pay_filter, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_clue_acquisition_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_clue_acquisition_hc_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(clue_acquisition_hc_ratio_single, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(clue_acquisition_hc_ratio_twin, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(effective_acquisition_coef_single_bid, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(effective_acquisition_coef_twin_bid, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(clue_support_hc_w, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_lpsdeep_support, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_edu_lps_deep_support, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(paied_course_order_paied_coeff, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_BOOL(enable_industry_user_explore_realestate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(realestate_hc_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(realestate_hc_uservalue, ks::AbtestBiz::AD_DSP, 32505856);
SPDM_ABTEST_STRING(realestate_deepopt_expid, ks::AbtestBiz::AD_DSP, "default");

SPDM_ABTEST_DOUBLE(outer_live_cpm_thr, ks::AbtestBiz::AD_DSP, 5.0);

SPDM_ABTEST_BOOL(enable_explore_inner_uplift_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_inner_user_value_hard_ad_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_inner_user_value_soft_ad_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_soft_uplift_score, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ecpm_threshold_explore_inner_native, ks::AbtestBiz::AD_DSP, 6.0);

SPDM_ABTEST_BOOL(enable_guess_you_like_impression_charge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sctr_default_mobile_guess_you_like, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(mobile_guess_you_like_sctr_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_guess_you_like_max_rank_quota, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(guess_you_like_max_rank_quota, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_BOOL(enable_follow_u4_tijia_jifei, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_u4_tijia_bujifei, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_bid_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_consum_power_price_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_high_value_u_level, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(high_value_atv_thresh, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(inner_live_default_gpm, ks::AbtestBiz::AD_DSP, 55);
SPDM_ABTEST_STRING(inner_high_value_visitor_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_inner_industry_frue, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(u4_tijia_tail, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_DOUBLE(follow_u4_tijia_jifei_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(follow_u4_tijia_jifei_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(follow_u4_tijia_bujifei_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(follow_u4_tijia_bujifei_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_pk_use_index, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(reward_merchant_roas_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(reward_merchant_orderpay_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_dup_hc_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(dup_exp_hc, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_original_photo_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(dup_skip_hc_originality_thresh, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(dup_skip_hc_bound_thresh, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(original_hc_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(original_shield_hc_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(original_exp_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(dup_bound_hc_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_feature_index_req_all_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(photo_cpm_ue_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(game_high_value_explore_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(wegame_nc_explore_bonus_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(game_high_ltv_bonus_effect_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_game_high_ltv_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_high_ltv_bonus_effect_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(kminigame_high_ltv_bonus_effect_tag, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(kminigame_high_ltv_bonus_effect_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_game_long_value_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_long_value_explore_launch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_long_value_explore_constant_hard, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_long_value_explore_constant_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_long_value_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_conversion_rate_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_ltv_beta, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_long_value_ensemble_a, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_long_value_ensemble_c, ks::AbtestBiz::AD_DSP, 0.0);
// 游戏 30 日 ltv 参数
SPDM_ABTEST_BOOL(enable_request_game_30d_ltv_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_30d_ltv_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_30d_ltv_global_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_30d_ltv_decay_weight, ks::AbtestBiz::AD_DSP, 0.99);
SPDM_ABTEST_INT64(game_30d_ltv_threshold, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_DOUBLE(game_30d_ltv_cpm_exp_ratio, ks::AbtestBiz::AD_DSP, 0.0);

// 游戏首冲党策略参数
SPDM_ABTEST_BOOL(enable_request_game_player_only_first_charge_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_player_only_first_charge_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_decay_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_INT64(game_player_only_first_charge_ecpc_req_threshold, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_coeff, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_lower_bound, ks::AbtestBiz::AD_DSP, 0.8);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_upper_bound, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_STRING(game_player_only_first_charge_ecpc_exp_name, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_INT64(game_player_only_first_charge_ecpc_key_type, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(game_player_only_first_charge_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_INT64(outer_live_game_conv_ecpc_req_threshold, ks::AbtestBiz::AD_DSP, 500);

SPDM_ABTEST_BOOL(enable_outer_live_fan_force, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_product_model_lab_score, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_initial_release_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_retarget_force_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(initial_release_base_prob, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(initial_release_prob_max, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(initial_release_prob_min, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(init_account_purchase_roi_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(drop_account_purchase_roi_coef, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(drop_min_account_purchase_roi_coef, ks::AbtestBiz::AD_DSP, -1.0);
SPDM_ABTEST_DOUBLE(drop_adload_admit_thresh_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(hc_update_bound, ks::AbtestBiz::AD_DSP, 0.05);
SPDM_ABTEST_DOUBLE(hc_update_coef, ks::AbtestBiz::AD_DSP, 0.4);
SPDM_ABTEST_DOUBLE(industry_default_ltv_hc_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(industry_default_target_roi, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(industry_purchase_ltv_hc_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(industry_purchase_roi_hc_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(industry_roi_hc_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(native_purchase_ltv_hc_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_purchase_roi_hc_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_roi_hc_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(industry_default_ltv_hc_filter_factor, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(follow_cpm_threshold, ks::AbtestBiz::AD_DSP, 4.0);
SPDM_ABTEST_BOOL(enable_use_gpm_for_guess_you_like_hard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_sub_page_id_gpm_for_guess_like, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_request_7r_conv_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(admit_7r_conv_ltv_ocpcs, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(sub_page_id_gmv_ratio_exp_tag_for_guess_like, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_industry_hc_pid_splittest_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(industry_hc_pid_splittest_exp_tag, ks::AbtestBiz::AD_DSP, 0);

SPDM_ABTEST_BOOL(enable_duanju_relative_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(duanju_relative_hc_alpha, ks::AbtestBiz::AD_DSP, 0.0);

SPDM_ABTEST_STRING(admit_pay_times_ocpcs, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_DOUBLE(tongxin_expore_hc_limit, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(tongxin_expore_hc_alpha, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_model_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_photo_soft_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mobile_soft_to_hard_auction_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mobile_soft2hard_max_auciton, ks::AbtestBiz::AD_DSP, 3000000.0);
SPDM_ABTEST_DOUBLE(model_explore_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(model_explore_hc_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(model_explore_filter_size, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_age_hc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_po_quan_bcee_strategy, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_game_roi_request_ltv27, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_duanju_close_polaris, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(admit_2_7d_pay_ltv_ocpcs, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_rank_benefit_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(default_rank_benefit_thresh, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_corporation_ceiling_rank_force_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_native_gpm_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(account_bidding_stable_hc_ratio_hard, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(account_bidding_stable_hc_ratio_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_account_bidding_stable_hc_ratio_hard, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_account_bidding_stable_hc_ratio_soft, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_ecpc_click_2_purchase_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_log_game_polaris_hc_pacing_ratio, ks::AbtestBiz::AD_DSP);
// [wangyunli] 外循环下发数据流 cpm 修正只使用 server show
SPDM_ABTEST_BOOL(enable_outer_delivery_sample_cpm_fix_with_server_show, ks::AbtestBiz::AD_DSP);
// [wangyunli] 外循环下发率数据流相关
SPDM_ABTEST_BOOL(enable_outer_sampler_judge_by_target_ad_num, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_sampler_pre_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_sample_creatives_replace_rank, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_disable_soft_user_group_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_online_calibration_with_cmd_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_native_roas_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enbale_gyl_calibration, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_item_card_merchant_product_id_hard_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_spu_0_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_roas_item_id_hard_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_roas_jichen_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_roas_item_id_soft_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_merchant_product_id_0_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_jichen_cvr_upper_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(item_card_jichen_cvr_upper_bound, ks::AbtestBiz::AD_DSP, 0.1);

SPDM_ABTEST_BOOL(enable_item_card_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_jichen_add_ocpc_action_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_item_card_jichen_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_soft_queue_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_spu_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(item_card_jichen_rate_delta, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_item_card_author_spu_jichen, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(item_card_not_jichen_cali_rate, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_item_card_not_jichen_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_not_jichen_cali2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_native_order_pay_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_order_pay_cali_ad_queue_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_roas_cali_ad_queue_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(roas_calibrate_smart_bidding_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
// [yuanyue03] 外循环表单独立校准参数
SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(lps_calibrate_smart_bidding_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);


SPDM_ABTEST_BOOL(enable_roas_native_use_imp_ltv_switch_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_native_game_gpm_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(outer_hard_game_gpm_cpm_ratio, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(game_ecpc_decay_weight, ks::AbtestBiz::AD_DSP, 0.99);
SPDM_ABTEST_INT64(game_ecpc_threshold, ks::AbtestBiz::AD_DSP, 500);
SPDM_ABTEST_BOOL(enable_outer_live_native_ueq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_live_native_ueq_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_invo_traffic_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invo_traffic_cali_top_layer_emb_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(invo_traffic_cali_model_emb_size, ks::AbtestBiz::AD_DSP, 790);
SPDM_ABTEST_DOUBLE(invo_traffic_cali_model_cvr_max_diff_percent, ks::AbtestBiz::AD_DSP, 0.3);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower_outer, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper_outer, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower_outer, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper_outer, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(native_bs_auto_bid_weight_outer, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_rank_bs_outer_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_bs_outer_ratio_native, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_bs_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_bs_outer_soft_photo, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_bs_exp_inner_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_lower_v2, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(hard_separate_billing_ratio_upper_v2, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_lower_v2, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(native_separate_billing_ratio_upper_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(separate_billing_auto_bid_weight_v2, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_BOOL(enable_experience_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(experience_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_outer_game_invoke_link, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_u_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_u_cvr_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(enable_outer_u_cvr_bound_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_outer_u_cvr_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_u_cvr_old_request_close, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_invoke_splash_cross_auc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_playlet_purchase_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_button_click, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_button_click_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_playtime, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_lps_playtime, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_playtime_strategy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_id_tail_number, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_live_ocpc_conf, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(outer_live_playtime_strategy_exp_name, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_ad_dsp_live_wx, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_wx2, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_fix_ad_live_no_simplified_live_room, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_lps_photo_to_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_live_lps_direct_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adload_adjust_cpm_thr_fixed, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_feed_live_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_feed_soft_cpm_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_explore_feed_soft_live_cpm_thr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(spu_weight_exp_tag_for_splash_inner_ad, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(big_promotion_gpm_hc_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_week_retention_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_print_factor_info, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecpc_whitelist_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ecpc_max_min, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_ecpc_max_min, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_and_hard_pk, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(soft_and_hard_pk_type, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_mix, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_jili, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_feed, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ecpc_max_min_ratio_follow, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_update_rta_second_predict_source_type_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_fanstop_feed_model_sctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_follow_fanstop_thanos_model_sctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_follow_cost_ratio_account_black_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(fix_follow_cost_ratio_account_black_list, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(new_mix_rank_bid_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(search_adx_adjust_boost, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_DOUBLE(search_live_tab_pos_1_relevance_threshold_v3, ks::AbtestBiz::AD_DSP, 1.6);
SPDM_ABTEST_DOUBLE(search_live_tab_pos_2_relevance_threshold_v3, ks::AbtestBiz::AD_DSP, 0.6);

SPDM_ABTEST_DOUBLE(search_shop_tab_pos_1_relevance_threshold_v4, ks::AbtestBiz::AD_DSP, 1.7);
SPDM_ABTEST_DOUBLE(search_shop_tab_pos_2_relevance_threshold_v4, ks::AbtestBiz::AD_DSP, 1.6);

SPDM_ABTEST_DOUBLE(search_ads_postition_1_rele_threshold_double_v1, ks::AbtestBiz::AD_DSP, 1.8);
SPDM_ABTEST_DOUBLE(reco_after_search_rele_threshold, ks::AbtestBiz::AD_DSP, 1.5);

SPDM_ABTEST_BOOL(enable_product_ltr_write_samples, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_mix_unify_gpm_pass, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rta_send_feature_id, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(search_item_card_bonus_combo_search, ks::AbtestBiz::AD_DSP, 75000000);
SPDM_ABTEST_INT64(search_item_card_bonus_goods_tab, ks::AbtestBiz::AD_DSP, 10000000);
SPDM_ABTEST_BOOL(enable_combo_search_page0_kbox_item_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_multi_logic_table, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_posterior_async_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_strong_boost_plugin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_last_page_size_for_first_pos_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_last_page_size_for_first_pos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_cpm_for_bonus_contain_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_cpc_boost_align, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_mcb_boost_align, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(search_modify_ratio_lower_bound, ks::AbtestBiz::AD_DSP, 0.1);
SPDM_ABTEST_DOUBLE(search_modify_ratio_upper_bound, ks::AbtestBiz::AD_DSP, 20.0);
SPDM_ABTEST_DOUBLE(rank_benifit_strong_sctr_bound, ks::AbtestBiz::AD_DSP, 2000.0);
SPDM_ABTEST_BOOL(disable_search_boost_ctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_regulate_boost_scope, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_modify_ratio_boost_style_only, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fanstop_dup_live_stream_id_fix, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_first_page_relevance_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_strong_card_choose_merge, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(search_first_page_photo_rele_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(search_first_page_live_rele_thr, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(disable_search_multi_list_inner_sort, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_fanstop_bid_boost_guard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(search_item_card_support_cpa_bid_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(search_item_card_support_mcb_cpa_bid_ratio, ks::AbtestBiz::AD_DSP, 0.6);
SPDM_ABTEST_BOOL(enable_search_prerank_hard_sample, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_goods_and_kbox_remove_ctr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_splash_boost_coef_immg, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_ecpc_switch, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_multi_opt_boost, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_multi_opt_boost_ltv1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_game_retarget_multi_opt_boost_nodiff, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(mini_game_retarget_ratio_max, ks::AbtestBiz::AD_DSP, 10.0);
SPDM_ABTEST_DOUBLE(mini_game_retarget_ratio_min, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(mini_game_retarget_purchase_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(mini_game_retarget_conv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_retarget_ecpc_boost_const, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_retarget_ecpc_discount_const, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_retarget_ecpc_avg_value_const, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_retarget_ecpc_boost_max_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_retarget_ecpc_discount_min_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(uaa_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(ual_ratio, ks::AbtestBiz::AD_DSP, 1.0);

// [jiangyuzhen03]
SPDM_ABTEST_BOOL(enable_unify_ecpm_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__1_next_stay_filter, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__2_zhutui_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__3_juxing_thres, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__4_native_ecpm_upper_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__5_follow_u4_zoom, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__6_billing_separate, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__7_old_ecpm_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__8_cpm_origin, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__9_same_product_penalty, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_ecpm_strategy__10_follow_charge_bonus, ks::AbtestBiz::AD_DSP);
// [jiangyuzhen03] end

SPDM_ABTEST_BOOL(enable_game_retarget_ecpc_boost_multitag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_retarget_ecpc_boost_multitag_const, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_BOOL(enable_search_duanju_independent, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outside_loop_server_client_show_rate_nebula, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outside_loop_server_client_show_rate_thanos, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_search_sctr_all_pos, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_sctr_all_pos_calc_benefit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_sctr_pos0, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(search_diversion_bid_boost_exp, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_search_pos_ctr_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_goods_search_gmv_callback, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_qcpx_live_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_qcpx_rearward, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_fix_ad_list_copy, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_reco_avoid_same_second_industry, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_force_reco_cpm_ratio_threshold, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_baokuanju_dynamic_coef_adjustment, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_coef_adjustment_with_t12, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_dynamic_coef_adjustment_with_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(playlet_dynamic_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(playlet_reco_retarget_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_ecpc_ratio_combine, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_bonus_amount_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_playlet_reco_retarget, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_reco_retarget_not_theme, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_good_soft_ad_new_standard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_good_native_photo_new_standard, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_old, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_soft_ad_disable_ad_mark_with_review_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_prerank_add_candidate_feature_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(search_rank_max_rsp_size, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_BOOL(enable_search_celebrity_bonus_normal, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_celebrity_bonus_authorname, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_explore_sim_rank, ks::AbtestBiz::AD_DSP);

// [chenxian] 短剧 iap 切分点
SPDM_ABTEST_BOOL(enable_playlet_iap_cut_point_to_click, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iap_cut_point_to_click_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iap_cvr_bound, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_iap_add_noctcvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_rank_playlet_click_pay_purchase, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_click_pay_purchase_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);

// [liyongchang] 短剧 iaa 实时校准到全归因的变换系数
SPDM_ABTEST_BOOL(enable_ad_dsp_playlet_iaa_ltv_transform, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_iaa_ltv_transform_upper_bound, ks::AbtestBiz::AD_DSP, 12.0);
SPDM_ABTEST_DOUBLE(playlet_calibration_ratio_max, ks::AbtestBiz::AD_DSP, 10.0);

// [jianghao07] 优质原生 nogap 新链路
SPDM_ABTEST_BOOL(enable_new_native_quality_status, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_native_quality_status_v2, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_outer_context_aware, ks::AbtestBiz::AD_DSP);

// [jianghao07] 优质原生价值验证
SPDM_ABTEST_BOOL(enable_remove_high_quality, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_remove_nogap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_remove_inner_nogap, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_remove_outer_nogap, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_outer_industry_boost_ranking, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_revenue_splash_modify, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_revenue_splash_sunset, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(outer_industry_boost_name, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_STRING(playlet_ecpc_exp_whitelist_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_DOUBLE(playlet_ecpc_lower_bound, ks::AbtestBiz::AD_DSP, 0.6);
SPDM_ABTEST_DOUBLE(playlet_ecpc_upper_bound, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_BOOL(enable_soft_ad_clue_predict_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_new_key_compatible, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_live_follow_rm_invalid_request_2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_chongdingxiang, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_feature_index_use_dragon_rpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(key_action_ltv_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(key_action_ltv_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(key_action_ltv_ecpc_gamma, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_win_auction_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_ecpc_lower, ks::AbtestBiz::AD_DSP, 0.7);
SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.3);
SPDM_ABTEST_DOUBLE(playlet_key_action_ltv_ecpc_gamma, ks::AbtestBiz::AD_DSP, 0.25);
SPDM_ABTEST_BOOL(enable_new_product_category_for_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(key_action_ltv_win_auction_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_new_inspire_soft_calibration_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_inspire_soft_calibration_order_paied, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_check_cmd_conflict_before, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_prerank_add_carm_to_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_inspire_soft_calibration_storewide, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_prerank_add_feature_to_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_prerank_add_feature_to_rank_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_sample_remove_adx_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_no_ltv_bid_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_log_rank_benefit, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_wentou_gimbal_ratio_dynamic, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_iaa_7_day_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_outer_cmd_independent, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_pay_split_to_impression, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_search_independent_industry_live, ks::AbtestBiz::AD_DSP);
// [liyuanqing]
SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict_ltv_new_normal, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict_ltv_old, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_only_playlet_invoked_model_predict_account, ks::AbtestBiz::AD_DSP);
// [chenxian]
SPDM_ABTEST_BOOL(enable_playlet_industry_conv2ltv_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_stop_playlet_origin_industry_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_playlet_uplift_cvr_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_model_ensemble_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_playlet_uplift_model_ensemble_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_two_head_cbu_admit_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_use_mid_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_use_industry_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_update_unify_ltv_v1, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_update_unify_ltv_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(playlet_uplift_admit_threshold_ratio, ks::AbtestBiz::AD_DSP, 2.0);
SPDM_ABTEST_DOUBLE(playlet_uplift_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(playlet_uplift_model_origin_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(playlet_uplift_model_uplift_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.2);
SPDM_ABTEST_DOUBLE(playlet_uplift_model_origin_ltv_ratio_mcb, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(playlet_uplift_model_uplift_ltv_ratio_mcb, ks::AbtestBiz::AD_DSP, 1.1);
SPDM_ABTEST_STRING(C_activity, ks::AbtestBiz::AD_DSP, "ActivityA");

SPDM_ABTEST_STRING(multi_smart_offer_gear_info,
            ks::AbtestBiz::AD_DSP, "1.9,0.9,9;3.9,1.9,20;4.9,1.9,20;5.9,2.4,25;7.9,2.9,35;9.9,3.9,45");
SPDM_ABTEST_STRING(multi_smart_offer_gear_info_dnc,
            ks::AbtestBiz::AD_DSP, "1.9,0.9,9;3.9,1.9,20;4.9,1.9,20;5.9,2.4,25;7.9,2.9,35;9.9,3.9,45");
SPDM_ABTEST_STRING(normal_multi_smart_offer_gear_info_dnc, ks::AbtestBiz::AD_DSP,
                "1.9,0.9,9;3.9,1.9,20;4.9,1.9,20;5.9,2.4,25;7.9,2.9,35;9.9,3.9,45");
SPDM_ABTEST_STRING(multi_smart_offer_gear_info_doc,
            ks::AbtestBiz::AD_DSP, "1.9,0.9,9;3.9,1.9,20;4.9,1.9,20;5.9,2.4,25;7.9,2.9,35;9.9,3.9,45");
SPDM_ABTEST_STRING(normal_multi_smart_offer_gear_info_doc, ks::AbtestBiz::AD_DSP,
                "1.9,0.9,9;3.9,1.9,20;4.9,1.9,20;5.9,2.4,25;7.9,2.9,35;9.9,3.9,45");
SPDM_ABTEST_BOOL(enable_playlet_uplift_gear_info_by_user_type, ks::AbtestBiz::AD_DSP);
// 1.9,0.9,9;3.9,1.9,20;4.9,1.9,20;5.9,2.4,25;7.9,2.9,35;9.9,3.9,45
SPDM_ABTEST_STRING(multi_smart_offer_value_ratio,
            ks::AbtestBiz::AD_DSP, "1.0,1.0,1.0,1.0,1.0,1.0");
SPDM_ABTEST_BOOL(enable_write_playlet_multi_smart_offer_2_context, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_mcb_independent_ratio, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_model_use_one_head_cvr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_model_use_one_head_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_playlet_uplift_model_stop_cbu_ecpc, ks::AbtestBiz::AD_DSP);

// [chenxian random cbu]
SPDM_ABTEST_BOOL(enable_playlet_smart_offer_default_by_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_random_stop_playlet_smart_offer_by_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_dnc, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(random_stop_playlet_smart_offer_ratio_doc, ks::AbtestBiz::AD_DSP, 10);

SPDM_ABTEST_BOOL(enable_random_playlet_smart_offer_by_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_dnc, ks::AbtestBiz::AD_DSP, 10);
SPDM_ABTEST_INT64(random_playlet_smart_offer_ratio_doc, ks::AbtestBiz::AD_DSP, 10);
// [chenxian multi uplift]
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_cmd_h6, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_ltv_cmd_h6, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_cmd_h5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_cmd_h4, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_cvr_cmd_h6, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_cvr_cmd_h6, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_cvr_cmd_h5, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_cvr_cmd_h4, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_model_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_normal_smart_offer_multi_uplift_model_rank, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_personal_offer_value, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_roi_threshold, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_origin_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_dnc_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(smart_offer_multi_doc_ltv_ratio, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_max_bonus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_max_roi, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_update_unify_ltv, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.5);
SPDM_ABTEST_DOUBLE(playlet_cartoon_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.51);
SPDM_ABTEST_BOOL(enable_cartoon_ensemble_weight, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_ltv_without_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_one_head_ltv_without_cbu, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(smart_offer_multi_uplift_dnc_max_dangwei_index, ks::AbtestBiz::AD_DSP, 1000);
SPDM_ABTEST_INT64(smart_offer_multi_uplift_doc_min_dangwei_index, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(smart_offer_multi_uplift_nouse_dangwei_index, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_STRING(playlet_cbu_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_change_playlet_conversion_purchase_add, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_smart_offer_multi_uplift_guaranteed, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(smart_offer_multi_uplift_ltv_threshold, ks::AbtestBiz::AD_DSP, 10000000.0);
// [cuirunpeng] 短视频内循环新校准开关及参数
SPDM_ABTEST_BOOL(enable_new_online_calibration_with_cmd_order_paied, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_inner_native_roas_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_new_calibration_params_native_roas, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(native_roas_new_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_roas_new_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_roas_new_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_roas_new_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_new_inner_native_order_pay_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_roas_online_calibration_by_stage_one_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_native_roas_online_calibration_by_stage_one_cmd, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_tube_to_inner_loop_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_item_card_order_paied_cali, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_new_order_paied_cmd_for_mall, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_new_order_paied_cmd_for_zhuanqian, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_use_new_order_paied_cmd_for_buyer_home, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_storewide_cali_ad_queue_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_inner_normal_storewide_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(top_ctcvr_num, ks::AbtestBiz::AD_DSP, 5);
SPDM_ABTEST_DOUBLE(normal_storewide_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(normal_storewide_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(normal_storewide_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(normal_storewide_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_inner_native_storewide_calibration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(native_storewide_smart_bidding_min_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_storewide_smart_bidding_max_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_storewide_exception_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(native_storewide_exception_lower_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enbale_splash_inner_live_roas_7days, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adjust_price_private_message, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(private_message_support_decay_group, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_white_list_map_for_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_white_list_map_for_rtabid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_merchant_photo_roas_cvr_auc_cross_test, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(disable_new_online_calibration_with_cmd_roas, ks::AbtestBiz::AD_DSP);

// [dingxiangkun]
SPDM_ABTEST_BOOL(disable_useless_calibration_strategy, ks::AbtestBiz::AD_DSP);

// [zhaoyi13]
SPDM_ABTEST_BOOL(enable_ue_check_tag, ks::AbtestBiz::AD_DSP);  // ue 优质广告实验开关
SPDM_ABTEST_INT64(outer_live_start_ecpc_dft_req_threshold, ks::AbtestBiz::AD_DSP, 100);
// 外循环引入看评率
SPDM_ABTEST_BOOL(enable_log_reco_cestr, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(outer_reco_cestr_ecpc_dft_req_threshold, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(photo_comment_cnt_thr, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_fiction_iaa_ipu_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fiction_iaa_hc_ipu_thr, ks::AbtestBiz::AD_DSP, 100);

// [zhangyuemeng]
SPDM_ABTEST_BOOL(enable_lsp_storewide_skip_bs, ks::AbtestBiz::AD_DSP);



SPDM_ABTEST_BOOL(enable_game_shoufa_pay_ecpc_boost, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_kminigame_product_cost_decay_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(kminigame_app_ecpc_lower, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(kminigame_app_ecpc_upper, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(kminigame_app_ecpc_exp, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_STRING(outer_minigame_explore_ecpc_ranking_tag, ks::AbtestBiz::AD_DSP, "");

SPDM_ABTEST_BOOL(enable_game_shoufa_force_show, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_industry_boost_ecpc_reserve_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_reserve_bid, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_reserve_bid_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_kminigame_app_avoid_black_account_reward_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(kminigame_app_black_account_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_all_orientation_industry_boost, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_outer_minigame_explore_ecpc_ranking, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_outer_game_explore_ecpc_reserve_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(outer_game_explore_ecpc_reserve_bid, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_game_explore_ecpc_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(outer_industry_boost_ecpc_upper_bound, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_kminigame_default_reward_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(kminigame_default_reward_ecpc_ratio, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(enable_game_iaa_coin_data, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kmini_game_iaa_old_version_ecpc, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(enable_game_iaa_roi7_bid_control_type, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(game_iaa_roi7_account_mod, ks::AbtestBiz::AD_DSP, 101);
SPDM_ABTEST_INT64(game_iaa_roi7_account_begin, ks::AbtestBiz::AD_DSP, 101);
SPDM_ABTEST_STRING(minigame_bidding_strategy_type, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_roi_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_roi_online_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_pacing_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_min_shallow_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_roi7_deep_min_deep_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_STRING(game_iaa_rou7_dynamic_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_game_bidding_formula_new, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(minigame_iaa_roi7_ratio_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_request_industry_game_iaa_ltv7, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(minigame_roi7_exp_tag, ks::AbtestBiz::AD_DSP, "");
SPDM_ABTEST_BOOL(enable_game_iaa_roi7_all_product, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_request_industry_game_iaa_ltv7_all_product, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_white_acount_request_industry_game_iaa_ltv7, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(game_iaa_deep_avg_pacing_alpha, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(game_iaa_deep_avg_pacing_beta, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_ad_iaa_7day_roas_predict, ks::AbtestBiz::AD_DSP);



// [lianghaoqiang]
SPDM_ABTEST_BOOL(enable_poi_distance_adjust, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(poi_distance_adjust_coef, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(poi_distance_adjust_half, ks::AbtestBiz::AD_DSP, 10.0);

// [luyuanquan]
SPDM_ABTEST_BOOL(enable_ad_dsp_item_imp_lps_message_local, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_ad_dsp_item_imp_lps_leadssubmit_local, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(nc_prerank_online_model_boost_num, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_INT64(ac_prerank_online_model_boost_num, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_BOOL(enable_fill_is_potential_nc, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_BOOL(fiction_retarget_subsidy_discount, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_kmini_game_iaa_new_version_ecpc, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fiction_ecpc_impression_cnt_thresh, ks::AbtestBiz::AD_DSP, 100);
SPDM_ABTEST_BOOL(enable_industry_fiction_iaa_ltv_ensemble, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_iaa_ltv_ensemble_weight, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_fiction_iap_pay_ltv_multi_head, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_fiction_iap_pay_ltv_multi_style_head, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(fiction_iap_uplift_k_formula_type, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_style_ltv_cal_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_offline_fiction_subsidy_uplift_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_iap_reverse_unify_ltv_cal_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fiction_iap_reverse_unify_ltv_cal_bias, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_BOOL(enable_fiction_iap_price_multi_head_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fiction_iap_default_max_real_pay_price, ks::AbtestBiz::AD_DSP, 19.9);
SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_weight_v2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(fiction_iap_uplift_unify_ltv_cal_bias_v2, ks::AbtestBiz::AD_DSP, 0.0);
SPDM_ABTEST_INT64(fcition_iap_uplift_price_formula_type, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_INT64(fiction_iap_nc_ecpc_impression_cnt_thresh, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_DOUBLE(fiction_iaa_unify_ltv_cal_weight, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_fiction_iaa_ltv_weight, ks::AbtestBiz::AD_DSP);


SPDM_ABTEST_BOOL(enable_xifan_adjust_suppress, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(xifan_suppress_adjust_ratio, ks::AbtestBiz::AD_DSP, 1.01);

// [dangpingbo]
SPDM_ABTEST_BOOL(enable_search_next_day_rs_filter_v2, ks::AbtestBiz::AD_DSP);

// [sunzhipeng03]
SPDM_ABTEST_BOOL(enable_fill_short_video_cvr_label_info_attr, ks::AbtestBiz::AD_DSP);
// [dongyao03]
SPDM_ABTEST_BOOL(enable_duanju_outer_ctr_industry_cali, ks::AbtestBiz::AD_DSP);

// [guohongkuan]
SPDM_ABTEST_BOOL(enable_reco_frrank_ue, ks::AbtestBiz::AD_DSP);  // 请求精排开关
SPDM_ABTEST_BOOL(enable_reco_live_frue, ks::AbtestBiz::AD_DSP);  // 请求直播精排 uescore 模型开关
SPDM_ABTEST_BOOL(enable_reco_frrank_ue_outside, ks::AbtestBiz::AD_DSP);  // 外循环请求精排开关
SPDM_ABTEST_BOOL(enable_reco_frrank_ue_outerprm, ks::AbtestBiz::AD_DSP);  // 外循环请求私信精排开关
SPDM_ABTEST_BOOL(enable_reco_frrank_ue_outerprm_normal, ks::AbtestBiz::AD_DSP);  // 外循环请求私信精排开关
SPDM_ABTEST_STRING(gamora_reco_frrank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "");  // 精选，默认值为空
SPDM_ABTEST_STRING(nebula_reco_frrank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "");  // 极速版，默认值为空
SPDM_ABTEST_STRING(gamora_reco_sv_frrank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "");  // 精选，默认值为空
SPDM_ABTEST_STRING(nebula_reco_sv_frrank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "");  // 极速版，默认值为空
SPDM_ABTEST_BOOL(enable_outerctr_reco_frrank_ue, ks::AbtestBiz::AD_DSP);  // 外循环请求精排模型开关
SPDM_ABTEST_STRING(gamora_reco_outerctr_frrank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "");  // 精选
SPDM_ABTEST_STRING(nebula_reco_outerctr_frrank_xtr_grpc_name, ks::AbtestBiz::AD_DSP, "");  // 极速版

//  [xiatian06]
SPDM_ABTEST_BOOL(enable_live_frrank_uescore_fill_context_stop, ks::AbtestBiz::AD_DSP);  //  直播精排 uescore 的落盘开关 // NOLINT

// [libingjie03]
SPDM_ABTEST_BOOL(enable_form_pm_integration, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_form_pm_integration_explore, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(fixed_form_pm_explore_rate, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_BOOL(enable_adjust_admit_integration, ks::AbtestBiz::AD_DSP);  // 修改准入组件口径
SPDM_ABTEST_BOOL(enable_qiwei_admit_integration, ks::AbtestBiz::AD_DSP);  // 企微窄准入组件开关
SPDM_ABTEST_DOUBLE(form_pm_integration_explore_rate1, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(form_pm_integration_explore_rate2, ks::AbtestBiz::AD_DSP, 1.0);
SPDM_ABTEST_DOUBLE(form_pm_integration_explore_rate3, ks::AbtestBiz::AD_DSP, 1.0);

SPDM_ABTEST_BOOL(disable_inner_native_roas_calibration, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(rank_cache_min_quota, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_BOOL(enable_rank_cache_add_log, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_rank_score_cache, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(rank_score_cache_tag, ks::AbtestBiz::AD_DSP, "score_cache");
SPDM_ABTEST_BOOL(enable_rank_score_cache_add_tag, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adlist_cache_inner_soft, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_adlist_cache_decice_by_corpus, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_STRING(rank_cache_hit_ratio_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(rank_cache_cpm_ttl_bucket_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_STRING(adlist_cache_quota_adjust_exp_tag, ks::AbtestBiz::AD_DSP, "base");
SPDM_ABTEST_BOOL(enable_fix_cache_size_adjust, ks::AbtestBiz::AD_DSP);

// [dingyiming05]
SPDM_ABTEST_INT64(rank_cache_cpm_topk, ks::AbtestBiz::AD_DSP, 5);

// [qiaolin]
SPDM_ABTEST_BOOL(enable_only_insert_high_ue_in_short_seq, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(high_ue_short_seq_len, ks::AbtestBiz::AD_DSP, 1);
SPDM_ABTEST_BOOL(enable_reco_frrank_ue_nolimit, ks::AbtestBiz::AD_DSP);  // 请求精排开关

// [menjunyi]
SPDM_ABTEST_BOOL(rta_second_request_add_paid2, ks::AbtestBiz::AD_DSP);
SPDM_KCONF_BOOL(ad.adRank2, enableDeviceSupportTaid);





SPDM_ABTEST_BOOL(enable_discount_effect_exp, ks::AbtestBiz::AD_DSP);

// 搜索 QCPX 相关参数
SPDM_ABTEST_BOOL(enable_search_qcpx_effect, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_DOUBLE(max_qpon_uplift_cxr_ratio, ks::AbtestBiz::AD_DSP, 100.)

SPDM_ABTEST_BOOL(enable_cache_ue_p2l_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_skip_ue_cache_key, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_cache_ue_photo_predict, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(ue_cache_photo_predict_ttl, ks::AbtestBiz::AD_DSP, 600);
SPDM_ABTEST_INT64(ue_cache_p2l_predict_ttl, ks::AbtestBiz::AD_DSP, 600);

// ICA 开关
SPDM_ABTEST_BOOL(enable_prerank_rank_ica_live, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_DOUBLE(ica_rank_input_live_quota_ratio, ks::AbtestBiz::AD_DSP, 1.0)

// [suntianyu06] 订单模型请求 ue score
SPDM_ABTEST_BOOL(enable_inner_order_reco_frrank_ue, ks::AbtestBiz::AD_DSP);  // 请求精排模型开关
SPDM_ABTEST_BOOL(enable_inner_order_sv_reco_frrank_ue, ks::AbtestBiz::AD_DSP);  // 请求精排模型开关

// [dangpingbo] RankHc 相关系数
SPDM_ABTEST_DOUBLE(search_rank_hc_ratio, ks::AbtestBiz::AD_DSP, 1.0);  // hc alpha 系数
SPDM_ABTEST_DOUBLE(search_rank_hc_model_ratio, ks::AbtestBiz::AD_DSP, 1.0);  // hc 模型 alpha 系数
SPDM_ABTEST_BOOL(enable_search_rank_hc_model, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_trace_migration, ks::AbtestBiz::AD_DSP);

SPDM_ABTEST_INT64(cid_max_item_ranking_list_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(cid_max_item_ranking_list_size, ks::AbtestBiz::AD_DSP, 200);

SPDM_ABTEST_INT64(cid_max_dup_photo_id_ranking_score_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(cid_max_dup_photo_id_ranking_score_size, ks::AbtestBiz::AD_DSP, 200);

SPDM_ABTEST_INT64(cid_account_max_item_ranking_list_idx, ks::AbtestBiz::AD_DSP, -1);
SPDM_ABTEST_INT64(cid_account_max_item_ranking_list_size, ks::AbtestBiz::AD_DSP, 200);


// [zhangpuyang]
SPDM_ABTEST_BOOL(enable_fill_bid_move_back, ks::AbtestBiz::AD_DSP);  // fill_bid 逻辑后移
SPDM_KCONF_BOOL(ad.adRank3, enable_CalcOriginPrice_Compute_Dot_in_preOnline);  // 打点在 pre_online 环境生效
// [zhangpuyang] 二次请求增加 unit_id
SPDM_KCONF_BOOL(ad.adRank3, enable_unit_id_in_second_bid_request);
// [cuirunpeng] 内循环短带 roas 两段模型参数联调开关
SPDM_ABTEST_BOOL(enable_inner_merchant_roas_param_tuning, ks::AbtestBiz::AD_DSP);


// [shizujun]
SPDM_ABTEST_BOOL(enable_discount_roi_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_discount_roi_opt_author, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_discount_roi_opt_bid, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_INT64(discount_roi_opt_quartile, ks::AbtestBiz::AD_DSP, 50);
SPDM_ABTEST_DOUBLE(makeup_roi_opt_ratio, ks::AbtestBiz::AD_DSP, 0.67);

// [liyitian]
SPDM_ABTEST_BOOL(enable_outer_ecpc_close, ks::AbtestBiz::AD_DSP);

}  // namespace ad_rank
}  // namespace ks
