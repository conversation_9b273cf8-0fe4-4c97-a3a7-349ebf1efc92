#pragma once

#include <algorithm>
#include <cstdint>
#include <iostream>
#include <map>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "absl/container/flat_hash_set.h"
#include "rapidjson/document.h"
#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf_data_split_0.pb.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf_data_split_1.pb.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf_data_split_2.pb.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf_data_split_3.pb.h"
#include "teams/ad/ad_rank_search/utils/kconf/kconf_data_split_4.pb.h"
#include "teams/ad/ad_index/framework/target/target_key.h"
#include "ks/base/abtest/session_context.h"
#include "teams/ad/engine_base/universe/operation_config/operation_config_flow_control.h"

namespace base {
class Json;
}  // namespace base

namespace Json {
class Value;
}  // namespace Json
namespace ks {
namespace ad_rank {

struct AdConvAdjustConfig {
    std::unordered_map<int64_t, double> account_map;
    std::unordered_map<int64_t, double> campaign_map;
    std::unordered_map<int64_t, double> unit_map;
    std::string debug_str;

    bool Load(const std::string& json_str);
};

struct RewardedCoinScalingBase {
  std::string coin_scaling_coef_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string deep_coin_scaling_coef_tag;
  std::string deep_coin_upper_tag;
  std::string deep_coin_lower_tag;
};

struct RewardedCoinScalingByAccountList
    : public ks::ad_base::kconf::KconfInitProto<kconf::RewardedCoinScalingByAccountConf> {
  std::unordered_map<int64, RewardedCoinScalingBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct PredictBoundValueConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::PredictBoundValueConfigPb> {
  bool Init() override;
  bool Bound(double* out, const double& ori_score, const std::string& cmd_key,
             const std::string& product_name) const;
  bool HasConfig(const std::string& cmd_key) const;
};

struct CorporationCeilingWhiteList
    : public ks::ad_base::kconf::KconfInitProto<kconf::CorporationCeilingWhiteListPb> {
  std::unordered_set<int64_t> product_ids;
  std::unordered_set<int64_t> account_ids;
  bool Init() override;
  bool Exist(int64_t account_id, int64_t product_id) const;
  void Clear();
 private:
  ad_base::TargetKeyConvertor str_2_int64_;
};

struct PoQuanPidConf {
  double price_discount_budget;
  double init_lambda;
  double pid_kp;
  bool enable_pid_control;
  double max_lambda;
  int64_t strategy_type;
  double max_ecpc_ratio;
  bool cpm_involved;
  PoQuanPidConf(double budget, double init_lambda, double pid_kp, bool enable_pid_control, double max_lambda_,
                int64_t strategy_type_, double max_ecpc_ratio_, bool cpm_involved_)
      : price_discount_budget(budget),
        init_lambda(init_lambda),
        pid_kp(pid_kp),
        enable_pid_control(enable_pid_control),
        max_lambda(max_lambda_),
        strategy_type(strategy_type_),
        max_ecpc_ratio(max_ecpc_ratio_),
        cpm_involved(cpm_involved_) {}
  PoQuanPidConf()
      : price_discount_budget(0),
        init_lambda(0),
        pid_kp(0),
        enable_pid_control(false),
        max_lambda(0.0),
        strategy_type(0),
        max_ecpc_ratio(1.0),
        cpm_involved(true) {}
};
struct PoQuanPidStrategyKconf
    : public ks::ad_base::kconf::KconfInitProto<kconf::PoQuanPidStrategyKconfPb> {
  std::unordered_map<std::string, std::vector<std::string>> ad_attr_2_tag_list_map;
  std::unordered_map<std::string, PoQuanPidConf> tag_2_conf_map;
  bool Init() override;
  void Clear();
};
struct OuterLoopCalibratedRatioForPrerank
    : public ks::ad_base::kconf::KconfInitProto<kconf::OuterLoopCalibratedRatioForPrerankPb> {
  std::unordered_map<std::string, std::vector<double>> product_calibrate_info;
  bool Init() override;
  void Clear();
};

struct NearbyAdmitAdConfig {
  std::unordered_set<int64_t> sec_industry;
  std::unordered_set<int64_t> industry_group;
};

struct AdMinBidWhitelistConfig {
  std::unordered_set<int64_t> sec_industry;
  std::unordered_set<int64_t> product_name;
};

struct AdjustReqCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<std::int64_t, double>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct RewardedCalibrateBase {
  double min_r_thr;
  double max_r_thr;
  int64 creative_item_num_thr;
  int64 unit_item_num_thr;
  int64 bucket_item_num_thr;
  int64 bucket_num;
};

struct NearbyRankAdmitWhiteList: public ks::ad_base::kconf::KconfInitProto<kconf::NearbyRankAdmitWhiteListConfig> {  //NOLINT
  std::unordered_map<int64_t, NearbyAdmitAdConfig> datas;
  std::string debug_str;
  bool Init() override;
};

struct NewRetentionThresholdToolConfig : public ks::ad_base::kconf::KconfInitProto<kconf::RetentionThresholdToolConfig> {   // NOLINT
  struct NewRetentionThresholdToolIter {
    absl::flat_hash_set<int64_t> product_ids;
    absl::flat_hash_set<int32_t> account_ids;
    int32_t media = 0;  // 0 暗投 1 明投 2 通投
    double threshold = 0.0;
  };
  std::vector<NewRetentionThresholdToolIter> threshold_tool;
  bool Init() override;
};

struct MinBidWhiteList: public ks::ad_base::kconf::KconfInitProto<kconf::MinBidWhiteListConfig> {
  std::unordered_map<int64_t, AdMinBidWhitelistConfig> datas;
  std::string debug_str;
  bool Init() override;
};

struct PurchaseRoiHcConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::PurchaseRoiHcConfPb> {
  std::unordered_set<std::string> product_names;
  std::unordered_set<int64_t> account_ids;
  std::unordered_set<int64_t> industry_ids;
  bool Init() override;
  bool AdmitHc(int64_t account_id, const std::string& product_name, int64_t industry_id) const;
  void Clear();
};

struct AppPageFormMaterialCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, std::unordered_map<int64_t, double>>>
      trade_cpm_map;
  std::unordered_map<int64_t, std::unordered_map<std::string, std::unordered_map<std::string, double>>>
      product_cpm_map;
  std::unordered_map<int64_t, std::unordered_map<std::string, std::unordered_map<int64_t, double>>>
      account_cpm_map;
  std::string debug_str;
  bool Load(const std::string& json_str);
};


struct SubIndustryGatherEcpcConfig {
  std::unordered_set<int64_t> first_industry;
  std::unordered_set<int64_t> second_industry;
  std::unordered_set<int64_t> account;
  std::unordered_set<int64_t> unit;
  std::unordered_set<int64_t> photo;
  std::unordered_set<int64_t> orientation;
};
struct IndustryGatherEcpcConfig {
  std::unordered_map<int64_t, SubIndustryGatherEcpcConfig> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct SysDftConfigParam {
  double ctr = 0.0;
  double p3r = 0.0;
  double ctr_cvr = 0.0;

  bool Load(const std::string &json_str);

  void describe(std::ostream& os) const {
    os << "ctr=" << ctr
       << "&p3r=" << p3r
       << "&ctr_cvf=" << ctr_cvr;
  }
};

inline std::ostream& operator<<(std::ostream& os, const SysDftConfigParam& rs) {
  rs.describe(os);
  return os;
}

struct ShallowSortUnitConfig {
  std::set<int64_t> unit_tail_set;
  std::set<int64_t> unit_id_set;
  int64_t upper_bound;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct NebulaExpTradeCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct NearbyCpmThrConfig {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct CtrThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct NewSdkAccountConfig {
  std::unordered_map<int64_t, std::pair<int64_t, double>> new_sdk_account_map;
  bool Load(const std::string& value);
};

struct CpmBonusWhitelistConfig {
  std::unordered_map<int64_t, double> bouns_whitelist_ratio_map;
  bool Load(const std::string& value);
};

struct ThanosExpTradeCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct ThanosFSExpTradeCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct ThanosInnerFlowExpTradeCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct ExpTradeCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct MinbidConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, int64_t>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct AdFlowUeqRatioConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};


struct AdFlowCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, std::unordered_map<int64_t, double>>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct AdFlowCxrThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, std::unordered_map<std::string, double>>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct GdtAdCompanyCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, double>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct ExpProductCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, double>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct ExpAccountCpmThrConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, double>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct ExpAccountMinbidConfig {
  std::unordered_map<int64_t, std::unordered_map<int64_t, int64_t>> datas;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct OnlineCpmThrConfig {
  std::unordered_map<int64_t, double> trade_cpm_map;
  std::unordered_map<std::string, double> product_cpm_map;
  std::unordered_map<int64_t, double> account_cpm_map;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct CtrCvrThrDiscountConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, double>> datas;
  bool Load(const std::string& value);
};

struct NewCreativeFeedBayesParams {
  double predict_unify_ctr_avg = 0.05;  // 平均 ctr
  // 平均 cvr 以 ocpx_action_type 为目标的转化。如果是付费, 那就是平均付费率
  double predict_unify_cvr_avg = 0.0005;
  double cpm_avg = 6000000.0;                  // 平均 cpm
  double predict_unify_ctr_alpha = 0.0;  // ctr 的 alpha
  double predict_unify_ctr_beta = 0.0;   // ctr 的 beta
  double predict_unify_cvr_alpha = 0.0;  // cvr 的 alpha
  double predict_unify_cvr_beta = 0.0;   // cvr 的 beta
};
struct NewCreativeFeedParamsConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, NewCreativeFeedBayesParams>> datas;
  std::string debug_str;

  bool Load(const std::string &json_str);
};

struct NewCreativeFeedParentIdParamsConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, NewCreativeFeedBayesParams>> datas;
  std::string debug_str;

  bool Load(const std::string &json_str);
};

struct NewCreativeDynamicFeedBonusParams {
    double finish_ratio = 0.7;
    double finish_multi = 1.0;
    double finish_pow = 1.0;
    double finish_category_cxr_weight = 0.5;
    double finish_creative_cxr_weight = 0.5;
    double finish_time_smooth_min = 0.8;
    double finish_time_smooth_max = 1.2;
    double completness_min = 0.7;
    double completness_max = 1.1;
    double completness_max_bonus = 0.05;
    double completness_min_bonus = 0.2;
    double uncertain_multi = 1.0;
    double uncertain_ratio = 0.3;
    double cxr_ratio = 0.1;
    double feed_avg_ctr = 0.05;
    double feed_avg_cvr = 0.003;
    double feed_avg_cpm = 2000000;
    double feed_cxr_ratio = 0.5;
    double feed_cpm_ratio = 0.5;
    double feed_pv_cpm_bonus_ratio = 0.5;
    double nofeed_avg_ctr = 0.1;
    double nofeed_avg_cvr = 0.0006;
    double nofeed_avg_cpm = 12000000;
    double nofeed_cxr_ratio = 0.5;
    double nofeed_cpm_ratio = 0.5;
    double nofeed_pv_cpm_bonus_ratio = 0.5;
    double cxr_high_power = 1.0;
    double cxr_second_industry_ratio = 0.0;
    double cxr_industry_ratio = 0.8;
    double cxr_self_ratio = 0.2;
    double high_quality_ratio = 1.0;
    double nofeed_cxr_high_power = 1.0;
    double nofeed_cxr_industry_ratio = 0.8;
    double nofeed_cxr_self_ratio = 0.2;
    double nofeed_high_quality_ratio = 1.0;
    double less_than_threhold_value = 0.5;
    double ctrcvr_high_power = 1.0;
    double nofeed_less_than_threhold_value = 0.0;
    double cpm_max_bonus = 0.0;
    double nofeed_cpm_max_bonus = 0.0;
};

struct NewCreativeFeedDynamicBonusParamsConfig {
  std::unordered_map<int64_t, NewCreativeDynamicFeedBonusParams> datas;
  std::string debug_str;

  bool Load(const std::string &json_str);
};

struct NewCreativeFeedDynamicParams {
  double new_creative_bayes_first_multi_param = 1.0;
  double new_creative_bayes_first_bid_exp_param = 1.0;
  double new_creative_bayes_first_all_exp_param = 1.0;
  double new_creative_bayes_second_ctr_exp_param = 1.0;
  double new_creative_bayes_second_ctr_multi_param = 1.0;
  double new_creative_bayes_second_cvr_exp_param = 1.0;
  double new_creative_bayes_second_cvr_multi_param = 1.0;
  double new_creative_bayes_third_exp_param = 1.0;
  double new_creative_bayes_third_multi_param = 1.0;
  double new_creative_bayes_fourth_exp_param = 1000.0;
  double new_creative_bayes_fourth_multi_param = 1.0;
};

struct NewCreativeFeedDynamicParamsConfig {
  std::unordered_map<int64_t, std::unordered_map<std::string, NewCreativeFeedDynamicParams>> datas;
  std::string debug_str;

  bool Load(const std::string &json_str);
};

struct ColdStartCondition {
  int64_t condition_id = -1;
  int64_t threshold = 0;
};

struct DetailFilter {
  int64_t field_id = -1;
  std::unordered_map<std::string, bool> o_fields;
  std::unordered_map<std::string, bool> x_fields;
};

struct FilterRule {
  int64_t filter_type = -1;
  std::vector<DetailFilter> detail_filters;
};

struct PidInfo {
  double k_p = 0.0;
  double k_i = 0.0;
  double k_d = 0.0;
};

struct BonusRuleParam {
  int64_t bonus_tag = -1;  // bonus_tag 用来唯一确定 bonus 的规则
  int64_t bouns_cpm_budget = 0;  // bonus 扶持的总的 bonus_cpm 总的预算
  int64_t is_cold_start = 0;  // 是否是冷启动阶段扶持
  int64_t is_fixed_bonus = 0;  // 是否是固定 bonus_cpm 扶持
  double fixed_bonus_cpm = 0.0;  // 固定的 bonus_cpm
  int64_t start_time = 0;  // 策略开始时间戳(s)
  int64_t end_time = 0;  // 策略结束时间戳(s)
  std::vector<FilterRule> filter_rules;  // 命中的流量维度/广告维度的条件
  std::vector<ColdStartCondition> cold_start_conditions;  // 如果是冷启动阶段，则筛选冷启动广告的条件
  PidInfo pid_info;  // 如果是 pid 自动调整 bonus_cpm, 对应的 p, i, d 参数
};

struct CreditGrantDeepCpaConfig {
  double default_grant_cpa_ratio = 1.0;
  std::unordered_map<int64_t, double> product_grant_cpa_ratio_map;
  std::unordered_map<int64_t, double> account_grant_cpa_ratio_map;
  std::string conf_json;
  bool Load(const std::string& value);
};

struct OnlinePriceDiscountConfig {
  std::unordered_map<int64_t, double> account_discount_map;
  std::unordered_map<int64_t, double> campaign_discount_map;
  std::unordered_map<int64_t, double> unit_discount_map;
  std::string debug_str;

  bool Load(const std::string& json_str);
};

struct DeepConvParamConfig {
  DeepConvParamConfig() = default;
  virtual ~DeepConvParamConfig() = default;
  double deep_conv_upper_bound;
  double deep_conv_lower_bound;
  double max_deep_rate_over_conv;
  double prerank_deep_conv_upper_bound;
  double prerank_deep_conv_lower_bound;

  bool Load(const std::string &v);

  void describe(std::ostream &os) const;
};

struct BonusCpmMaxBound {
  std::vector<std::vector<int64_t>> cpaBidRangeList;
  std::unordered_map<int64_t, double> bonusCpmMaxBoundMap;
};

struct ProductInfosData{
  double alpha;
  double target;
  double upbound;
  double lowbound;
  std::unordered_set<int64> unit_ids_set;
  std::unordered_set<int64> account_ids_set;
};

struct EcpcProductAndUnitConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::EcpcProductAndUnitConfig> {  // NOLINT
  std::unordered_map<std::string, ProductInfosData> product_infos;
  std::unordered_set<int64> total_unitids_set;
  std::unordered_set<int64> total_accountids_set;
  bool Init() override;
};

struct AdPositionColdStartRerankData : public ks::ad_base::kconf::KconfInitProto<kconf::AdPositionColdStartRerankConfig> {  // NOLINT
  std::unordered_map<std::string, double> product_name_ratio_map;
  std::unordered_map<std::string, double> account_id_ratio_map;
  std::unordered_map<std::string, double> unit_id_ratio_map;
  std::unordered_map<std::string, double> pos_id_ratio_map;
  std::unordered_map<std::string, double> pos_account_id_ratio_map;
  bool Init() override;
};

struct HighQualityPhotoRatioConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::HighQualityPhotoRatioConfig> {  // NOLINT
  std::unordered_map<std::string, double> account_id_ratio_map;
  bool Init() override;
};

struct PayNovelAdjustConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::PayNovelAdjustConfig> {
  std::unordered_map<std::string, double> unit_ratio_map;
  bool Init() override;
};

struct SocialIndustryExploreData : public ks::ad_base::kconf::KconfInitProto<kconf::SocialIndustryExploreConfig> {  // NOLINT
  std::unordered_set<int64> account_id_set;
  std::unordered_set<int64> unit_id_set;
  std::unordered_set<int64> product_id_set;
  bool Init() override;
};

struct McdaUpModelEcpcLaunchWhitelistData : public ks::ad_base::kconf::KconfInitProto<kconf::McdaUpModelEcpcLaunchWhitelistConfig> {  // NOLINT
  std::unordered_set<std::string> purchase_product_set;
  std::unordered_set<std::string> roas_product_set;
  bool Init() override;
};

struct NewGameBaseData {
  std::unordered_set<int64> account_ids_set;
  std::unordered_set<int64> unit_ids_set;
  std::unordered_set<int64> creative_ids_set;
};

struct SkipChargeWhiteListConfigData : public ks::ad_base::kconf::KconfInitProto<kconf::SkipChargeWhiteListConfig> {  // NOLINT
  std::unordered_map<int64, NewGameBaseData> skip_datas;
  bool Init() override;
};

struct SkipMinbidFilterWhiteListData : public ks::ad_base::kconf::KconfInitProto<kconf::SkipMinbidFilterWhiteListConfig> {  // NOLINT
  std::unordered_map<int64, NewGameBaseData> skip_filter_datas;
  bool Init() override;
};

inline std::ostream &operator<<(std::ostream &os, const DeepConvParamConfig &ok) {
  ok.describe(os);
  return os;
}

struct ExtMerchantBonusBlackList: public ks::ad_base::kconf::KconfInitProto<kconf::ExtMerchantBonusBlackListPb> {  // NOLINT
    bool Init() override;
    bool IsInBlackList(int64_t account_id, int64_t campaign_id,
                       int64_t unit_id, int64_t creative_id) const;

 private:
    std::set<int64_t> account_set;
    std::set<int64_t> campaign_set;
    std::set<int64_t> unit_set;
    std::set<int64_t> creative_set;
};

struct SimpleFlowContollerConfig {
  bool Load(const std::string& json_str);

  double slow_start_step = 0.1;
  double slow_start_init = 0.1;
  double brake_start_cost_percent = 0.8;
  double brake_target = 0.1;
  double brake_speed = 1.0;

  void describe(std::ostream& os) const;
};

inline std::ostream& operator<<(std::ostream& os, const SimpleFlowContollerConfig& obj) {
  obj.describe(os);
  return os;
}

struct ExploreLiveConfig {
  ExploreLiveConfig() = default;
  virtual ~ExploreLiveConfig() = default;

  bool enable_explore_live_fans_top;    // 是否允许发现页投放直播粉条
  bool enable_main_ab_test;             // 是否允许主版 AB 实验
  bool enable_thanos_ab_test;           // 是否允许滑滑版 AB 实验
  bool enable_nebula_ab_test;           // 是否允许极速版 AB 实验
  int32_t explore_live_fans_top_ratio;  // 控制总的发现页直播粉条的占比，用户尾号后两位
  int32_t main_explore_live_ratio;  // 控制主版双列发现页直播粉条的占比，用户尾号后两位
  int32_t nebula_explore_live_ratio;  // 控制极速版发现页直播粉条的占比，用户尾号后两位
  int32_t thanos_explore_live_ratio;  // 控制滑滑版发现页直播粉条的占比，用户尾号后两位
  int32_t explore_live_main_vv_step;  // 控制主版发现页直播粉条的 vv 步长
  int32_t explore_live_thanos_vv_step;  // 控制滑滑版发现页直播粉条的 vv 步长
  int32_t explore_live_nebula_vv_step;  // 控制极速版版发现页直播粉条的 vv 步长
  int32_t explore_live_main_time_sec_step;  // 控制主版发现页直播粉条的出现时长步长，单位：秒
  int32_t explore_live_thanos_time_sec_step;  // 控制滑滑发现页直播粉条的出现时长步长，单位：秒
  int32_t explore_live_nebula_time_sec_step;  // 控制极速版发现页直播粉条的出现时长步长，单位：秒
  int32_t explore_live_day_limit;             // 控制发现页直播粉条每天每用户最大展示量
  int32_t explore_live_retrieval_max_count;  // 控制发现页最大召回的直播数量
  int32_t predict_max_ps_live;               // 控制请求 ps 的最大量
  std::string android_main_app_ver;          // 安卓主版最低版本号
  std::string android_nebula_app_ver;        // 安卓极速版最低版本号
  std::string ios_main_app_ver;              // iOS 主版最低版本号
  std::string ios_nebula_app_ver;            // iOS 极速版最低版本号
  bool Load(const std::string& json_str);

  void describe(std::ostream& os) const;

 private:
  std::string raw_json_str;
};

inline std::ostream& operator<<(std::ostream& os, const ExploreLiveConfig& csc) {
  csc.describe(os);
  return os;
}

struct InnerRecruimentPlcTailExpConfig {
  InnerRecruimentPlcTailExpConfig() = default;
  virtual ~InnerRecruimentPlcTailExpConfig() = default;
  std::unordered_map<std::string, std::unordered_set<int>> datas;
  std::string debug_str;
  bool Load(const std::string& json_str);
};

struct RewardedCoinDataBase {
  std::string prefix;
  std::string enable_tag;
  std::string avg_tcpm_tag;
  std::string global_cpm_tag;
  std::string user_value_tag;
  std::string ecpm_bias_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string ratio_upper_tag;
  std::string ratio_lower_tag;
  int64  base_coin_num;
  double user_low_value_percentile_thr;
  double user_low_value_ratio;
  double user_percentile_bias;
  double global_cpm;
  double new_user_value_ratio;
};

struct MultiRewardedCoinDataList
    : public ks::ad_base::kconf::KconfInitProto<kconf::MultiRewardedCoinDataConf> {
  std::unordered_map<int64, RewardedCoinDataBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct SingleExpTagUnifyCalcCoinUpper {
  std::string exp_tag;
  int64_t default_value;
  std::unordered_map<std::string, int64_t> subpage_to_upper;
};

struct IncentiveAdUnifyCalcCoinUpperList
    : public ks::ad_base::kconf::KconfInitProto<kconf::IncentiveAdUnifyCalcCoinUpper> {
  std::vector<SingleExpTagUnifyCalcCoinUpper> coin_upper_conf;
  bool Init() override;
};

struct SingleIncntvAdUnifyCalcCoinMdpLtvKeyValue {
  std::vector<double> key_lower_list;
  std::vector<double> key_upper_list;
  double key_to_value;
};
struct SingleIncntvAdMdpLtvUnifyCalcCoinKeyNameKeyValue {
  std::vector<std::string> key_name_list;
  std::vector<SingleIncntvAdUnifyCalcCoinMdpLtvKeyValue> key_value_list;
};

struct MultiIncntvAdUnifyCalcCoinMdpLtvKeyValueList
    : public ks::ad_base::kconf::KconfInitProto<kconf::MultiIncntvAdUnifyCalcCoinMdpLtvKeyValueV2List> {
  std::unordered_map<std::string, SingleIncntvAdMdpLtvUnifyCalcCoinKeyNameKeyValue> exp_tag_to_kv_list;
  bool Init() override;
};

struct SingleIncntvAdUnifyCalcCoinMdpLtvKey {
  double lower;
  double upper;
  std::string key_name;
};

struct MultiIncntvAdUnifyCalcCoinMdpLtvKeyList
    : public ks::ad_base::kconf::KconfInitProto<kconf::MultiIncntvAdUnifyCalcCoinMdpLtvKey> {
  std::vector<SingleIncntvAdUnifyCalcCoinMdpLtvKey> key_list;
  bool Init() override;
};

struct IncntvAdBinToLinerCoef {
  double lower;
  double upper;
  double weight;
  double bias;
};

struct IncntvAdPredictNext1ViewValue2Coef
    : public ks::ad_base::kconf::KconfInitProto<kconf::IncntvAdPredictNext1ViewValue2CoefProto> {
  std::unordered_map<std::string, std::vector<IncntvAdBinToLinerCoef>> subpageid_to_bin_wb_list;
  bool Init() override;
};

struct DeepRewardedCoinDataBase {
  std::string enable_tag;
  std::string enable_treatment_tag;
  std::string user_value_tag;
  std::string ecpm_bias_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string ratio_upper_tag;
  std::string ratio_lower_tag;
  int64  base_coin_num;
  double low_value_ctcvr_k;
  double low_value_cvr_k;
  double low_value_uplift_k;
  double low_value_thr;
  double user_percentile_bias;
  double new_user_value_ratio;
};

struct DeepRewardedCoinDataList
    : public ks::ad_base::kconf::KconfInitProto<kconf::DeepRewardedCoinDataConf> {
  std::unordered_map<std::string, DeepRewardedCoinDataBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct SearchInspireRewardedCoinDataBase {
  std::string prefix;
  std::string enable_tag;
  std::string avg_tcpm_tag;
  std::string global_cpm_tag;
  std::string user_value_tag;
  std::string ecpm_bias_tag;
  std::string coin_upper_tag;
  std::string coin_lower_tag;
  std::string ratio_upper_tag;
  std::string ratio_lower_tag;
  int64  base_coin_num;
  double user_low_value_percentile_thr;
  double user_low_value_ratio;
  double user_percentile_bias;
  double global_cpm;
  double new_user_value_ratio;
  std::string view_roi;
};

struct MultiRewardedCoinSearchInspireAdBoxDataList
    : public ks::ad_base::kconf::KconfInitProto<kconf::MultiRewardedCoinSearchInspireAdBoxDataConf> {
  std::unordered_map<int64, SearchInspireRewardedCoinDataBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct DeepRewardedCoinSearchInspireAdBoxDataList
    : public ks::ad_base::kconf::KconfInitProto<kconf::DeepRewardedCoinSearchInspireAdBoxDataConf> {
  std::unordered_map<std::string, DeepRewardedCoinDataBase> config_datas;
  std::string debug_str;
  bool Init() override;
};

struct SearchAdBoxOcpxExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxOcpxExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxProductExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxProductExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxRequestSceneExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxRequestSceneExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxItemTypeExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxItemTypeExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxSingleColOcpxExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxSingleColOcpxExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxSingleColProductExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxSingleColProductExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxSingleColRequestSceneExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxSingleColRequestSceneExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchAdBoxSingleColItemTypeExpBoost
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireAdBoxSingleColItemTypeExpConfig> {
  std::unordered_map<std::string, std::unordered_map<std::string, double>> config_datas;
  bool Init() override;
};

struct SearchInspirePvAdmitDataBase {
  std::string enable_search_inspire;
  std::string enable_order_paied_style;
  std::string enable_conv_style;
  std::string enable_invoked_style;
};

struct SearchInspirePvAdmitConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspirePvAdmitConf> {
  std::unordered_map<int64, SearchInspirePvAdmitDataBase> config_datas;
  bool Init() override;
};

struct SearchInspireDeepRewardTypeDataBase {
  std::string coin_upper;
  std::string coin_lower;
  std::string deep_roi;
};

struct SearchInspireDeepRewardConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireDeepRewardConf> {
  std::unordered_map<int64, std::unordered_map<std::string, SearchInspireDeepRewardTypeDataBase>>
    config_datas;
  bool Init() override;
};

class IdSelector : public ks::ad_base::kconf::KconfInitProto<kconf::AdvSelector> {
 public:
  bool Init() override;
  bool Select(int64_t id) const;

 private:
  absl::flat_hash_set<int64_t> whitelist_;
};

class ActivateAdvertiserWhiteConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireStyleActivateAdvertiserWhiteConfig> {
 public:
  bool Init() override;
  bool IsInWhiteList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_whitelist_;
  absl::flat_hash_set<int64_t> industry_id_v3_whitelist_;
  absl::flat_hash_set<int64_t> product_id_whitelist_;
};

class ActivateAdvertiserBlackConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireStyleActivateAdvertiserBlackConfig> {
 public:
  bool Init() override;
  bool IsInBlackList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_blacklist_;
  absl::flat_hash_set<int64_t> industry_id_v3_blacklist_;
  absl::flat_hash_set<int64_t> product_id_blacklist_;
};

class SitePageInvokedAdWhiteConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireStyleActivateAdvertiserWhiteConfig> {
 public:
  bool Init() override;
  bool IsInWhiteList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_whitelist_;
  absl::flat_hash_set<int64_t> industry_id_v3_whitelist_;
  absl::flat_hash_set<int64_t> product_id_whitelist_;
};

class InvokedAdvertiserBlackConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireStyleInvokedAdvertiserBlackConfig> {
 public:
  bool Init() override;
  bool IsInBlackList(const int64_t& account_id, const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_blacklist_;
  absl::flat_hash_set<int64_t> product_id_blacklist_;
};

class InspireFormAdvertiserWhiteConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireFormAdvertiserWhiteConfigPb> {
 public:
  bool Init() override;
  bool IsInWhiteList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_whitelist_;
  absl::flat_hash_set<int64_t> industry_id_v3_whitelist_;
  absl::flat_hash_set<int64_t> product_id_whitelist_;
};

class MicroVideoAdvertiserWhiteConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::DspStyleMicroVideoAdvertiserWhiteConfig> {
 public:
  bool Init() override;
  bool IsInWhiteList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_whitelist_;
  absl::flat_hash_set<int64_t> industry_id_v3_whitelist_;
  absl::flat_hash_set<int64_t> product_id_whitelist_;
};

class LiveAdvertiserWhiteConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireStyleLiveAdvertiserWhiteConfig> {
 public:
  bool Init() override;
  bool IsInWhiteList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_whitelist_;
  absl::flat_hash_set<int64_t> industry_id_v3_whitelist_;
  absl::flat_hash_set<int64_t> product_id_whitelist_;
};

class LiveAdvertiserBlackConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::InspireStyleLiveAdvertiserBlackConfig> {
 public:
  bool Init() override;
  bool IsInBlackList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_blacklist_;
  absl::flat_hash_set<int64_t> industry_id_v3_blacklist_;
  absl::flat_hash_set<int64_t> product_id_blacklist_;
};

class SearchActivateAdvertiserBlackConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireStyleActivateAdvertiserBlackConfig> {
 public:
  bool Init() override;
  bool IsInBlackList(const int64_t& account_id, const int64_t& industry_id_v3,
                     const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_blacklist_;
  absl::flat_hash_set<int64_t> industry_id_v3_blacklist_;
  absl::flat_hash_set<int64_t> product_id_blacklist_;
};

class SearchInvokedAdvertiserBlackConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::SearchInspireStyleInvokedAdvertiserBlackConfig> {
 public:
  bool Init() override;
  bool IsInBlackList(const int64_t& account_id, const int64_t& product_id) const;

 private:
  absl::flat_hash_set<int64_t> account_id_blacklist_;
  absl::flat_hash_set<int64_t> product_id_blacklist_;
};

struct TargetOpData {
  int64_t ocpx_action_type;
  int64_t target_id;
  std::unordered_set<int64_t> account_id_set;
  std::unordered_set<std::string> product_name_set;
  std::unordered_set<int64_t> account_tail_id_set;
  bool enable_mcb;
  bool enable_boost;
  bool enable_hc_ctrl;
};

struct TargetSearchKconfLoadConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::TargetSearchKconf> {
  std::map<int64_t, TargetOpData> target_opt;
  std::vector<int64_t> vaild_target_id_list;
  std::set<int64_t> account_id_set_all;
  std::set<std::string> product_name_set_all;
  std::set<int64_t> account_tail_id_set_all;
  bool Init() override;
};

struct OcpxCaliConfig {
  double calibration_ratio = 1.0;
  double lower_bound = 1.0;
  double upper_bound = 1.0;

  OcpxCaliConfig() = default;
  OcpxCaliConfig(double calibration_ratio, double lower_bound, double upper_bound)
      : calibration_ratio(calibration_ratio), lower_bound(lower_bound), upper_bound(upper_bound) {}
};

struct PecCouponAuthorBase {
  int64  threshold = 0;
  double discount_ratio = 0.0;
  double discount_lower = 0.0;
  double discount_upper = 1.0;
  bool is_new_fans_author = false;
  int32 discount_start = 0;
  int64 base_price = 0;
  bool enable_effect_new_customer = false;
};

struct QcpxStatCouponConfigBase {
  int64 threshold;
  double discount_ratio;
  std::unordered_map<std::string, double> cvr_uplift_ratios;
};

class QcpxStatStrategyConfig : public ks::ad_base::kconf::KconfInitProto<kconf::QcpxStatStrategyConfigPb> {
 public:
  bool Init() override;
  std::unordered_map<std::string, std::vector<std::string>> feature_types;
  std::unordered_map<std::string, std::vector<QcpxStatCouponConfigBase>> coupon_configs;
};

class QcpxCouponConfig : public ks::ad_base::kconf::KconfInitProto<kconf::QcpxCouponConfigPb> {
 public:
  bool Init() override;
  int64 GetAuthorTemplate(bool use_new, const int64 author_id) const;
 private:
  std::unordered_map<int64, int64> author2coupon_map0;
  std::unordered_map<int64, int64> author2coupon_map1;
};

struct QcpxVauthorCouponConfigBase {
  int64 threshold;
  double discount_ratio;
  double adjust_ratio;
  std::unordered_map<std::string, double> cvr_uplift_ratios;
};

struct QcpxCouponLibBase {
  int32_t threshold;
  int32_t amount;
};

class QcpxCouponLib : public ks::ad_base::kconf::KconfInitProto<kconf::QcpxCouponLibPb> {  // NOLINT
 public:
  bool Init() override;
  std::unordered_map<int32_t, QcpxCouponLibBase> coupon_lib;
};

class QcpxPackageConfigRelation : public ks::ad_base::kconf::KconfInitProto<kconf::QcpxPackageConfigRelationPb> {  // NOLINT
 public:
  bool Init() override;
  std::unordered_map<int64_t, std::unordered_set<int64_t>> pkg2coupons;
};

class QcpxPackageConfigRelationShelf : public ks::ad_base::kconf::KconfInitProto<kconf::QcpxPackageConfigRelationPb> {  // NOLINT
 public:
  bool Init() override;
  std::unordered_map<int64_t, std::unordered_set<int64_t>> pkg2coupons_shelf;
};

class QcpxCouponAccess : public ks::ad_base::kconf::KconfInitProto<kconf::QcpxCouponAccessPb> {  // NOLINT
 public:
  bool Init() override;
  std::unordered_map<std::string, std::unordered_set<int32_t>> coupon_access;
};

struct CidQcpxStatCouponConfigBase {
  int64 threshold;
  double discount_ratio;
  std::unordered_map<std::string, double> cvr_uplift_ratios;
};
class CidQcpxStatStrategyConfig : public ks::ad_base::kconf::KconfInitProto<kconf::CidQcpxStatStrategyConfigPb> { // NOLINT
 public:
  bool Init() override;
  std::unordered_map<std::string, std::vector<std::string>> feature_types;
  std::unordered_map<std::string, std::vector<CidQcpxStatCouponConfigBase>> coupon_configs;
};

struct SmallGameForceDirectAbTestConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::SmallGameForceDirectConf> {
 public:
  bool Init() override;
  bool GetProductAbTestSwitch(const std::string& product_name, std::string* ab_key) const;
  const std::unordered_set<std::string>& GetAllAbTestSwitch() const;
 private:
  std::unordered_map<int64, std::string> product_id_2_ab_switch;
  std::unordered_set<std::string> ab_switchs;
};

struct UnifyAdloadControlStrategyConf {
  // 准入总开关
  bool enable_admit = false;
  bool enable_tag_check = false;
  bool enable_auction_bid_ub = false;
  // 广告维度准入条件
  absl::flat_hash_set<int64_t> biz_set;
  absl::flat_hash_set<int64_t> industry_id_set;
  absl::flat_hash_set<int32_t> ad_queue_type_set;
  absl::flat_hash_set<int32_t> item_type_set;
  absl::flat_hash_set<int32_t> ocpc_action_type_set;
  bool ocpc_action_type_admit_switch = false;
  absl::flat_hash_set<int32_t> native_review_status_set;

  // 设备维度准入条件
  absl::flat_hash_set<std::string> product_set;
  absl::flat_hash_set<std::string> user_tag_set;
  absl::flat_hash_set<std::string> buyer_level_set;
  int64_t refresh_times_lb = 0;
  int64_t refresh_times_ub = 0;
  int64_t ad_request_times_lb = 0;
  int64_t ad_request_times_ub = 0;
  int64_t ad_sensitive_user_tag = 0;
  absl::flat_hash_set<std::string> live_core_user_tag_set;
  int32_t live_high_value_user_tag = 0;
  bool live_user_admit_switch = false;

  // 参数上下界
  double show_ratio_output_ub = 0.0;
  double show_ratio_output_lb = 0.0;
  double cpm_thr_output_ub = 0.0;
  double cpm_thr_output_lb = 0.0;
  double fixed_cpm_thr_output_ub = 0.0;
  double fixed_cpm_thr_output_lb = 0.0;
  double power_adjust_output_ub = 0.0;
  double power_adjust_output_lb = 0.0;

  // cpm && vtr 准入门槛
  double cpm_thr = 0.0;
  int64_t auction_bid_thr = 0;
  int64_t auction_bid_ub = 0;

  // 其他参数
  bool enable_max_server_show_ratio = false;

  UnifyAdloadControlStrategyConf() = default;
  explicit UnifyAdloadControlStrategyConf(const kconf::UnifyAdloadControlExpConf_ControlStrategyConf& conf);
};

struct UnifyAdloadControlExpConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::UnifyAdloadControlExpConf> {
 public:
  bool Init() override;
  absl::flat_hash_map<std::string, absl::flat_hash_map<int64_t, UnifyAdloadControlStrategyConf>> conf;
};

struct CommercialUserGroupClass
    : public ks::ad_base::kconf::KconfInitProto<kconf::CommercialUserGroup> {
 public:
  bool Init() override;
  absl::flat_hash_map<std::string, absl::flat_hash_set<std::string>> high_cpm_map;
  absl::flat_hash_map<std::string, absl::flat_hash_set<std::string>> low_cpm_map;
};

struct KuaiGameInspireConfClass
    : public ks::ad_base::kconf::KconfInitProto<kconf::KuaiGameInspireConf> {
 public:
  bool Init() override;
  absl::flat_hash_set<int64_t> page_id_set;
  absl::flat_hash_set<int64_t> pos_id_set;
  absl::flat_hash_map<std::string, double> admit_advertiser_map;
};

struct AdjustConf {
  double min_score = 1.0;
  double max_score = 1.0;
  double ecpc_weight = 1.0;
};

struct DetailConf {
  std::string predict_id;
  double ecpc_alpha = 1.0;
  double industry_model_ecpc_upper = 1.0;
  double industry_model_ecpc_lower = 1.0;
  std::vector<AdjustConf> manual_adjust_conf;
};

struct UpgradedIndustryModelEcpcConfClass
    : public ks::ad_base::kconf::KconfInitProto<kconf::UpgradedIndustryModelEcpcConf> {
 public:
  bool Init() override;
  absl::flat_hash_map<std::string, DetailConf> conf;
};

struct InnerAccountAdmitClass
    : public ks::ad_base::kconf::KconfInitProto<kconf::InnerAccountAdmit> {
 public:
  bool Init() override;
  std::vector<std::string> account_type_list;
  std::vector<int64> agent_id_list;
};

struct UnifyAdloadControlData : public ks::ad_base::kconf::KconfInitProto<kconf::UnifyAdloadControlOutput> {
  bool Init() override;
  absl::flat_hash_map<std::string, kconf::UnifyAdloadControlOutput_ControlOutput> kv_map;
};

struct RealestateDeepOptBase {
    std::unordered_set<int64_t> account_set;
    double pcvr_lowbound = 0.0;
    double pcvr_w = 0.0;
};

struct RealestateDeepOptConfig : public ks::ad_base::kconf::KconfInitProto<kconf::RealestateDeepOptConf> {
  bool Init() override;
  absl::flat_hash_map<std::string, absl::flat_hash_map<std::string, RealestateDeepOptBase>> conf;
};

struct UalDefConfStruct
    : public ks::ad_base::kconf::KconfInitProto<kconf::UalDefConf> {
  bool Init() override;
  bool Exist(const std::string& campaign_type, const std::string& ocpx_action_type) const;
 private:
  std::unordered_map<std::string, std::unordered_set<std::string>> config;
};
struct RetentionDaysDropListConfig
  : public ks::ad_base::kconf::KconfInitProto<kconf::RetentionDaysDropList> {   // NOLINT
  struct RetentionDaysDropIter {
    absl::flat_hash_set<int64_t> retention_product_ids;
    absl::flat_hash_set<int32_t> retention_account_ids;
    double threshold = 0.0;
  };
  std::vector<RetentionDaysDropIter> threshold_tool;
  bool Init() override;
};

struct OuterloopNcMaxOcpxBlackSetStruct
    : public ks::ad_base::kconf::KconfInitProto<kconf::OuterloopNcMaxOcpxBlackSet> {
  bool Init() override;
  bool Exist(const std::string& exp_bucket, const int64_t& ocpx_action_type) const;
 private:
  std::unordered_map<std::string, std::unordered_set<int64_t>> config;
};

struct IndustryCampaignTypeDarkControlConfigStruct :
    public ks::ad_base::kconf::KconfInitProto<kconf::IndustryCampaignTypeDarkControlConfig> {
  bool Init() override;
  bool GetCorporationNameConfigValue(const int64_t& corporation_id, double* value) const;
  bool GetIndustryCampaignTypeConfigValue(const int64_t& second_industry_id_v5,
                                         const kuaishou::ad::AdEnum::CampaignType& campaign_type,
                                         double* value) const;

 private:
  std::unordered_map<int64_t, double> corporation_id_map;
};

struct OuterloopNcMaxWlevelConfigStruct
    : public ks::ad_base::kconf::KconfInitProto<kconf::OuterloopNcMaxWlevelConfig> {
  bool Init() override;
  bool Exist(const std::string& exp_bucket, const std::string& w_level) const;
  std::unordered_map<std::string, std::unordered_map<std::string, int64_t>> topn_config;
  std::unordered_map<std::string, std::unordered_map<std::string, double>> hcratio_config;
};

struct OuterLoopAcIndustryStruct
    : public ks::ad_base::kconf::KconfInitProto<kconf::OuterLoopAcIndustryConfig> {
  bool Init() override;
  std::unordered_map<int64_t, std::unordered_set<int64_t>> config;
};

struct SensitiveUserScope {
  std::set<std::string> age_segment;
  std::vector<std::pair<int64_t, int64_t>> phone_price;
  std::set<std::string> user_active_degree;
  bool is_unlogin_user{false};
  bool is_down_active_user{false};
};

struct SensitiveUserCpmThrConf {
  SensitiveUserScope sensitive_user_scope;
  std::set<std::string> app_id;
  double cpm_thr{0.};
  explicit SensitiveUserCpmThrConf(
      const kconf::SplashRTBSensitiveUserCpmThrExpConfPb::SensitiveUserCpmThrConf&);
};

struct SplashRTBSensitiveUserCpmThrExpConf
    : public ks::ad_base::kconf::KconfInitProto<kconf::SplashRTBSensitiveUserCpmThrExpConfPb> {
  bool Init() override;
  std::unordered_map<std::string, SensitiveUserCpmThrConf> exp_config;
};

struct DisableFactorConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::DisableFactorConfigPb> {
  bool Init() override;
  bool InUdfList(const std::string &factor_name) const {
    return udf.find(factor_name) != udf.end();
  }
  bool InEcpcList(const std::string &factor_name) const {
    return ecpc.find(factor_name) != ecpc.end();
  }
  bool InFilterList(const std::string &factor_name) const {
    return filter.find(factor_name) != filter.end();
  }
 private:
  std::unordered_set<std::string> udf;
  std::unordered_set<std::string> ecpc;
  std::unordered_set<std::string> filter;
};

struct EcpmUnifyBoundConfig: public ks::ad_base::kconf::KconfInitProto<kconf::EcpmUnifyBoundConfigPb> {
 public:
  bool Init() override;
  // {lower_bound, upper_bound}, 单位：厘/次 == 元/千次
  std::pair<int64_t, int64_t> GetBounds(const ks::SessionContext& ab_context) const;

 private:
  int64_t GetValue(
    const ks::SessionContext& ab_context,
    const kconf::EcpmUnifyBoundConfigPb::Value& value,
    int64_t default_value) const;

 private:
  static const int64_t kHardLowerBound = 0;  // 硬编码兜底下界 0 厘
  static const int64_t kHardUpperBound = 100000;  // 硬编码兜底上界 100,000 厘（100 元）
};

struct OuterSelfServiceEcpcConfigStruct
    : public ks::ad_base::kconf::KconfInitProto<kconf::OuterSelfServiceEcpcConfigPb> {
  bool Init() override;
  struct EcpcConfig {
    absl::flat_hash_map<std::string, double> product_ecpc_config;
    absl::flat_hash_map<int64_t, double> industry_ecpc_config;
    absl::flat_hash_map<int64_t, double> account_ecpc_config;
  };
  absl::flat_hash_map<int64_t, EcpcConfig> exp_configs;
};

struct CommonInterventionEcpcConfig
    : public ks::ad_base::kconf::KconfInitProto<kconf::CommonInterventionEcpcConfigPb> {
  bool Init() override;
  bool Enable() const {
    return pb().enable();
  }
  bool InPageIdWhiteList(int64_t page_id) const {
    // 为空默认全部生效
    if (page_id_white_list.size() == 0) {
      return true;
    }
    return page_id_white_list.find(page_id) != page_id_white_list.end();
  }
  const std::string& GetTypeSelect() const {
    return pb().type_select();
  }
  bool GetItemTypeRatio(const std::string &key, double *ratio) const {
    if (!ratio) return false;
    auto iter = item_type_map.find(key);
    if (iter != item_type_map.end()) {
      *ratio =  iter->second;
      return true;
    }
    return false;
  }
  bool GetAccountTypeRatio(const std::string &key, double *ratio) const {
    if (!ratio) return false;
    auto iter = account_type_map.find(key);
    if (iter != account_type_map.end()) {
      *ratio =  iter->second;
      return true;
    }
    return false;
  }
  bool GetCampaignTypeRatio(const std::string &key, double *ratio) const {
    if (!ratio) return false;
    auto iter = campaign_type_map.find(key);
    if (iter != campaign_type_map.end()) {
      *ratio =  iter->second;
      return true;
    }
    return false;
  }
  bool GetOcpxActionTypeRatio(const std::string &key, double *ratio) const {
    if (!ratio) return false;
    auto iter = ocpx_action_type_map.find(key);
    if (iter != ocpx_action_type_map.end()) {
      *ratio =  iter->second;
      return true;
    }
    return false;
  }
 private:
  absl::flat_hash_set<int64_t> page_id_white_list;
  absl::flat_hash_map<std::string, double> item_type_map;
  absl::flat_hash_map<std::string, double> account_type_map;
  absl::flat_hash_map<std::string, double> campaign_type_map;
  absl::flat_hash_map<std::string, double> ocpx_action_type_map;
};

struct PmFwhEeParaStruct
    : public ks::ad_base::kconf::KconfInitProto<kconf::PmFwhEeParaPb> {
  bool Init() override;
  struct EcpcRatio {
    std::vector<double> low;
    std::vector<double> mid;
    std::vector<double> high;
  };
  absl::flat_hash_map<int64_t, EcpcRatio> group_map;
  double lower_bound;
  double upper_bound;
  std::unordered_set<int64_t> white_industry;
};


// end

}  // namespace ad_rank
}  // namespace ks
