syntax  = "proto3";
option cc_enable_arenas = true;
package ks.ad_rank.kconf;

import "teams/ad/ad_proto/kuaishou/fanstop/common/fans_top_enums.proto";

message ServerStrategyConf {
  message PluginConf {
    string name = 1;
    string enable_tag  = 2;
  }

  message CallPoint {
    repeated PluginConf default_scene = 1;
    repeated PluginConf universe_scene = 2;
    repeated PluginConf native_scene = 3;
  }
  map<string, CallPoint> calculate_benefit = 1;
  map<string, CallPoint> auction = 2;
  map<string, CallPoint> reserve_threshold = 3;
  map<string, CallPoint> amd_reserve_threshold = 4;
  map<string, CallPoint> amd_calc_benefit = 5;
  map<string, CallPoint> fanstop_ranking = 6;
  map<string, CallPoint> inner_fanstop_strategy = 7;
}

message RewardedCoinScalingByAccountConf {
  message RewardedCoinScalingConf {
    string coin_scaling_coef_tag = 1;
    string coin_upper_tag  = 2;
    string coin_lower_tag = 3;
    string deep_coin_scaling_coef_tag = 4;
    string deep_coin_upper_tag = 5;
    string deep_coin_lower_tag = 6;
  }
  map<int64, RewardedCoinScalingConf> rewarded_coin_scaling_conf_list = 1;
}

message PredictBoundValueConfigPb {
  message BoundConfig {
    double max = 1;
    double min = 2;
  }
  message CmdkeyConfig {
    BoundConfig default_conf = 1;
    map<string, BoundConfig> product_conf = 2;
  }
  map<string, CmdkeyConfig> cmdkey_config = 1;
}

message MultiPredictBoundValueConfigPb {
  map<string, PredictBoundValueConfigPb> version_map = 1;
}

message CalibrationConf {
  repeated double cvr_weights = 1;
  repeated double media_weights = 2;
  repeated double time_hour_weights = 3;
  repeated double action_type_weights = 4;
  repeated double new_creative_tag_weights = 5;
  repeated double platform_weights = 6;
  repeated double first_industry_weights = 7;
  repeated double medium_attri_weights = 8;
  repeated double account_weights = 9;
  double w_cvr = 11;
}

message CorporationCeilingWhiteListPb {
  repeated string product_names = 1;
  repeated int64 account_ids = 2;
}

message PecConfigItem {
  string display_type = 1;
  int64  rewarded_stage = 2;
  int64  shallow_rewarded_point = 3;
  int64  deep_rewarded_point = 4;
  int64  deep_rewarded_price = 5;
  repeated int64 coin_for_select = 6;
  repeated string coin_select_subkeys = 7;
  double impression_thr = 8;
  double pec_bid_weight = 9;
  double pec_reward_weight = 10;
  int64  pec_rewarded_price = 11;
  bool   enable_fix_price = 12;
  bool   enable_personal = 13;
  int64  pec_coin_type = 14;
  bool   is_first_pay_rewarded = 15;
  bool   enable_rebate_coin = 16;
  double rebate_percent = 17;
  int64  max_rebate_num = 18;
  repeated int64  deep_coin_for_select = 19;
  double  delta_cvr_ratio = 20;
  double  calibrate_ratio = 21;
  double  max_uplift_ratio= 22;
  double  reward_control_ratio = 23;
  double  pec_reward_rate = 24;
  double  deep_pec_reward_rate = 25;
}

message AdSmartOfferValues {
  message DiscountInfo {
    repeated uint64 single_price_range = 1;
    int32 discount_ratio = 2;
    int32 offer_ratio = 3;
    uint64 interval = 4;
  }
  message SmartOfferValue {
    bool use_default = 1;
    double default_ratio = 2;
    double pay_value = 3;
    double offer_value = 4;
    int64 package_lessons = 5;
    int64 ms_coin_amount = 6;
    int64 activity_interval = 7;
    double pay_adjust = 8;
    double offer_adjust = 9;
    double pay_weight = 10;
    double offer_weight = 11;
    map<int64, int64> tiered_value = 12;
    double min_conf_pay = 13;
    double min_conf_offer = 14;
    double max_conf_pay = 15;
    double max_conf_offer = 16;
    bool enable_mini_offer = 17;
    double ltv_score_thd  = 18;
    int64 lessons_without_offer = 19;
    int64 coins_without_offer = 20;
    double pay_without_offer = 21;
    double mini_offer = 22;
    repeated DiscountInfo package_discount = 23;
    DiscountInfo retain_info = 24;
    double uplift_result_ratio = 25;
    double uplift_result_adjust = 26;
    int64 chaoxiaoe_filtered_activity_interval = 27;
    double chaoxiaoe_filtered_pay_value = 28;
    double chaoxiaoe_filtered_offer_value = 29;
  }
    map<string, SmartOfferValue> version = 1;
}

message AdSmartOfferConfig {
  message ExpConf {
    map<string, string> item_conf =1;
    map<string, string> user_conf =2;
  }
  map<string, ExpConf> exp_list = 1;
}

message PecParamsConfig {
  map<string, PecConfigItem> confs = 1;
}

message PecPersonalizationCoinNum {
  map<string, int32> config = 1;
}

message PecRewardedPointMockConfig {
  message RewardedConfigItem {
    int32 rewarded_point = 1;
    string display_type = 2;
    int32  rewarded_stage = 3;
  }
  map<string, RewardedConfigItem> confs = 1;
}

message IncentiveAdMockConfig {
    message IncentiveConfigItem {
      int64 pec_rewarded_price = 1;
      int64 deep_pec_rewarded_price = 2;
    }
    map<string, IncentiveConfigItem> confs = 1;
}

message SensitiveThirdCategoryConfig {
  message SensitiveIdList {
    repeated int64 ids = 1;
  }
  map<string, SensitiveIdList> exp_list = 1;
}

message PecCouponDiscountConfig {
  message CvrUpliftList {
     repeated double uplift_cvrs = 1;
  }
  message PecCouponDiscountParam {
    repeated string tag = 1;  // 所需要用到的特征
    repeated int32 dim = 2;  // 各特征的维度大小 "dim" : [4, 3]
    double ltv_bucket_size = 3;
    double cvr_bucket_size = 4;
    map<string, double> optimal_discount = 5;
    map<string, CvrUpliftList> uplift_cvr_stat = 6;
    repeated double ltv_bucket_list = 7;
    repeated double cvr_bucket_list = 8;
    repeated double roas_cvr_bucket_list = 9;
  }
  map<string, PecCouponDiscountParam> exp_confs = 1;
}

message InnerLoopProjectBonusConf {
  repeated int64 invalid_bonus_tag = 1;
  double base_bonus_ratio = 2;
  double min_bonus_cpm = 3;
  double default_alpha = 4;
  map<int64, double> base_bonus_ratio_map = 5;
}

message RevertBonusTagsConf {
  message RevertBonusTags {
    repeated int64 tags = 1;
  }
  map<string, RevertBonusTags> confs = 1;
}

message ClueFwhIntentRatioConf {
  message FwhIntentRatio {
    repeated double ratio = 1;
  }
  map<string, FwhIntentRatio> confs = 1;
}

message GuessYouLikeMobileSctrConf {
  message GuessYouLikeMobileSctr {
    map<int64, double> sub_page_id_sctr = 1;
  }
  map<string, GuessYouLikeMobileSctr> confs = 1;
}

message TieredColdstartStrategyConf {
  message TieredColdstartStrategy {
    double imp_thre1 = 1;
    double boost_rate1 = 2;
    double imp_thre2 = 3;
    double pxs_thre2 = 4;
    double clk_thre2 = 5;
    double boost_rate2 = 6;
    double hit_rate2 = 7;
    double pxs_thre3 = 8;
    double clk_thre3 = 9;
    double boost_rate3 = 10;
    double hit_rate3 = 11;
    double cvt_thre = 12;
    double hit_rate0 = 13;
  }

message ModelScoreStrategy {
    double bonus_ratio1 = 1;
    double bonus_ratio2 = 2;
    double bonus_ratio3 = 3;
    double bonus_ratio4 = 4;
    double score_thre1 = 5;
    double score_thre2 = 6;
    double score_thre3 = 7;
    double score_thre4 = 8;
    double hc_ratio1 = 9;
    double hc_ratio2 = 10;
    double hc_ratio3 = 11;
    double hc_ratio4 = 12;
  }

  message SingleStrategyConf {
    map<int64, double> phase = 1;
  }
  map<string, TieredColdstartStrategy> photo_conf = 1;
  map<string, TieredColdstartStrategy> live_conf = 2;
  map<string, SingleStrategyConf> photo_phase = 3;
  map<string, SingleStrategyConf> live_phase = 4;
  map<string, SingleStrategyConf> similar_type = 5;
  map<string, SingleStrategyConf> item_phase = 6;
  int32 tag_size = 7;
  map<int32, double> tail_ratio = 8;
  int32 tag_size_v1 = 9;
  map<string, SingleStrategyConf> p2l_phase = 10;
  map<string, SingleStrategyConf> p2l_soft_phase = 11;
  map<string, SingleStrategyConf> p2l_hard_phase = 12;
  map<string, SingleStrategyConf> photo_soft_phase = 13;
  map<string, SingleStrategyConf> photo_hard_phase = 14;
  map<string, ModelScoreStrategy> p2l_model_phase = 15;
  map<string, ModelScoreStrategy> photo_model_phase = 16;
  map<int32, double> uplift_pid_conf = 17;
}

message FristnPriceRatio {
  map<int64, double> base = 1;
  map<int64, double> base1 = 2;
  map<int64, double> exp = 3;
  map<int64, double> exp1 = 4;
}

message OutsideEcpcConf {
    message FeatureValue {
      map<int64, string> account_id = 1;
      map<string, string> product_name = 2;
      string info = 3;
    }
    message AdmitConf {
      map<string, bool> product_name = 1;
      map<int64, bool> account_id = 2;
    }
    map<int64, FeatureValue> exp = 1;
    map<int64, FeatureValue> exp_splash = 2;
    AdmitConf admit = 3;
}

message NextStayRelevanceAdmitConf {
    message FeatureValue {
      double left_interval = 1;
      double right_interval = 2;
    }
    message Value {
      repeated FeatureValue feature_value = 1;
    }
    map<int64, Value> admit = 1;
}

message DirectMerchantMcdaConf {
  message McdaValue {
    double alpha_value = 1;
    double boost_up = 2;
    double boost_down = 3;
    double alpha = 4;
    double drop_alpha = 5;
    double drop_alpha_value = 6;
    int64 group_id = 7;
    int64 user_tag = 8;
  }
  map<string, McdaValue> admit = 1;
  map<string, McdaValue> admit_account = 2;
}

message CidGoodsTypeSupportNewConf {
  message GoodsTypeInfo {
    double bid_ratio = 1;
    double price_ratio = 2;
  }
  message DimZkInfo {
    double bid_ratio = 1;
    double price_ratio = 2;
    repeated int64 ocpxs = 3;
  }
  map<string, GoodsTypeInfo> goods_type_info = 1;
  double cid_goods_global_bid_ratio = 2;
  double cid_goods_global_price_ratio = 3;
  map<string, DimZkInfo> account_zk_info = 4;
  map<string, DimZkInfo> corporation_zk_info = 5;
}

message RewardCvrReviseConf {
  message ReviseNode {
    double reward_r = 1;
    double normal_r = 2;
    string reward_style = 3;
  }
  message ReviseConf {
    repeated ReviseNode revise_node = 1;
  }
  map<string, ReviseConf> admit = 1;
}

message MerchantPlatformHcConf {
  message GpmValue{
    map<string, string> ad_purchase = 1;
    map<string, string> app_invoked = 2;
    map<string, string> ad_conversion = 3;
  }

  message GpmDoubleValue{
    map<string, double> ad_purchase = 1;
    map<string, double> app_invoked = 2;
    map<string, double> ad_conversion = 3;
    map<string, string> ad_paytimes = 4;
  }

  message SuportValue {
    map<string, string> rta = 1;
    map<string, string> adx = 2;
    map<string, string> other = 3;
  }

  message SuportDoubleValue {
    map<string, double> rta = 1;
    map<string, double> adx = 2;
    map<string, double> other = 3;
  }

  message DefaultValue {
    map<string, string> ads = 1;
  }

  message DefaultDoubleValue {
    map<string, double> ads = 1;
  }

  GpmValue gpm = 1;
  SuportValue suport = 2;
  DefaultValue other = 3;
  double gpm_ratio = 4;
  double gpm_default_price = 5;
  double suport_ratio = 6;
  double suport_ecpc_bound_up = 7;
  double suport_ecpc_bound_down = 8;
  double cpm_ratio = 9;
  double hc_bound_up_value = 10;
  double hc_ratio_thr = 11;
  double app_invoked_gpm_ratio = 12;
  double ad_conversion_gpm_ratio = 13;
  GpmDoubleValue gpm_info = 14;
  SuportDoubleValue suport_info = 15;
  DefaultDoubleValue other_info = 16;
}

message OutsideEcpcConfDetail {
    message FeatureValue {
      double up_value = 1;
      double down_value = 2;
      double e_value = 3;
      double p_value = 4;
      int64 ecpc_end_point = 5;
      bool begin_style = 6;
      bool drop_and_take_ecpc = 7;
      double drop_down_value = 8;
      double take_up_value = 9;
      }
    map<string, FeatureValue> version = 1;
}

message LALpsValidCluesProductEcpcConfig {
  message Values {
    double up_value = 1;  //  调价系数上界
    double down_value = 2;  // 调价系数下限
    int32 req_threshold = 3; // 产品维度请求阈值下限
    double decay_weight = 4;
    double cvr_coeff = 5;
  }
  map<int64, Values> exp = 1;
}

message HcPidKconf {
  message Value {
    map<int64, string> second_industry_ids = 1;
    map<int64, string> campaign_types = 2;
    map<int64, string> ocpc_action_types = 3;
    bool second_industry_valid = 4;
    bool campaign_type_valid = 5;
    bool ocpc_action_type_valid = 6;
    double industry_hc_up_ratio = 7;   // pv 行业 hc 上限
    double industry_hc_up_value = 8;   // pv hc 上限
  }
  map<int64, Value> first_industry_ids = 1;
  bool first_industry_valid = 2;
  map<int64, Value> crm_center_ids = 3;
  bool crm_center_valid = 4;
}

message PtdsFirstClickConf {
  message Values {
      double up_value = 1;  //  调价系数上界
      double down_value = 2;  // 调价系数下限
      double e_value = 3;  // tanh 函数自变量权重  越大 调价越激进
      double p_value = 4;  // 调价系数的 偏移系数
  }
  message FeatureValue {
    map<int64, Values> account_id = 1;
    map<string, Values> product_name = 2;
  }
  map<int64, FeatureValue> exp = 1;
}

message UnitTailSet {
  repeated uint32 unit_tail_set = 1;
}
message MerchantMcbOcpxUnitTailMap {
  map<string, UnitTailSet> value_map = 1;
}

message BonusDropStaConf {
   message FeatureValue {
     map<int64, double> unit_id = 1;
     map<int64, double> account_id = 2;
     map<string, double> product_name = 3;
   }
   map<int64, FeatureValue> bonus_drop = 1;
}

message NearbyRankAdmitWhiteListConfig {
  message Interval {
    repeated string value = 1;
  }
  message IntervalArr {
    map<string, Interval> values = 1;
  }
  map<string, IntervalArr> interval_arr = 1;
}

message MinBidWhiteListConfig {
  message Interval {
    repeated string value = 1;
  }
  message IntervalArr {
    map<string, Interval> values = 1;
  }
  map<string, IntervalArr> interval_arr = 1;
}

message McbEcpcUnitRange {
  message UnitRange {
    int64 tail_start = 1;
    int64 tail_end = 2;
  }
  map<string, UnitRange> mcb_ecpc = 1;
}

message OnlineUnifyCtrCaliManuallyConfig {
  map<int64, double> account_ratio = 1;
  map<string, double> product_ratio = 2;
  map<string, double> ocpx_account_ratio = 3;
  map<string, double> ocpx_product_ratio = 4;
  map<string, double> page_account_ratio = 5;
  map<string, double> page_product_ratio = 6;
  map<string, double> subpage_account_ratio = 7;
  map<string, double> subpage_product_ratio = 8;
  map<string, double> queue_account_ratio = 9;
  map<string, double> queue_product_ratio = 10;
  map<string, double> campaign_account_ratio = 11;
  map<string, double> campaign_product_ratio = 12;
  map<string, double> creativetype_bidtype_ocpx_product_ratio = 13;
  map<string, double> creativetype_bidtype_ocpx_account_ratio = 14;
  map<string, double> creativetype_bidtype_ocpx_deepconv_product_ratio = 15;
  map<string, double> creativetype_bidtype_ocpx_deepconv_account_ratio = 16;
  map<string, double> itemtype_product_ratio = 17;
  map<string, double> itemtype_account_ratio = 18;
  map<string, double> account_campaign_ratio = 19;
  map<string, double> account_campaign_page_ratio = 20;
  map<string, double> account_ocpx_ratio = 21;
  map<string, double> account_ocpx_page_ratio = 22;
  map<string, double> account_campaign_ocpc_ratio = 23;
  map<string, double> account_campaign_ocpc_page_ratio = 24;
  map<string, double> account_str_ratio = 25;
}
message OnlineUnifyCvrCaliManuallyConfig {
  map<int64, double> account_ratio = 1;
  map<string, double> product_ratio = 2;
  map<string, double> ocpx_account_ratio = 3;
  map<string, double> ocpx_product_ratio = 4;
  map<string, double> page_account_ratio = 5;
  map<string, double> page_product_ratio = 6;
  map<string, double> subpage_account_ratio = 7;
  map<string, double> subpage_product_ratio = 8;
  map<string, double> queue_account_ratio = 9;
  map<string, double> queue_product_ratio = 10;
  map<string, double> campaign_account_ratio = 11;
  map<string, double> campaign_product_ratio = 12;
  map<string, double> creativetype_bidtype_ocpx_product_ratio = 13;
  map<string, double> creativetype_bidtype_ocpx_account_ratio = 14;
  map<string, double> creativetype_bidtype_ocpx_deepconv_product_ratio = 15;
  map<string, double> creativetype_bidtype_ocpx_deepconv_account_ratio = 16;
  map<string, double> itemtype_product_ratio = 17;
  map<string, double> itemtype_account_ratio = 18;
  map<string, double> account_campaign_ratio = 19;
  map<string, double> account_campaign_page_ratio = 20;
  map<string, double> account_ocpx_ratio = 21;
  map<string, double> account_ocpx_page_ratio = 22;
  map<string, double> account_campaign_ocpc_ratio = 23;
  map<string, double> account_campaign_ocpc_page_ratio = 24;
  map<string, double> account_str_ratio = 25;
}
message OnlineUnifyDcvrCaliManuallyConfig {
  map<int64, double> account_ratio = 1;
  map<string, double> product_ratio = 2;
  map<string, double> ocpx_account_ratio = 3;
  map<string, double> ocpx_product_ratio = 4;
  map<string, double> page_account_ratio = 5;
  map<string, double> page_product_ratio = 6;
  map<string, double> subpage_account_ratio = 7;
  map<string, double> subpage_product_ratio = 8;
  map<string, double> page_account_ocpx_deepconv_ratio = 9;
  map<string, double> page_product_ocpx_deepconv_ratio = 10;
  map<string, double> queue_account_ratio = 11;
  map<string, double> queue_product_ratio = 12;
  map<string, double> campaign_account_ratio = 13;
  map<string, double> campaign_product_ratio = 14;
  map<string, double> creativetype_bidtype_ocpx_product_ratio = 15;
  map<string, double> creativetype_bidtype_ocpx_account_ratio = 16;
  map<string, double> creativetype_bidtype_ocpx_deepconv_product_ratio = 17;
  map<string, double> creativetype_bidtype_ocpx_deepconv_account_ratio = 18;
  map<string, double> itemtype_product_ratio = 19;
  map<string, double> itemtype_account_ratio = 20;
}
message OnlineUnifyLtvCaliManuallyConfig {
  map<int64, double> account_ratio = 1;
  map<string, double> product_ratio = 2;
  map<string, double> ocpx_account_ratio = 3;
  map<string, double> ocpx_product_ratio = 4;
  map<string, double> page_account_ratio = 5;
  map<string, double> page_product_ratio = 6;
  map<string, double> subpage_account_ratio = 7;
  map<string, double> subpage_product_ratio = 8;
  map<string, double> page_account_ocpx_deepconv_ratio = 9;
  map<string, double> page_product_ocpx_deepconv_ratio = 10;
  map<string, double> queue_account_ratio = 11;
  map<string, double> queue_product_ratio = 12;
  map<string, double> campaign_account_ratio = 13;
  map<string, double> campaign_product_ratio = 14;
  map<string, double> creativetype_bidtype_ocpx_product_ratio = 15;
  map<string, double> creativetype_bidtype_ocpx_account_ratio = 16;
  map<string, double> creativetype_bidtype_ocpx_deepconv_product_ratio = 17;
  map<string, double> creativetype_bidtype_ocpx_deepconv_account_ratio = 18;
  map<string, double> itemtype_product_ratio = 19;
  map<string, double> itemtype_account_ratio = 20;
  map<string, double> account_campaign_ratio = 21;
  map<string, double> account_campaign_page_ratio = 22;
  map<string, double> account_ocpx_ratio = 23;
  map<string, double> account_ocpx_page_ratio = 24;
  map<string, double> account_campaign_ocpc_ratio = 25;
  map<string, double> account_campaign_ocpc_page_ratio = 26;
  map<string, double> account_str_ratio = 27;
}
message OnlineUnifyCtrDropManuallyConfig {
  map<string, double> account_thr = 1;
  map<string, double> product_thr = 2;
  map<string, double> ocpx_account_thr = 3;
  map<string, double> ocpx_product_thr = 4;
  map<string, double> page_account_thr = 5;
  map<string, double> page_product_thr = 6;
  map<string, double> subpage_account_thr = 7;
  map<string, double> subpage_product_thr = 8;
  map<string, double> queue_account_thr = 9;
  map<string, double> queue_product_thr = 10;
}
message OnlineUnifyCvrDropManuallyConfig {
  map<string, double> account_thr = 1;
  map<string, double> product_thr = 2;
  map<string, double> ocpx_account_thr = 3;
  map<string, double> ocpx_product_thr = 4;
  map<string, double> page_account_thr = 5;
  map<string, double> page_product_thr = 6;
  map<string, double> subpage_account_thr = 7;
  map<string, double> subpage_product_thr = 8;
  map<string, double> queue_account_thr = 9;
  map<string, double> queue_product_thr = 10;
}
message OnlineUnifyDcvrDropManuallyConfig {
  map<string, double> account_thr = 1;
  map<string, double> product_thr = 2;
  map<string, double> ocpx_account_thr = 3;
  map<string, double> ocpx_product_thr = 4;
  map<string, double> page_account_thr = 5;
  map<string, double> page_product_thr = 6;
  map<string, double> subpage_account_thr = 7;
  map<string, double> subpage_product_thr = 8;
  map<string, double> queue_account_thr = 9;
  map<string, double> queue_product_thr = 10;
}

message AdxCpmTaxesConf {
  message ExpConf {
    map<string, double> taxes = 1;
    repeated string white_install_apps = 2;
  }
  repeated int64 page_id = 1;
  map<string, ExpConf> exp = 2;
}

message OverseasTrafficManuallyConfig {
  map<string, double> corporation_name_account_type_list = 1;
  map<string, double> account_id_account_type_list = 2;
  map<string, double> corporation_name_account_id_list = 3;
  map<string, double> corporation_name_account_id_account_type_list = 4;
}

message CommonOfflineCalibrateWhite {
  map<string, string> ctr_ocpc = 1;
  map<string, string> cvr_ocpc = 2;
  map<string, string> dcvr_ocpc_dconv = 3;
}

message CommonOfflineCalibrateBlack {
  map<string, string> ctr_products = 1;
  map<string, string> ctr_accounts = 2;
  map<string, string> cvr_products = 3;
  map<string, string> cvr_accounts = 4;
  map<string, string> dcvr_products = 5;
  map<string, string> dcvr_accounts = 6;
  map<string, string> ctr_ocpx = 7;
  map<string, string> cvr_ocpx = 8;
  map<string, string> dcvr_ocpx = 9;
  map<string, string> ctr_secv5 = 10;
  map<string, string> cvr_secv5 = 11;
  map<string, string> dcvr_secv5 = 12;
}

message GameOptWhitelistConf {
  message Info {
    map<int64, double> account_ids = 1;
    map<string, double> product_names = 2;
  }
  map<string, Info> conf_list = 1;
}

message MtProductAccountExp {
  message MtPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtLps {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtJinjian {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtCov {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtKeyInapp {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvReten {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtRoas {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvThirdParty {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtPurchaseThirdParty {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  map<int64, MtPurchase> mt_purchase = 1;
  map<int64, MtLps> mt_lps = 2;
  map<int64, MtJinjian> mt_jinjian = 3;
  map<int64, MtCov> mt_cov = 4;
  map<int64, MtKeyInapp> mt_key_inapp = 5;
  map<int64, MtConvReten> mt_conv_reten = 6;
  map<int64, MtRoas> mt_roas = 7;
  map<int64, MtConvThirdParty> mt_conv_third_party = 8;
  map<int64, MtPurchaseThirdParty> mt_purchase_third_party = 9;
  map<int64, MtConvPurchase> mt_conv_purchase = 10;
}

message MtProductAccountLaunch {
  message MtPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtLps {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtJinjian {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtCov {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtKeyInapp {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvReten {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtRoas {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvThirdParty {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtPurchaseThirdParty {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  map<int64, MtPurchase> mt_purchase = 1;
  map<int64, MtLps> mt_lps = 2;
  map<int64, MtJinjian> mt_jinjian = 3;
  map<int64, MtCov> mt_cov = 4;
  map<int64, MtKeyInapp> mt_key_inapp = 5;
  map<int64, MtConvReten> mt_conv_reten = 6;
  map<int64, MtRoas> mt_roas = 7;
  map<int64, MtConvThirdParty> mt_conv_third_party = 8;
  map<int64, MtPurchaseThirdParty> mt_purchase_third_party = 9;
  map<int64, MtConvPurchase> mt_conv_purchase = 10;
}

message PreMtProductAccount {
  message MtPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtLps {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtJinjian {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtCov {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtKeyInapp {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtConvReten {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtRoas {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  map<int64, MtPurchase> mt_purchase = 1;
  map<int64, MtLps> mt_lps = 2;
  map<int64, MtJinjian> mt_jinjian = 3;
  map<int64, MtCov> mt_cov = 4;
  map<int64, MtKeyInapp> mt_key_inapp = 5;
  map<int64, MtConvReten> mt_conv_reten = 6;
  map<int64, MtRoas> mt_roas = 7;
}

message McdaUpExtraWhiteList {
  message Conv {
    map<string, double> product_name = 1;
  }
  message Lps {
    map<string, double> product_name = 1;
  }
  message Jinjian {
    map<string, double> product_name = 1;
  }
  message Purchase {
    map<string, double> product_name = 1;
  }
  map<int64, Conv> conv = 1;
  map<int64, Lps> lps = 2;
  map<int64, Jinjian> jinjian = 3;
  map<int64, Purchase> purchase = 4;
}

message InnerMcdaProductAccountExp {
  message MtPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtLps {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtJinjian {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtCov {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtKeyInapp {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  map<int64, MtPurchase> mt_purchase = 1;
  map<int64, MtLps> mt_lps = 2;
  map<int64, MtJinjian> mt_jinjian = 3;
  map<int64, MtCov> mt_cov = 4;
  map<int64, MtKeyInapp> mt_key_inapp = 5;
}

message InnerMcdaProductAccountLaunch {
  message MtPurchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtLps {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtJinjian {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtCov {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  message MtKeyInapp {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  map<int64, MtPurchase> mt_purchase = 1;
  map<int64, MtLps> mt_lps = 2;
  map<int64, MtJinjian> mt_jinjian = 3;
  map<int64, MtCov> mt_cov = 4;
  map<int64, MtKeyInapp> mt_key_inapp = 5;
}

message NovelConvPurchaseWhiteConf {
  message Purchase {
    map<int64, double> account_id = 1;
    map<string, double> product_name = 2;
  }
  map<int64, Purchase> novel_purchase = 1;
}

message InvokeEcpcConf {
  message EcpcValues {
    double max_bound = 1;
    double min_bound = 2;
    double p_value = 3;
    double left_thr = 4;
    double right_thr = 5;
  }
  map<int64, EcpcValues> account_id = 1;
}

message FederatedWhitelistConfig {
  map<string, double> product_name = 1;
  map<int64, double> account = 2;
  map<string, double> product_name_pay = 3;
  map<int64, double> account_pay = 4;
}
// end
