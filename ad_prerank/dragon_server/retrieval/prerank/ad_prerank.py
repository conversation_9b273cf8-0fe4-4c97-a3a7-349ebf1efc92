#!/usr/bin/env python3
# coding=utf-8

import os
import sys
import copy
dragon_path=os.path.abspath('../../../../../../dragon/')
ad_prerank_path=os.path.abspath('../../..')
dragon_node_path = os.path.abspath('../../../../engine_base/dragon_node/py/')
dragon_exts = os.path.abspath('../../../../dragon_exts')
sys.path.append(dragon_path)
sys.path.append(ad_prerank_path)
sys.path.append('../../../../../../')
sys.path.append(dragon_node_path)
sys.path.append(dragon_exts)
from typing import Optional
from dragonfly.common_leaf_dsl import LeafFlow, OfflineRunner, LeafService, IndexSource
from dragonfly.common_leaf_flow import LeafFlowCore
from dragonfly.ext.ad_model.ad_model_mixin import AdModelApiMixin
from dragonfly.ext.ad_router.ad_router_mixin import AdRouterApiMixin
from dragonfly.common_leaf_util import get_dynamic_param
from ad_dragonfly.ad_prerank.ad_prerank_mixin import AdPrerankApiMixin
from ad_dragonfly.mechanism.ad_bid_service.ad_bid_service_mixin import AdBidServiceApiMixin
from ad_dragonfly.base.model.model_mixin import ModelApiMixin
from ad_dragonfly.base.params.params_mixin import ParamsApiMixin
from ks_leaf_functional.core.module import module
from dragonfly.decorators import parallel, async_retrieve, async_enrich
from ks_leaf_bootstrap.utils import gen_service_graph_def
from dragonfly.common_leaf_dsl import LeafFlow, current_flow
from dragonfly.modular.data_manager import DataManager, data_manager, ab_param as ab, kconf_param as kconf
from teams.ad.ad_feature_index.remote_table_sdk.remote_table_mixin import  RemoteAdTableMixin
from teams.ad.ad_feature_index.remote_table_sdk.remote_table_util import extract_key_attrs
from teams.ad.grid.remote_table.remote_table_mixin import RemoteGridMixin
from dragon_server.retrieval.prerank.remote_table_config import remote_bid_config, remote_bid_config_new_fields, all_new_fields
from dragon_server.retrieval.prerank.remote_table_config import gen_diff_config, get_diff_attr
from typing_extensions import Self
from fake_node_output_mixin import FakeNodeOutputMixin

DataManager.set_user_id_attr("user_id_for_ab_param")

bid_info_columns = ["deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
                        "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
                        "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
                        "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
                        "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
                        "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
                        "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
                        "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
                        "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr"
                        ,"product_cpa_bid","product_roi_ratio", "first_coef", "second_coef", "deep_cpa_bid",
                        "auto_cpa_bid_exp","fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio",
                        "auto_bid_explore", "total_cost", "total_target_cost",
                        "today_cost", "today_target_cost", "cost", "target_cost",
                        "bid_coef", "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "cpa_bid_coef", "aigc_bid_coef",
                        "use_ap_gimbal_ratio", "inspire_dark_control_pid_value", "wentou_campaign_calibration","cost_cap_type", "inspire_auto_dark_filter",
                        "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio",
                        "raw_auto_roas", "auto_roas_modify_overlay", "auto_cpa_bid_modify_overlay",
                        "ad_table_unit_bid_info_campaign_table_id", "ad_table_unit_bid_info_unit_table_id", "ad_table_unit_bid_info_account_table_id", "page_achieve_ratio",
                        "photo_ocpc_storewide_cali","photo_ocpc_order_cali","wentou_increment_bid_coef",]

class AbOptSwitcher:

  def __init__(self) -> None:
    self.__enable_ab_opt = False

  def __enter__(self):
    if self.__enable_ab_opt:
      return
    else:
      data_manager.__enter__()

  def __exit__(self, exc_type, exc_val, exc_tb):
    if self.__enable_ab_opt:
      return
    else:
      data_manager.__exit__()

  def set_enable_ab_opt(self, enable_ab_opt: bool):
    self.__enable_ab_opt = enable_ab_opt

class BidInfoFlow(LeafFlow, AdPrerankApiMixin):
  def get_live_bid_info(self):
    self \
    .bid_info_enricher(
      item_tables = ["inner_live_item_table", "outer_live_item_table", "one_step_live_item_table"],
      kess_name = "ad-dragon-bid-service",
      ad_queue_type = 0,
      is_inner_ad = False,
      dragon_prerank_fix_cpa_bid = '{{' + ab("dragon_prerank_fix_cpa_bid", False) + '}}',
      import_common_attrs = ["llsid", "deep_group_tag", "group_tag_enum", "page_id", "bid_service_failed",
                           "sub_page_id", "request_flow_type", "deep_coef_groug_tag", "flow_type",
                           "target_deploy", "pos_id", "is_rewarded", "is_inspire_live", "is_inspire_mix", "age_segment", "gender", "buyer_effective_type"],
      import_item_attrs = ["unit_id", "creative_id", "live_creative_type", "ad_queue_type", "is_inner_ad",
                           "is_fan_follow", "is_no_sale_live_stream_ad", "cpa_bid_coef", "photo_id", "auto_dark_control", "payment_per_order", "creative_material_type"],
      output_columns = ["deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
                        "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
                        "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
                        "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
                        "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
                        "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
                        "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
                        "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
                        "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr"
                        , "product_cpa_bid","product_roi_ratio", "cost_cap_type", "first_coef", "second_coef",
                        "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
                        "total_cost", "total_target_cost", "bid_coef",
                        "today_cost", "today_target_cost", "cost", "target_cost",
                        "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "aigc_bid_coef",
                        "use_ap_gimbal_ratio", "inspire_dark_control_pid_value", "wentou_campaign_calibration", "inspire_auto_dark_filter",
                        "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "wentou_increment_bid_coef"]
    )
    return self

  def get_out_photo_bid_info(self):
    self \
    .bid_info_enricher(
      item_tables = ["outer_hard_photo_item_table", "outer_soft_photo_item_table"],
      kess_name = "ad-dragon-bid-service",
      ad_queue_type = 0,
      is_inner_ad = False,
      dragon_prerank_fix_cpa_bid = '{{' + ab("dragon_prerank_fix_cpa_bid", False) + '}}',
      import_common_attrs = ["llsid", "deep_group_tag", "group_tag_enum", "page_id", "bid_service_failed",
                           "sub_page_id", "request_flow_type", "deep_coef_groug_tag", "flow_type",
                           "target_deploy", "pos_id", "is_rewarded", "is_inspire_live", "is_inspire_mix", "age_segment", "gender", "buyer_effective_type"],
      import_item_attrs = ["unit_id", "creative_id", "live_creative_type", "ad_queue_type", "is_inner_ad", "cpa_bid_coef", "auto_dark_control", "payment_per_order", "creative_material_type"],
      output_columns = ["deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
                        "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
                        "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
                        "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
                        "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
                        "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
                        "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
                        "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
                        "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr"
                        ,"product_cpa_bid","product_roi_ratio", "first_coef", "second_coef", "deep_cpa_bid",
                        "auto_cpa_bid_exp","fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio",
                        "auto_bid_explore", "total_cost", "total_target_cost",
                        "today_cost", "today_target_cost", "cost", "target_cost",
                        "bid_coef", "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "cpa_bid_coef", "aigc_bid_coef",
                        "use_ap_gimbal_ratio", "inspire_dark_control_pid_value", "wentou_campaign_calibration", "inspire_auto_dark_filter",
                        "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "wentou_increment_bid_coef"]
    )
    return self

# Flow definition
class AdPrerankFlow(LeafFlow, AdPrerankApiMixin, ModelApiMixin, AdModelApiMixin, ParamsApiMixin, RemoteAdTableMixin, AdBidServiceApiMixin, FakeNodeOutputMixin, RemoteGridMixin, AdRouterApiMixin):
  @module()
  def bid_prepare(self):
    """
    参数预处理阶段
    1) 导入 ab 参数
    2) 反序列化 Message: MixRankRequest
    3) 定制化 Processor:mix_rank_pre_process, 完成不同 mix 策略前共同的预处理工作
    """
    current_flow() \
    .get_kconf_params(
      kconf_configs=[
        dict(kconf_key="ad.engine.enableBudgetTagPartition",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_budget_tag_partition"
        ),
        dict(kconf_key="ad.bidServer.enableGetNobidOrderPaiedGroup",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_nobid_order_paied"
        ),
        dict(kconf_key="ad.bidServer.enableGetNobidOrderPaiedGroupNew",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_nobid_order_paied_new"
        ),
        dict(kconf_key="ad.bidServer.enableGetNobidRoasGroup",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_nobid_roas"
        ),
        dict(kconf_key="ad.bidServer.enableMerchantFollowRoi",
           value_type="bool",
           default_value=True,
           export_common_attr="enable_nobid_follow_roi"
        ),
        dict(kconf_key="ad.bidServer.enableRecoMerchant",
           value_type="bool",
           default_value=True,
           export_common_attr="enable_reco_merchant"
        ),
        dict(kconf_key="ad.adserver2.enableGetMerchantAutoRoas",
           value_type="bool",
           default_value=True,
           export_common_attr="enable_live_roas"
        ),
        dict(kconf_key="ad.adserver2.enableGetMerchantLiveCpaBid",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_live_cpa"
        ),
        dict(kconf_key="ad.adserver2.enableGetJinnusROAS",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_jinniu_roas"
        ),
        dict(kconf_key="ad.adserver2.enableGetRecoROAS",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_reco_roas"
        ),
        dict(kconf_key="ad.bidServer2.enableGetMerchantStorewideGroupTag",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_merchant_storewide"
        ),
        dict(kconf_key="ad.adtarget.universeLiveMerchant",
           value_type="bool",
           default_value=True,
           export_common_attr="enable_universe_live_merchant"
        ),
        dict(kconf_key="ad.bidServer.enableAccountUnitPriceRatio",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_account_unit_price_ratio"
        ),
        dict(kconf_key="ad.bidServer.competeCampaignConfig",
           value_type="json",
           json_path="topic",
           export_common_attr="compete_campaign_topic"
        ),
        dict(kconf_key="ad.adserver.payTimesNum",
           value_type="double",
           default_value=10.0,
           export_common_attr="pay_times_num"
        ),
        dict(kconf_key="ad.bidServer.enableBidServiceTraceLog",
           value_type="bool",
           default_value=True,
           export_common_attr="enable_bid_service_trace_log"
        ),
        dict(kconf_key="ad.bidServer.BidServiceTraceLogRatio",
           value_type="double",
           default_value=0.1,
           export_common_attr="bid_service_trace_log_ratio"
        ),
        dict(kconf_key="ad.bidServer.enableProjectInfo",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_project_info"
        ),
        dict(kconf_key="ad.bidServer.enableLiveOcpmBidColdStartModifyTag",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_live_ocpm_bid_cold_start_modifyTag"
        ),
        dict(kconf_key="ad.bidServer.enableSendCompeteCampaignInfo",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_send_compete_campaign_info"
        ),
        dict(kconf_key="ad.bidServer.enableFanstopCampaignLaunchType",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_fanstop_campaign_launch_type"
        ),
        dict(kconf_key="ad.bidServer.adRankEspLiveAtvValue",
           value_type="double",
           default_value=50.0,
           export_common_attr="esp_live_roas_atv_default_value"
        ),
        dict(kconf_key="ad.bidServer.enableUnloginControl",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_unlogin_control"
        ),
        dict(kconf_key="ad.adtarget3.enableTraceBase",
           value_type="bool",
           default_value=False,
           export_common_attr="enable_trace_base"
        )
    ]) \
    .if_('enable_unlogin_control > 0 and is_unlogin_user > 0') \
      .get_abtest_params(
        biz_name = "AD_DSP",
        user_id = 0,
        ab_params = [
          ("user_budget_charge_tag_dsp", 0),
          ("merchant_no_bid_order_paied_group_new", "adlogfull_ocpm"),
          ("merchant_no_bid_order_paied_group", "adlogfull_ocpm"),
          ("merchant_no_bid_roas_group", "adlogfull_ocpm"),
          ("merchant_no_bid_follow_roi_group", "adlogfull_ocpm"),
          ("merchant_no_bid_group", "adlogfull_ocpm"),
          ("merchant_reco_group", "adlogfull_ocpm"),
          ("merchant_live_roas_bid_group_tag", "adlogfull_ocpm"),
          ("storewide_live_bid_group_tag", "adlogfull_ocpm"),
          ("storewide_merchant_bid_group_tag", "adlogfull_ocpm"),
          ("merchant_live_cpa_bid_group_tag", "adlogfull_ocpm"),
          ("jinniu_ad_merchant_roas_bid_group_tag", "adlogfull_ocpm"),
          ("merchant_reco_roas_bid_group_tag", "adlogfull_ocpm"),
          ("enable_universe_nobid_order_paied", False),
          ("enable_universe_nobid_roas", False),
          ("merchant_universe_no_bid_roas_group", "adlogfull_ocpm"),
          ("enable_universe_nobid_follow_roi", False),
          ("merchant_universe_no_bid_follow_roi_group", "adlogfull_ocpm"),
          ("enable_universe_nobid_other", False),
          ("merchant_universe_no_bid_group", "universe_ocpm"),
          ("universe_live_no_bid_cpa_group", "universe_ocpm"),
          ("universe_photo_roas_merchant_group", "universe_ocpm"),
          ("universe_live_merchant_group", "universe_ocpm"),
          ("esp_nobid_unit_cpa_bid_ratio", 0.5),
          ("enable_esp_nobid_unit_cpa_bid", False),
          ("enable_esp_nobid_unit_cpa_bid_max", False),
          ("enable_esp_deli_nobid_cpa_bid", False),
          ("enable_fanstop_temu_nobid_cpa_bid", False),
          ("search_flow_skip_auto_cpa_bid", False),
          ("inner_search_flow_skip_auto_cpa_bid", False),
          ("user_budget_charge_tag_fanstop", 0),
          ("auto_bid_data_source", 0),
          ("universe_photo_cpa_merchant_group", "universe_ocpm"),
          ("enable_post_server_author_atv",False),
          ("enable_post_server_author_fade_atv",False),
          ("rta_ratio_exp_sta_tag",0),
          ("enable_ptds_auto_cpa_bid_rewrite",False),
          ("enable_ptds_auto_cpa_bid_rewrite_price",False),
          ("mh_tcpl_autobid_multiplier_str",""),
          ("bid_operability_multiplier", 1.0),
          ("mh_tcpl_autobid_seed_str",""),
          ("ptds_auto_cpa_bid_rewrite_tag",0),
          ("ptds_auto_cpa_bid_rewrite_price_tag",0),
          ("enable_inner_loop_default_exp",False),
          ("is_live_default_exp",False),
          ("is_merchant_reco_default_exp",False),
          ("is_merchant_reco_roas_default_exp",False),
          ("is_jinniu_default_exp",False),
          ("enable_mobile_use_bid_target",False),
          ("enable_fill_atv_for_all_ocpx",False),
          ("enable_fill_atv_for_storewide",False),
          ("enable_inner_campaign_bid_unit_price",False),
          ("enable_inner_campaign_bid_unit_trans",False),
          ("enable_ab_bid_explore_group_idx", False),
          ("ab_bid_explore_group_idx", 0),
          ("enable_ecpc_bidservice",False),
          ("is_holdout_live_storewide_ecpc",False),
          ("is_holdout_photo_storewide_ecpc",False),
          ("enable_storewide_incompatible_bidservice_ecpc",False),
          ("enable_roas_block_no_sale_ad",False),
          ("enable_storewide_follow_cali_ab",False),
          ("enable_touda_author_cali",False),
          ("enable_page_bid_cali",False),
          ("enable_mobile_page_bid_cali",False),
          ("enable_switch_to_flow_page_bid_cali",False),
          ("enable_inner_nobid_page_cali",False),
          ("enable_mobile_switch_to_flow_page_bid_cali",False),
          ("enable_follow_select_split_cali",False),
          ("enable_hosting_follow_select_cali",False),
          ("enable_default_follow_select_cali",False),
          ("follow_select_cali_block_eop",False),
          ("follow_select_cali_block_roas",False),
          ("select_explore_cali_split",False),
          ("enable_achieve_page_bid_cali",False),
          ("enable_achieve_page_bid_tail_cali",False),
          ("enable_achieve_page_bid_mobile_tail_cali",False),
          ("enable_cap_achieve_page_bid",False),
          ("enable_cap_achieve_page_bid_mobile",False),
          ("enable_storewide_search_page_adjust_bid",False),
          ("page_bid_cali_key",""),
          ("enable_inner_campaign_bid_cali",False),
          ("storewide_auto_roas_adjust_weight", 1.0),
          ("cold_storewide_auto_roas_adjust_weight", 1.0),
          ("storewide_soft_queue_boost_weight", 1.0),
          ("storewide_hard_queue_boost_weight", 1.0),
          ("storewide_search_page_p2l_bid_weight", 1.0),
          ("storewide_search_page_live_bid_weight", 1.0),
          ("photo_storewide_roas_adjust_weight", 1.0),
          ("photo_storewide_roi_ratio", 1.0),
          ("skip_auto_manage_open", False),
          ("universe_skip_auto_manage_open", False),
          ("enable_uax_single_bid", False),
          ("enable_close_auto_bid", False),
          ("enable_wentou_split_auto_bid", False),
          ("bid_fixed_coef", 1.0),
          ("disable_iaa_auto_roas_default", False),
          ("enable_auto_roas_default", False),
          ("enable_compete_new_info", False),
          ("ad_merchant_state_tag",""),
          ("enable_opt_tag_set", False),
          ("ad_merchant_opt_state_tag", ""),
          ("merchant_storewide_bidservice_ecpc_tag",""),
          ("enable_merchant_storewide_bidservice_ecpc", False),
          ("ad_table_attr_migration_stage", 0),
          ("enable_playlet_iap_mcb_ratio", False),
          ("playlet_iap_mcb_ratio", 1.0),
          ("enable_playlet_iap_ocpm_ratio", False),
          ("playlet_iap_ocpm_ratio", 1.0),
          ("enable_playlet_iaa_mcb_ratio", False),
          ("playlet_iaa_mcb_ratio", 1.0),
          ("enable_playlet_iaa_ocpm_ratio", False),
          ("playlet_iaa_ocpm_ratio", 1.0),
          ("new_spu_boost_ratio", 1.0),
          ("buyer_boost_ratio", 1.0),
          ("enable_high_atv_author_cali",False),
          ("high_atv_author_adjust_bound",1.0),
          ("enable_locallife_price_boost_tool", False),
          ("enable_locallife_price_boost_fix_source_type", False),
          ("enable_locallife_price_boost_fix_default_ratio", False),
          ("enable_locallife_price_boost_interactive_form", False),
          ("enable_cid_boost_skip_unlogin_user", False),
          ("enable_cid_assist_user_corporation_boost", False),
          ("cid_assist_user_corporation_boost_ratio", 1.0),
          ("enable_inner_cid_prod_bid", False),
          ("enable_storewide_follow_cali_ab", False),
          ("inner_cid_prod_bid", 1.0),
          ("enable_cid_fine_grained_boost", False),
          ("enable_cid_user_type_boost", False),
          ("enable_cid_retrieval_tag_boost", False),
          ("enable_cid_corporation_name_boost", False),
          ("cid_user_type_boost_ratio", 1.0),
          ("cid_retrieval_tag_boost_ratio", 1.0),
          ("enable_cid_luhc_user_type_boost", False),
          ("cid_luhc_user_type_boost_ratio", 1.0),
          ("enable_cid_bidservice_ecpc_random_exp", False),
          ("enable_cid_bidservice_ecpc_exp", False),
          ("enable_cid_bidservice_ecpc_opt_exp", False),
          ("cid_bidservice_ecpc_adjust_ratio", 1.0),
        ]
      ) \
    .else_() \
    .get_abtest_params(
        biz_name = "AD_DSP",
        ab_params = [
          ("user_budget_charge_tag_dsp", 0),
          ("merchant_no_bid_order_paied_group_new", "adlogfull_ocpm"),
          ("merchant_no_bid_order_paied_group", "adlogfull_ocpm"),
          ("merchant_no_bid_roas_group", "adlogfull_ocpm"),
          ("merchant_no_bid_follow_roi_group", "adlogfull_ocpm"),
          ("merchant_no_bid_group", "adlogfull_ocpm"),
          ("merchant_reco_group", "adlogfull_ocpm"),
          ("merchant_live_roas_bid_group_tag", "adlogfull_ocpm"),
          ("storewide_live_bid_group_tag", "adlogfull_ocpm"),
          ("storewide_merchant_bid_group_tag", "adlogfull_ocpm"),
          ("merchant_live_cpa_bid_group_tag", "adlogfull_ocpm"),
          ("jinniu_ad_merchant_roas_bid_group_tag", "adlogfull_ocpm"),
          ("merchant_reco_roas_bid_group_tag", "adlogfull_ocpm"),
          ("enable_universe_nobid_order_paied", False),
          ("enable_universe_nobid_roas", False),
          ("merchant_universe_no_bid_roas_group", "adlogfull_ocpm"),
          ("enable_universe_nobid_follow_roi", False),
          ("merchant_universe_no_bid_follow_roi_group", "adlogfull_ocpm"),
          ("enable_universe_nobid_other", False),
          ("merchant_universe_no_bid_group", "universe_ocpm"),
          ("universe_live_no_bid_cpa_group", "universe_ocpm"),
          ("universe_photo_roas_merchant_group", "universe_ocpm"),
          ("universe_live_merchant_group", "universe_ocpm"),
          ("esp_nobid_unit_cpa_bid_ratio", 0.5),
          ("enable_esp_nobid_unit_cpa_bid", False),
          ("enable_esp_nobid_unit_cpa_bid_max", False),
          ("enable_esp_deli_nobid_cpa_bid", False),
          ("enable_fanstop_temu_nobid_cpa_bid", False),
          ("search_flow_skip_auto_cpa_bid", False),
          ("inner_search_flow_skip_auto_cpa_bid", False),
          ("user_budget_charge_tag_fanstop", 0),
          ("auto_bid_data_source", 0),
          ("universe_photo_cpa_merchant_group", "universe_ocpm"),
          ("enable_post_server_author_atv",False),
          ("enable_post_server_author_fade_atv",False),
          ("rta_ratio_exp_sta_tag",0),
          ("enable_ptds_auto_cpa_bid_rewrite",False),
          ("enable_ptds_auto_cpa_bid_rewrite_price",False),
          ("mh_tcpl_autobid_multiplier_str",""),
          ("bid_operability_multiplier", 1.0),
          ("mh_tcpl_autobid_seed_str",""),
          ("ptds_auto_cpa_bid_rewrite_tag",0),
          ("ptds_auto_cpa_bid_rewrite_price_tag",0),
          ("enable_inner_loop_default_exp",False),
          ("is_live_default_exp",False),
          ("is_merchant_reco_default_exp",False),
          ("is_merchant_reco_roas_default_exp",False),
          ("is_jinniu_default_exp",False),
          ("enable_mobile_use_bid_target",False),
          ("enable_fill_atv_for_all_ocpx",False),
          ("enable_fill_atv_for_storewide",False),
          ("enable_inner_campaign_bid_unit_price",False),
          ("enable_inner_campaign_bid_unit_trans",False),
          ("enable_ab_bid_explore_group_idx", False),
          ("ab_bid_explore_group_idx", 0),
          ("enable_ecpc_bidservice",False),
          ("enable_storewide_incompatible_bidservice_ecpc",False),
          ("enable_roas_block_no_sale_ad",False),
          ("enable_storewide_follow_cali_ab",False),
          ("enable_touda_author_cali",False),
          ("enable_page_bid_cali",False),
          ("enable_mobile_page_bid_cali",False),
          ("enable_switch_to_flow_page_bid_cali",False),
          ("enable_inner_nobid_page_cali",False),
          ("enable_mobile_switch_to_flow_page_bid_cali",False),
          ("enable_follow_select_split_cali",False),
          ("enable_hosting_follow_select_cali",False),
          ("enable_default_follow_select_cali",False),
          ("follow_select_cali_block_eop",False),
          ("follow_select_cali_block_roas",False),
          ("select_explore_cali_split",False),
          ("enable_achieve_page_bid_cali",False),
          ("enable_achieve_page_bid_tail_cali",False),
          ("enable_achieve_page_bid_mobile_tail_cali",False),
          ("enable_cap_achieve_page_bid",False),
          ("enable_cap_achieve_page_bid_mobile",False),
          ("page_bid_cali_key",""),
          ("enable_inner_campaign_bid_cali",False),
          ("storewide_auto_roas_adjust_weight", 1.0),
          ("cold_storewide_auto_roas_adjust_weight", 1.0),
          ("storewide_soft_queue_boost_weight", 1.0),
          ("storewide_hard_queue_boost_weight", 1.0),
          ("photo_storewide_roas_adjust_weight", 1.0),
          ("storewide_search_page_p2l_bid_weight", 1.0),
          ("storewide_search_page_live_bid_weight", 1.0),
          ("photo_storewide_roi_ratio", 1.0),
          ("skip_auto_manage_open", False),
          ("universe_skip_auto_manage_open", False),
          ("enable_uax_single_bid", False),
          ("enable_storewide_search_page_adjust_bid", False),
          ("enable_close_auto_bid", False),
          ("enable_wentou_split_auto_bid", False),
          ("bid_fixed_coef", 1.0),
          ("disable_iaa_auto_roas_default", False),
          ("enable_auto_roas_default", False),
          ("enable_compete_new_info", False),
          ("ad_merchant_state_tag",""),
          ("enable_opt_tag_set", False),
          ("ad_merchant_opt_state_tag", ""),
          ("merchant_storewide_bidservice_ecpc_tag",""),
          ("enable_merchant_storewide_bidservice_ecpc", False),
          ("ad_table_attr_migration_stage", 0),
          ("enable_playlet_iap_mcb_ratio", False),
          ("playlet_iap_mcb_ratio", 1.0),
          ("enable_playlet_iap_ocpm_ratio", False),
          ("playlet_iap_ocpm_ratio", 1.0),
          ("enable_playlet_iaa_mcb_ratio", False),
          ("playlet_iaa_mcb_ratio", 1.0),
          ("enable_playlet_iaa_ocpm_ratio", False),
          ("playlet_iaa_ocpm_ratio", 1.0),
          ("new_spu_boost_ratio", 1.0),
          ("buyer_boost_ratio", 1.0),
          ("enable_high_atv_author_cali",False),
          ("high_atv_author_adjust_bound",1.0),
          ("enable_locallife_price_boost_tool", False),
          ("enable_locallife_price_boost_fix_source_type", False),
          ("enable_locallife_price_boost_fix_default_ratio", False),
          ("enable_locallife_price_boost_interactive_form", False),
          ("enable_cid_boost_skip_unlogin_user", False),
          ("enable_cid_assist_user_corporation_boost", False),
          ("cid_assist_user_corporation_boost_ratio", 1.0),
          ("enable_inner_cid_prod_bid", False),
          ("enable_storewide_follow_cali_ab", False),
          ("inner_cid_prod_bid", 1.0),
          ("enable_cid_fine_grained_boost", False),
          ("enable_cid_user_type_boost", False),
          ("enable_cid_retrieval_tag_boost", False),
          ("enable_cid_corporation_name_boost", False),
          ("cid_user_type_boost_ratio", 1.0),
          ("cid_retrieval_tag_boost_ratio", 1.0),
          ("enable_cid_luhc_user_type_boost", False),
          ("cid_luhc_user_type_boost_ratio", 1.0),
          ("enable_cid_bidservice_ecpc_random_exp", False),
          ("enable_cid_bidservice_ecpc_exp", False),
          ("enable_cid_bidservice_ecpc_opt_exp", False),
          ("cid_bidservice_ecpc_adjust_ratio", 1.0),
        ]
      ) \
    .end_if_()
    return self

  @module()
  def pre_resize_bid_item(self, table_name, **kwargs):
    current_flow() \
    .pre_resize_item_attr(num_attrs = [
        "acc_cold_start_coef",
        "account_id",
        "account_type",
        "ad_bid_server_group_tag",
        "aggre_key",
        "author_id",
        "auto_adjust",
        "auto_atv",
        "auto_bid_explore",
        "auto_build",
        "auto_cpa_bid",
        "bid_auto_cpa_bid",
        "auto_cpa_bid_modify_tag",
        "auto_deep_cpa_bid",
        "auto_manage",
        "auto_roas",
        "bid_auto_roas",
        "auto_roas_modify_tag",
        "basic_group_tag",
        "bid_ad_type",
        "bid_coef",
        "bid_data_type",
        "bid_explore_cnt",
        "bid_explore_group_idx",
        "bid_gimbal_ratio",
        "bid_lower_bound",
        "bid_server_type",
        "bid_strategy",
        "bid_strategy_group",
        "bid_type",
        "bid_upper_bound",
        "bit_info",
        "bucket_size",
        "budget_coef",
        "budget_smart_allocation",
        "budget_status_conf_",
        "campaign_id",
        "periodic_delivery_type",
        "campaign_type",
        "cap_bid_type",
        "charge_mode",
        "city_product_id",
        "cost_cap_p",
        "cost_cap_q",
        "cost_cap_type",
        "cpa_bid",
        "cpa_coef_lower",
        "cpa_coef_upper",
        "cpa_ratio",
        "deep_conversion_type",
        "deep_cpa_bid",
        "deep_flow_control_rate",
        "deep_group_tag",
        "deep_min_bid_coef",
        "deep_min_coef",
        "delivery_type",
        "enable_merge_bid",
        "explore_bid_type",
        "explore_put_type",
        "exp_start_ts",
        "fans_count",
        "fanstop_category",
        "fanstop_cvr_threshold_ratio",
        "find_bid_info",
        "first_coef",
        "first_industry_id",
        "first_industry_id_v5",
        "first_industry_id_v6",
        "group_idx",
        "guarantee_min_cost",
        "industry_id_v3",
        "inspire_auto_dark_filter",
        "inspire_ad_roi",
        "inspire_dark_control_pid_value",
        "is_account_bidding",
        "is_author_fans_cost",
        "is_author_fans_target_cost",
        "is_cold_unit_photo",
        "is_cost_cap",
        "is_hosting",
        "is_in_acc_explore_status",
        "is_inner_delivery",
        "is_lowest_cost",
        "is_not_bid_ad",
        "is_skip_bid_server",
        "is_uax",
        "item_type",
        "live_launch_type",
        "mcb_group_tag",
        "middle_bid",
        "modify_group_idx",
        "nobid_cpa_bid",
        "nobid_roi_ratio",
        "ocpm_inner_strategy_tag",
        "ocpx_action_type",
        "page_achieve_ratio",
        "inspire_roi_cali_ratio",
        "pc_target_cost_today_sum_timestamp",
        "pc_target_cost_total_sum_price",
        "price_ratio",
        "product_cpa_bid",
        "product_id",
        "product_roi_ratio",
        "promotion_type",
        "raw_auto_cpa_bid",
        "real_sctr",
        "reset_group_tag",
        "roi_ratio",
        "scene_oriented_type",
        "second_coef",
        "second_industry_id_v5",
        "second_industry_id_v6",
        "speed",
        "storewide_incompatible_type",
        "sub_bid_coef",
        "target_cpa",
        "target_factor",
        "total_cost",
        "total_target_cost",
        "twin_bid_strategy",
        "unit_calibration",
        "unit_feature_type",
        "unit_source_type",
        "update_timestamp",
        "use_second_industry_v5",
        "use_second_industry_v6",
        "wentou_campaign_calibration",
        "wentou_campaign_gimbal_ratio",
        "unit_id",
        "ad_queue_type",
        "is_fan_follow",
        "is_no_sale_live_stream_ad",
        "live_creative_type",
        "explore_ext_type",
        "aigc_bid_coef",
        "bid_gimbal_type",
        "hit_white_account_explore_cost",
        "use_ap_gimbal_ratio",
        "photo_ocpc_storewide_cali",
        "photo_ocpc_order_cali",
        "wentou_increment_bid_coef"
      ],
      num_list_attrs = [
      ],
      string_attrs = [
        "ad_auto_bid_info",
        "product_name",
        "account_wentou_gimbal_ratio_str",
        "account_wentou_bid_coef_hit_flag_str"
      ],
      string_list_attrs = [
      ],
      extra_attrs = [
        "ad_auto_bid_info_pb"
      ],
      item_table = table_name,
      max_resize_size = 2000
    )
    return self

  @module()
  def local_bid_info(self, table_name, **kwargs):
    current_flow() \
    .bid_prepare() \
    .pre_resize_bid_item(table_name) \
    .pre_handler(table_name) \
    .fill_bid_info(table_name) \
    .fill_group_tag(table_name) \
    .calc_auto_bid_info_id(table_name)
    return self

  @module()
  def pre_handler(self, table_name):
    current_flow() \
    .ad_fill_info(
      item_table = table_name,
    ) \
    .fill_fanstop_bid_info(
      item_table = table_name,
      user_budget_charge_tag_fanstop = "{{user_budget_charge_tag_fanstop}}",
    )
    return self

  @module()
  def fill_auto_bid_info(self, table_name, **kwargs):
    current_flow() \
    .ad_table_account_bid_info_v2(
      item_table = table_name,
      enable_account_unit_price_ratio = "{{enable_account_unit_price_ratio}}",
      enable_uax_single_bid = "{{enable_uax_single_bid}}",
      enable_playlet_iap_ocpm_ratio = "{{enable_playlet_iap_ocpm_ratio}}",
      playlet_iap_ocpm_ratio = "{{playlet_iap_ocpm_ratio}}",
      enable_playlet_iaa_ocpm_ratio = "{{enable_playlet_iaa_ocpm_ratio}}",
      playlet_iaa_ocpm_ratio = "{{playlet_iaa_ocpm_ratio}}",
      bid_data_trans_type = "account",
      id = "ad_table_account_bid_info_id",
    ) \
    .ad_table_campaign_bid_info_v2(
      item_table = table_name,
      user_budget_charge_tag_dsp = "{{user_budget_charge_tag_dsp}}",
      skip_auto_manage_open = "{{skip_auto_manage_open}}",
      enable_playlet_iap_mcb_ratio = "{{enable_playlet_iap_mcb_ratio}}",
      playlet_iap_mcb_ratio = "{{playlet_iap_mcb_ratio}}",
      enable_playlet_iaa_mcb_ratio = "{{enable_playlet_iaa_mcb_ratio}}",
      playlet_iaa_mcb_ratio = "{{playlet_iap_mca_ratio}}",
      bid_data_trans_type = "mcb",
      id = "ad_table_campaign_bid_info_id",
    ) \
    .ad_table_unit_bid_info_v2(
      item_table = table_name,
      campaign_table_id = "ad_table_campaign_bid_info_id",
      unit_table_id = "ad_table_unit_bid_info_id",
      deep_id = "basic_ad_table_unit_bid_info_deep_id",
      reset_id = "ad_table_unit_bid_info_reset_id",
      enable_inner_campaign_bid_unit_price = "{{enable_inner_campaign_bid_unit_price}}",
      enable_inner_campaign_bid_unit_trans = "{{enable_inner_campaign_bid_unit_trans}}",
      bid_data_trans_type = "unit",
      follow_select_cali_block_eop = "{{follow_select_cali_block_eop}}",
      follow_select_cali_block_roas = "{{follow_select_cali_block_roas}}",
      select_explore_cali_split = "{{select_explore_cali_split}}",
      enable_default_follow_select_cali = "{{enable_default_follow_select_cali}}",
      enable_hosting_follow_select_cali = "{{enable_hosting_follow_select_cali}}",
      enable_follow_select_split_cali = "{{enable_follow_select_split_cali}}",
      enable_roas_block_no_sale_ad = "{{enable_roas_block_no_sale_ad}}",
      enable_storewide_follow_cali_ab = "{{enable_storewide_follow_cali_ab}}",
      enable_touda_author_cali = "{{enable_touda_author_cali}}",
    ) \
    .ad_table_unit_bid_info_v2(
      item_table = table_name,
      campaign_table_id = "ad_table_campaign_bid_info_id",
      unit_table_id = "ad_table_unit_bid_info_id",
      deep_id = "basic_ad_table_unit_bid_info_deep_id",
      reset_id = "ad_table_unit_bid_info_reset_id",
      module_name = "ad_table_unit_bid_info_refactor_merchant",
      bid_tag = "merchant",
      enable_inner_campaign_bid_unit_price = "{{enable_inner_campaign_bid_unit_price}}",
      enable_inner_campaign_bid_unit_trans = "{{enable_inner_campaign_bid_unit_trans}}",
      bid_data_trans_type = "merchant",
      follow_select_cali_block_eop = "{{follow_select_cali_block_eop}}",
      follow_select_cali_block_roas = "{{follow_select_cali_block_roas}}",
      select_explore_cali_split = "{{select_explore_cali_split}}",
      enable_default_follow_select_cali = "{{enable_default_follow_select_cali}}",
      enable_hosting_follow_select_cali = "{{enable_hosting_follow_select_cali}}",
      enable_follow_select_split_cali = "{{enable_follow_select_split_cali}}",
      enable_roas_block_no_sale_ad = "{{enable_roas_block_no_sale_ad}}",
      enable_storewide_follow_cali_ab = "{{enable_storewide_follow_cali_ab}}",
      enable_touda_author_cali = "{{enable_touda_author_cali}}",
    )
    return self

  @module()
  def fill_bid_info(self, table_name):
    current_flow() \
    .default_bid_info_enricher(
      item_table = table_name,
      bid_ad_type = 1,
      basic_group_tag = 0,
      bid_server_type = 0,
      is_skip_bid_server = 0,
      bit_info = "bit_info",
      bid_data_type = "bid_data_type",
      budget_smart_allocation = "budget_smart_allocation",
      ocpx_action_type = "ocpx_action_type",
      deep_conversion_type = "deep_conversion_type",
      bid_service_trace_log_ratio = "{{bid_service_trace_log_ratio}}",
      speed = "speed",
      bid_strategy = "bid_strategy",
      account_type = "account_type",
      bid_type = "bid_type",
      live_launch_type = "live_launch_type",
    ) \
    .get_deep_group_tag(
      item_table = table_name,
      deep_conversion_type = "deep_conversion_type",
      deep_cpa_bid = "deep_cpa_bid",
      deep_group_tag = "deep_group_tag",
    ) \
    .esp_no_bid_enricher(
      item_table = table_name,
      esp_nobid_unit_cpa_bid_ratio = "{{esp_nobid_unit_cpa_bid_ratio}}",
      enable_esp_nobid_unit_cpa_bid = "{{enable_esp_nobid_unit_cpa_bid}}",
      enable_esp_nobid_unit_cpa_bid_max = "{{enable_esp_nobid_unit_cpa_bid_max}}",
      enable_esp_deli_nobid_cpa_bid = "{{enable_esp_deli_nobid_cpa_bid}}",
      enable_fanstop_temu_nobid_cpa_bid = "{{enable_fanstop_temu_nobid_cpa_bid}}",
      esp_live_roas_atv_default_value = "{{esp_live_roas_atv_default_value}}",
      enable_post_server_author_atv = "{{enable_post_server_author_atv}}",
      enable_post_server_author_fade_atv = "{{enable_post_server_author_fade_atv}}",
      enable_fill_atv_for_all_ocpx = "{{enable_fill_atv_for_all_ocpx}}",
      enable_fill_atv_for_storewide = "{{enable_fill_atv_for_storewide}}",
      unit_id = "unit_id",
      author_id = "author_id",
      account_id = "account_id",
      ocpx_action_type = "ocpx_action_type",
      promotion_type = "promotion_type",
      campaign_type = "campaign_type",
      account_type = "account_type",
      speed = "speed",
      live_creative_type = "live_creative_type",
      bit_info = "bit_info",
      bid_strategy = "bid_strategy",
      bid_type = "bid_type",
      fans_count = "fans_count",
      nobid_cpa_bid = "nobid_cpa_bid",
      nobid_roi_ratio = "nobid_roi_ratio",
      auto_atv = "auto_atv",
    )
    return self
  
  @module()
  def fill_group_tag(self, table_name):
    current_flow() \
    .bid_exp_info(
      item_table = table_name,
      deep_conversion_type = "deep_conversion_type",
      first_industry_id = "first_industry_id",
      industry_id_v3 = "industry_id_v3",
      product_id = "product_id",
      ocpx_action_type = "ocpx_action_type",
      speed = "speed",
      bit_info = "bit_info",
      campaign_type = "campaign_type",
      group_idx = "group_idx",
      exp_start_ts = "exp_start_ts",
      ad_bid_server_group_tag = "ad_bid_server_group_tag",
      user_budget_charge_tag_dsp = "{{user_budget_charge_tag_dsp}}",
      skip_auto_manage_open = "{{skip_auto_manage_open}}",
    ) \
    .update_fanstop_group_tag(
      item_table = table_name,
      user_budget_charge_tag_fanstop = "{{user_budget_charge_tag_fanstop}}",
      enable_fanstop_campaign_launch_type = "{{enable_fanstop_campaign_launch_type}}",
      campaign_type = "campaign_type",
      account_type = "account_type",
      bid_type = "bid_type",
      bid_strategy = "bid_strategy",
      unit_id = "unit_id",
      ocpx_action_type = "ocpx_action_type",
      campaign_id = "campaign_id",
      item_type = "item_type",
      live_launch_type = "live_launch_type",
      bit_info = "bit_info",
      bid_ad_type = "bid_ad_type",
      group_idx = "group_idx",
      basic_group_tag = "basic_group_tag",
      bid_server_type = "bid_server_type",
      is_skip_bid_server = "is_skip_bid_server",
      bid_data_type = "bid_data_type",
    ) \
    .update_group_tag(table_name) \
    .account_group_tag_modify(
      item_table = table_name,
      bit_info = "bit_info",
      is_hosting = "is_hosting",
      bid_type = "bid_type",
      account_id = "account_id",
      product_id = "product_id",
      ocpx_action_type = "ocpx_action_type",
      deep_conversion_type = "deep_conversion_type",
      campaign_type = "campaign_type",
      group_idx = "group_idx",
      ad_bid_server_group_tag = "ad_bid_server_group_tag",
      is_account_bidding = "is_account_bidding",
    )
    return self

  @module()
  def calc_auto_bid_info_id(self, table_name):
    current_flow() \
    .parse_bid_data_trans_config(
      item_table = table_name,
      bid_data_trans_type = "account",
      out_attrs = "account_bid_data_trans_attrs",
    ) \
    .ad_table_account_bid_info_calc_id(
      item_table = table_name,
      basic_id = "ad_table_account_bid_info_id",
      backup_id = "backup_ad_table_account_bid_info_id",
      enable_uax_single_bid = "{{enable_uax_single_bid}}",
    ) \
    .ad_table_campaign_bid_info_calc_id(
      item_table = table_name,
      basic_id = "ad_table_campaign_bid_info_id",
      backup_id = "backup_ad_table_campaign_bid_info_id",
      skip_auto_manage_open = "{{skip_auto_manage_open}}",
    ) \
    .parse_bid_data_trans_config(
      item_table = table_name,
      bid_data_trans_type = "merchant",
      out_attrs = "merchant_bid_data_trans_attrs",
    ) \
    .ad_table_unit_bid_info_calc_id(
      item_table = table_name,
      bid_tag = "merchant",
      basic_unit_table_id = "ad_table_unit_bid_info_id",
      basic_campaign_table_id = "ad_table_campaign_bid_info_id",
      backup_unit_table_id = "backup_ad_table_unit_bid_info_unit_table_id",
      backup_campaign_table_id = "backup_ad_table_unit_bid_info_campaign_table_id",
    ) \
    .ad_table_unit_bid_info_calc_id(
      item_table = table_name,
      basic_unit_table_id = "ad_table_unit_bid_info_id",
      basic_campaign_table_id = "ad_table_campaign_bid_info_id",
      backup_unit_table_id = "backup_ad_table_unit_bid_info_unit_table_id",
      backup_campaign_table_id = "backup_ad_table_unit_bid_info_campaign_table_id",
    ) \
    .ad_table_unit_bid_info_calc_deep_id(
      item_table = table_name,
      basic_unit_table_id = "ad_table_unit_bid_info_id",
      basic_campaign_table_id = "ad_table_campaign_bid_info_id",
      backup_unit_table_id = "backup_ad_table_unit_bid_info_unit_table_id",
      backup_campaign_table_id = "backup_ad_table_unit_bid_info_campaign_table_id",
      id = "basic_ad_table_unit_bid_info_deep_id",
    ) \
    .ad_table_unit_bid_info_calc_reset_id(
      item_table = table_name,
      bid_tag = "merchant",
      id = "ad_table_unit_bid_info_reset_id",
    )
    return self

  @module()
  def update_group_tag(self, table_name):
    current_flow() \
    .group_tag_calc(
      item_table = table_name,
      bid_ad_type="photo_acc_explore",
      group_tag="merchant_acc_explore",
      basic_group_tag=5,
      bid_server_type=2,
      is_skip_bid_server=1,
      func_name="BidPhotoAccExplore"
    ) \
    .group_tag_calc(
      item_table = table_name,
      bid_ad_type="live_acc_explore",
      group_tag="merchant_acc_explore",
      basic_group_tag=5,
      bid_server_type=2,
      is_skip_bid_server=1,
      func_name="BidLiveAccExplore"
    ) \
    .if_("enable_nobid_order_paied_new == 1") \
      .group_tag_calc_for_nobid(
        item_table = table_name,
        ab_group_tag_name="{{merchant_no_bid_order_paied_group_new}}",
        bid_ad_type="no_bid",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=0,
        func_name="BidNoBidOrderPaied"
      ) \
    .end_if_() \
    .if_("enable_nobid_order_paied == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_no_bid_order_paied_group}}",
        bid_ad_type="no_bid",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=0,
        func_name="BidNoBidOrderPaied"
      ) \
    .end_if_() \
    .if_("enable_nobid_roas == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_no_bid_roas_group}}",
        bid_ad_type="no_bid",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=0,
        func_name="BidNoBidRoas"
      ) \
    .end_if_() \
    .if_("enable_nobid_follow_roi == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_no_bid_follow_roi_group}}",
        bid_ad_type="no_bid",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=0,
        func_name="BidNoBidMerchantFollowRoi"
      ) \
    .end_if_() \
    .group_tag_calc(
      item_table = table_name,
      ab_group_tag_name="{{merchant_no_bid_group}}",
      bid_ad_type="no_bid",
      group_tag="adlogfull_ocpm",
      basic_group_tag=5,
      bid_server_type=2,
      is_skip_bid_server=0,
      func_name="BidNoBid"
    ) \
    .if_("enable_reco_merchant == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_reco_group}}",
        bid_ad_type="merchant_photo",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=0,
        func_name="BidMerchantPhoto"
      ) \
    .end_if_() \
    .if_("enable_live_roas == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_live_roas_bid_group_tag}}",
        bid_ad_type="live_roas",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=1,
        func_name="BidLiveROAS"
      ) \
    .end_if_() \
    .group_tag_calc(
      item_table = table_name,
      ab_group_tag_name="{{storewide_live_bid_group_tag}}",
      # 直播全站单独处理前会走到 BidLiveCpa 里，这个字段现在没用了，就保持无 diff 吧
      bid_ad_type="live_cpa_bid",
      group_tag="adlogfull_ocpm",
      basic_group_tag=5,
      bid_server_type=2,
      is_skip_bid_server=1,
      func_name="BidLiveStorewideROAS"
    ) \
    .if_("enable_live_cpa == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_live_cpa_bid_group_tag}}",
        bid_ad_type="live_cpa_bid",
        group_tag="adlogfull_ocpm",
        basic_group_tag=5,
        bid_server_type=2,
        is_skip_bid_server=1,
        func_name="BidLiveCpa"
      ) \
    .end_if_() \
    .if_("enable_jinniu_roas == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{jinniu_ad_merchant_roas_bid_group_tag}}",
        bid_ad_type="jinniu_roas",
        group_tag="adlogfull_ocpm",
        basic_group_tag=0,
        bid_server_type=2,
        is_skip_bid_server=1,
        func_name="BidJinniuROAS"
      ) \
    .end_if_() \
    .if_("enable_reco_roas == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{merchant_reco_roas_bid_group_tag}}",
        bid_ad_type="reco_roas",
        group_tag="adlogfull_ocpm",
        basic_group_tag=0,
        bid_server_type=2,
        is_skip_bid_server=1,
        func_name="BidRecoRoas"
      ) \
    .end_if_() \
    .if_("enable_merchant_storewide == 1") \
      .group_tag_calc(
        item_table = table_name,
        ab_group_tag_name="{{storewide_merchant_bid_group_tag}}",
        bid_ad_type="reco_roas",
        group_tag="adlogfull_ocpm",
        basic_group_tag=0,
        bid_server_type=2,
        is_skip_bid_server=1,
        func_name="BidMerchantStoreWide"
      ) \
    .end_if_()
    return self

  @module()
  def ad_table_account_bid_info_refactor(self, table_name):
    current_flow() \
    .parse_bid_data_trans_config(
      item_table = table_name,
      bid_data_trans_type = "account",
      out_attrs = "account_bid_data_trans_attrs",
    ) \
    .ad_table_account_bid_info_calc_id(
      item_table = table_name,
      basic_id = "ad_table_account_bid_info_id",
      backup_id = "backup_ad_table_account_bid_info_id",
      enable_uax_single_bid = "{{enable_uax_single_bid}}",
      enable_playlet_iap_ocpm_ratio = "{{enable_playlet_iap_ocpm_ratio}}",
      playlet_iap_ocpm_ratio = "{{playlet_iap_ocpm_ratio}}",
      enable_playlet_iaa_ocpm_ratio = "{{enable_playlet_iaa_ocpm_ratio}}",
      playlet_iaa_ocpm_ratio = "{{playlet_iaa_ocpm_ratio}}",
    )
    return self

  @module()
  def ad_table_campaign_bid_info_refactor(self, table_name):
    current_flow() \
    .ad_table_campaign_bid_info_calc_id(
      item_table = table_name,
      basic_id = "ad_table_campaign_bid_info_id",
      backup_id = "backup_ad_table_campaign_bid_info_id",
      skip_auto_manage_open = "{{skip_auto_manage_open}}",
      enable_playlet_iap_mcb_ratio = "{{enable_playlet_iap_mcb_ratio}}",
      playlet_iap_mcb_ratio = "{{playlet_iap_mcb_ratio}}",
      enable_playlet_iaa_mcb_ratio = "{{enable_playlet_iaa_mcb_ratio}}",
      playlet_iaa_mcb_ratio = "{{playlet_iaa_mcb_ratio}}",
    )
    return self

  @module()
  def ad_table_unit_bid_info_refactor(self, table_name):
    current_flow() \
    .parse_bid_data_trans_config(
      item_table = table_name,
      bid_data_trans_type = "merchant",
      out_attrs = "merchant_bid_data_trans_attrs",
    ) \
    .ad_table_unit_bid_info_calc_id(
      item_table = table_name,
      basic_unit_table_id = "ad_table_unit_bid_info_id",
      basic_campaign_table_id = "ad_table_campaign_bid_info_id",
      backup_unit_table_id = "backup_ad_table_unit_bid_info_unit_table_id",
      backup_campaign_table_id = "backup_ad_table_unit_bid_info_campaign_table_id",
    ) \
    .ad_table_unit_bid_info_calc_deep_id(
      item_table = table_name,
      basic_unit_table_id = "ad_table_unit_bid_info_id",
      basic_campaign_table_id = "ad_table_campaign_bid_info_id",
      backup_unit_table_id = "backup_ad_table_unit_bid_info_unit_table_id",
      backup_campaign_table_id = "backup_ad_table_unit_bid_info_campaign_table_id",
      id = "basic_ad_table_unit_bid_info_deep_id",
    ) \
    .ad_table_unit_bid_info_calc_reset_id(
      item_table = table_name,
      id = "ad_table_unit_bid_info_reset_id",
    )
    return self

  @module()
  def post_process(self, table_name, **kwargs):
    current_flow() \
    .post_handler(
      item_table = table_name,
      pay_times_num = "{{pay_times_num}}",
      enable_live_ocpm_bid_cold_start_modifyTag = "{{enable_live_ocpm_bid_cold_start_modifyTag}}",
      enable_ab_bid_explore_group_idx = "{{enable_ab_bid_explore_group_idx}}",
      ab_bid_explore_group_idx = "{{ab_bid_explore_group_idx}}",
      enable_ecpc_bidservice = "{{enable_ecpc_bidservice}}",
      is_holdout_live_storewide_ecpc = "{{is_holdout_live_storewide_ecpc}}",
      is_holdout_photo_storewide_ecpc = "{{is_holdout_photo_storewide_ecpc}}",
      enable_storewide_incompatible_bidservice_ecpc = "{{enable_storewide_incompatible_bidservice_ecpc}}",
      storewide_auto_roas_adjust_weight = "{{storewide_auto_roas_adjust_weight}}",
      cold_storewide_auto_roas_adjust_weight = "{{cold_storewide_auto_roas_adjust_weight}}",
      storewide_soft_queue_boost_weight = "{{storewide_soft_queue_boost_weight}}",
      storewide_hard_queue_boost_weight = "{{storewide_hard_queue_boost_weight}}",
      photo_storewide_roas_adjust_weight = "{{photo_storewide_roas_adjust_weight}}",
      storewide_search_page_p2l_bid_weight = "{{storewide_search_page_p2l_bid_weight}}",
      storewide_search_page_live_bid_weight = "{{storewide_search_page_live_bid_weight}}",
      photo_storewide_roi_ratio = "{{photo_storewide_roi_ratio}}",
      enable_roas_block_no_sale_ad = "{{enable_roas_block_no_sale_ad}}",
      enable_high_atv_author_cali = "{{enable_high_atv_author_cali}}",
      high_atv_author_adjust_bound = "{{high_atv_author_adjust_bound}}",
      enable_storewide_search_page_adjust_bid = "{{enable_storewide_search_page_adjust_bid}}",
      enable_page_bid_cali = "{{enable_page_bid_cali}}",
      enable_mobile_page_bid_cali = "{{enable_mobile_page_bid_cali}}",
      enable_switch_to_flow_page_bid_cali = "{{enable_switch_to_flow_page_bid_cali}}",
      enable_inner_nobid_page_cali = "{{enable_inner_nobid_page_cali}}",
      enable_mobile_switch_to_flow_page_bid_cali = "{{enable_mobile_switch_to_flow_page_bid_cali}}",
      follow_select_cali_block_eop = "{{follow_select_cali_block_eop}}",
      follow_select_cali_block_roas = "{{follow_select_cali_block_roas}}",
      select_explore_cali_split = "{{select_explore_cali_split}}",
      enable_default_follow_select_cali = "{{enable_default_follow_select_cali}}",
      enable_hosting_follow_select_cali = "{{enable_hosting_follow_select_cali}}",
      enable_follow_select_split_cali = "{{enable_follow_select_split_cali}}",
      enable_achieve_page_bid_cali = "{{enable_achieve_page_bid_cali}}",
      enable_achieve_page_bid_tail_cali = "{{enable_achieve_page_bid_tail_cali}}",
      enable_achieve_page_bid_mobile_tail_cali = "{{enable_achieve_page_bid_mobile_tail_cali}}",
      enable_cap_achieve_page_bid = "{{enable_cap_achieve_page_bid}}",
      enable_cap_achieve_page_bid_mobile = "{{enable_cap_achieve_page_bid_mobile}}",
      page_bid_cali_key = "{{page_bid_cali_key}}",
      enable_inner_campaign_bid_cali = "{{enable_inner_campaign_bid_cali}}",
      new_spu_boost_ratio = "{{new_spu_boost_ratio}}",
      buyer_boost_ratio = "{{buyer_boost_ratio}}",
      bit_info = "bit_info",
      ocpx_action_type = "ocpx_action_type",
      bid_auto_cpa_bid = "bid_auto_cpa_bid",
      reset_group_tag = "reset_group_tag",
      bid_auto_roas = "bid_auto_roas",
      target_cpa = "target_cpa",
      ocpm_inner_strategy_tag = "ocpm_inner_strategy_tag",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
      raw_auto_cpa_bid = "raw_auto_cpa_bid",
      ad_bid_server_group_tag = "ad_bid_server_group_tag",
      acc_cold_start_coef = "acc_cold_start_coef",
      bid_coef = "bid_coef",
      account_id = "account_id",
      bid_data_type = "bid_data_type",
      live_creative_type = "live_creative_type",
      price_ratio = "price_ratio",
      payment_per_order = "payment_per_order",
      auto_dark_control = "auto_dark_control",
      inspire_dark_control_pid_value = "inspire_dark_control_pid_value",
      inspire_ad_roi = "inspire_ad_roi",
      inspire_auto_dark_filter = "inspire_auto_dark_filter",
      bid_operability_multiplier = "{{bid_operability_multiplier}}",
      ad_merchant_state_tag = "{{ad_merchant_state_tag}}",
      enable_opt_tag_set = "{{enable_opt_tag_set}}",
      ad_merchant_opt_state_tag = "{{ad_merchant_opt_state_tag}}",
      merchant_storewide_bidservice_ecpc_tag = "{{merchant_storewide_bidservice_ecpc_tag}}",
      enable_merchant_storewide_bidservice_ecpc = "{{enable_merchant_storewide_bidservice_ecpc}}",
      enable_locallife_price_boost_tool = "{{enable_locallife_price_boost_tool}}",
      enable_locallife_price_boost_fix_source_type = "{{enable_locallife_price_boost_fix_source_type}}",
      enable_locallife_price_boost_fix_default_ratio = "{{enable_locallife_price_boost_fix_default_ratio}}",
      enable_locallife_price_boost_interactive_form = "{{enable_locallife_price_boost_interactive_form}}",
      enable_cid_boost_skip_unlogin_user = "{{enable_cid_boost_skip_unlogin_user}}",
      enable_cid_assist_user_corporation_boost = "{{enable_cid_assist_user_corporation_boost}}",
      cid_assist_user_corporation_boost_ratio = "{{cid_assist_user_corporation_boost_ratio}}",
      enable_inner_cid_prod_bid = "{{enable_inner_cid_prod_bid}}",
      enable_storewide_follow_cali_ab = "{{enable_storewide_follow_cali_ab}}",
      inner_cid_prod_bid = "{{inner_cid_prod_bid}}",
      enable_cid_fine_grained_boost = "{{enable_cid_fine_grained_boost}}",
      enable_cid_user_type_boost = "{{enable_cid_user_type_boost}}",
      enable_cid_retrieval_tag_boost = "{{enable_cid_retrieval_tag_boost}}",
      enable_cid_corporation_name_boost = "{{enable_cid_corporation_name_boost}}",
      cid_user_type_boost_ratio = "{{cid_user_type_boost_ratio}}",
      cid_retrieval_tag_boost_ratio = "{{cid_retrieval_tag_boost_ratio}}",
      enable_cid_luhc_user_type_boost = "{{enable_cid_luhc_user_type_boost}}",
      cid_luhc_user_type_boost_ratio = "{{cid_luhc_user_type_boost_ratio}}",
      enable_cid_bidservice_ecpc_random_exp = "{{enable_cid_bidservice_ecpc_random_exp}}",
      enable_cid_bidservice_ecpc_exp = "{{enable_cid_bidservice_ecpc_exp}}",
      enable_cid_bidservice_ecpc_opt_exp = "{{enable_cid_bidservice_ecpc_opt_exp}}",
      cid_bidservice_ecpc_adjust_ratio = "{{cid_bidservice_ecpc_adjust_ratio}}",
    ) \
    .no_bid_post_handler(
      item_table = table_name,
      ocpx_action_type = "ocpx_action_type",
      speed = "speed",
      promotion_type = "promotion_type",
      update_timestamp = "update_timestamp",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
      campaign_type = "campaign_type",
      author_id = "author_id",
      account_id = "account_id",
      bit_info = "bit_info",
      bid_strategy = "bid_strategy",
      bid_type = "bid_type",
      enable_inner_nobid_page_cali = "{{enable_inner_nobid_page_cali}}",
    ) \
    .account_bid_post_handler(
      item_table = table_name,
      enable_project_info = "{{enable_project_info}}",
      enable_close_auto_bid = "{{enable_close_auto_bid}}",
      enable_wentou_split_auto_bid = "{{enable_wentou_split_auto_bid}}",
      bid_fixed_coef = "{{bid_fixed_coef}}",
      ocpx_action_type = "ocpx_action_type",
      roi_ratio = "roi_ratio",
      cpa_bid = "cpa_bid",
      campaign_id = "campaign_id",
      account_id = "account_id",
      bid_coef = "bid_coef",
      bid_auto_roas = "bid_auto_roas",
      bid_auto_cpa_bid = "bid_auto_cpa_bid",
      auto_roas = "auto_roas",
      auto_roas_modify_tag = "auto_roas_modify_tag",
      auto_cpa_bid = "auto_cpa_bid",
      auto_cpa_bid_modify_tag = "auto_cpa_bid_modify_tag",
      is_account_bidding = "is_account_bidding",
      ad_bid_server_group_tag = "ad_bid_server_group_tag"
    ) \
    .fill_cold_start_bid_info(
      item_table = table_name,
      disable_iaa_auto_roas_default = "{{disable_iaa_auto_roas_default}}",
      enable_auto_roas_default = "{{enable_auto_roas_default}}",
    )
    return self

  @module()
  def msg_send(self, table_name, **kwargs):
    with data_manager:
      current_flow() \
    .if_("enable_send_compete_campaign_info == 1 and is_one_model_nearline_flow ~= 1") \
      .compete_campaign_produce(
        item_table = table_name,
        bid_strategy_group = "bid_strategy_group",
        campaign_id = "campaign_id",
        enable_compete_new_info = "{{enable_compete_new_info}}",
      ) \
    .end_if_() \
    .if_("enable_bid_service_trace_log == 1") \
      .trace_log_produce(item_table = table_name) \
    .end_if_()
    return self

  @module()
  def remote_feature_index_diff(self, name, **config):
    self.request_remote_table_opt(
      **config,
      save_async_status_to="remote_table",
      no_check=True
    )
    return self

  @module()
  def outer_photo_remote_and_diff(self):
    self.if_('enable_prerank_local_bid == 1')

    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["outer_hard_photo_item_table", "outer_soft_photo_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(module_name="outer_hard", table_name="outer_hard_photo_item_table")
    self.fill_auto_bid_info(module_name="outer_soft", table_name="outer_soft_photo_item_table")
    self.post_process(module_name="outer_hard_post", table_name="outer_hard_photo_item_table")
    self.post_process(table_name="outer_soft_photo_item_table", module_name="outer_soft_post")
    self.end_if_()
    self.if_("enable_remote_table_and_diff == 1")
    self.remote_table_diff_and_alias(
      input_table_name="outer_hard_photo_item_table",
      output_columns = bid_info_columns,
    )
    self.remote_table_diff_and_alias(
      input_table_name="outer_soft_photo_item_table",
      output_columns = bid_info_columns,
    )
    self.end_if_()
    return self
 
  @module()
  def outer_photo_feature_bid(self):
    outer_photo_remote_bid_config = copy.deepcopy(remote_bid_config)
    outer_photo_remote_bid_config["request_tables"]["global_ad_table"]["item_tables"] = ["outer_hard_photo_item_table", "outer_soft_photo_item_table"]
    outer_photo_remote_bid_config_new_fields = copy.deepcopy(remote_bid_config_new_fields)
    outer_photo_remote_bid_config_new_fields["request_tables"]["global_ad_table"]["item_tables"] = ["outer_hard_photo_item_table", "outer_soft_photo_item_table"]

    self.local_bid_info(module_name= "hard", table_name="outer_hard_photo_item_table")
    self.local_bid_info(module_name= "soft", table_name="outer_soft_photo_item_table")
    self.if_("enable_new_fields_from_forward == 1 or enable_new_fields_from_forward_diff == 1").remote_feature_index_diff(
      name = "grid_outer5",
      **outer_photo_remote_bid_config_new_fields,
    ).else_().remote_feature_index_diff(
      name = "grid_outer6",
      **outer_photo_remote_bid_config
    ).end_if_()
    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["outer_hard_photo_item_table", "outer_soft_photo_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(module_name="outer_hard", table_name="outer_hard_photo_item_table")
    self.fill_auto_bid_info(module_name="outer_soft", table_name="outer_soft_photo_item_table")
    self.post_process(module_name="outer_hard_post", table_name="outer_hard_photo_item_table")
    self.post_process(table_name="outer_soft_photo_item_table", module_name="outer_soft_post")
    self.msg_send(module_name="outer_hard_msg_send", table_name="outer_hard_photo_item_table")
    self.msg_send(module_name="outer_soft_msg_send", table_name="outer_soft_photo_item_table")
    return self

  @module()
  def live_remote_and_diff(self):
    self.if_('enable_prerank_local_bid == 1')
    self.remote_table_diff_and_alias(
      input_table_name="inner_live_item_table",
      output_columns = bid_info_columns,
      is_clear = True,
    )
    self.remote_table_diff_and_alias(
      input_table_name="outer_live_item_table",
      output_columns = bid_info_columns,
      is_clear = True,
    )
    self.remote_table_diff_and_alias(
      input_table_name="one_step_live_item_table",
      output_columns = bid_info_columns,
      is_clear = True,
    )
    self.feature_parse_bid_info(
      item_tables = ["inner_live_item_table","one_step_live_item_table","outer_live_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.if_("remote_table == 1").end_if_()
    self.fill_auto_bid_info(table_name="inner_live_item_table", module_name="inner_live")
    self.fill_auto_bid_info(table_name="one_step_live_item_table", module_name="one_step_live")
    self.fill_auto_bid_info(table_name="outer_live_item_table", module_name="outer_live")
    self.post_process(table_name="inner_live_item_table", module_name="inner_live_post")
    self.post_process(table_name="one_step_live_item_table", module_name="one_step_live_post")
    self.post_process(table_name="outer_live_item_table", module_name="outer_live_post")
    self.if_("enable_remote_table_and_diff == 1")
    self.remote_table_diff_and_alias(
      input_table_name="inner_live_item_table",
      output_columns = bid_info_columns,
    )
    self.remote_table_diff_and_alias(
      input_table_name="outer_live_item_table",
      output_columns = bid_info_columns,
    )
    self.remote_table_diff_and_alias(
      input_table_name="one_step_live_item_table",
      output_columns = bid_info_columns,
    )
    self.end_if_()
    self.end_if_()
    return self

  @module()
  def live_feature_bid(self):
    live_remote_bid_config = copy.deepcopy(remote_bid_config)
    live_remote_bid_config["request_tables"]["global_ad_table"]["item_tables"] = ["inner_live_item_table", "outer_live_item_table", "one_step_live_item_table"]
    live_remote_bid_config_new_fields = copy.deepcopy(remote_bid_config_new_fields)
    live_remote_bid_config_new_fields["request_tables"]["global_ad_table"]["item_tables"] = ["inner_live_item_table", "outer_live_item_table", "one_step_live_item_table"]

    self.local_bid_info(table_name="inner_live_item_table", module_name="inner_live")
    self.local_bid_info(table_name="one_step_live_item_table", module_name="one_step_live")
    self.local_bid_info(table_name="outer_live_item_table", module_name="outer_live")
    self.if_("enable_new_fields_from_forward == 1 or enable_new_fields_from_forward_diff == 1").remote_feature_index_diff(
      name = "grid_live5",
      **live_remote_bid_config_new_fields
    ).else_().remote_feature_index_diff(
      name = "grid_live6",
      **live_remote_bid_config
    ).end_if_()
    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["inner_live_item_table","one_step_live_item_table","outer_live_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(table_name="inner_live_item_table", module_name="inner_live_fill")
    self.fill_auto_bid_info(table_name="one_step_live_item_table", module_name="one_step_live_fill")
    self.fill_auto_bid_info(table_name="outer_live_item_table", module_name="outer_live_fill")
    self.post_process(table_name="inner_live_item_table", module_name="inner_live_post")
    self.post_process(table_name="one_step_live_item_table", module_name="one_step_live_post")
    self.post_process(table_name="outer_live_item_table", module_name="outer_live_post")
    self.msg_send(table_name="inner_live_item_table", module_name="inner_live_msg_send")
    self.msg_send(table_name="one_step_live_item_table", module_name="one_step_live_msg_send")
    self.msg_send(table_name="outer_live_item_table", module_name="outer_live_msg_send")
    return self

  @module()
  def inner_photo_hard_remote_and_diff(self):
    self.if_('enable_prerank_local_bid == 1')
    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["inner_photo_hard_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(table_name="inner_photo_hard_item_table")
    self.post_process(table_name="inner_photo_hard_item_table")
    self.remote_table_diff_and_alias(
      input_table_name="inner_photo_hard_item_table",
      output_columns = bid_info_columns,
      is_clear = True,
    )
    self.end_if_()
    return self

  @module()
  def inner_photo_hard_feature_bid(self):
    inner_photo_hard_remote_bid_config = copy.deepcopy(remote_bid_config)
    inner_photo_hard_remote_bid_config["request_tables"]["global_ad_table"]["item_tables"] = ["inner_photo_hard_item_table"]
    inner_photo_hard_remote_bid_config_new_fields = copy.deepcopy(remote_bid_config_new_fields)
    inner_photo_hard_remote_bid_config_new_fields["request_tables"]["global_ad_table"]["item_tables"] = ["inner_photo_hard_item_table"]

    self.local_bid_info(table_name="inner_photo_hard_item_table", module_name="inner_hard_bid_info")
    self.if_("enable_new_fields_from_forward == 1 or enable_new_fields_from_forward_diff == 1").remote_feature_index_diff(
      name = "grid_inner5",
      **inner_photo_hard_remote_bid_config_new_fields
    ).else_().remote_feature_index_diff(
      name = "grid_inner6",
      **inner_photo_hard_remote_bid_config
    ).end_if_()
    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["inner_photo_hard_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(table_name="inner_photo_hard_item_table")
    self.post_process(table_name="inner_photo_hard_item_table")
    self.msg_send(table_name="inner_photo_hard_item_table")
    return self

  @module()
  def inner_photo_soft_remote_and_diff(self):
    self.if_('enable_prerank_local_bid == 1')
    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["inner_photo_soft_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(table_name="inner_photo_soft_item_table")
    self.post_process(table_name="inner_photo_soft_item_table")
    self.if_("enable_remote_table_and_diff == 1")
    self.remote_table_diff_and_alias(
      input_table_name="inner_photo_soft_item_table",
      output_columns = bid_info_columns,
      is_clear = True,
    )
    self.end_if_()
    self.end_if_()
    return self

  @module()
  def inner_photo_soft_feature_bid(self):
    inner_photo_soft_remote_bid_config = copy.deepcopy(remote_bid_config)
    inner_photo_soft_remote_bid_config["request_tables"]["global_ad_table"]["item_tables"] = ["inner_photo_soft_item_table"]
    inner_photo_soft_remote_bid_config_new_fields = copy.deepcopy(remote_bid_config_new_fields)
    inner_photo_soft_remote_bid_config_new_fields["request_tables"]["global_ad_table"]["item_tables"] = ["inner_photo_soft_item_table"]

    self.local_bid_info(table_name="inner_photo_soft_item_table", module_name="inner_soft_bid_info")
    self.if_("enable_new_fields_from_forward == 1 or enable_new_fields_from_forward_diff == 1").remote_feature_index_diff(
      name = "grid_inner11",
      **inner_photo_soft_remote_bid_config_new_fields
    ).else_().remote_feature_index_diff(
      name = "grid_inner12",
      **inner_photo_soft_remote_bid_config
    ).end_if_()
    self.if_("remote_table == 1").end_if_()
    self.feature_parse_bid_info(
      item_tables = ["inner_photo_soft_item_table"],
      trans_unit_attr = remote_bid_config["request_tables"]["unit_auto_bid_table"]["local_field_type"],
      trans_campaign_attr = remote_bid_config["request_tables"]["campaign_auto_bid_table"]["local_field_type"],
      trans_account_attr = remote_bid_config["request_tables"]["account_auto_bid_table"]["local_field_type"],
    )
    self.fill_auto_bid_info(table_name="inner_photo_soft_item_table")
    self.post_process(table_name="inner_photo_soft_item_table")
    self.msg_send(table_name="inner_photo_soft_item_table")
    return self

  @module()
  def inner_photo_hard_bid_guard(self):
    current_flow() \
      .remote_table_diff_and_alias(
        input_table_name="inner_photo_hard_item_table",
        output_columns = bid_info_columns,
      ) \
      .prerank_bid_guard(
        item_table = "inner_photo_hard_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "aigc_bid_coef",
             "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter", "new_spu_tag","project_ocpx_action_type",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "creative_material_type", "wentou_increment_bid_coef"]
      )
    return self

  @module()
  def inner_photo_soft_bid_guard(self):
    current_flow() \
      .remote_table_diff_and_alias(
        input_table_name="inner_photo_soft_item_table",
        output_columns = bid_info_columns,
      ) \
      .prerank_bid_guard(
        item_table = "inner_photo_soft_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr", "cost_cap_type",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "aigc_bid_coef",
            "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter", "new_spu_tag","project_ocpx_action_type",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "creative_material_type","wentou_increment_bid_coef"]
      )
    return self

  @module()
  def live_bid_guard(self):
    current_flow() \
      .remote_table_diff_and_alias(
        input_table_name="inner_live_item_table",
        output_columns = bid_info_columns,
      ) \
      .remote_table_diff_and_alias(
        input_table_name="outer_live_item_table",
        output_columns = bid_info_columns,
      ) \
      .remote_table_diff_and_alias(
        input_table_name="one_step_live_item_table",
        output_columns = bid_info_columns,
      ) \
      .prerank_bid_guard(
        item_table = "inner_live_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr", "cost_cap_type",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "aigc_bid_coef",
            "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "creative_material_type","wentou_increment_bid_coef"]
      ) \
      .prerank_bid_guard(
        item_table = "outer_live_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr",
              "product_cpa_bid","product_roi_ratio", "first_coef", "second_coef",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "aigc_bid_coef",
            "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "creative_material_type", "wentou_increment_bid_coef"]
      ) \
      .prerank_bid_guard(
        item_table = "one_step_live_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr", "cost_cap_type",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "aigc_bid_coef",
            "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "wentou_increment_bid_coef"
        ]
      )
    return self

  @module()
  def outer_bid_guard(self):
    current_flow() \
      .prerank_bid_guard(
        item_table = "outer_soft_photo_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr",
              "product_cpa_bid","product_roi_ratio", "first_coef", "second_coef", "deep_cpa_bid",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "cpa_bid_coef", "aigc_bid_coef",
            "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "creative_material_type", "wentou_increment_bid_coef"]
      ) \
      .prerank_bid_guard(
        item_table = "outer_hard_photo_item_table",
        dragon_prerank_unfix_iaa_auto_roas_default = '{{' + ab("dragon_prerank_unfix_iaa_auto_roas_default", False) + '}}',
        dragon_prerank_fix_auto_roas_default = '{{' + ab("dragon_prerank_fix_auto_roas_default", False) + '}}',
        enable_ab_guard_bid = '{{' + ab("enable_ab_guard_bid", False) + '}}',
        disable_mini_app_gurad_account_bidding = '{{' + ab("disable_mini_app_gurad_account_bidding", False) + '}}',
        enable_playlet_gurad_account_bidding = '{{' + ab("enable_playlet_gurad_account_bidding", False) + '}}',
        skip_fanstop_play_nobid_fix = '{{' + ab("skip_fanstop_play_nobid_fix", False) + '}}',
        import_item_attrs = [
            "deep_group_tag", "ad_bid_server_group_tag", "bid_ad_type",
              "group_idx", "is_skip_bid_server", "exp_start_ts", "bid_server_type",
              "find_bid_info", "need_reset", "bid_explore_group_idx", "fanstop_cvr_threshold_ratio",
              "is_account_bidding", "auto_atv", "aggre_key", "auto_cpa_bid",
              "auto_cpa_bid_modify_tag", "auto_roas", "auto_roas_modify_tag",
              "acc_cold_start_coef", "raw_auto_cpa_bid", "ad_auto_bid_info",
              "twin_bid_strategy", "cpa_bid", "roi_ratio", "auto_deep_cpa_bid",
              "deep_flow_control_rate", "deep_min_coef", "deep_min_bid_coef",
              "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr",
              "product_cpa_bid","product_roi_ratio", "first_coef", "second_coef", "deep_cpa_bid",
              "fanstop_guaranteed_ratio", "cpm_bound", "fanstop_cost_ratio", "auto_bid_explore",
              "total_cost", "total_target_cost", "bid_coef",
              "today_cost", "today_target_cost", "cost", "target_cost",
              "hit_white_account_explore_cost", "bid_gimbal_type", "bid_gimbal_ratio", "sub_bid_coef", "cpa_bid_coef", "aigc_bid_coef",
            "use_ap_gimbal_ratio", "wentou_campaign_calibration", "inspire_auto_dark_filter",
            "account_wentou_gimbal_ratio_str", "account_wentou_bid_coef_hit_flag_str", "wentou_campaign_gimbal_ratio", "creative_material_type", "wentou_increment_bid_coef"]
      )
    return self

  @module()
  def inner_photo_hard_layer(self):
    current_flow() \
      .amd_layer_manager() \

    return self

  @module()
  def live_filter(self):
    current_flow() \
      .inspire_dark_control_enricher(
        input_table_name = "inner_live_item_table",
        is_inner_ad = "is_inner_ad",
        inspire_auto_dark_filter = "inspire_auto_dark_filter",
        import_common_attrs = ["llsid", "deep_group_tag", "group_tag_enum", "page_id",
                           "sub_page_id", "request_flow_type", "deep_coef_groug_tag", "flow_type",
                           "target_deploy", "pos_id", "is_rewarded", "is_inspire_live", "is_inspire_mix", "age_segment", "gender", "buyer_effective_type"]
      )
    return self

  @module()
  def inner_photo_soft_filter(self):
    current_flow() \
      .if_("enable_prerank_skip_superfluous_processor_inner_soft == 0 or is_rewarded == 1 or is_inspire_live == 1") \
      .inspire_dark_control_enricher(
        input_table_name = "inner_photo_soft_item_table",
        is_inner_ad = "is_inner_ad",
        inspire_auto_dark_filter = "inspire_auto_dark_filter",
        import_common_attrs = ["llsid", "deep_group_tag", "group_tag_enum", "page_id",
                           "sub_page_id", "request_flow_type", "deep_coef_groug_tag", "flow_type",
                           "target_deploy", "pos_id", "is_rewarded", "is_inspire_live", "is_inspire_mix", "age_segment", "gender", "buyer_effective_type"]
      ) \
      .end_if_() \
      
    return self

  @module()
  def inner_photo_hard_filter(self):
    current_flow() \
      .if_("enable_prerank_skip_superfluous_processor_inner_hard == 0 or is_rewarded == 1 or is_inspire_live == 1") \
      .inspire_dark_control_enricher(
        input_table_name = "inner_photo_hard_item_table",
        is_inner_ad = "is_inner_ad",
        inspire_auto_dark_filter = "inspire_auto_dark_filter",
        import_common_attrs = ["llsid", "deep_group_tag", "group_tag_enum", "page_id",
                           "sub_page_id", "request_flow_type", "deep_coef_groug_tag", "flow_type",
                           "target_deploy", "pos_id", "is_rewarded", "is_inspire_live", "is_inspire_mix", "age_segment", "gender", "buyer_effective_type"]
      ) \
      .end_if_() \
      
    return self

  @module()
  def common_prepare(self, name):
    current_flow() \
    .ad_common_prepare(name) \
    .ad_router_common_prepare(name)
    return self;

  @module()
  def ad_common_prepare(self, name):
    """
    参数预处理阶段
    1) 导入 ab 参数
    2) 反序列化 Message: MixRankRequest
    3) 定制化 Processor:mix_rank_pre_process, 完成不同 mix 策略前共同的预处理工作
    """
    current_flow() \
    .get_abtest_params(
      ab_params = [
        {
          "param_name": "use_fanstop_common_attr",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "use_fanstop_common_attr",
        },
        {
          "param_name": "enable_prerank_remote_ad_table",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_remote_ad_table",
        },
        {
          "param_name": "enable_adjust_native_live",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_adjust_native_live",
        },
        {
          "param_name": "enable_adjust_esp_mobile",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_adjust_esp_mobile",
        },
        {
          "param_name": "enable_adjust_fanstop_live",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_adjust_fanstop_live",
        },
        {
          "param_name": "enable_adjust_outer_live",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_adjust_outer_live",
        },
        {
          "param_name": "unify_bid_info_columns",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "unify_bid_info_columns"
        },
        {
          "param_name": "enable_prerank_redis_cache",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_redis_cache"
        },
        {
          "param_name": "enable_rank_adlist_cache",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_rank_adlist_cache"
        },
        {
          "param_name": "enable_rank_adlist_cache_skip_prerank_sort",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_rank_adlist_cache_skip_prerank_sort"
        },
        {
          "param_name": "prerank_find_optimization_by_hq",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "prerank_find_optimization_by_hq"
        },
        {
          "param_name": "enable_remote_table_and_diff",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_remote_table_and_diff"
        },
        {
          "param_name": "enable_predict_for_live_follow",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_predict_for_live_follow"
        },
        {
          "param_name": "enable_mix_live_one_two_list",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_mix_live_one_two_list"
        },
        {
          "param_name": "enable_follow_mix_live",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_follow_mix_live"
        },
        {
          "param_name": "prerank_mix_one_step_delivery_ensemble_weight",
          "default_value": 1.0,
          "param_type": "double",
          "attr_name": "prerank_mix_one_step_delivery_ensemble_weight",
        },
        {
          "param_name": "prerank_mix_one_step_ecpm_ensemble_weight",
          "default_value": 1.0,
          "param_type": "double",
          "attr_name": "prerank_mix_one_step_ecpm_ensemble_weight",
        },
        {
          "param_name": "shelf_live_prerank_ctcvr_ensemble_fix_weight",
          "default_value": 5.0,
          "param_type": "double",
          "attr_name": "shelf_live_prerank_ctcvr_ensemble_fix_weight",
        },
        {
          "param_name": "shelf_live_prerank_ltr_ensemble_fix_weight",
          "default_value": 5.0,
          "param_type": "double",
          "attr_name": "shelf_live_prerank_ltr_ensemble_fix_weight",
        },
        {
          "param_name": "prerank_mix_live_e2e_ecpm_ensemble_weight",
          "default_value": 0.0,
          "param_type": "double",
          "attr_name": "prerank_mix_live_e2e_ecpm_ensemble_weight",
        },
        {
          "param_name": "prerank_mix_live_ltr_ensemble_weight",
          "default_value": 2.0,
          "param_type": "double",
          "attr_name": "prerank_mix_live_ltr_ensemble_weight",
        },
        {
          "param_name": "prerank_live_ltr_ensemble_weight",
          "default_value": 2.0,
          "param_type": "double",
          "attr_name": "prerank_live_ltr_ensemble_weight",
        },
        {
          "param_name": "disable_outer_ecpm",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "disable_outer_ecpm",
        },
        {
          "param_name": "prerank_mix_live_e2e_ensemble_weight",
          "default_value": 5.0,
          "param_type": "double",
          "attr_name": "prerank_mix_live_e2e_ensemble_weight",
        },    
        {
          "param_name": "enable_new_fields_from_forward_prerank",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_new_fields_from_forward"
        },
        {
          "param_name": "enable_skip_dcaf_module",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_skip_dcaf_module",
        },
        {
          "param_name": "enable_prerank_rank_ica_inner",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_rank_ica_inner",
        },
        {
          "param_name": "enable_prerank_rank_ica_outer",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_rank_ica_outer",
        },
        {
          "param_name": "enable_prerank_rank_ica_live",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_rank_ica_live",
        },
        {
          "param_name": "enable_update_outerloop_nc_kconf",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_update_outerloop_nc_kconf",
        },
        {
          "param_name": "enable_async_clear_handler",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_async_clear_handler",
        },
        {
          "param_name": "prerank_direct_predict_flag",
          "default_value": 0,
          "param_type": "int",
          "attr_name": "prerank_direct_predict_flag_ab",
        },
        {
          "param_name": "prerank_direct_predict_cmdkey_group",
          "default_value": "base",
          "param_type": "string",
          "attr_name": "prerank_direct_predict_cmdkey_group",
        },
        {
          "param_name": "use_data_converter_v2_prerank",
          "default_value": 0,
          "param_type": "int",
          "attr_name": "use_data_converter_v2_prerank_ab",
        },
        {
          "param_name": "enable_prerank_user_info_bs_truncate",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_user_info_bs_truncate_ab",
        },
        {
          "param_name": "enable_prerank_user_info_bs_compress",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_user_info_bs_compress_ab",
        },
        {
          "param_name": "enable_prerank_skip_superfluous_processor_inner_hard",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_skip_superfluous_processor_inner_hard",
        },
        {
          "param_name": "enable_prerank_skip_superfluous_processor_inner_soft",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_skip_superfluous_processor_inner_soft",
        },
        {
          "param_name": "enable_prerank_skip_superfluous_processor_outer_hard",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_skip_superfluous_processor_outer_hard",
        },
        {
          "param_name": "enable_prerank_skip_superfluous_processor_outer_soft",
          "default_value": False,
          "param_type": "bool",
          "attr_name": "enable_prerank_skip_superfluous_processor_outer_soft",
        }
      ]
    )\
    .get_kconf_params(
      kconf_configs = [
        {
          "kconf_key": "ad.prerank_server.parseResponseMode",
          "value_type": "int",
          "export_common_attr": "parse_response_mode",
          "default_value": 0,
        },
        {
          "kconf_key": "ad.prerank_server.requestRemoteTable",
          "default_value": 0,
          "value_type": "int",
          "export_common_attr": "enable_prerank_request_remote_table"
        },
        {
          "kconf_key": "ad.prerank_server.enableLocalBid",
          "default_value": False,
          "value_type": "bool",
          "export_common_attr": "enable_prerank_local_bid"
        },
        {
          "kconf_key": "ad.prerank_server.enableSingleFeatureBid",
          "default_value": False,
          "value_type": "bool",
          "export_common_attr": "enable_prerank_feature_bid"
        },
        {
          "kconf_key": "ad.prerank_server.newFieldsFromForwardDiff",
          "default_value": False,
          "value_type": "bool",
          "export_common_attr": "enable_new_fields_from_forward_diff"
        },
        {
          "kconf_key": "ad.prerank_server.enableGridDiff",
          "default_value": False,
          "value_type": "bool",
          "export_common_attr": "enable_grid_diff"
        },
        {
          "kconf_key": "ad.prerank_server.prerankDirectPredictFlag",
          "default_value": 0,
          "value_type": "int",
          "export_common_attr": "prerank_direct_predict_flag_kconf"
        },
        {
          "kconf_key": "ad.prerank_server.useDataConverterV2",
          "default_value": 0,
          "value_type": "int",
          "export_common_attr": "use_data_converter_v2_prerank_kconf"
        },
        {
          "kconf_key": "ad.prerank_server.prerankUserInfoBSTruncate",
          "default_value": 0,
          "value_type": "int",
          "export_common_attr": "enable_prerank_user_info_bs_truncate_kconf"
        },
        {
          "kconf_key": "ad.prerank_server.prerankUserInfoBSCompress",
          "default_value": False,
          "value_type": "bool",
          "export_common_attr": "enable_prerank_user_info_bs_compress_kconf"
        }
      ]
    ) \
    .common_switch_mock_enricher() \
    .prepare_common_data(output_common_attrs = ["has_inner_soft_photo", "has_inner_hard_photo", "has_live", "has_outer_photo"])
    return self

  @module()
  def ad_router_common_prepare(self, name):
    current_flow() \
    .context_data_init_enricher(output_common_attrs = ["prerank_direct_predict_flag", "outer_predict_enum", "inner_soft_predict_enum", "inner_hard_predict_enum", "live_predict_enum","enable_prerank_user_info_bs_truncate","enable_prerank_user_info_bs_compress"]) \
    .ad_router_common_data_prepare()
    return self

  @module()
  def inner_photo_hard_predict(self):
    current_flow() \
    .context_feature_build_enricher(
      enable_fill_inner_trigger_emb_feature = '{{' + ab("enable_fill_inner_trigger_emb_feature", False) + '}}'
    ) \
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "inner_photo_hard_item_table",
          item_feature_table_name = "inner_photo_hard_item_feature_table"
        )
    ]) \
    .user_feature_build_enricher() \
    .default_prerank_inner_photo_hard_cmd_manager(
      enable_p2l_register_ecpm_cmd = '{{' + ab("enable_p2l_register_ecpm_cmd", False) + '}}',
      enable_p2l_register_photo_ctcvr_cmd = '{{' + ab("enable_p2l_register_photo_ctcvr_cmd", False) + '}}',
      enable_prerank_ctcvr_replace_e2e = '{{' + ab("enable_prerank_ctcvr_replace_e2e", False) + '}}',
      enable_p2l_register_photo_e2e_cmd = '{{' + ab("enable_p2l_register_photo_e2e_cmd", False) + '}}',
      enable_esp_fill_all_ltv = '{{' + ab("enable_esp_fill_all_ltv", False) + '}}',
      enable_hard_photo_neg_cmd = '{{' + ab("enable_hard_photo_neg_cmd", False) + '}}',
      enable_product_prerank_cmd = '{{' + ab("enable_product_prerank_cmd", False) + '}}',
      enable_hard_photo_multi_interest_cmd = '{{' + ab("enable_hard_photo_multi_interest_cmd", False) + '}}',
      enable_regist_hard_photo_unify_ltr_cmd = '{{' + ab("enable_regist_hard_photo_unify_ltr_cmd", False) + '}}',
      enable_regist_hard_photo_unify_ctcvr_cmd = '{{' + ab("enable_regist_hard_photo_unify_ctcvr_cmd", False) + '}}',
      enable_local_life_use_outer_prerank_cmd = '{{' + ab("enable_local_life_use_outer_prerank_cmd", False) + '}}',
      enable_hard_ecomm_interest_cmd = '{{' + ab("enable_hard_ecomm_interest_cmd", False) + '}}',
      enable_prerank_item_card_hard_ltr_cmd = '{{' + ab("enable_prerank_item_card_hard_ltr_cmd", False) + '}}',
      enable_prerank_shelf_ltr_cmd = '{{' + ab("enable_prerank_shelf_ltr_cmd", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_prerank_shelf_ctcvr_cmd = '{{' + ab("enable_prerank_shelf_ctcvr_cmd", False) + '}}',
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_mix_ltr_score_cmd = '{{' + ab("enable_prerank_mix_ltr_score_cmd_inner_hard", False) + '}}',
    ) \
    .default_prerank_local_life_cmd_manager(
      output_model_cmd_table_name = "inner_photo_hard_cmd_table",
      item_table= "inner_photo_hard_item_table",
      enable_local_life_use_outer_prerank_cmd = '{{' + ab("enable_local_life_use_outer_prerank_cmd", False) + '}}'
    ) \
    .model_register(
      register_table = [
        dict(
          item_table_name = "inner_photo_hard_item_table",
          cmd_table_name = "inner_photo_hard_cmd_table"
        )
    ]) \
    .calc_model_predict_extra_timeout(
      kconf_param="ad.prerank_server.modelPredictExtraTimeout",
      ab_param="ad_prerank_model_predict_extra_timeout"
    ) \
    .ad_router_predict_process_inner_hard() \
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "prerank_context_feature",
      item_feature_tables = ["inner_photo_hard_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "inner_photo_hard_cmd_table",
          "item_table" : "inner_photo_hard_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      extra_timeout = "{{model_predict_extra_timeout}}",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "inner_hard"
    )
    return self

  @module()
  def inner_photo_hard_predict_callback(self):
    current_flow() \
    .ad_router_predict_wait_inner_hard() \
    .default_prerank_inner_photo_hard_model_item_proc(
      enable_esp_live_roas_prerank_atv_p2l = '{{' + ab("enable_esp_live_roas_prerank_atv_p2l", False) + '}}',
      esp_live_roas_prerank_atv_p2l_ratio = '{{' + ab("esp_live_roas_prerank_atv_p2l_ratio", 1.0) + '}}',
      enable_esp_live_roas_prerank_atv_v2 = '{{' + ab("enable_esp_live_roas_prerank_atv_v2", False) + '}}',
      enable_hard_p2l_interest_ctr = '{{' + ab("enable_hard_p2l_interest_ctr", False) + '}}',
      enable_hard_photo_interest_ctr = '{{' + ab("enable_hard_photo_interest_ctr", False) + '}}',
      enable_remove_hard_photo_sctr = '{{' + ab("enable_remove_hard_photo_sctr", False) + '}}',
      item_table = "inner_photo_hard_item_table",
      import_item_attrs = [
            "deep_group_tag",
            "ad_bid_server_group_tag",
            "bid_ad_type",
            "group_idx",
            "is_skip_bid_server",
            "exp_start_ts",
            "bid_server_type",
            "find_bid_info",
            "need_reset",
            "is_account_bidding",
            "auto_atv",
            "aggre_key",
            "auto_cpa_bid",
            "auto_cpa_bid_modify_tag",
            "auto_roas",
            "auto_roas_modify_tag",
            "acc_cold_start_coef",
            "raw_auto_cpa_bid",
            "ad_auto_bid_info",
            "twin_bid_strategy",
            "cpa_bid",
            "roi_ratio",
            "auto_deep_cpa_bid",
            "deep_flow_control_rate",
            "deep_min_coef",
            "deep_min_bid_coef",
            "inspire_auto_dark_filter"]
    ) \
    .ad_router_predict_diff(scene = "inner_hard") \
    .model_trace()
    return self

  @module()
  def inner_photo_soft_predict(self):
    current_flow() \
    .default_prerank_inner_photo_soft_context_feature_build_enricher(
      enable_fill_inner_trigger_emb_feature_inner_soft = '{{' + ab("enable_fill_inner_trigger_emb_feature_inner_soft", False) + '}}'
    ) \
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "inner_photo_soft_item_table",
          item_feature_table_name = "inner_photo_soft_item_feature_table"
        )
    ]) \
    .user_feature_build_enricher() \
    .default_prerank_inner_photo_soft_cmd_manager(
      enable_prerank_divide_p_p2l = '{{' + ab("enable_prerank_divide_p_p2l", False) + '}}' ,
      enable_split_ecomm_ctcvr_cmd = '{{' + ab("enable_split_ecomm_ctcvr_cmd", False) + '}}',
      enable_prerank_item_card_soft_ltr_cmd = '{{' + ab("enable_prerank_item_card_soft_ltr_cmd", False) + '}}',
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_prerank_trigger_item_relative_skip_inner_soft = '{{' + ab("enable_prerank_trigger_item_relative_skip_inner_soft", False) + '}}',
      enable_prerank_trigger_item_relative_inner_soft_split = '{{' + ab("enable_prerank_trigger_item_relative_inner_soft_split", False) + '}}',
      enable_prerank_trigger_item_relative_score_cmd_inner_soft_fanstop = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd_inner_soft_fanstop", False) + '}}',
      enable_prerank_trigger_item_relative_score_cmd_inner_soft_esp = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd_inner_soft_esp", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_prerank_mix_ltr_score_cmd = '{{' + ab("enable_prerank_mix_ltr_score_cmd_inner_soft", False) + '}}',
    ) \
    .default_prerank_local_life_cmd_manager(
      output_model_cmd_table_name = "inner_photo_soft_cmd_table",
      item_table= "inner_photo_soft_item_table",
      enable_local_life_use_outer_prerank_cmd = '{{' + ab("enable_local_life_use_outer_prerank_cmd", False) + '}}'
    ) \
    .model_register(
      register_table = [
        dict(
          item_table_name = "inner_photo_soft_item_table",
          cmd_table_name = "inner_photo_soft_cmd_table"
        )
    ]) \
    .calc_model_predict_extra_timeout(
      kconf_param="ad.prerank_server.modelPredictExtraTimeout",
      ab_param="ad_prerank_model_predict_extra_timeout"
    ) \
    .ad_router_predict_process_inner_soft() \
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "prerank_inner_photo_soft_context_feature",
      item_feature_tables = ["inner_photo_soft_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "inner_photo_soft_cmd_table",
          "item_table" : "inner_photo_soft_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      extra_timeout = "{{model_predict_extra_timeout}}",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "inner_soft"
    )
    return self

  @module()
  def inner_photo_soft_predict_callback(self):
    current_flow() \
    .ad_router_predict_wait_inner_soft() \
    .default_prerank_inner_photo_soft_model_item_proc(
      item_table = "inner_photo_soft_item_table",
    ) \
    .ad_router_predict_diff(scene = "inner_soft") \
    .model_trace()
    return self

  @module()
  def outer_predict(self):
    current_flow() \
    .context_feature_build_enricher(
      output_context_feature_table_name = "outer_context_feature",
      enable_outer_prerank_add_candidate_top_feature = '{{' + ab("enable_outer_prerank_add_candidate_top_feature", False) + '}}',
      enable_outer_prerank_candidate_feature_hard_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_hard_sample_count", 0) + '}}',
      enable_outer_prerank_candidate_feature_soft_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_soft_sample_count", 0) + '}}',
      enable_fill_inner_trigger_emb_feature = '{{' + ab("enable_fill_inner_trigger_emb_feature", False) + '}}'
    ) \
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "outer_soft_photo_item_table",
          item_feature_table_name = "outer_soft_photo_item_feature_table"
        ),
        dict(
          item_table_name = "outer_hard_photo_item_table",
          item_feature_table_name = "outer_hard_photo_item_feature_table"
        )
    ]) \
    .user_feature_build_enricher(
      enable_outer_prerank_add_candidate_feature = '{{' + ab("enable_outer_prerank_add_candidate_feature", False) + '}}',
      enable_outer_prerank_candidate_feature_hard_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_hard_sample_count", 0) + '}}',
      enable_outer_prerank_candidate_feature_soft_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_soft_sample_count", 0) + '}}'
      ) \
    .default_prerank_outer_photo_cmd_manager(
      item_table = "outer_hard_photo_item_table",
      cmd_table_name = "outer_hard_photo_cmd_table",
      enable_user_exp_recall_predict = '{{' + ab("enable_user_exp_recall_predict", False) + '}}',
      enable_target_search_linucb_score_exp = '{{' + ab("enable_target_search_linucb_score_exp", False) + '}}',
      enable_target_search_tail_id_exp = '{{' + ab("enable_target_search_tail_id_exp", False) + '}}',
      enable_prerank_cvr_order_submit_v202305 = '{{' + ab("enable_prerank_cvr_order_submit_v202305", False) + '}}',
      enable_prerank_seven_day_pay_times = '{{' + ab("enable_prerank_seven_day_pay_times", False) + '}}',
      enable_prerank_lps_v202305 = '{{' + ab("enable_prerank_lps_v202305", False) + '}}',
      enable_prerank_ecpm2ctcvr = '{{' + ab("enable_prerank_ecpm2ctcvr", False) + '}}',
      enable_prerank_rewarded_sep = '{{' + ab("enable_prerank_rewarded_sep", False) + '}}',
      enable_prerank_other_v202305 = '{{' + ab("enable_prerank_other_v202305", False) + '}}',
      enable_prerank_leads_v202505 = '{{' + ab("enable_prerank_leads_v202505", False) + '}}',
      enable_prerank_msg_v202506 = '{{' + ab("enable_prerank_msg_v202506", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_order_submit_v202306 = '{{' + ab("enable_prerank_order_submit_v202306", False) + '}}',
      enable_purchase_roi_conv2_purchase = '{{' + ab("enable_purchase_roi_conv2_purchase", False) + '}}',
      enable_prerank_cpm_ltr_cmd_new = '{{' + ab("enable_prerank_cpm_ltr_cmd_new", False) + '}}',
      enable_deprecate_outer_prerank_ctcvr_cmd='{{' + ab("enable_deprecate_outer_prerank_ctcvr_cmd", False) + '}}',
      enable_prerank_llm = '{{' + ab("enable_prerank_llm", False) + '}}',
      enable_prerank_llm_rw = '{{' + ab("enable_prerank_llm_rw", False) + '}}',
      enable_prerank_no_ecpm = '{{' + ab("enable_prerank_no_ecpm", False) + '}}',
      prerank_ecpm_chain_type ='{{' + ab("prerank_ecpm_chain_type", 0) + '}}',
      enable_preranking_cpc_bid_predict_ltr = '{{' + ab("enable_preranking_cpc_bid_predict_ltr", False) + '}}',
      enable_preranking_dnc_ctcvr_predict = '{{' + ab("enable_preranking_dnc_ctcvr_predict", False) + '}}',
      enable_iaa_one_model = '{{' + ab("enable_iaa_one_model", False) + '}}',
      disable_mini_game_one_model = '{{' + ab("disable_mini_game_one_model", False) + '}}',
      enable_preranking_dnc_ctcvr_incentive_predict = '{{' + ab("enable_preranking_dnc_ctcvr_incentive_predict", False) + '}}',
      enable_stop_potential_nc_user = '{{' + ab("enable_stop_potential_nc_user", False) + '}}',
      enable_stop_low_active_user = '{{' + ab("enable_stop_low_active_user", False) + '}}',
      enable_prerank_dnc_model_support_all_ocpx = '{{' + ab("enable_prerank_dnc_model_support_all_ocpx", False) + '}}',
      enable_prerank_dnc_model_support_iaa_ocpx = '{{' + ab("enable_prerank_dnc_model_support_iaa_ocpx", False) + '}}',
      enabel_stop_w5_level_request = '{{' + ab("enabel_stop_w5_level_request", False) + '}}',
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_prerank_iaa_roas_predict_cmd = '{{' + ab("enable_prerank_iaa_roas_predict_cmd", False) + '}}',
      enable_prerank_roas_iaap_predict_cmd = '{{' + ab("enable_prerank_roas_iaap_predict_cmd", False) + '}}',
      enable_prerank_iaa_roas_advertise_ltv = '{{' + ab("enable_prerank_iaa_roas_advertise_ltv", False) + '}}',
      enable_prerank_playlet_iaa_predict_cmd = '{{' + ab("enable_prerank_playlet_iaa_predict_cmd", False) + '}}',
      enable_prerank_playlet_iap_predict_cmd_normal = '{{' + ab("enable_prerank_playlet_iap_predict_cmd_normal", False) + '}}',
      enable_prerank_playlet_ltv_model_only = '{{' + ab("enable_prerank_playlet_ltv_model_only", False) + '}}',
      enable_deprecate_prerank_rewarded_ctcvr = '{{' + ab("enable_deprecate_prerank_rewarded_ctcvr", False) + '}}',
      enable_deprecate_prerank_rewarded_ecpm = '{{' + ab("enable_deprecate_prerank_rewarded_ecpm", False) + '}}',
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_prerank_mix_ltr_score_cmd = '{{' + ab("enable_prerank_mix_ltr_score_cmd_outer_hard", False) + '}}',
      disable_prerank_conv_nextstay = '{{' + ab("disable_prerank_conv_nextstay", False) + '}}',
      enable_add_conv2_purchase = '{{' + ab("enable_add_conv2_purchase", False) + '}}',
      ) \
    .default_prerank_outer_photo_cmd_manager(
      item_table = "outer_soft_photo_item_table",
      cmd_table_name = "outer_soft_photo_cmd_table",
      enable_user_exp_recall_predict = '{{' + ab("enable_user_exp_recall_predict", False) + '}}',
      enable_target_search_linucb_score_exp = '{{' + ab("enable_target_search_linucb_score_exp", False) + '}}',
      enable_target_search_tail_id_exp = '{{' + ab("enable_target_search_tail_id_exp", False) + '}}',
      enable_prerank_cvr_order_submit_v202305 = '{{' + ab("enable_prerank_cvr_order_submit_v202305", False) + '}}',
      enable_prerank_seven_day_pay_times = '{{' + ab("enable_prerank_seven_day_pay_times", False) + '}}',
      enable_prerank_lps_v202305 = '{{' + ab("enable_prerank_lps_v202305", False) + '}}',
      enable_prerank_ecpm2ctcvr = '{{' + ab("enable_prerank_ecpm2ctcvr", False) + '}}',
      enable_prerank_rewarded_sep = '{{' + ab("enable_prerank_rewarded_sep", False) + '}}',
      enable_prerank_other_v202305 = '{{' + ab("enable_prerank_other_v202305", False) + '}}',
      enable_prerank_leads_v202505 = '{{' + ab("enable_prerank_leads_v202505", False) + '}}',
      enable_prerank_msg_v202506 = '{{' + ab("enable_prerank_msg_v202506", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_order_submit_v202306 = '{{' + ab("enable_prerank_order_submit_v202306", False) + '}}',
      enable_purchase_roi_conv2_purchase = '{{' + ab("enable_purchase_roi_conv2_purchase", False) + '}}',
      enable_prerank_cpm_ltr_cmd_new = '{{' + ab("enable_prerank_cpm_ltr_cmd_new", False) + '}}',
      enable_deprecate_outer_prerank_ctcvr_cmd='{{' + ab("enable_deprecate_outer_prerank_ctcvr_cmd", False) + '}}',
      enable_prerank_llm = '{{' + ab("enable_prerank_llm", False) + '}}',
      enable_prerank_llm_rw = '{{' + ab("enable_prerank_llm_rw", False) + '}}',
      enable_prerank_no_ecpm = '{{' + ab("enable_prerank_no_ecpm", False) + '}}',
      prerank_ecpm_chain_type ='{{' + ab("prerank_ecpm_chain_type", 0) + '}}',
      enable_preranking_cpc_bid_predict_ltr = '{{' + ab("enable_preranking_cpc_bid_predict_ltr", False) + '}}',
      enable_preranking_dnc_ctcvr_predict = '{{' + ab("enable_preranking_dnc_ctcvr_predict", False) + '}}',
      enable_iaa_one_model = '{{' + ab("enable_iaa_one_model", False) + '}}',
      disable_mini_game_one_model = '{{' + ab("disable_mini_game_one_model", False) + '}}',
      enable_preranking_dnc_ctcvr_incentive_predict = '{{' + ab("enable_preranking_dnc_ctcvr_incentive_predict", False) + '}}',
      enable_stop_potential_nc_user = '{{' + ab("enable_stop_potential_nc_user", False) + '}}',
      enable_stop_low_active_user = '{{' + ab("enable_stop_low_active_user", False) + '}}',
      enable_prerank_dnc_model_support_all_ocpx = '{{' + ab("enable_prerank_dnc_model_support_all_ocpx", False) + '}}',
      enable_prerank_dnc_model_support_iaa_ocpx = '{{' + ab("enable_prerank_dnc_model_support_iaa_ocpx", False) + '}}',
      enabel_stop_w5_level_request = '{{' + ab("enabel_stop_w5_level_request", False) + '}}',
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_prerank_iaa_roas_predict_cmd = '{{' + ab("enable_prerank_iaa_roas_predict_cmd", False) + '}}',
      enable_prerank_roas_iaap_predict_cmd = '{{' + ab("enable_prerank_roas_iaap_predict_cmd", False) + '}}',
      enable_prerank_iaa_roas_advertise_ltv = '{{' + ab("enable_prerank_iaa_roas_advertise_ltv", False) + '}}',
      enable_prerank_playlet_iaa_predict_cmd = '{{' + ab("enable_prerank_playlet_iaa_predict_cmd", False) + '}}',
      enable_prerank_playlet_iap_predict_cmd_normal = '{{' + ab("enable_prerank_playlet_iap_predict_cmd_normal", False) + '}}',
      enable_prerank_playlet_ltv_model_only = '{{' + ab("enable_prerank_playlet_ltv_model_only", False) + '}}',
      enable_deprecate_prerank_rewarded_ctcvr = '{{' + ab("enable_deprecate_prerank_rewarded_ctcvr", False) + '}}',
      enable_deprecate_prerank_rewarded_ecpm = '{{' + ab("enable_deprecate_prerank_rewarded_ecpm", False) + '}}',
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_prerank_mix_ltr_score_cmd = '{{' + ab("enable_prerank_mix_ltr_score_cmd_outer_soft", False) + '}}',
      disable_prerank_conv_nextstay = '{{' + ab("disable_prerank_conv_nextstay", False) + '}}',
      enable_add_conv2_purchase = '{{' + ab("enable_add_conv2_purchase", False) + '}}',
      ) \
    .model_register(
      register_table = [
        dict(
          item_table_name = "outer_soft_photo_item_table",
          cmd_table_name = "outer_soft_photo_cmd_table"
        ),
        dict(
          item_table_name = "outer_hard_photo_item_table",
          cmd_table_name = "outer_hard_photo_cmd_table"
        )
    ]) \
    .calc_model_predict_extra_timeout(
      kconf_param="ad.prerank_server.modelPredictExtraTimeout",
      ab_param="ad_prerank_outer_model_predict_extra_timeout"
    ) \
    .ad_router_predict_process_outer() \
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "outer_context_feature",
      item_feature_tables = ["outer_soft_photo_item_feature_table", "outer_hard_photo_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "outer_soft_photo_cmd_table",
          "item_table" : "outer_soft_photo_item_table"
        },
        {
          "cmd_table" : "outer_hard_photo_cmd_table",
          "item_table" : "outer_hard_photo_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      extra_timeout = "{{model_predict_extra_timeout}}",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "outer"
    )
    return self

  @module()
  def outer_predict_callback(self):
    current_flow() \
    .ad_router_predict_wait_outer() \
    .default_prerank_outer_photo_model_item_proc(
      input_table_name = "outer_hard_photo_item_table",
      pre_sc_k0_conv = '{{' + ab("pre_sc_k0_conv", 0.0) + '}}',
      pre_sc_k0_conv2ltv1 = '{{' + ab("pre_sc_k0_conv2ltv1", 0.0) + '}}',
      pre_sc_k0_conv2ltv7 = '{{' + ab("pre_sc_k0_conv2ltv7", 0.0) + '}}',
      pre_sc_k0_conv2next = '{{' + ab("pre_sc_k0_conv2next", 0.0) + '}}',
      pre_sc_k0_conv2key = '{{' + ab("pre_sc_k0_conv2key", 0.0) + '}}',
      pre_sc_k0_lps = '{{' + ab("pre_sc_k0_lps", 0.0) + '}}',
      pre_sc_k0_pay = '{{' + ab("pre_sc_k0_pay", 0.0) + '}}',
      pre_sc_k0_os = '{{' + ab("pre_sc_k0_os", 0.0) + '}}',
      pre_sc_k0_rest = '{{' + ab("pre_sc_k0_rest", 0.0) + '}}',
      enable_cid_prerank_cvr_boost_exp = '{{' + ab("enable_cid_prerank_cvr_boost_exp", 0) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_prerank_fuse_purchase = '{{' + ab("enable_prerank_fuse_purchase", False) + '}}',
      cid_prerank_cvr_boost_ratio = '{{' + ab("cid_prerank_cvr_boost_ratio", 1.0) + '}}',
      disable_prerank_conv_nextstay = '{{' + ab("disable_prerank_conv_nextstay", False) + '}}',
      prerank_conv_nextstay_default_value = '{{' + ab("prerank_conv_nextstay_default_value", 1.0) + '}}'
      ) \
    .default_prerank_outer_photo_model_item_proc(
      input_table_name = "outer_soft_photo_item_table",
      pre_sc_k0_conv = '{{' + ab("pre_sc_k0_conv", 0.0) + '}}',
      pre_sc_k0_conv2ltv1 = '{{' + ab("pre_sc_k0_conv2ltv1", 0.0) + '}}',
      pre_sc_k0_conv2ltv7 = '{{' + ab("pre_sc_k0_conv2ltv7", 0.0) + '}}',
      pre_sc_k0_conv2next = '{{' + ab("pre_sc_k0_conv2next", 0.0) + '}}',
      pre_sc_k0_conv2key = '{{' + ab("pre_sc_k0_conv2key", 0.0) + '}}',
      pre_sc_k0_lps = '{{' + ab("pre_sc_k0_lps", 0.0) + '}}',
      pre_sc_k0_pay = '{{' + ab("pre_sc_k0_pay", 0.0) + '}}',
      pre_sc_k0_os = '{{' + ab("pre_sc_k0_os", 0.0) + '}}',
      pre_sc_k0_rest = '{{' + ab("pre_sc_k0_rest", 0.0) + '}}',
      enable_cid_prerank_cvr_boost_exp = '{{' + ab("enable_cid_prerank_cvr_boost_exp", 0) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_prerank_fuse_purchase = '{{' + ab("enable_prerank_fuse_purchase", False) + '}}',
      cid_prerank_cvr_boost_ratio = '{{' + ab("cid_prerank_cvr_boost_ratio", 1.0) + '}}',
      disable_prerank_conv_nextstay = '{{' + ab("disable_prerank_conv_nextstay", False) + '}}',
      prerank_conv_nextstay_default_value = '{{' + ab("prerank_conv_nextstay_default_value", 1.0) + '}}'
      ) \
    .ad_router_predict_diff(scene = "outer") \
    .model_trace()
    return self

  @module()
  def live_predict(self):
    current_flow() \
    .context_feature_build_enricher(
      output_context_feature_table_name = "live_context_feature"
    ) \
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "inner_live_item_table",
          item_feature_table_name = "inner_live_item_feature_table"
        ),
        dict(
          item_table_name = "outer_live_item_table",
          item_feature_table_name = "outer_live_item_feature_table"
        )
    ]) \
    .user_feature_build_enricher(
      enable_inner_live_prerank_add_candidate_feature = '{{' + ab("enable_inner_live_prerank_add_candidate_feature_v1", False) + '}}'
    ) \
    .default_prerank_inner_live_cmd_manager(
      disable_follow_ctcvr =  '{{' + ab("disable_follow_ctcvr", False) + '}}',
      enable_merchant_live_prerank_gpm_factor = '{{' + ab("enable_merchant_live_prerank_gpm_factor", False) + '}}',
      enable_e2e_exclude_live = '{{' + ab("enable_e2e_exclude_live", False) + '}}',
      enable_prerank_live_ltr = '{{' + ab("enable_prerank_live_ltr", False) + '}}',
      enable_prerank_live_card_ltr_cmd = '{{' + ab("enable_prerank_live_card_ltr_cmd", False) + '}}',
      enable_prerank_live_card_ctcvr_cmd = '{{' + ab("enable_prerank_live_card_ctcvr_cmd", False) + '}}',
      enable_prerank_live_ctcvr_unify_cmd = '{{' + ab("enable_prerank_live_ctcvr_unify_cmd", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_live_multi_interest_cmd = '{{' + ab("enable_live_multi_interest_cmd", False) + '}}',
      enable_split_p2l_live_inroom_cmd = '{{' + ab("enable_split_p2l_live_inroom_cmd", False) + '}}',
      enable_split_p2l_live_p3s_pay_cmd = '{{' + ab("enable_split_p2l_live_p3s_pay_cmd", False) + '}}',
      disable_live_ecpm_cmd_register = '{{' + ab("disable_live_ecpm_cmd_register", False) + '}}',
    ) \
    .default_prerank_outer_live_cmd_manager(
      enable_prerank_cpm_ltr_cmd_new='{{' + ab("enable_prerank_cpm_ltr_cmd_new", False) + '}}',
      enable_prerank_cpm_ltr_cmd_live='{{' + ab("enable_prerank_cpm_ltr_cmd_live", False) + '}}',
      prerank_live_ltv_support_twin_bid='{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_locallife_live_user_inner_prerank='{{' + ab("enable_locallife_live_user_inner_prerank", False) + '}}',
      enable_deprecate_outer_prerank_ctcvr_cmd_v2='{{' + ab("enable_deprecate_outer_prerank_ctcvr_cmd_v2", False) + '}}',
      ) \
    .model_register(
      register_table = [
        dict(
          item_table_name = "inner_live_item_table",
          cmd_table_name = "inner_live_cmd_table"
        ),
        dict(
          item_table_name = "outer_live_item_table",
          cmd_table_name = "outer_live_cmd_table"
        )
    ]) \
    .calc_model_predict_extra_timeout(
      kconf_param="ad.prerank_server.modelPredictExtraTimeout",
      ab_param="ad_prerank_model_predict_extra_timeout"
    ) \
    .ad_router_predict_process_live() \
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "live_context_feature",
      item_feature_tables = ["inner_live_item_feature_table", "outer_live_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "inner_live_cmd_table",
          "item_table" : "inner_live_item_table"
        },
        {
          "cmd_table" : "outer_live_cmd_table",
          "item_table" : "outer_live_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      enable_mix_rank_hist_page_info_fea = True,
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      extra_timeout = "{{model_predict_extra_timeout}}",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "live"
    )
    return self

  @module()
  def live_predict_callback(self):
    current_flow() \
    .ad_router_predict_wait_live() \
    .default_prerank_inner_live_model_item_proc(
      item_table = "inner_live_item_table",
      enable_esp_live_roas_prerank_atv_v2 = '{{' + ab("enable_esp_live_roas_prerank_atv_v2", False) + '}}',
      import_item_attrs = [
            "deep_group_tag",
            "ad_bid_server_group_tag",
            "bid_ad_type",
            "group_idx",
            "is_skip_bid_server",
            "exp_start_ts",
            "bid_server_type",
            "find_bid_info",
            "need_reset",
            "is_account_bidding",
            "auto_atv",
            "aggre_key",
            "auto_cpa_bid",
            "auto_cpa_bid_modify_tag",
            "auto_roas",
            "auto_roas_modify_tag",
            "acc_cold_start_coef",
            "raw_auto_cpa_bid",
            "ad_auto_bid_info",
            "twin_bid_strategy",
            "cpa_bid",
            "roi_ratio",
            "auto_deep_cpa_bid",
            "deep_flow_control_rate",
            "deep_min_coef",
            "deep_min_bid_coef",
            "is_lowest_cost", "is_cost_cap", "cost_cap_p", "cost_cap_q", "real_sctr", "cost_cap_type",
            "fanstop_cvr_threshold_ratio", "inspire_auto_dark_filter"]
    ) \
    .default_prerank_outer_live_model_item_proc(
      item_table = "outer_live_item_table",
      import_item_attrs = [
            "deep_group_tag",
            "ad_bid_server_group_tag",
            "bid_ad_type",
            "group_idx",
            "is_skip_bid_server",
            "exp_start_ts",
            "bid_server_type",
            "find_bid_info",
            "need_reset",
            "is_account_bidding",
            "auto_atv",
            "aggre_key",
            "auto_cpa_bid",
            "auto_cpa_bid_modify_tag",
            "auto_roas",
            "auto_roas_modify_tag",
            "acc_cold_start_coef",
            "raw_auto_cpa_bid",
            "ad_auto_bid_info",
            "twin_bid_strategy",
            "cpa_bid",
            "roi_ratio",
            "auto_deep_cpa_bid",
            "deep_flow_control_rate",
            "deep_min_coef",
            "deep_min_bid_coef", "product_cpa_bid","product_roi_ratio"]
    ) \
    .ad_router_predict_diff(scene = "live") \
    .model_trace()
    return self

  @module()
  def inner_photo_hard_sort(self):
    current_flow() \
    .if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_inner == 0 or enable_prerank_rank_ica == 0") \
    .if_("is_rewarded == 0") \
    .dcaf_redis(
      enable_biz_name = True,
      biz_name = "inner_hard_photo",
      sub_page_id = "sub_page_id",
      version_id = '{{' + ab("dcaf_rank_version_inner_hard_photo", "") + '}}',
    ) \
    .else_() \
    .dcaf_redis(
      enable_biz_name = '{{' + ab("enable_dcaf_rank_inner_hard_photo_rewarded", False) + '}}',
      biz_name = "inner_hard_photo_rewarded",
      sub_page_id = "sub_page_id",
      version_id = '{{' + ab("dcaf_rank_version_inner_hard_photo_rewarded", "") + '}}',
    ) \
    .end_if_() \
    .end_if_() \
    .inner_hard_photo_auto_cpa_bid_enricher(
      input_table_name = "inner_photo_hard_item_table",
      esp_live_roas_prerank_atv_p2l_ratio = '{{' + ab("esp_live_roas_prerank_atv_p2l_ratio", 1.0) + '}}',
      enable_esp_live_roas_prerank_atv_v2 = '{{' + ab("enable_esp_live_roas_prerank_atv_v2", False) + '}}',
      amd_photo_sort_bid_separate_weight = '{{' + ab("amd_photo_sort_bid_separate_weight", 1.0) + '}}',
      amd_photo_billing_ratio = '{{' + ab("amd_photo_billing_ratio", 1.0) + '}}',
      enable_hard_photo_cal_sort_separate_bid = '{{' + ab("enable_hard_photo_cal_sort_separate_bid", False) + '}}',
      enable_hard_photo_prerank_bid_t7_conv_ratio = '{{' + ab("enable_hard_photo_prerank_bid_t7_conv_ratio", False) + '}}',
      hard_prerank_sep_bid_weight = '{{' + ab("hard_prerank_sep_bid_weight", -1.0) + '}}',
      auto_roas = "bid_auto_roas",
      roi_ratio = "roi_ratio",
      auto_cpa_bid = "bid_auto_cpa_bid",
      scene_oriented_type = "scene_oriented_type",
      enable_rank_adlist_cache = '{{' + ab("enable_rank_adlist_cache", False) + '}}',
      enable_rank_adlist_cache_skip_prerank_sort = '{{' + ab("enable_rank_adlist_cache_skip_prerank_sort", False) + '}}',
    ) \
    .if_("enable_rank_adlist_cache == 0 or is_hit_cache == 0 or enable_rank_adlist_cache_skip_prerank_sort == 0") \
    .inner_hard_photo_ecpm_enricher(
      input_table_name = "inner_photo_hard_item_table",
      auto_roas = "bid_auto_roas",
      roi_ratio = "roi_ratio",
      scene_oriented_type = "scene_oriented_type",
      enable_ecpm_separate_sort_bid = '{{' + ab("enable_ecpm_separate_sort_bid", False) + '}}',
      hard_prerank_sep_bid_weight = '{{' + ab("hard_prerank_sep_bid_weight", -1.0) + '}}',
      hard_photo_ecpm_bid_power = '{{' + ab("hard_photo_ecpm_bid_power", 1.0) + '}}',
      hard_p2l_ecpm_bid_power = '{{' + ab("hard_p2l_ecpm_bid_power", 1.0) + '}}',
      enable_prerank_bs_inner = '{{' + ab("enable_prerank_bs_inner", False) + '}}',
      prerank_bs_bid_ratio_inner = '{{' + ab("prerank_bs_bid_ratio_inner", 1.0) + '}}',
      enable_hard_photo_interest_ctr_score = '{{' + ab("enable_hard_photo_interest_ctr_score", False) + '}}',
      enable_hard_p2l_interest_ctr_score = '{{' + ab("enable_hard_p2l_interest_ctr_score", False) + '}}',
    ) \
    .inner_hard_photo_calc_delivery_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_delivery_rate_ctcvr = '{{' + ab("enable_delivery_rate_ctcvr_amd", False) + '}}',
      enable_new_creative_explore = '{{' + ab("enable_new_creative_explore", False) + '}}',
      coldstart_delivery_rate_std_dev = '{{' + ab("coldstart_delivery_rate_std_dev", 0.01) + '}}',
      coldstart_explore_exp_tag = '{{' + ab("coldstart_explore_exp_tag", 20) + '}}',
      enable_reward_split_bid = '{{' + ab("enable_reward_split_bid", False) + '}}',
      enable_prerank_ltr_bid = '{{' + ab("enable_prerank_ltr_bid", False) + '}}',
      enable_prerank_ltr_bid_p2l = '{{' + ab("enable_prerank_ltr_bid_p2l", False) + '}}',
      enable_prerank_ltr_bid_log = '{{' + ab("enable_prerank_ltr_bid_log", False) + '}}',
      enable_e2e_separate_sort_bid = '{{' + ab("enable_e2e_separate_sort_bid", False) + '}}',
      enable_prerank_bs_inner = '{{' + ab("enable_prerank_bs_inner", False) + '}}',
      enable_prerank_bs_inner_roi = '{{' + ab("enable_prerank_bs_inner_roi", False) + '}}',
      prerank_bs_bid_ratio_inner = '{{' + ab("prerank_bs_bid_ratio_inner", 1.0) + '}}',
      prerank_bs_bid_ratio_inner_roi = '{{' + ab("prerank_bs_bid_ratio_inner_roi", 1.0) + '}}',
      hard_prerank_sep_bid_weight = '{{' + ab("hard_prerank_sep_bid_weight", -1.0) + '}}',
      hard_prerank_fans_top_roi_10 = '{{' + ab("hard_prerank_fans_top_roi_10", False) + '}}',
      u0_pow_ratio_ltr = '{{' + ab("u0_pow_ratio_ltr", 1.0) + '}}',
      enable_p2l_ltr_use_atv = '{{' + ab("enable_p2l_ltr_use_atv", False) + '}}',
      buyer_effective_type = "buyer_effective_type",
      is_rewarded = "is_rewarded",
      ocpx_action_type = "ocpx_action_type",
      live_creative_type = "live_creative_type",
      auto_cpa_bid = "bid_auto_cpa_bid",
      auto_roas = "bid_auto_roas",
      roi_ratio = "roi_ratio",
      new_creative_tag = "new_creative_tag",
      delivery_rate = "delivery_rate",
      new_creative_delivery_rate = "new_creative_delivery_rate",
      e2e_idx = "e2e_idx",
      scene_oriented_type = "scene_oriented_type",
    ) \
    .inner_hard_photo_cpm_ltr_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_cpm_ltr_ctcvr = '{{' + ab("enable_cpm_ltr_ctcvr_amd", False) + '}}',
      enable_hard_prerank_ctcvr_fix = '{{' + ab("enable_hard_prerank_ctcvr_fix", False) + '}}',
      enable_rewarded_ctcvr_cal_ecpm = '{{' + ab("enable_rewarded_ctcvr_cal_ecpm", False) + '}}',
      enable_ctcvr_separate_sort_bid = '{{' + ab("enable_ctcvr_separate_sort_bid", False) + '}}',
      hard_prerank_sep_bid_weight = '{{' + ab("hard_prerank_sep_bid_weight", -1.0) + '}}',
      hard_prerank_fans_top_roi_10 = '{{' + ab("hard_prerank_fans_top_roi_10", False) + '}}',
      enable_record_ctcvr_gpm = '{{' + ab("enable_record_ctcvr_gpm", False) + '}}',
      roas_ctcvr_gpm_weight = '{{' + ab("roas_ctcvr_gpm_weight", 0.0) + '}}',
      enable_prerank_bs_inner = '{{' + ab("enable_prerank_bs_inner", False) + '}}',
      enable_prerank_bs_inner_roi = '{{' + ab("enable_prerank_bs_inner_roi", False) + '}}',
      prerank_bs_bid_ratio_inner = '{{' + ab("prerank_bs_bid_ratio_inner", 1.0) + '}}',
      prerank_bs_bid_ratio_inner_roi = '{{' + ab("prerank_bs_bid_ratio_inner_roi", 1.0) + '}}',
      u0_pow_ratio_ctcvr = '{{' + ab("u0_pow_ratio_ctcvr", 1.0) + '}}',
      buyer_effective_type = "buyer_effective_type",
      auto_roas = "bid_auto_roas",
      roi_ratio = "roi_ratio",
      auto_cpa_bid = "bid_auto_cpa_bid",
      scene_oriented_type = "scene_oriented_type",
    ) \
    .inner_hard_photo_neg_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_hard_photo_neg_cmd = '{{' + ab("enable_hard_photo_neg_cmd", False) + '}}',
      enable_product_prerank_cmd = '{{' + ab("enable_product_prerank_cmd", False) + '}}',
    ) \
    .inner_hard_photo_multi_interest_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_hard_photo_multi_interest_cmd = '{{' + ab("enable_hard_photo_multi_interest_cmd", False) + '}}',
      hard_photo_multi_interest_a_ensemble_weight = '{{' + ab("hard_photo_multi_interest_a_ensemble_weight", 0.0) + '}}',
      hard_photo_multi_interest_b_ensemble_weight = '{{' + ab("hard_photo_multi_interest_b_ensemble_weight", 0.0) + '}}',
      hard_photo_multi_interest_c_ensemble_weight = '{{' + ab("hard_photo_multi_interest_c_ensemble_weight", 0.0) + '}}',
      hard_p2l_multi_interest_a_ensemble_weight = '{{' + ab("hard_p2l_multi_interest_a_ensemble_weight", 0.0) + '}}',
      hard_p2l_multi_interest_b_ensemble_weight = '{{' + ab("hard_p2l_multi_interest_b_ensemble_weight", 0.0) + '}}',
      hard_p2l_multi_interest_c_ensemble_weight = '{{' + ab("hard_p2l_multi_interest_c_ensemble_weight", 0.0) + '}}',
    ) \
    .if_("enable_prerank_skip_superfluous_processor_inner_hard == 0 or (page_id ~= 10011 and page_id ~= 11001)") \
    .trigger_item_relative_enricher(
      input_table_name = "inner_photo_hard_item_table",
      separated_photo_and_p2l = True,
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank_inner_hard", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_trigger_relative_sort_with_bid = '{{' + ab("enable_trigger_relative_sort_with_bid", False) + '}}',
      enable_ltr_relative_score_ensemble = '{{' + ab("enable_ltr_relative_score_ensemble", False) + '}}',
      enable_ltr_score_sigmoid = '{{' + ab("enable_ltr_score_sigmoid", False) + '}}',
      relative_score_weight = '{{' + ab("relative_score_weight_inner_hard", 0.0) + '}}',
      ltr_score_weight = '{{' + ab("ltr_score_weight_inner_hard", 0.0) + '}}',
      ltr_score_scale = '{{' + ab("ltr_score_scale_inner_hard", 1.0) + '}}',
      prerank_relative_ensemble_exp_tag = '{{' + ab("prerank_relative_ensemble_exp_tag", "") + '}}',
      enable_relative_ltr_inner_soft_fix = '{{' + ab("enable_relative_ltr_inner_soft_fix", False) + '}}',
      enable_relative_score_mul_bid = '{{' + ab("enable_relative_score_mul_bid", False) + '}}',
      enable_relative_score_sigmoid = '{{' + ab("enable_relative_score_sigmoid", False) + '}}',
      relative_score_scale = '{{' + ab("relative_score_scale_inner_hard", 1.0) + '}}',
      enable_inner_explore_ltr_sim_score_calc = '{{' + ab("enable_inner_explore_ltr_sim_score_calc", False) + '}}',
    ) \
    .end_if_() \
    .if_("enable_prerank_skip_superfluous_processor_inner_hard == 0") \
    .inner_hard_photo_shelf_delivery_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_prerank_shelf_ltr_cmd = '{{' + ab("enable_prerank_shelf_ltr_cmd", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_fix_cpm_ltr_attr = '{{' + ab("enable_fix_cpm_ltr_attr_inner_hard", False) + '}}',
    ) \
    .end_if_() \
    .inner_hard_photo_gpm_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_gpm_prerank_p2l = '{{' + ab("enable_gpm_prerank_inner_hard_p2l", False) + '}}',
      enable_gpm_prerank_photo = '{{' + ab("enable_gpm_prerank_inner_hard_photo", False) + '}}',
      disable_gpm_prerank_reward = '{{' + ab("disable_gpm_prerank_reward_inner_hard", False) + '}}',
      enable_gpm_fix_ctcvr = '{{' + ab("enable_gpm_fix_ctcvr_inner_hard", False) + '}}',
      enable_gpm_roas = '{{' + ab("enable_gpm_roas_inner_hard", False) + '}}',
      prerank_gpm_roas_scale = '{{' + ab("prerank_gpm_roas_scale_inner_hard", 10000.0) + '}}',
      prerank_gpm_ecpm_weight_p2l = '{{' + ab("prerank_gpm_ecpm_weight_inner_hard_p2l", 0.0) + '}}',
      prerank_gpm_ecpm_weight_photo = '{{' + ab("prerank_gpm_ecpm_weight_inner_hard_photo", 0.0) + '}}',
    ) \
    .if_("%s %s"%(ab("enable_retarget_enricher_up", False), "== 1")) \
    .prerank_retarget_tag_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_fix_action_cate3_tag = '{{' + ab("enable_fix_action_cate3_tag", False) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      enable_sign_ecpm_idx = '{{' + ab("enable_sign_ecpm_idx", False) + '}}',
      shorttime_action_gap = '{{' + ab("shorttime_action_gap", 0) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_shelf_add_order_seller = '{{' + ab("enable_shelf_add_order_seller", False) + '}}',
      enable_shelf_sign_search_author = '{{' + ab("enable_shelf_sign_search_author", False) + '}}',
      enable_shelf_sign_search_item = '{{' + ab("enable_shelf_sign_search_item", False) + '}}',
      enable_fix_retarget_item_clk = '{{' + ab("enable_fix_retarget_item_clk", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}',
      enable_sign_mix_photo_idx = '{{' + ab("enable_sign_mix_photo_idx", False) + '}}',
      enable_sign_mix_author_idx = '{{' + ab("enable_sign_mix_author_idx", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
    ) \
    .end_if_() \
    .inner_hard_photo_ensemble_score_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_product_interest_up = '{{' + ab("enable_product_interest_up", False) + '}}',
      product_interest_up_ratio = '{{' + ab("product_interest_up_ratio", 1.0) + '}}',
      product_interest_tag = '{{' + ab("product_interest_tag", 0) + '}}',
      disable_p2l_ecpm_ensemble = '{{' + ab("disable_p2l_ecpm_ensemble", False) + '}}',
      enable_p2l_ctcvr_ensemble = '{{' + ab("enable_p2l_ctcvr_ensemble", False) + '}}',
      enable_inner_hard_photo_only_ltr = '{{' + ab("enable_inner_hard_photo_only_ltr", False) + '}}',
      enable_inner_hard_p2l_only_ltr = '{{' + ab("enable_inner_hard_p2l_only_ltr", False) + '}}',
      enable_hard_photo_neg_cmd = '{{' + ab("enable_hard_photo_neg_cmd", False) + '}}',
      enable_product_prerank_cmd = '{{' + ab("enable_product_prerank_cmd", False) + '}}',
      enable_hard_photo_multi_interest_cmd = '{{' + ab("enable_hard_photo_multi_interest_cmd", False) + '}}',
      hard_photo_multi_interest_ensemble_weight = '{{' + ab("hard_photo_multi_interest_ensemble_weight", 0.0) + '}}',
      hard_p2l_multi_interest_ensemble_weight = '{{' + ab("hard_p2l_multi_interest_ensemble_weight", 0.0) + '}}',
      prerank_photo_living_ensemble_scale = '{{' + ab("prerank_photo_living_ensemble_scale", 1.0) + '}}',
      prerank_photo_neg_ensemble_weight = '{{' + ab("prerank_photo_neg_ensemble_weight", 0.0) + '}}',
      prerank_p2l_neg_ensemble_weight = '{{' + ab("prerank_p2l_neg_ensemble_weight", 0.0) + '}}',
      prerank_neg_rate_threshold = '{{' + ab("prerank_neg_rate_threshold", 0.0) + '}}',
      prerank_neg_drop_rate_threshold = '{{' + ab("prerank_neg_drop_rate_threshold", 0.0) + '}}',
      prerank_cpm_ltr_ensemble_weight = '{{' + ab("merchant_prerank_cpm_ltr_ensemble_weight_", 1.0) + '}}',
      prerank_e2e_ensemble_weight = '{{' + ab("merchant_prerank_e2e_ensemble_weight_", 1.0) + '}}',
      prerank_ecpm_ensemble_weight = '{{' + ab("merchant_prerank_ecpm_ensemble_weight_", 1.0) + '}}',
      prerank_p2l_e2e_ensemble_weight = '{{' + ab("merchant_prerank_p2l_e2e_ensemble_weight_", 1.0) + '}}',
      prerank_p2l_ecpm_ensemble_weight = '{{' + ab("merchant_prerank_p2l_ecpm_ensemble_weight_", 1.0) + '}}',
      prerank_p2l_cpm_ltr_ensemble_weight = '{{' + ab("merchant_prerank_p2l_cpm_ltr_ensemble_weight_", 1.0) + '}}',
      prerank_esp_photo_gpm_ensemble_weight = '{{' + ab("prerank_esp_photo_gpm_ensemble_weight", 0.0) + '}}',
      enable_hyperbolic_ensemble_score = '{{' + ab("enable_hyperbolic_ensemble_score_inner_hard", False) + '}}',
      enable_hyperbolic_ensemble_score_v2 = '{{' + ab("enable_hyperbolic_ensemble_score_v2_inner_hard", False) + '}}',
      enable_es_v2_quantile = '{{' + ab("enable_es_v2_quantile_inner_hard", False) + '}}',
      es_v2_quantile = '{{' + ab("es_v2_quantile_inner_hard", 0.5) + '}}',
      enable_mean_multiplier = '{{' + ab("enable_mean_multiplier_inner_hard", False) + '}}',
      prerank_ensemble_score_exp_name = '{{' + ab("prerank_ensemble_score_exp_name_inner_hard", "") + '}}',
      prerank_ensemble_score_exp_name_p2l = '{{' + ab("prerank_ensemble_score_exp_name_p2l_inner_hard", "") + '}}',
      enable_use_old_es_score_for_value = '{{' + ab("enable_use_old_es_score_for_value_inner_hard", False) + '}}',
      enable_use_old_es_score_for_value_p2l = '{{' + ab("enable_use_old_es_score_for_value_p2l_inner_hard", False) + '}}',
      enable_ensemble_sort_rebuild = '{{' + ab("enable_hard_photo_target_ensemble_sort_rebuild", False) + '}}',
      enable_photo_u0_ensemble = '{{' + ab("enable_photo_u0_ensemble", False) + '}}',
      buyer_effective_type = "buyer_effective_type",
      prerank_ctcvr_ensemble_weight_u0 = '{{' + ab("merchant_prerank_ctcvr_ensemble_weight_u0", 1.0) + '}}',
      prerank_ltr_ensemble_weight_u0 = '{{' + ab("merchant_prerank_ltr_ensemble_weight_u0", 1.0) + '}}',
      prerank_ecpm_ensemble_weight_u0 = '{{' + ab("merchant_prerank_ecpm_ensemble_weight_u0", 1.0) + '}}',
      enable_t7_roi_prerank_boost = '{{' + ab("enable_t7_roi_prerank_conv_ratio", False) + '}}',
      t7_roi_prerank_boost_ratio = '{{' + ab("t7_roi_prerank_boost_ratio", 1.0) + '}}',
      enable_roas_p2l_prerank_boost = '{{' + ab("enable_roas_p2l_prerank_boost", False) + '}}',
      roas_p2l_prerank_boost_ratio = '{{' + ab("roas_p2l_prerank_boost_ratio", 1.0) + '}}',
      unify_roas_p2l_prerank_boost_ratio = '{{' + ab("unify_roas_p2l_prerank_boost_ratio_hard", 1.0) + '}}',
      enable_esp_follow_adjust = '{{' + ab("enable_esp_follow_adjust", False) + '}}',
      esp_follow_buyer_adjust_tag = '{{' + ab("esp_follow_buyer_adjust_tag", "") + '}}',
      esp_follow_follow_num_adjust_tag = '{{' + ab("esp_follow_follow_num_adjust_tag", "") + '}}',
      enable_storewide_roas_p2l_prerank_boost = '{{' + ab("enable_storewide_roas_p2l_prerank_boost", False) + '}}',
      storewide_roas_p2l_prerank_boost_ratio = '{{' + ab("storewide_roas_p2l_prerank_boost_ratio", 1.0) + '}}',
      enable_gpm_prerank_p2l = '{{' + ab("enable_gpm_prerank_inner_hard_p2l", False) + '}}',
      enable_gpm_prerank_photo = '{{' + ab("enable_gpm_prerank_inner_hard_photo", False) + '}}',
      disable_gpm_prerank_reward = '{{' + ab("disable_gpm_prerank_reward_inner_hard", False) + '}}',
      prerank_p2l_gpm_ensemble_weight = '{{' + ab("prerank_gpm_ensemble_weight_inner_hard_p2l", 0.0) + '}}',
      enable_shelf_merchant_ensemble = '{{' + ab("enable_shelf_merchant_ensemble", False) + '}}',
      prerank_ecpm_ensemble_weight_shelf = '{{' + ab("prerank_ecpm_ensemble_weight_shelf", 0.0) + '}}',
      prerank_ltr_ensemble_weight_shelf = '{{' + ab("prerank_ltr_ensemble_weight_shelf", 0.0) + '}}',
      prerank_ctcvr_ensemble_weight_shelf = '{{' + ab("prerank_ctcvr_ensemble_weight_shelf", 0.0) + '}}',
      prerank_photo_neg_ensemble_weight_shelf = '{{' + ab("prerank_photo_neg_ensemble_weight_shelf", 0.0) + '}}',
      prerank_shelf_ltr_ensemble_weight_shelf = '{{' + ab("prerank_shelf_ltr_ensemble_weight_shelf", 0.0) + '}}',
      prerank_gpm_ensemble_weight = '{{' + ab("prerank_gpm_ensemble_weight_inner_hard", 0.0) + '}}',
      e2e_topk_protect_photo_cnt = '{{' + ab("e2e_topk_protect_inner_photo_hard_cnt", 0) + '}}',
      e2e_topk_protect_photo_ratio = '{{' + ab("e2e_topk_protect_inner_photo_hard_ratio", 0.0) + '}}',
      e2e_topk_protect_p2l_cnt = '{{' + ab("e2e_topk_protect_inner_p2l_hard_cnt", 0) + '}}',
      e2e_topk_protect_p2l_ratio = '{{' + ab("e2e_topk_protect_inner_p2l_hard_ratio", 0.0) + '}}',
      cpm_ltr_topk_protect_photo_cnt = '{{' + ab("cpm_ltr_topk_protect_inner_photo_hard_cnt", 0) + '}}',
      cpm_ltr_topk_protect_photo_ratio = '{{' + ab("cpm_ltr_topk_protecpm_topk_protect_outer_photo_hard_cntect_inner_photo_hard_ratio", 0.0) + '}}',
      cpm_ltr_topk_protect_p2l_cnt = '{{' + ab("cpm_ltr_topk_protect_inner_p2l_hard_cnt", 0) + '}}',
      cpm_ltr_topk_protect_p2l_ratio = '{{' + ab("cpm_ltr_topk_protect_inner_p2l_hard_ratio", 0.0) + '}}',
      enable_e2e_topk_v2_photo = '{{' + ab("enable_e2e_topk_v2_photo_inner_hard", False) + '}}',
      enable_e2e_topk_v2_p2l = '{{' + ab("enable_e2e_topk_v2_p2l_inner_hard", False) + '}}',
      enable_pure_sort_es = '{{' + ab("enable_pure_sort_es_inner_hard", False) + '}}',
      enable_cpm_ltr_topk_v2_photo = '{{' + ab("enable_cpm_ltr_topk_v2_photo_inner_hard", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_cpm_ltr_topk_v2_p2l = '{{' + ab("enable_cpm_ltr_topk_v2_p2l_inner_hard", False) + '}}',
      e2e_boost_deboost_range_photo = '{{' + ab("e2e_boost_deboost_range_photo_inner_hard", "0.0,0.0") + '}}',
      e2e_boost_deboost_range_p2l = '{{' + ab("e2e_boost_deboost_range_p2l_inner_hard", "0.1,0.1") + '}}',
      cpm_ltr_boost_deboost_range_photo = '{{' + ab("cpm_ltr_boost_deboost_range_photo_inner_hard", "0.1,0.1") + '}}',
      cpm_ltr_boost_deboost_range_p2l = '{{' + ab("cpm_ltr_boost_deboost_range_p2l_inner_hard", "0.1,0.1") + '}}',
      e2e_boost_deboost_strength_photo = '{{' + ab("e2e_boost_deboost_strength_photo_inner_hard", "1.0,1.0") + '}}',
      e2e_boost_deboost_strength_p2l = '{{' + ab("e2e_boost_deboost_strength_p2l_inner_hard", "1.0,1.0") + '}}',
      cpm_ltr_boost_deboost_strength_photo = '{{' + ab("cpm_ltr_boost_deboost_strength_photo_inner_hard", "1.0,1.0") + '}}',
      cpm_ltr_boost_deboost_strength_p2l = '{{' + ab("cpm_ltr_boost_deboost_strength_p2l_inner_hard", "1.0,1.0") + '}}',
      enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank", False) + '}}',
      prerank_trigger_relative_score_ensemble_weight_photo = '{{' + ab("prerank_trigger_relative_score_ensemble_weight_inner_hard_photo", 0.0) + '}}',
      prerank_trigger_relative_score_ensemble_weight_p2l = '{{' + ab("prerank_trigger_relative_score_ensemble_weight_inner_hard_p2l", 0.0) + '}}',
      trigger_relative_score_request_times_threshold = '{{' + ab("trigger_relative_score_request_times_threshold", 0) + '}}',
      enable_relative_score_hc_ratio_prerank = '{{' + ab("enable_relative_score_hc_ratio_prerank", False) + '}}',
      enable_up_refresh_relative_hc_only = '{{' + ab("enable_up_refresh_relative_hc_only", False) + '}}',
      enable_relative_weight_dec_with_request_times = '{{' + ab("enable_relative_weight_dec_with_request_times", False) + '}}',
      enable_prerank_trigger_item_relative_score_boost = '{{' + ab("enable_prerank_trigger_item_relative_score_boost", False) + '}}',
      prerank_trigger_item_relative_score_boost_threshold = '{{' + ab("prerank_trigger_item_relative_score_boost_threshold", 0.0) + '}}',
      prerank_trigger_item_relative_score_boost_ratio = '{{' + ab("prerank_trigger_item_relative_score_boost_ratio_inner_hard", 1.0) + '}}',
      enable_prerank_class_dist_reform = '{{' + ab("enable_prerank_class_dist_reform_inner_hard", False) + '}}',
      enable_class_dist_adp = '{{' + ab("enable_class_dist_adp_inner_hard", False) + '}}',
      prerank_class_dist_reform_param = '{{' + ab("prerank_class_dist_reform_param_inner_hard", "1.0,1.0,1.0,1.0,1.0,1.0,1.0") + '}}',
      enable_hybrid_ltr_path = '{{' + ab("enable_hybrid_ltr_path_inner_hard", False) + '}}',
      enable_hybrid_ltr_bid = '{{' + ab("enable_hybrid_ltr_bid_inner_hard", False) + '}}',
      enable_inner_cold_start_adjust = '{{' + ab("enable_inner_cold_start_adjust", False) + '}}',
      enable_inner_cold_start_topk_adjust = '{{' + ab("enable_inner_cold_start_topk_adjust", False) + '}}',
      coldstart_topk_exp_tag_prerank = '{{' + ab("coldstart_topk_exp_tag_prerank", "") + '}}',
      hybrid_ltr_mul_params = '{{' + ab("hybrid_ltr_mul_params_inner_hard", "1.0,1.0,1.0") + '}}',
      cid_roas_prerank_account_id_boost_ratio = '{{' + ab("cid_roas_prerank_account_id_boost_ratio", 1.0) + '}}',
      cid_paid_prerank_account_id_boost_ratio = '{{' + ab("cid_paid_prerank_account_id_boost_ratio", 1.0) + '}}',
      enable_ensemble_only_model_score = '{{' + ab("enable_ensemble_only_model_score", False) + '}}',
      prerank_trigger_item_relative_idx_boost_threshold = '{{' + ab("prerank_trigger_item_relative_idx_boost_threshold", 0) + '}}',
      prerank_inner_explore_relative_boost_ltr_idx = '{{' + ab("prerank_inner_explore_relative_boost_ltr_idx", 0) + '}}',
      enable_prerank_ensemble_score_presonal_cem = '{{' + ab("enable_prerank_ensemble_score_presonal_cem_inner_hard", False) + '}}',
      enable_prerank_ensemble_score_presonal_cem_p2l = '{{' + ab("enable_prerank_ensemble_score_presonal_cem_p2l_inner_hard", False) + '}}',
    ) \
    .end_if_() \
    .if_("%s %s"%(ab("enable_write_inner_hard_ad_info_to_redis", False), "== 1")) \
    .write_info_to_redis_enricher(
      input_table_name = "inner_photo_hard_item_table",
      info_to_redis_cluster_name = '{{' + ab("inner_hard_ad_info_to_redis_cluster_name", "recoAdNativeCoopStrategy") + '}}',
      info_to_redis_ttl = '{{' + ab("inner_hard_ad_info_to_redis_ttl", 259200) + '}}',
      info_to_redis_max_photo_id_num = '{{' + ab("inner_hard_ad_info_to_redis_max_photo_id_num", 500) + '}}',
      info_to_redis_key_prefix = '{{' + ab("inner_hard_ad_info_to_redis_key_prefix", "a4rin_hard_") + '}}',
    ) \
    .end_if_() \

    return self

  def amd_layer_manager(self):
    self.if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_inner == 0 or enable_prerank_rank_ica == 0") \
    .if_("is_rewarded == 0") \
    .dcaf(
      enable_explore = "enable_rank_quota_explore_inner_hard_photo",
      version_id = '{{' + ab("dcaf_rank_version_inner_hard_photo", "") + '}}',
      dcaf_degrade_ratio = '{{' + ab("dcaf_degrade_ratio_inner", 1.0) + '}}',
      smooth_function = '{{' + ab("dcaf_smooth_func_inner_hard_photo", "") + '}}',
      disable_dcaf_with_constant = '{{' + ab("disable_dcaf_with_constant", False) + '}}',
      enable_rank_rt_exp_quota = '{{' + ab("enable_rank_rt_exp_quota", False) + '}}',
      enable_biz_name= '{{' + ab("enable_dcaf_rank_inner_hard_photo", False) + '}}',
      biz_name = "inner_hard_photo",
      fake_type = "fake_type",
    ) \
    .else_() \
    .dcaf(
      enable_explore = "enable_rank_quota_explore_inner_hard_photo_rewarded",
      version_id = '{{' + ab("dcaf_rank_version_inner_hard_photo_rewarded", "") + '}}',
      dcaf_degrade_ratio = '{{' + ab("dcaf_degrade_ratio_inner", 1.0) + '}}',
      smooth_function = '{{' + ab("dcaf_smooth_func_inner_hard_photo_rewarded", "") + '}}',
      disable_dcaf_with_constant = '{{' + ab("disable_dcaf_with_constant", False) + '}}',
      enable_rank_rt_exp_quota = '{{' + ab("enable_rank_rt_exp_quota", False) + '}}',
      enable_biz_name= '{{' + ab("enable_dcaf_rank_inner_hard_photo_rewarded", False) + '}}',
      biz_name = "inner_hard_photo_rewarded",
      fake_type = "fake_type",
    ) \
    .end_if_() \
    .end_if_() \
    .inner_hard_photo_layer_manager_enricher(
      ranking_quota = '{{' + ab("merchant_prerank_max_quota", 0) + '}}',
      shelf_merchant_ranking_quota = '{{' + ab("shelf_merchant_ranking_quota", 70) + '}}',
      shelf_append_ab_tag = '{{' + ab("shelf_append_ab_tag", "base") + '}}',
      enable_shelf_append_ab_tag = '{{' + ab("enable_shelf_append_ab_tag", False) + '}}',
      enable_shelf_skip_quota_new = '{{' + ab("enable_shelf_skip_quota_new", False) + '}}',
      enable_shelf_merchant_ranking_quota = '{{' + ab("enable_shelf_merchant_ranking_quota", False) + '}}',
      enable_explore = "enable_rank_quota_explore_inner_hard_photo",
      enable_rewarded_explore = "enable_rank_quota_explore_inner_hard_photo_rewarded",
      append_ab_tag = '{{' + ab("prerank_append_strategy_amd", "base") + '}}',
      InspireMerchant_prerank_max_quota = '{{' + ab("InspireMerchant_prerank_max_quota", 120) + '}}',
      inner_hard_main_upper = '{{' + ab("inner_hard_main_upper", 300) + '}}',
      enable_biz_name = '{{' + ab("enable_dcaf_rank_inner_hard_photo", False) + '}}',
      enable_rewarded_dcaf = '{{' + ab("enable_dcaf_rank_inner_hard_photo_rewarded", False) + '}}',
      enable_quota_value_dynamic = '{{' + ab("enable_quota_value_dynamic", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_ranking_quota_pacing = '{{' + ab("enable_ranking_quota_pacing", False) + '}}',
      drop_down_invert_ab = '{{' + ab("drop_down_invert_ab", False) + '}}',
      enable_not_drop_down = '{{' + ab("enable_not_drop_down", False) + '}}',
      enable_expand_quota_degrade = '{{' + ab("enable_expand_quota_degrade", False) + '}}',
      enable_inner_hard_photo_expand_ratio = '{{' + ab("enable_inner_hard_photo_expand_ratio", False) + '}}',
      enable_modify_expand_quota_u_level = '{{' + ab("enable_modify_expand_quota_u_level", False) + '}}',
      enable_dcaf_ratio_first = '{{' + ab("enable_dcaf_ratio_first", False) + '}}',
      enable_user_group_quota_strategy = '{{' + ab("enable_user_group_quota_strategy", False) + '}}',
      enable_user_quota_for_outer = '{{' + ab("enable_user_quota_for_outer", False) + '}}',
      enable_unify_ih_main_strategy = '{{' + ab("enable_unify_ih_main_strategy", False) + '}}',
      user_quota_tag = '{{' + ab("user_quota_tag", "") + '}}',
      drop_down_invert_ratio = '{{' + ab("drop_down_invert_ratio", 1.0) + '}}',
      is_inspire_merchant = "is_merchant_inspire",
      app_id = "app_id",
      user_gmv = "user_gmv",
      biz_name = "inner_hard_photo",
      layer_table_name = "inner_hard_photo_layer_table",
      quota_ratio = '{{' + ab("quota_ratio_inner_hard", 1.0) + '}}',
      rank_quota_exp_name = '{{' + ab("rank_quota_exp_name_inner_hard", "") + '}}',
      quota_limit_ratio = '{{' + ab("quota_limit_ratio_inner_hard", 1.0) + '}}',
      enable_quota_limit = '{{' + ab("enable_quota_limit_inner_hard", False) + '}}',
      enable_quota_limit_new = '{{' + ab("enable_quota_limit_new_inner_hard", False) + '}}',
      enable_quota_limit_by_hour = '{{' + ab("enable_quota_limit_by_hour", False) + '}}',
      quota_limit_ratio_exp_tag = '{{' + ab("quota_limit_ratio_exp_tag", "") + '}}',
      inner_hard_base_quota = '{{' + ab("inner_hard_base_quota", 120) + '}}',
      enable_base_expand = '{{' + ab("enable_base_expand", False) + '}}',
      enable_expand_ratio_cliff = '{{' + ab("enable_expand_ratio_cliff_inner_hard", False) + '}}',
      inner_hard_photo_expand_cliff_ratio = '{{' + ab("inner_hard_photo_expand_cliff_ratio", 1.0) + '}}',
      enable_expand_ratio_cliff_page = '{{' + ab("enable_expand_ratio_cliff_page_inner_hard", False) + '}}',
      expand_ratio_cliff_page_str = '{{' + ab("expand_ratio_cliff_page_str_inner_hard", "") + '}}',
      enable_prerank_rank_ica_inner = '{{' + ab("enable_prerank_rank_ica_inner", False) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      inner_buyer_exp_name = '{{' + ab("inner_buyer_exp_name", "") + '}}',
      enable_inner_buyer_rank_quota = '{{' + ab("enable_inner_buyer_rank_quota", False) + '}}',
      enable_prerank_local_life_dui_tou_quota_limit = '{{' + ab("enable_prerank_local_life_dui_tou_quota_limit", False) + '}}',
      max_quota_exp_name = '{{' + ab("max_quota_exp_name", "") + '}}',
      enable_inner_buyer_rank_max_quota = '{{' + ab("enable_inner_buyer_rank_max_quota", False) + '}}',
      none_one_model_quota_drop_down_ratio = '{{' + ab("none_one_model_quota_drop_down_ratio_inner_hard", 1.0) + '}}'
    ) \
    .if_("%s %s %s"%("is_shelf_merchant_traffic == 1 and", ab("enable_shelf_effect_enricher", False), "== 1")) \
    .inner_shelf_layer_effect_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_shelf_hard_diversity = '{{' + ab("enable_shelf_hard_diversity", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_shelf_soft_diversity = '{{' + ab("enable_shelf_soft_diversity", False) + '}}',
      enable_shelf_prerank_p2l_quota = '{{' + ab("enable_shelf_prerank_p2l_quota", False) + '}}',
      enable_shelf_prerank_diversity = '{{' + ab("enable_shelf_prerank_diversity", False) + '}}',
      enable_shelf_prerank_diversity_v1 = '{{' + ab("enable_shelf_prerank_diversity_v1", False) + '}}',
      shelf_prerank_effect_exp_tag = '{{' + ab("shelf_prerank_effect_exp_tag", "") + '}}',
      shelf_itemid_thresh = '{{' + ab("shelf_itemid_thresh", 3) + '}}',
      shelf_itemid_photoid_thresh = '{{' + ab("shelf_itemid_photoid_thresh", 3) + '}}',
      shelf_itemid_materail_thresh = '{{' + ab("shelf_itemid_materail_thresh", 3) + '}}',
      shelf_effect_select_all_quota = '{{' + ab("shelf_effect_select_all_quota", 150) + '}}',
      shelf_effect_select_hard_quota = '{{' + ab("shelf_effect_select_hard_quota", 75) + '}}',
      shelf_effect_select_soft_quota = '{{' + ab("shelf_effect_select_soft_quota", 75) + '}}',
      shelf_effect_select_p2l_quota = '{{' + ab("shelf_effect_select_p2l_quota", 20) + '}}',
      shelf_admit_unit_thresh = '{{' + ab("shelf_admit_unit_thresh", 3) + '}}',
      shelf_admit_account_thresh = '{{' + ab("shelf_admit_account_thresh", 5) + '}}',
    ) \
    .else_() \
    .inner_hard_photo_layer_effect_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      is_inspire_live = "is_inspire_live",
      enable_prerank_photo_mobile_quota_split = '{{' + ab("enable_prerank_photo_mobile_quota_split", False) + '}}',
      enable_prerank_photo_mobile_quota_append = '{{' + ab("enable_prerank_photo_mobile_quota_append", False) + '}}',
      enable_unify_photo_quota = '{{' + ab("enable_unify_photo_quota", False) + '}}',
      enable_unify_p2l_quota = '{{' + ab("enable_unify_p2l_quota", False) + '}}',
      prerank_p2l_all_quota = '{{' + ab("prerank_p2l_all_quota", 25) + '}}',
      enable_priority_recall = '{{' + ab("enable_priority_recall", False) + '}}',
      priority_recall_type = '{{' + ab("priority_recall_type", -1) + '}}',
      priority_recall_num = '{{' + ab("priority_recall_num", 0) + '}}',
      hard_photo_target_photo_mobile_quota = '{{' + ab("hard_photo_target_photo_mobile_quota", 0) + '}}',
      hard_photo_target_p2l_mobile_independent_quota = '{{' + ab("hard_photo_target_p2l_mobile_independent_quota", 0) + '}}',
      hard_photo_target_p2l_independent_quota = '{{' + ab("hard_photo_target_p2l_independent_quota", 0) + '}}',
      hard_p2l_non_roas_quota_ratio = '{{' + ab("hard_p2l_non_roas_quota_ratio", 1.0) + '}}',
      hard_photo_target_non_live_p2l_inspire_live_quota = '{{' + ab("hard_photo_target_non_live_p2l_inspire_live_quota", 0) + '}}',
      merchant_prerank_admit_unit_thresh = '{{' + ab("merchant_prerank_admit_unit_thresh", 0) + '}}',
      merchant_prerank_admit_unit_thresh_new = '{{' + ab("merchant_prerank_admit_unit_thresh_new", 0) + '}}',
      merchant_prerank_admit_unit_thresh_p2l = '{{' + ab("merchant_prerank_admit_unit_thresh_p2l", 0) + '}}',
      merchant_prerank_admit_cate3_thresh = '{{' + ab("merchant_prerank_admit_cate3_thresh", 0) + '}}',
      merchant_prerank_admit_live_thresh = '{{' + ab("merchant_prerank_admit_live_thresh", 0) + '}}',
      merchant_prerank_admit_account_thresh = '{{' + ab("merchant_prerank_admit_account_thresh", 0) + '}}',
      merchant_prerank_admit_account_thresh_new = '{{' + ab("merchant_prerank_admit_account_thresh_new", 0) + '}}',
      merchant_prerank_admit_account_thresh_p2l = '{{' + ab("merchant_prerank_admit_account_thresh_p2l", 0) + '}}',
      merchant_prerank_admit_photo_ocpx_thresh = '{{' + ab("merchant_prerank_admit_photo_ocpx_thresh", 0) + '}}',
      merchant_prerank_admit_photo_ocpx_thresh_new = '{{' + ab("merchant_prerank_admit_photo_ocpx_thresh_new", 0) + '}}',
      merchant_prerank_admit_photo_ocpx_thresh_p2l = '{{' + ab("merchant_prerank_admit_photo_ocpx_thresh_p2l", 0) + '}}',
      enable_hard_diversity_photo_p2l_split = '{{' + ab("enable_hard_diversity_photo_p2l_split", False) + '}}',
      storewide_prerank_photo_ocpx_diversity_inc = '{{' + ab("storewide_prerank_photo_ocpx_diversity_inc", 0) + '}}',
      enable_hard_photo_prerank_div_refactor = '{{' + ab("enable_hard_photo_prerank_div_refactor", False) + '}}',
      enable_hard_photo_prerank_div_skip_refactor = '{{' + ab("enable_hard_photo_prerank_div_skip_refactor", False) + '}}',
      enable_hard_photo_prerank_spu_div_refactor = '{{' + ab("enable_hard_photo_prerank_spu_div_refactor", False) + '}}',
      enable_adjust_quota_by_prerank_score = '{{' + ab("enable_adjust_quota_by_prerank_score", False) + '}}',
      enable_interest_adjust = '{{' + ab("enable_interest_adjust", False) + '}}',
      high_interest_adjust_ratio = '{{' + ab("high_interest_adjust_ratio", 1.0) + '}}',
      low_interest_adjust_ratio = '{{' + ab("low_interest_adjust_ratio", 1.0) + '}}',
      enable_use_ltr_score = '{{' + ab("enable_use_ltr_score", False) + '}}',
      enable_use_ctcvr_score = '{{' + ab("enable_use_ctcvr_score", False) + '}}',
      reward_p2l_quota_factor = '{{' + ab("reward_p2l_quota_factor", 1.0) + '}}',
      p2l_quota_factor = '{{' + ab("p2l_quota_factor", 1.0) + '}}',
      p2l_quota_upper_bound = '{{' + ab("p2l_quota_upper_bound", 200) + '}}',
      p2l_quota_lower_bound = '{{' + ab("p2l_quota_lower_bound", 1) + '}}',
      amd_p2l_quota_ratio = '{{' + ab("amd_p2l_quota_ratio", 0.3) + '}}',
      amd_reward_p2l_quota_ratio = '{{' + ab("amd_reward_p2l_quota_ratio", 0.3) + '}}',
      enable_amd_adjust_photo_p2l_ratio = '{{' + ab("enable_amd_adjust_photo_p2l_ratio", False) + '}}',
      enable_prerank_rank_ica_inner = '{{' + ab("enable_prerank_rank_ica_inner", False) + '}}',
      ica_rank_quota_inner_hard_p2l_ratio = '{{' + ab("ica_rank_quota_inner_hard_p2l_ratio", 0.3) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      amd_adjust_photo_p2l_exp_tag = '{{' + ab("amd_adjust_photo_p2l_exp_tag", "default") + '}}'
    ) \
    .end_if_() \
    .if_("%s %s"%(ab("enable_close_layer", False), "== 0")) \
    .layer_ecology_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_prerank_inner_adx_baosong = '{{' + ab("enable_prerank_inner_adx_baosong", False) + '}}',
      enable_prerank_buyer_shallow_baosong = '{{' + ab("enable_prerank_buyer_shallow_baosong", False) + '}}',
      enable_prerank_lsp_green_channel = '{{' + ab("enable_prerank_lsp_green_channel", False) + '}}',
      prerank_buyer_shallow_baosong_type = '{{' + ab("prerank_buyer_shallow_baosong_type", "U4+") + '}}',
      enable_prerank_new_account_support = '{{' + ab("enable_prerank_new_account_support", False) + '}}',
      enable_update_prerank_new_account_support = '{{' + ab("enable_update_prerank_new_account_support", False) + '}}',
      enable_update_mobile_support = '{{' + ab("enable_update_mobile_support", False) + '}}',
      disable_prerank_reward_mingtou_support = '{{' + ab("disable_prerank_reward_mingtou_support", False) + '}}',
      enable_prerank_original_photo_support = '{{' + ab("enable_prerank_original_photo_support", False) + '}}',
      enable_prerank_new_item_support = '{{' + ab("enable_prerank_new_item_support", False) + '}}',
      enable_new_spu_prerank = '{{' + ab("enable_new_spu_prerank", False) + '}}',
      new_item_recall_tag = '{{' + ab("new_item_recall_tag", 0) + '}}',
      enable_native_degree_tag_hard = '{{' + ab("enable_native_degree_tag_hard", False) + '}}',
      enable_all_coldstart_flink = '{{' + ab("enable_all_coldstart_flink", False) + '}}',
      enable_topk_photo_prerank = '{{' + ab("enable_topk_photo_prerank", False) + '}}',
      enable_inner_cold_start_adjust = '{{' + ab("enable_inner_cold_start_adjust", False) + '}}',
      enable_coldboot_score_explore = '{{' + ab("enable_coldboot_score_explore_2", False) + '}}',
      coldboot_score_explore_ratio = '{{' + ab("coldboot_score_explore_ratio_2", 1.0) + '}}',
      enable_follow_realtime_high_gmv_author_baosong = '{{' + ab("enable_follow_realtime_high_gmv_author_baosong", False) + '}}',
      follow_realtime_high_gmv_author_retrival_tag = '{{' + ab("follow_realtime_high_gmv_author_retrival_tag", 0) + '}}',
      enable_inner_aigc_photo_support = '{{' + ab("enable_inner_aigc_photo_support", False) + '}}',
      buyer_effective_type = "buyer_effective_type",
      follow_userid_list = "follow_userid_list",
      is_inner_rewarded_mingtou_ads = "is_inner_rewarded_mingtou_ads",
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      ad_strategy_tag = "ad_strategy_tag",
      is_new_model_creative = "is_new_model_creative",
      new_creative_tag_realtime = "new_creative_tag_realtime",
      is_original_photo = "is_original_photo",
      account_type = "account_type",
      is_esp = "is_esp",
      ocpx_action_type = "ocpx_action_type",
      author_id = "author_id",
      is_new_merchant = "is_new_merchant",
      is_smb_account = "is_smb_account",
      account_create_source = "account_create_source",
      new_spu_tag = "new_spu_tag",
      project_ocpx_action_type = "project_ocpx_action_type",
      creative_material_type = "creative_material_type",
      multi_retrieval_tag = "multi_retrieval_tag",
      real_seller_main_category_id = "real_seller_main_category_id",
      ast_30d_pay_order_amt = "ast_30d_pay_order_amt",
      strategy_crowd_info = "strategy_crowd_info",
      enable_prerank_innerloop_cid_ad_support = '{{' + ab("enable_prerank_innerloop_cid_ad_support", False) + '}}',
      enable_cid_support_skip_unlogin_user = '{{' + ab("enable_cid_support_skip_unlogin_user", False) + '}}',
      enable_inner_explore_sim_ltr_append = '{{' + ab("enable_inner_explore_sim_ltr_append", False) + '}}',
      inner_explore_sim_ltr_append_idx = '{{' + ab("inner_explore_sim_ltr_append_idx", 0) + '}}',
      inner_explore_sim_ltr_append_score = '{{' + ab("inner_explore_sim_ltr_append_score", 0.0) + '}}',
      enable_nearline_multi_recall = '{{' + ab("enable_nearline_multi_recall", False) + '}}',
    ) \
    .layer_guaranteed_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      force_impression_multi_tagid_list = '{{' + ab("force_impression_multi_tagid_list", "66,88") + '}}',
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_reservation_guaranteed_preranking = '{{' + ab("enable_reservation_guaranteed_preranking", False) + '}}',
      enable_mobile_hard_photo_guaranteed_preranking = '{{' + ab("enable_mobile_hard_photo_guaranteed_preranking", False) + '}}',
      mobile_hard_photo_guaranteed_preranking_tag = '{{' + ab("mobile_hard_photo_guaranteed_preranking_tag", 200) + '}}',
      enable_mobile_hard_p2l_guaranteed_preranking = '{{' + ab("enable_mobile_hard_p2l_guaranteed_preranking", False) + '}}',
      mobile_hard_p2l_guaranteed_preranking_tag = '{{' + ab("mobile_hard_p2l_guaranteed_preranking_tag", 42) + '}}',
      ensemble_score = "ensemble_score",
      enable_search_retarget_guaranteed_preranking = '{{' + ab("enable_search_retarget_guaranteed_preranking", False) + '}}',
      enable_inner_prerank_search_append = '{{' + ab("enable_inner_prerank_search_append_new", False) + '}}',
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      account_type = "account_type",
      author_id = "author_id",
      campaign_type = "campaign_type",
      ocpx_action_type = "ocpx_action_type",
      multi_retrieval_tag = "multi_retrieval_tag",
    ) \
    .layer_nomal_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      merchant_prerank_tdm_tag = '{{' + ab("merchant_prerank_tdm_tag", 0) + '}}',
      pr_recall_tag = '{{' + ab("pr_recall_tag", 0) + '}}',
      enable_whole_roi_append = '{{' + ab("enable_whole_roi_append_dragon", False) + '}}',
      enable_whole_roi_append_qhc = '{{' + ab("enable_whole_roi_append_qhc", False) + '}}',
      enable_qcpx_live_prerank = '{{' + ab("enable_qcpx_live_prerank", False) + '}}',
      enable_qcpx_photo_prerank = '{{' + ab("enable_qcpx_photo_prerank", False) + '}}',
      enable_prerank_lsp_append = '{{' + ab("enable_prerank_lsp_append", False) + '}}',
      enable_prerank_t7_roi_append = '{{' + ab("enable_prerank_t7_roi_append", False) + '}}',
      enable_prerank_cost_drop_photo_append = '{{' + ab("enable_prerank_cost_drop_photo_append", False) + '}}',
      enable_item_card_append = '{{' + ab("enable_item_card_append", False) + '}}',
      enable_item_card_append_for_buyer_home_page = '{{' + ab("enable_item_card_append_for_buyer_home_page", False) + '}}',
      enable_item_card_append_for_mall = '{{' + ab("enable_item_card_append_for_mall", False) + '}}',
      enable_cbo_append = '{{' + ab("enable_cbo_append", False) + '}}',
      merchant_prerank_recall_channel_tag = '{{' + ab("merchant_prerank_recall_channel_tag", 0) + '}}',
      enable_inner_trigger_item_append_wanhe = '{{' + ab("enable_inner_trigger_item_append_wanhe", False) + '}}',
      enable_inner_trigger_item_path_append = '{{' + ab("enable_inner_trigger_item_path_append", False) + '}}',
      enable_inner_trigger_item_tag_append = '{{' + ab("enable_inner_trigger_item_tag_append", False) + '}}',
      inner_trigger_item_strategy_tag = '{{' + ab("inner_trigger_item_strategy_tag", "") + '}}',
      inner_trigger_item_strategy_tag_thr = '{{' + ab("inner_trigger_item_strategy_tag_thr", 1.0) + '}}',
      enable_adx_recall_path_append = '{{' + ab("enable_adx_recall_path_append", False) + '}}',
      enable_item_card_speed_test = '{{' + ab("enable_item_card_speed_test", False) + '}}',
      enable_fix_item_card_speed_test = '{{' + ab("enable_fix_item_card_speed_test", False) + '}}',
      enable_shelf_speed_test_append_up = '{{' + ab("enable_shelf_speed_test_append_up", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_new_cus_support = '{{' + ab("enable_new_cus_support", False) + '}}',
      enable_filter_jili_page = '{{' + ab("enable_filter_jili_page", False) + '}}',
      enable_new_cus_support_v2 = '{{' + ab("enable_new_cus_support_v2", False) + '}}',
      enable_smb_coldstart_support = '{{' + ab("enable_smb_coldstart_support", False) + '}}',
      enable_smb_coldstart_support_two = '{{' + ab("enable_smb_coldstart_support_two", False) + '}}',
      enable_shelf_p2l_support = '{{' + ab("enable_shelf_p2l_support", False) + '}}',
      enable_c0c1_support = '{{' + ab("enable_c0c1_support", False) + '}}',
      enable_inner_explore_relative_recall_tag_support = '{{' + ab("enable_inner_explore_relative_recall_tag_support", False) + '}}',
      enable_inner_explore_relative_score_support = '{{' + ab("enable_inner_explore_relative_score_support", False) + '}}',
      relative_score_support_threshold = '{{'+ ab("relative_score_support_threshold", 1.0) + '}}',
      ensemble_score = "ensemble_score",
      creative_material_type = "creative_material_type",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      multi_retrieval_tag = "multi_retrieval_tag",
      create_source_type = "create_source_type",
    ) \
    .layer_nondelivery_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_nondelivery", False) + '}}',
      enable_ensemble_score_model = '{{' + ab("enable_ensemble_score_model", False) + '}}',
      enable_ecom_click_action_retarget = '{{' + ab("enable_ecom_click_action_retarget", False) + '}}',
      enable_ecom_follow_action_retarget = '{{' + ab("enable_ecom_follow_action_retarget", False) + '}}',
      enable_ecom_purchase_action_retarget = '{{' + ab("enable_ecom_purchase_action_retarget", False) + '}}',
      enable_ecom_search_action_retarget = '{{' + ab("enable_ecom_search_action_retarget", False) + '}}',
      enable_ecom_comment_action_retarget = '{{' + ab("enable_ecom_comment_action_retarget", False) + '}}',
      enable_ecom_zc_interact_author_retarget = '{{' + ab("enable_ecom_zc_interact_author_retarget", False) + '}}',
      enable_ecom_zc_long_play_action_retarget = '{{' + ab("enable_ecom_zc_long_play_action_retarget", False) + '}}',
      enable_ecom_reco_action_author_retarget = '{{' + ab("enable_ecom_reco_action_author_retarget", False) + '}}',
      enable_inner_sid_support = '{{' + ab("enable_inner_sid_support", False) + '}}',
      enable_inner_t7_roi_support = '{{' + ab("enable_inner_t7_roi_support", False) + '}}',
      enable_fix_click_action_sign = '{{' + ab("enable_fix_click_action_sign", False) + '}}',
      enable_fix_action_cate3_sign = '{{' + ab("enable_fix_action_cate3_sign", False) + '}}',
      enable_high_grade_recall_path_support = '{{' + ab("enable_high_grade_recall_path_support", False) + '}}',
      enable_ecom_action_cate3_retarget = '{{' + ab("enable_ecom_action_cate3_retarget", False) + '}}',
      enable_limit_cate3_nums = '{{' + ab("enable_limit_cate3_nums", False) + '}}',
      cate3_length_threshold = '{{' + ab("cate3_length_threshold", 0) + '}}',
      cate3_time_threshold = '{{' + ab("cate3_time_threshold", 0) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      short_action_up_weight = '{{' + ab("short_action_up_weight", 1.0) + '}}',
      enable_ecom_interest = '{{' + ab("enable_ecom_interest", False) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}',
      merchant_prerank_interest_tag = '{{' + ab("merchant_prerank_interest_tag", "") + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      multi_retrieval_tag = "multi_retrieval_tag",
      enable_adx_retarget_support = '{{' + ab("enable_adx_retarget_support", False) + '}}',
      adx_retarget_path = '{{' + ab("adx_retarget_path", "") + '}}',
    ) \
    .layer_operational_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_live_prerank_white_baosong = '{{' + ab("enable_live_prerank_white_baosong", False) + '}}',
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_inner_aigc_photo_support = '{{' + ab("enable_inner_aigc_photo_support", False) + '}}',
    ) \
    .layer_outer_aigc_support_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_inner_aigc_photo_guarentee_append = '{{' + ab("enable_inner_aigc_photo_guarentee_append", False) + '}}',
      enalbe_inner_aigc_photo_guarentee_append_v2 = '{{' + ab("enalbe_inner_aigc_photo_guarentee_append_v2", False) + '}}',
    ) \
    .layer_special_support_f1_enricher(
      input_table_name = "inner_photo_hard_item_table",
      input_layer_table_name = "inner_hard_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_inner_ceiling_preranking = '{{' + ab("enable_inner_ceiling_preranking", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
    ) \
    .end_if_() \
    .layer_supplement_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_prerank_layer_supplement = '{{' + ab("enable_prerank_layer_supplement_cache_amd", False) + '}}',
      enable_prerank_layer_supplement_v2 = '{{' + ab("enable_prerank_layer_supplement_inner_photo_hard", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
    ) \
    .if_("%s %s"%(ab("enable_retarget_enricher_up", False), "== 0")) \
    .prerank_retarget_tag_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_fix_action_cate3_tag = '{{' + ab("enable_fix_action_cate3_tag", False) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      shorttime_action_gap = '{{' + ab("shorttime_action_gap", 0) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}'
    ) \
    .end_if_() \
    .layer_storewide_item_upper_enricher(
      input_table_name = "inner_photo_hard_item_table",
      enable_layer_product_storewide = '{{' + ab("enable_layer_product_storewide", False) + '}}'
    )
    
    return self

  # fanstop begin
  @module()
  def inner_photo_soft_sort(self):
    current_flow() \
    .if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_inner == 0 or enable_prerank_rank_ica == 0") \
    .dcaf_redis(
      enable_biz_name=True,
      biz_name="inner_soft_photo",
      sub_page_id="sub_page_id",
      version_id = '{{' + ab("dcaf_rank_version_inner_soft_photo", "") + '}}',
    ) \
    .end_if_() \
    .inner_soft_photo_bonus_enricher(
      item_table = "inner_photo_soft_item_table",
      input_table_name = "inner_photo_soft_item_table",
      native_ad_support_project_exp_id = '{{' + ab("native_ad_support_project_exp_id", 0) + '}}',
      account_id = "account_id",
      payer_id = "payer_id",
      author_id = "author_id",
    ) \
    .if_("is_prerank == 0") \
      .inner_soft_photo_shuffle_enricher(
        input_table_name = "inner_photo_soft_item_table",
      ) \
      .inner_soft_photo_item_dedup_enricher(
        input_table_name = "inner_photo_soft_item_table",
        enable_ocpx_dup_filter_recall = '{{' + ab("enable_ocpx_dup_filter_recall", False) + '}}',
        disable_ocpx_dup_filter_photo = '{{' + ab("disable_ocpx_dup_filter_photo", False) + '}}',
        enable_nobid_item_ocpx_drop_fix = '{{' + ab("enable_nobid_item_ocpx_drop_fix", False) + '}}',
        nobid_item_ocpx_quota = '{{' + ab("nobid_item_ocpx_quota", 1) + '}}',
      ) \
    .else_() \
      .if_("is_prerank == 1 and is_prerank_ps_succ == 1") \
        .inner_soft_photo_ecpm_enricher(
          input_table_name = "inner_photo_soft_item_table",
          max_prerank_soft_face_spu_match_baosong_num = '{{' + ab("max_prerank_soft_face_spu_match_baosong_num", 20) + '}}',
          followee_retrieval_max_num = '{{' + ab("followee_retrieval_max_num", 10) + '}}',
          xinghe_retrieval_max_num = '{{' + ab("xinghe_retrieval_max_num", 0) + '}}',
          soft_esp_pc_ctcvr_coef = '{{' + ab("soft_esp_pc_ctcvr_coef", 0.0) + '}}',
          enable_soft_prerank_use_dup_filter = '{{' + ab("enable_soft_prerank_use_dup_filter", False) + '}}',
          fix_soft_auto_roas = '{{' + ab("fix_soft_auto_roas", False) + '}}',
          enabel_exclude_soft_photo_random = '{{' + ab("enabel_exclude_soft_photo_random", False) + '}}',
          enable_exlude_soft_photo_bonus_rate = '{{' + ab("enable_exlude_soft_photo_bonus_rate", False) + '}}',
          enable_soft_photo_e2e_splid_bid = '{{' + ab("enable_soft_photo_e2e_splid_bid", False) + '}}',
          enable_soft_photo_e2e_splid_bid_v2 = '{{' + ab("enable_soft_photo_e2e_splid_bid_v2", False) + '}}',
          enable_soft_photo_e2e_splid_bid_v3 = '{{' + ab("enable_soft_photo_e2e_splid_bid_v3", False) + '}}',
          enable_soft_photo_e2e_splid_bid_v4 = '{{' + ab("enable_soft_photo_e2e_splid_bid_v4", False) + '}}',
          enable_soft_photo_e2e_splid_bid_v5 = '{{' + ab("enable_soft_photo_e2e_splid_bid_v5", False) + '}}',
          enable_soft_photo_t7_roi_cal_bid_new = '{{' + ab("enable_soft_photo_t7_roi_cal_bid_new", False) + '}}',
          enable_soft_photo_ctcvr_bid_refactor = '{{' + ab("enable_soft_photo_ctcvr_bid_refactor", False) + '}}',
          enable_soft_photo_ctcvr_bid_v3_refactor = '{{' + ab("enable_soft_photo_ctcvr_bid_v3_refactor", False) + '}}',
          enable_soft_photo_ctcvr_bid_v4_refactor = '{{' + ab("enable_soft_photo_ctcvr_bid_v4_refactor", False) + '}}',
          enable_soft_photo_ctcvr_bid_v5_refactor = '{{' + ab("enable_soft_photo_ctcvr_bid_v5_refactor", False) + '}}',
          fanstop_photo_sort_bid_separate_weight = '{{' + ab("fanstop_photo_sort_bid_separate_weight", 1.0) + '}}',
          fanstop_photo_billing_ratio = '{{' + ab("fanstop_photo_billing_ratio", 1.0) + '}}',
          enable_soft_photo_cal_sort_separate_bid = '{{' + ab("enable_soft_photo_cal_sort_separate_bid", False) + '}}',
          soft_prerank_sep_bid_weight = '{{' + ab("soft_prerank_sep_bid_weight", -1.0) + '}}',
          enable_soft_photo_prerank_bid_no10 = '{{' + ab("enable_soft_photo_prerank_bid_no10", False) + '}}',
          disable_soft_photo_prerank_t7roi_conv_ratio = '{{' + ab("disable_soft_photo_prerank_t7roi_conv_ratio", False) + '}}',
          default_soft_mobile_roi = '{{' + ab("default_soft_mobile_roi", 0.5) + '}}',
          enable_prerank_bs_inner = '{{' + ab("enable_prerank_bs_inner", False) + '}}',
          enable_prerank_bs_inner_roi = '{{' + ab("enable_prerank_bs_inner_roi", False) + '}}',
          prerank_bs_bid_ratio_inner = '{{' + ab("prerank_bs_bid_ratio_inner", 1.0) + '}}',
          prerank_bs_bid_ratio_inner_roi = '{{' + ab("prerank_bs_bid_ratio_inner_roi", 1.0) + '}}',
          enable_fanstopv2_autobidli_exp_prerank = '{{' + ab("enable_fanstopv2_autobidli_exp_prerank", False) + '}}',
          enable_fanstop_autobidli_exp_prerank_fix = '{{' + ab("enable_fanstop_autobidli_exp_prerank_fix", False) + '}}',
          enable_fanstop_autobidli_exp_all_prerank_photo = '{{' + ab("enable_fanstop_autobidli_exp_all_prerank_photo", False) + '}}',
          pos = "pos",
          fans_top_type = "fans_top_type",
          auto_roas = "bid_auto_roas",
          scene_oriented_type = "scene_oriented_type",
          enable_gpm_prerank = '{{' + ab("enable_gpm_prerank_inner_soft", False) + '}}',
          enable_gpm_prerank_only_thanos = '{{' + ab("enable_gpm_prerank_only_thanos_inner_soft", False) + '}}',
          enable_gpm_roas = '{{' + ab("enable_gpm_roas_inner_soft", False) + '}}',
          prerank_gpm_roas_scale = '{{' + ab("prerank_gpm_roas_scale_inner_soft", 10000.0) + '}}',
          enable_gpm_ecpm_weight_split = '{{' + ab("enable_gpm_ecpm_weight_split_inner_soft", False) + '}}',
          prerank_gpm_ecpm_weight_p2l = '{{' + ab("prerank_gpm_ecpm_weight_inner_soft_p2l", 0.0) + '}}',
          prerank_gpm_ecpm_weight_photo = '{{' + ab("prerank_gpm_ecpm_weight_inner_soft_photo", 0.0) + '}}',
        ) \
        .inner_soft_photo_item_dedup_enricher(
          input_table_name = "inner_photo_soft_item_table",
          enable_ocpx_dup_filter_recall = '{{' + ab("enable_ocpx_dup_filter_recall", False) + '}}',
          disable_ocpx_dup_filter_photo = '{{' + ab("disable_ocpx_dup_filter_photo", False) + '}}',
          enable_nobid_item_ocpx_drop_fix = '{{' + ab("enable_nobid_item_ocpx_drop_fix", False) + '}}',
          nobid_item_ocpx_quota = '{{' + ab("nobid_item_ocpx_quota", 1) + '}}',
          storewide_dup_quota = '{{' + ab("storewide_dup_quota", 0) + '}}',
        ) \
        .if_("enable_prerank_skip_superfluous_processor_inner_soft == 0 or (page_id ~= 10011 and page_id ~= 11001)") \
        .trigger_item_relative_enricher(
          input_table_name = "inner_photo_soft_item_table",
          enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
          enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank_inner_soft", False) + '}}',
          enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
          enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
          enable_trigger_relative_sort_with_bid = '{{' + ab("enable_trigger_relative_sort_with_bid", False) + '}}',
          enable_ltr_relative_score_ensemble = '{{' + ab("enable_ltr_relative_score_ensemble", False) + '}}',
          enable_ltr_score_sigmoid = '{{' + ab("enable_ltr_score_sigmoid", False) + '}}',
          relative_score_weight = '{{' + ab("relative_score_weight_inner_soft", 0.0) + '}}',
          ltr_score_weight = '{{' + ab("ltr_score_weight_inner_soft", 0.0) + '}}',
          ltr_score_scale = '{{' + ab("ltr_score_scale_inner_soft", 1.0) + '}}',
          prerank_relative_ensemble_exp_tag = '{{' + ab("prerank_relative_ensemble_exp_tag", "") + '}}',
          enable_relative_ltr_inner_soft_fix = '{{' + ab("enable_relative_ltr_inner_soft_fix", False) + '}}',
          enable_relative_score_mul_bid = '{{' + ab("enable_relative_score_mul_bid", False) + '}}',
          enable_relative_score_sigmoid = '{{' + ab("enable_relative_score_sigmoid", False) + '}}',
          relative_score_scale = '{{' + ab("relative_score_scale_inner_soft", 1.0) + '}}',
          enable_inner_explore_ltr_sim_score_calc = '{{' + ab("enable_inner_explore_ltr_sim_score_calc", False) + '}}',
        ) \
        .end_if_() \
        .inner_soft_photo_es_lr_enricher(
          input_table_name = "inner_photo_soft_item_table",
          enable_prerank_es_lr = '{{' + ab("enable_prerank_es_lr_inner_soft", False) + '}}',
          es_lr_model_exp_name = '{{' + ab("es_lr_model_exp_name_inner_soft", "") + '}}',
        ) \
        .if_("%s %s"%(ab("enable_retarget_enricher_up", False), "== 1")) \
        .prerank_retarget_tag_enricher(
          input_table_name = "inner_photo_soft_item_table",
          enable_fix_action_cate3_tag = '{{' + ab("enable_fix_action_cate3_tag", False) + '}}',
          longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
          shorttime_action_gap = '{{' + ab("shorttime_action_gap", 0) + '}}',
          enable_sign_ecpm_idx = '{{' + ab("enable_sign_ecpm_idx", False) + '}}',
          enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
          enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
          enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
          enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}',
          enable_sign_mix_photo_idx = '{{' + ab("enable_sign_mix_photo_idx", False) + '}}',
          enable_sign_mix_author_idx = '{{' + ab("enable_sign_mix_author_idx", False) + '}}',
          enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
        ) \
        .end_if_() \
        .inner_soft_photo_sort_enricher(
          input_table_name = "inner_photo_soft_item_table",
          enable_product_interest_up = '{{' + ab("enable_product_interest_up", False) + '}}',
          product_interest_up_ratio = '{{' + ab("product_interest_up_ratio", 1.0) + '}}',
          product_interest_tag = '{{' + ab("product_interest_tag", 0) + '}}',
          prerank_e2e_ensemble_weight = '{{' + ab("soft_prerank_e2e_ensemble_weight", 1.0) + '}}',
          prerank_ecpm_ensemble_weight = '{{' + ab("soft_prerank_ecpm_ensemble_weight", 1.0) + '}}',
          soft_p2l_ensemble_factor = '{{' + ab("soft_p2l_ensemble_factor", 1.0) + '}}',
          enable_t7_roi_prerank_boost = '{{' + ab("enable_t7_roi_prerank_conv_ratio", False) + '}}',
          t7_roi_prerank_boost_ratio = '{{' + ab("t7_roi_prerank_boost_ratio", 1.0) + '}}',
          enable_replace_ensemble_index_default = '{{' + ab("enable_replace_ensemble_index_default_inner_soft", False) + '}}',
          enable_hyperbolic_fix_index = '{{' + ab("enable_hyperbolic_fix_index_inner_soft", False) + '}}',
          enable_hyperbolic_ensemble_score = '{{' + ab("enable_hyperbolic_ensemble_score_inner_soft", False) + '}}',
          enable_hyperbolic_ensemble_score_v2 = '{{' + ab("enable_hyperbolic_ensemble_score_v2_inner_soft", False) + '}}',
          enable_mean_multiplier = '{{' + ab("enable_mean_multiplier_inner_soft", False) + '}}',
          prerank_ensemble_score_exp_name = '{{' + ab("prerank_ensemble_score_exp_name_inner_soft", "") + '}}',
          prerank_ensemble_score_exp_name_p2l = '{{' + ab("prerank_ensemble_score_exp_name_p2l_inner_soft", "") + '}}',
          enable_use_old_es_score_for_value = '{{' + ab("enable_use_old_es_score_for_value_inner_soft", False) + '}}',
          enable_gpm_prerank = '{{' + ab("enable_gpm_prerank_inner_soft", False) + '}}',
          enable_prerank_es_lr = '{{' + ab("enable_prerank_es_lr_inner_soft", False) + '}}',
          enable_es_v2_quantile = '{{' + ab("enable_es_v2_quantile_inner_soft", False) + '}}',
          es_v2_quantile = '{{' + ab("es_v2_quantile_inner_soft", 0.5) + '}}',
          enable_ecpm_resort = '{{' + ab("enable_ecpm_resort_inner_soft", False) + '}}',
          prerank_gpm_ensemble_weight = '{{' + ab("prerank_gpm_ensemble_weight_inner_soft", 0.0) + '}}',
          enable_roas_p2l_prerank_boost = '{{' + ab("enable_roas_p2l_prerank_boost", False) + '}}',
          roas_p2l_prerank_boost_ratio = '{{' + ab("roas_p2l_prerank_boost_ratio_soft", 1.0) + '}}',
          unify_roas_p2l_prerank_boost_ratio = '{{' + ab("unify_roas_p2l_prerank_boost_ratio_soft", 1.0) + '}}',
          enable_esp_follow_adjust = '{{' + ab("enable_esp_follow_adjust", False) + '}}',
          esp_follow_buyer_adjust_tag = '{{' + ab("esp_follow_buyer_adjust_tag", "") + '}}',
          esp_follow_follow_num_adjust_tag = '{{' + ab("esp_follow_follow_num_adjust_tag", "") + '}}',
          enable_storewide_roas_p2l_prerank_boost = '{{' + ab("enable_storewide_roas_p2l_prerank_boost", False) + '}}',
          storewide_roas_p2l_prerank_boost_ratio = '{{' + ab("storewide_roas_p2l_prerank_boost_ratio", 1.0) + '}}',
          enable_fanstop_phone_call_boost = '{{' + ab("enable_fanstop_phone_call_boost_prerank", False) + '}}',
          enable_fanstop_mock_phone_call_prerank_boost = '{{' + ab("enable_fanstop_mock_phone_call_prerank_boost", False) + '}}',
          enable_fanstop_prms_ecpc = '{{' + ab("enable_fanstop_prms_user_ecpc_prerank", False) + '}}',
          fanstop_prms_ecpc_upperbound_msg = '{{' + ab("fanstop_prms_user_ecpc_upperbound_private_message_prerank", 1.0) + '}}',
          fanstop_prms_ecpc_lowerbound_msg = '{{' + ab("fanstop_prms_user_ecpc_lowerbound_private_message_prerank", 1.0) + '}}',
          fanstop_prms_ecpc_upperbound_leads = '{{' + ab("fanstop_prms_user_ecpc_upperbound_leads_submit_prerank", 1.0) + '}}',
          fanstop_prms_ecpc_lowerbound_leads = '{{' + ab("fanstop_prms_user_ecpc_lowerbound_leads_submit_prerank", 1.0) + '}}',
          fanstop_private_message_ab_pcxr = '{{' + ab("fanstop_private_message_ab_pcxr_prerank", 1.0) + '}}',
          fanstop_leads_submit_ab_pcxr = '{{' + ab("fanstop_leads_submit_ab_pcxr_prerank", 1.0) + '}}',
          ecpm_topk_protect_inner_photo_soft_cnt = '{{' + ab("ecpm_topk_protect_inner_photo_soft_cnt", 0) + '}}', # wuyibo03
          ecpm_topk_protect_inner_photo_soft_ratio = '{{' + ab("ecpm_topk_protect_inner_photo_soft_ratio", 0.0) + '}}',
          e2e_topk_protect_inner_photo_soft_cnt = '{{' + ab("e2e_topk_protect_inner_photo_soft_cnt", 0) + '}}',
          e2e_topk_protect_inner_photo_soft_ratio = '{{' + ab("e2e_topk_protect_inner_photo_soft_ratio", 0.0) + '}}',
          cid_roas_prerank_boost_ratio = '{{' + ab("cid_roas_prerank_boost_ratio", 1.0) + '}}',
          cid_paied_prerank_boost_ratio = '{{' + ab("cid_paied_prerank_boost_ratio", 1.0) + '}}',
          cid_roas_prerank_boost_ratio_v2 = '{{' + ab("cid_roas_prerank_boost_ratio_v2", 1.0) + '}}',
          cid_paied_prerank_boost_ratio_v2 = '{{' + ab("cid_paied_prerank_boost_ratio_v2", 1.0) + '}}',
          cid_roas_prerank_account_id_boost_ratio = '{{' + ab("cid_roas_prerank_account_id_boost_ratio", 1.0) + '}}',
          cid_paid_prerank_account_id_boost_ratio = '{{' + ab("cid_paid_prerank_account_id_boost_ratio", 1.0) + '}}',
          prerank_p2l_e2e_ensemble_weight = '{{' + ab("soft_prerank_p2l_e2e_ensemble_weight", 1.0) + '}}',
          prerank_p2l_ecpm_ensemble_weight = '{{' + ab("soft_prerank_p2l_ecpm_ensemble_weight", 1.0) + '}}',
          enable_prerank_divide_p_p2l_ensemble_score = '{{' + ab("enable_prerank_divide_p_p2l_ensemble_score", False) + '}}' ,
          enable_prerank_divide_with_score = '{{' + ab("enable_prerank_divide_with_score", False) + '}}' ,
          enable_inner_soft_only_ltr = '{{' + ab("enable_inner_soft_only_ltr", False) + '}}' ,
          enable_inner_soft_only_ltr_idx = '{{' + ab("enable_inner_soft_only_ltr_idx", False) + '}}' ,
          enable_e2e_topk_v2 = '{{' + ab("enable_e2e_topk_v2_inner_soft", False) + '}}',
          enable_pure_sort_es = '{{' + ab("enable_pure_sort_es_inner_soft", False) + '}}',
          enable_split_p2l_boost_deboost = '{{' + ab("enable_split_p2l_boost_deboost_inner_soft", False) + '}}',
          e2e_boost_deboost_range = '{{' + ab("e2e_boost_deboost_range_inner_soft", "0.1,0.1") + '}}',
          e2e_boost_deboost_strength = '{{' + ab("e2e_boost_deboost_strength_inner_soft", "1.0,1.0") + '}}',
          e2e_split_p2l_boost_deboost_strength = '{{' + ab("e2e_split_p2l_boost_deboost_strength_inner_soft", "0.1,0.1,0.1,0.1") + '}}',
          enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank", False) + '}}',
          prerank_trigger_relative_score_ensemble_weight = '{{' + ab("prerank_trigger_relative_score_ensemble_weight_inner_soft", 0.0) + '}}',
          trigger_relative_score_request_times_threshold = '{{' + ab("trigger_relative_score_request_times_threshold", 0) + '}}',
          enable_relative_score_hc_ratio_prerank = '{{' + ab("enable_relative_score_hc_ratio_prerank", False) + '}}',
          enable_up_refresh_relative_hc_only = '{{' + ab("enable_up_refresh_relative_hc_only", False) + '}}',
          enable_relative_weight_dec_with_request_times = '{{' + ab("enable_relative_weight_dec_with_request_times", False) + '}}',
          enable_prerank_trigger_item_relative_score_boost = '{{' + ab("enable_prerank_trigger_item_relative_score_boost", False) + '}}',
          prerank_trigger_item_relative_score_boost_threshold = '{{' + ab("prerank_trigger_item_relative_score_boost_threshold", 0.0) + '}}',
          prerank_trigger_item_relative_score_boost_ratio = '{{' + ab("prerank_trigger_item_relative_score_boost_ratio_inner_soft", 1.0) + '}}',
          enable_prerank_class_dist_reform = '{{' + ab("enable_prerank_class_dist_reform_inner_soft", False) + '}}',
          enable_class_dist_adp = '{{' + ab("enable_class_dist_adp_inner_soft", False) + '}}',
          enable_inner_cold_start_adjust = '{{' + ab("enable_inner_cold_start_adjust", False) + '}}',
          enable_inner_cold_start_topk_adjust = '{{' + ab("enable_inner_cold_start_topk_adjust", False) + '}}',
          coldstart_topk_exp_tag_prerank = '{{' + ab("coldstart_topk_exp_tag_prerank", "") + '}}',
          prerank_class_dist_reform_param = '{{' + ab("prerank_class_dist_reform_param_inner_soft", "1.0,1.0,1.0,1.0,1.0,1.0,1.0") + '}}',
          enable_hybrid_ltr_path = '{{' + ab("enable_hybrid_ltr_path_inner_soft", False) + '}}',
          enable_hybrid_ltr_bid = '{{' + ab("enable_hybrid_ltr_bid_inner_soft", False) + '}}',
          hybrid_ltr_mul_params = '{{' + ab("hybrid_ltr_mul_params_inner_soft", "1.0,1.0,1.0") + '}}',
          enable_ensemble_only_model_score = '{{' + ab("enable_ensemble_only_model_score", False) + '}}',
          prerank_trigger_item_relative_idx_boost_threshold = '{{' + ab("prerank_trigger_item_relative_idx_boost_threshold", 0) + '}}',
          prerank_inner_explore_relative_boost_ltr_idx = '{{' + ab("prerank_inner_explore_relative_boost_ltr_idx", 0) + '}}',
          enable_unify_p_p2l_prerank_ensemble_score = '{{' + ab("enable_unify_p_p2l_prerank_ensemble_score_inner_soft", False) + '}}',
          enable_prerank_ensemble_score_presonal_cem = '{{' + ab("enable_prerank_ensemble_score_presonal_cem_inner_soft", False) + '}}',
        ) \
      .end_if_() \
    .end_if_() \
    .if_("%s %s"%(ab("enable_write_inner_soft_ad_info_to_redis", False), "== 1")) \
    .write_info_to_redis_enricher(
      input_table_name = "inner_photo_soft_item_table",
      info_to_redis_cluster_name = '{{' + ab("inner_soft_ad_info_to_redis_cluster_name", "recoAdNativeCoopStrategy") + '}}',
      info_to_redis_ttl = '{{' + ab("inner_soft_ad_info_to_redis_ttl", 259200) + '}}',
      info_to_redis_max_photo_id_num = '{{' + ab("inner_soft_ad_info_to_redis_max_photo_id_num", 500) + '}}',
      info_to_redis_key_prefix = '{{' + ab("inner_soft_ad_info_to_redis_key_prefix", "a4rin_") + '}}',
    ) \
    .end_if_() \

    return self

  @module()
  def inner_photo_soft_layer(self):
    current_flow() \
    .if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_inner == 0 or enable_prerank_rank_ica == 0") \
    .dcaf(
      enable_explore = "enable_rank_quota_explore_inner_soft_photo",
      version_id = '{{' + ab("dcaf_rank_version_inner_soft_photo", "") + '}}',
      dcaf_degrade_ratio = '{{' + ab("dcaf_degrade_ratio_inner", 1.0) + '}}',
      disable_rewarded_dcaf_ratio = '{{' + ab("disable_rewarded_dcaf_ratio", False) + '}}',
      enable_rank_rt_exp_quota = '{{' + ab("enable_rank_rt_exp_quota", False) + '}}',
      smooth_function = '{{' + ab("dcaf_smooth_func_inner_soft_photo", "") + '}}',
      disable_dcaf_with_constant = '{{' + ab("disable_dcaf_with_constant", False) + '}}',
      enable_biz_name = True,
      biz_name = "inner_soft_photo",
      fake_type = "fake_type",
    ) \
    .end_if_() \
    .inner_soft_photo_layer_manager_enricher(
      #enable_explore = "enable_rank_quota_explore_inner_hard_photo",
      append_ab_tag = '{{' + ab("prerank_append_strategy_fanstop", "base") + '}}',
      trunc_target_max_rank_photo = '{{' + ab("trunc_target_max_rank_photo", 0) + '}}',
      trunc_target_max_rank_live = '{{' + ab("trunc_target_max_rank_live", 0) + '}}',
      no_sell_photo_quota = '{{' + ab("no_sell_photo_quota", 20) + '}}',
      no_sell_live_quota = '{{' + ab("no_sell_live_quota", 10) + '}}',
      follow_photo_quota = '{{' + ab("follow_photo_quota", 0) + '}}',
      follow_live_inner_photo_quota = '{{' + ab("follow_live_inner_photo_quota", 530) + '}}',
      fix_follow_photo_quota = '{{' + ab("fix_follow_photo_quota", False) + '}}',
      drop_down_invert_ab = '{{' + ab("drop_down_invert_ab", False) + '}}',
      drop_down_invert_ratio = '{{' + ab("drop_down_invert_ratio", 1.0) + '}}',
      enable_not_drop_down = '{{' + ab("enable_not_drop_down", False) + '}}',
      enable_user_group_quota_strategy = '{{' + ab("enable_user_group_quota_strategy", False) + '}}',
      enable_user_quota_for_outer = '{{' + ab("enable_user_quota_for_outer", False) + '}}',
      enable_unify_innersoft_quota = '{{' + ab("enable_unify_innersoft_quota", False) + '}}',
      enable_expand_quota_degrade = '{{' + ab("enable_expand_quota_degrade", False) + '}}',
      enable_inner_soft_photo_expand_ratio = '{{' + ab("enable_inner_soft_photo_expand_ratio", False) + '}}',
      enable_inner_soft_photo_quota_unify = '{{' + ab("enable_inner_soft_photo_quota_unify", False) + '}}',
      enable_modify_expand_quota_u_level = '{{' + ab("enable_modify_expand_quota_u_level", False) + '}}',
      enable_dcaf_ratio_first = '{{' + ab("enable_dcaf_ratio_first", False) + '}}',
      user_quota_tag = '{{' + ab("user_quota_tag", "") + '}}',
      inner_soft_main_upper = '{{' + ab("inner_soft_main_upper", 300) + '}}',
      enable_interest_adjust = '{{' + ab("enable_interest_adjust", False) + '}}',
      high_interest_adjust_ratio = '{{' + ab("high_interest_adjust_ratio", 1.0) + '}}',
      low_interest_adjust_ratio = '{{' + ab("low_interest_adjust_ratio", 1.0) + '}}',
      biz_name = "inner_soft_photo",
      layer_table_name = "inner_soft_photo_layer_table",
      is_prerank = "is_prerank",
      quota_ratio = '{{' + ab("quota_ratio_inner_soft", 1.0) + '}}',
      rank_quota_exp_name = '{{' + ab("rank_quota_exp_name_inner_soft", "") + '}}',
      quota_limit_ratio = '{{' + ab("quota_limit_ratio_inner_soft", 1.0) + '}}',
      quota_limit_ratio_cache = '{{' + ab("quota_limit_ratio_inner_soft_cache", 1.0) + '}}',
      enable_quota_limit = '{{' + ab("enable_quota_limit_inner_soft", False) + '}}',
      enable_quota_limit_new = '{{' + ab("enable_quota_limit_new_inner_soft", False) + '}}',
      enable_quota_limit_by_hour = '{{' + ab("enable_quota_limit_by_hour", False) + '}}',
      enable_ica_inner_soft_notprerank = '{{' + ab("enable_ica_inner_soft_notprerank", False) + '}}',
      quota_limit_ratio_exp_tag = '{{' + ab("quota_limit_ratio_exp_tag", "") + '}}',
      enable_expand_ratio_cliff = '{{' + ab("enable_expand_ratio_cliff_inner_soft", False) + '}}',
      inner_soft_photo_expand_cliff_ratio = '{{' + ab("inner_soft_photo_expand_cliff_ratio", 1.0) + '}}',
      enable_expand_ratio_cliff_page = '{{' + ab("enable_expand_ratio_cliff_page_inner_soft", False) + '}}',
      expand_ratio_cliff_page_str = '{{' + ab("expand_ratio_cliff_page_str_inner_soft", "") + '}}',
      enable_prerank_rank_ica_inner = '{{' + ab("enable_prerank_rank_ica_inner", False) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      inner_buyer_exp_name = '{{' + ab("inner_buyer_exp_name", "") + '}}',
      enable_inner_buyer_rank_quota = '{{' + ab("enable_inner_buyer_rank_quota", False) + '}}',
      enable_prerank_local_life_dui_tou_quota_limit = '{{' + ab("enable_prerank_local_life_dui_tou_quota_limit", False) + '}}',
      max_quota_exp_name = '{{' + ab("max_quota_exp_name", "") + '}}',
      enable_inner_buyer_rank_max_quota = '{{' + ab("enable_inner_buyer_rank_max_quota", False) + '}}',
      none_one_model_quota_drop_down_ratio = '{{' + ab("none_one_model_quota_drop_down_ratio_inner_soft", 1.0) + '}}'
    ) \
    .inner_soft_photo_layer_effect_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_prerank_divide_p_p2l_quota = '{{' + ab("enable_prerank_divide_p_p2l_quota", False) + '}}' ,
      prerank_soft_p2l_all_quota = '{{' + ab("prerank_soft_p2l_all_quota", 25) + '}}',
      enable_soft_photo_prerank_diversity = '{{' + ab("enable_soft_photo_prerank_diversity", False) + '}}',
      enable_soft_photo_prerank_spu_diversity = '{{' + ab("enable_soft_photo_prerank_spu_diversity", False) + '}}',
      soft_prerank_fanstop_max_ratio = '{{' + ab("soft_prerank_fanstop_max_ratio", -1.0) + '}}',
      fanstop_prerank_admit_unit_thresh = '{{' + ab("fanstop_prerank_admit_unit_thresh", 0) + '}}',
      fanstop_prerank_admit_unit_thresh_new = '{{' + ab("fanstop_prerank_admit_unit_thresh_new", 0) + '}}',
      fanstop_prerank_admit_unit_thresh_p2l = '{{' + ab("fanstop_prerank_admit_unit_thresh_p2l", 0) + '}}',
      fanstop_prerank_admit_account_thresh = '{{' + ab("fanstop_prerank_admit_account_thresh", 0) + '}}',
      fanstop_prerank_admit_account_thresh_new = '{{' + ab("fanstop_prerank_admit_account_thresh_new", 0) + '}}',
      fanstop_prerank_admit_account_thresh_p2l = '{{' + ab("fanstop_prerank_admit_account_thresh_p2l", 0) + '}}',
      fanstop_prerank_admit_photo_ocpx_thresh = '{{' + ab("fanstop_prerank_admit_photo_ocpx_thresh", 0) + '}}',
      fanstop_prerank_admit_photo_ocpx_thresh_new = '{{' + ab("fanstop_prerank_admit_photo_ocpx_thresh_new", 0) + '}}',
      fanstop_prerank_admit_photo_ocpx_thresh_p2l = '{{' + ab("fanstop_prerank_admit_photo_ocpx_thresh_p2l", 0) + '}}',
      enable_soft_diversity_photo_p2l_split = '{{' + ab("enable_soft_diversity_photo_p2l_split", False) + '}}',
      storewide_prerank_photo_ocpx_diversity_inc = '{{' + ab("storewide_prerank_photo_ocpx_diversity_inc", 0) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      soft_p2l_quota_ratio = '{{' + ab("soft_p2l_quota_ratio", 0.0) + '}}',
      soft_p2l_expand_quota = '{{' + ab("soft_p2l_expand_quota", 0) + '}}'
      ) \
    .if_("%s %s"%(ab("enable_close_layer", False), "== 0")) \
    .layer_ecology_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_prerank_buyer_shallow_baosong = '{{' + ab("enable_prerank_buyer_shallow_baosong", False) + '}}',
      enable_prerank_inner_adx_baosong = '{{' + ab("enable_prerank_inner_adx_baosong", False) + '}}',
      enable_prerank_lsp_green_channel = '{{' + ab("enable_prerank_lsp_green_channel", False) + '}}',
      prerank_buyer_shallow_baosong_type = '{{' + ab("prerank_buyer_shallow_baosong_type", "U4+") + '}}',
      enable_prerank_new_account_support = '{{' + ab("enable_prerank_new_account_support", False) + '}}',
      enable_update_prerank_new_account_support = '{{' + ab("enable_update_prerank_new_account_support", False) + '}}',
      enable_update_mobile_support = '{{' + ab("enable_update_mobile_support", False) + '}}',
      disable_prerank_reward_mingtou_support = '{{' + ab("disable_prerank_reward_mingtou_support", False) + '}}',
      enable_prerank_original_photo_support = '{{' + ab("enable_prerank_original_photo_support", False) + '}}',
      enable_prerank_new_item_support = '{{' + ab("enable_prerank_new_item_support", False) + '}}',
      new_item_recall_tag = '{{' + ab("new_item_recall_tag", 0) + '}}',
      no_sell_budget_bound = '{{' + ab("no_sell_budget_bound", 0) + '}}',
      enable_prerank_new_inner_p0_baosong = '{{' + ab("enable_prerank_new_inner_p0_baosong", False) + '}}',
      enable_all_coldstart_flink = '{{' + ab("enable_all_coldstart_flink", False) + '}}',
      enable_topk_photo_prerank = '{{' + ab("enable_topk_photo_prerank", False) + '}}',
      enable_inner_cold_start_adjust = '{{' + ab("enable_inner_cold_start_adjust", False) + '}}',
      enable_native_degree_tag = '{{' + ab("enable_native_degree_tag", False) + '}}',
      enable_coldboot_score_explore = '{{' + ab("enable_coldboot_score_explore_2", False) + '}}',
      coldboot_score_explore_ratio = '{{' + ab("coldboot_score_explore_ratio_2", 1.0) + '}}',
      enable_new_spu_prerank = '{{' + ab("enable_new_spu_prerank", False) + '}}',
      enable_follow_realtime_high_gmv_author_baosong = '{{' + ab("enable_follow_realtime_high_gmv_author_baosong", False) + '}}',
      follow_realtime_high_gmv_author_retrival_tag = '{{' + ab("follow_realtime_high_gmv_author_retrival_tag", 0) + '}}',
      buyer_effective_type = "buyer_effective_type",
      follow_userid_list = "follow_userid_list",
      is_inner_rewarded_mingtou_ads = "is_inner_rewarded_mingtou_ads",
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      ad_strategy_tag = "ad_strategy_tag",
      is_new_model_creative = "is_new_model_creative",
      new_creative_tag_realtime = "new_creative_tag_realtime",
      is_original_photo = "is_original_photo",
      account_type = "account_type",
      is_esp = "is_esp",
      ocpx_action_type = "ocpx_action_type",
      author_id = "author_id",
      is_new_merchant = "is_new_merchant",
      is_smb_account = "is_smb_account",
      account_create_source = "account_create_source",
      multi_retrieval_tag = "multi_retrieval_tag",
      real_seller_main_category_id = "real_seller_main_category_id",
      ast_30d_pay_order_amt = "ast_30d_pay_order_amt",
      strategy_crowd_info = "strategy_crowd_info",
      enable_fanstop_small_c_photo_baosong = '{{' + ab("enable_fanstop_small_c_photo_baosong", False) + '}}',
      enable_prerank_mobile_photo_support = '{{' + ab("enable_prerank_mobile_photo_support", False) + '}}',
      enable_prerank_innerloop_cid_ad_support = '{{' + ab("enable_prerank_innerloop_cid_ad_support", False) + '}}',
      campaign_type = "campaign_type",
      enable_inner_explore_sim_ltr_append = '{{' + ab("enable_inner_explore_sim_ltr_append", False) + '}}',
      inner_explore_sim_ltr_append_idx = '{{' + ab("inner_explore_sim_ltr_append_idx", 0) + '}}',
      inner_explore_sim_ltr_append_score = '{{' + ab("inner_explore_sim_ltr_append_score", 0.0) + '}}',
    ) \
    .layer_operational_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_live_prerank_white_baosong = '{{' + ab("enable_live_prerank_white_baosong", False) + '}}',
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_inner_aigc_photo_support = '{{' + ab("enable_inner_aigc_photo_support", False) + '}}',
      enable_nearline_multi_recall = '{{' + ab("enable_nearline_multi_recall", False) + '}}',
    ) \
    .layer_guaranteed_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_reservation_guaranteed_preranking = '{{' + ab("enable_reservation_guaranteed_preranking", False) + '}}',
      enable_mobile_hard_photo_guaranteed_preranking = '{{' + ab("enable_mobile_hard_photo_guaranteed_preranking", False) + '}}',
      mobile_hard_photo_guaranteed_preranking_tag = '{{' + ab("mobile_hard_photo_guaranteed_preranking_tag", 200) + '}}',
      enable_mobile_hard_p2l_guaranteed_preranking = '{{' + ab("enable_mobile_hard_p2l_guaranteed_preranking", False) + '}}',
      mobile_hard_p2l_guaranteed_preranking_tag = '{{' + ab("mobile_hard_p2l_guaranteed_preranking_tag", 42) + '}}',
      ensemble_score = "ensemble_score",
      enable_search_retarget_guaranteed_preranking = '{{' + ab("enable_search_retarget_guaranteed_preranking", False) + '}}',
      enable_inner_prerank_search_append = '{{' + ab("enable_inner_prerank_search_append_new", False) + '}}',
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      account_type = "account_type",
      author_id = "author_id",
      campaign_type = "campaign_type",
      ocpx_action_type = "ocpx_action_type",
      multi_retrieval_tag = "multi_retrieval_tag",
    ) \
    .layer_nomal_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_whole_roi_append = '{{' + ab("enable_whole_roi_append_dragon", False) + '}}',
      enable_whole_roi_append_qhc = '{{' + ab("enable_whole_roi_append_qhc", False) + '}}',
      enable_qcpx_live_prerank = '{{' + ab("enable_qcpx_live_prerank", False) + '}}',
      enable_qcpx_photo_prerank = '{{' + ab("enable_qcpx_photo_prerank", False) + '}}',
      enable_prerank_lsp_append = '{{' + ab("enable_prerank_lsp_append", False) + '}}',
      enable_prerank_lsp_append_skip_follow = '{{' + ab("enable_prerank_lsp_append_skip_follow", False) + '}}',
      enable_prerank_t7_roi_append = '{{' + ab("enable_prerank_t7_roi_append", False) + '}}',
      enable_prerank_cost_drop_photo_append = '{{' + ab("enable_prerank_cost_drop_photo_append", False) + '}}',
      enable_cbo_append = '{{' + ab("enable_cbo_append", False) + '}}',
      enable_inner_trigger_item_append_wanhe = '{{' + ab("enable_inner_trigger_item_append_wanhe", False) + '}}',
      enable_inner_trigger_item_path_append = '{{' + ab("enable_inner_trigger_item_path_append", False) + '}}',
      enable_inner_trigger_item_tag_append = '{{' + ab("enable_inner_trigger_item_tag_append", False) + '}}',
      inner_trigger_item_strategy_tag = '{{' + ab("inner_trigger_item_strategy_tag", "") + '}}',
      inner_trigger_item_strategy_tag_thr = '{{' + ab("inner_trigger_item_strategy_tag_thr", 1.0) + '}}',
      enable_adx_recall_path_append = '{{' + ab("enable_adx_recall_path_append", False) + '}}',
      enable_new_cus_support = '{{' + ab("enable_new_cus_support", False) + '}}',
      enable_filter_jili_page = '{{' + ab("enable_filter_jili_page", False) + '}}',
      enable_new_cus_support_v2 = '{{' + ab("enable_new_cus_support_v2", False) + '}}',
      enable_smb_coldstart_support = '{{' + ab("enable_smb_coldstart_support", False) + '}}',
      enable_smb_coldstart_support_two = '{{' + ab("enable_smb_coldstart_support_two", False) + '}}',
      enable_c0c1_support = '{{' + ab("enable_c0c1_support", False) + '}}',
      enable_inner_explore_relative_recall_tag_support = '{{' + ab("enable_inner_explore_relative_recall_tag_support", False) + '}}',
      enable_inner_explore_relative_score_support = '{{' + ab("enable_inner_explore_relative_score_support", False) + '}}',
      relative_score_support_threshold = '{{'+ ab("relative_score_support_threshold", 1.0) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      multi_retrieval_tag = "multi_retrieval_tag",
    ) \
    .layer_nondelivery_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_nondelivery", False) + '}}',
      enable_ensemble_score_model = '{{' + ab("enable_ensemble_score_model", False) + '}}',
      enable_ecom_click_action_retarget = '{{' + ab("enable_ecom_click_action_retarget", False) + '}}',
      enable_ecom_follow_action_retarget = '{{' + ab("enable_ecom_follow_action_retarget", False) + '}}',
      enable_ecom_purchase_action_retarget = '{{' + ab("enable_ecom_purchase_action_retarget", False) + '}}',
      enable_ecom_comment_action_retarget = '{{' + ab("enable_ecom_comment_action_retarget", False) + '}}',
      enable_ecom_zc_interact_author_retarget = '{{' + ab("enable_ecom_zc_interact_author_retarget", False) + '}}',
      enable_ecom_zc_long_play_action_retarget = '{{' + ab("enable_ecom_zc_long_play_action_retarget", False) + '}}',
      enable_ecom_reco_action_author_retarget = '{{' + ab("enable_ecom_reco_action_author_retarget", False) + '}}',
      enable_inner_sid_support = '{{' + ab("enable_inner_sid_support", False) + '}}',
      enable_inner_t7_roi_support = '{{' + ab("enable_inner_t7_roi_support", False) + '}}',
      enable_fix_click_action_sign = '{{' + ab("enable_fix_click_action_sign", False) + '}}',
      enable_fix_action_cate3_sign = '{{' + ab("enable_fix_action_cate3_sign", False) + '}}',
      enable_ecom_action_cate3_retarget = '{{' + ab("enable_ecom_action_cate3_retarget", False) + '}}',
      enable_limit_cate3_nums = '{{' + ab("enable_limit_cate3_nums", False) + '}}',
      enable_high_grade_recall_path_support = '{{' + ab("enable_high_grade_recall_path_support", False) + '}}',
      cate3_length_threshold = '{{' + ab("cate3_length_threshold", 0) + '}}',
      cate3_time_threshold = '{{' + ab("cate3_time_threshold", 0) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      short_action_up_weight = '{{' + ab("short_action_up_weight", 1.0) + '}}',
      enable_ecom_interest = '{{' + ab("enable_ecom_interest", False) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}',
      merchant_prerank_interest_tag = '{{' + ab("merchant_prerank_interest_tag", "") + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      multi_retrieval_tag = "multi_retrieval_tag",
      enable_adx_retarget_support = '{{' + ab("enable_adx_retarget_support", False) + '}}',
      adx_retarget_path = '{{' + ab("adx_retarget_path", "") + '}}',
    ) \
    .layer_special_support_f1_enricher(
      input_table_name = "inner_photo_soft_item_table",
      input_layer_table_name = "inner_soft_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_inner_ceiling_preranking = '{{' + ab("enable_inner_ceiling_preranking", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
    ) \
    .end_if_() \
    .layer_supplement_enricher(
      input_table_name = "inner_photo_soft_item_table",
      enable_prerank_layer_supplement = '{{' + ab("enable_prerank_layer_supplement_cache_fanstop", False) + '}}',
      enable_prerank_layer_supplement_v2 = '{{' + ab("enable_prerank_layer_supplement_inner_photo_soft", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
    ) \
    .if_("%s %s %s"%("is_prerank == 1 and", ab("enable_fanstop_cache_sort_opt", False), "== 0")) \
      .backup_sort_enricher(
        input_table_name = "inner_photo_soft_item_table",
      ) \
    .end_if_() \
    .if_("%s %s"%(ab("enable_retarget_enricher_up", False), "== 0")) \
    .prerank_retarget_tag_enricher(
      input_table_name = "inner_photo_soft_item_table",
      enable_fix_action_cate3_tag = '{{' + ab("enable_fix_action_cate3_tag", False) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      shorttime_action_gap = '{{' + ab("shorttime_action_gap", 0) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}'
    ) \
    .end_if_() \
    .layer_storewide_item_upper_enricher(
      input_table_name = "inner_photo_soft_item_table",
      enable_layer_product_storewide = '{{' + ab("enable_layer_product_storewide", False) + '}}'
    ) \

    return self
# fanstop ned

# live start
  @module()
  def inner_live_rank_score(self):
    current_flow() \
    .inner_live_auto_cpa_bid_enricher(
      input_table_name = "inner_live_item_table",
      item_table = "inner_live_item_table",
      enable_esp_live_roas_prerank_atv_v2 = '{{' + ab("enable_esp_live_roas_prerank_atv_v2", False) + '}}',
    ) \
    .inner_live_delivery_enricher(
      input_table_name = "inner_live_item_table",
      enable_prerank_live_separate_billing = '{{' + ab("enable_prerank_live_separate_billing", False) + '}}',
      enable_live_target_separate_billing = '{{' + ab("enable_live_target_separate_billing", False) + '}}',
      enable_prerank_ctcvr_replace_e2e = '{{' + ab("enable_prerank_ctcvr_replace_e2e", False) + '}}',
      enable_prerank_ctcvr_replace_e2e_mobile = '{{' + ab("enable_prerank_ctcvr_replace_e2e_mobile", False) + '}}',
      enable_live_prerank_nobid_exp = '{{' + ab("enable_live_prerank_nobid_exp", False) + '}}',
      enable_live_prerank_mobile_bid_exp = '{{' + ab("enable_live_prerank_mobile_bid_exp", False) + '}}',
      enable_fanstopv2_autobidli_exp_prerank = '{{' + ab("enable_fanstopv2_autobidli_exp_prerank", False) + '}}',
      enable_fanstop_autobidli_exp_prerank_fix = '{{' + ab("enable_fanstop_autobidli_exp_prerank_fix", False) + '}}',
      enable_fanstop_autobidli_exp_all_prerank_live =  '{{' + ab("enable_fanstop_autobidli_exp_all_prerank_live", False) + '}}',
      prerank_live_bid_price_ratio = '{{' + ab("prerank_live_bid_price_ratio", 1.0) + '}}',
      prerank_live_auto_bid_weight = '{{' + ab("prerank_live_auto_bid_weight", 1.0) + '}}',
      enable_prerank_bs_inner_live = '{{' + ab("enable_prerank_bs_inner_live", False) + '}}',
      prerank_bs_bid_ratio_inner_live = '{{' + ab("prerank_bs_bid_ratio_inner_live", 1.0) + '}}',
      enable_prerank_bs_inner_live_roi = '{{' + ab("enable_prerank_bs_inner_live_roi", False) + '}}',
      prerank_bs_bid_ratio_inner_live_roi = '{{' + ab("prerank_bs_bid_ratio_inner_live_roi", 1.0) + '}}',
      prerank_roi_live_ctcvr_factor = '{{' + ab("prerank_roi_live_ctcvr_factor", 1.0) + '}}',
      prerank_pc_roas_live_ctcvr_factor = '{{' + ab("prerank_pc_roas_live_ctcvr_factor", 1.0) + '}}',
      prerank_pc_live_ctcvr_factor = '{{' + ab("prerank_pc_live_ctcvr_factor", 1.0) + '}}',
      prerank_mobile_live_ctcvr_factor = '{{' + ab("prerank_mobile_live_ctcvr_factor", 1.0) + '}}',
      enable_fix_prerank_v3_quota = '{{' + ab("enable_fix_prerank_v3_quota", False) + '}}',
      enable_fix_prerank_v3_quota_v2 = '{{' + ab("enable_fix_prerank_v3_quota_v2", False) + '}}',
      enable_fix_prerank_v3_quota_v3 = '{{' + ab("enable_fix_prerank_v3_quota_v3", False) + '}}',
      scene_oriented_type = "scene_oriented_type",
    ) \
    .if_("enable_prerank_live_ltr == 1") \
      .inner_live_ltr_enricher(
        input_table_name = "inner_live_item_table",
        enable_ltr_prerank_live_separate_billing = '{{' + ab("enable_prerank_live_ltr_separate_billing", False) + '}}',
        enable_live_target_separate_billing = '{{' + ab("enable_live_target_separate_billing", False) + '}}',
        enable_prerank_live_ltr = '{{' + ab("enable_prerank_live_ltr", False) + '}}',
        enable_prerank_live_ltr_e2e = '{{' + ab("enable_prerank_live_ltr_e2e", False) + '}}',
        enable_live_ltr_prerank_nobid_exp = '{{' + ab("enable_live_ltr_prerank_nobid_exp", False) + '}}',
        prerank_live_bid_price_ratio = '{{' + ab("prerank_live_bid_price_ratio", 1.0) + '}}',
        prerank_live_auto_bid_weight = '{{' + ab("prerank_live_auto_bid_weight", 1.0) + '}}',
        enable_prerank_bs_inner_live = '{{' + ab("enable_prerank_bs_inner_live", False) + '}}',
        prerank_bs_bid_ratio_inner_live = '{{' + ab("prerank_bs_bid_ratio_inner_live", 1.0) + '}}',
        enable_prerank_bs_inner_live_roi = '{{' + ab("enable_prerank_bs_inner_live_roi", False) + '}}',
        prerank_bs_bid_ratio_inner_live_roi = '{{' + ab("prerank_bs_bid_ratio_inner_live_roi", 1.0) + '}}',
        enable_fix_prerank_v3_quota_v2 = '{{' + ab("enable_fix_prerank_v3_quota_v2", False) + '}}',
        enable_fix_prerank_v3_quota_v3 = '{{' + ab("enable_fix_prerank_v3_quota_v3", False) + '}}',
        scene_oriented_type = "scene_oriented_type",
      ) \
    .end_if_() \
    .inner_live_multi_interest_enricher(
      input_table_name = "inner_live_item_table",
      enable_fix_prerank_v3_quota_v2 = '{{' + ab("enable_fix_prerank_v3_quota_v2", False) + '}}',
      enable_fix_prerank_v3_quota_v3 = '{{' + ab("enable_fix_prerank_v3_quota_v3", False) + '}}',
      enable_live_multi_interest_cmd = '{{' + ab("enable_live_multi_interest_cmd", False) + '}}',
      live_pc_multi_interest_a_ensemble_weight = '{{' + ab("live_pc_multi_interest_a_ensemble_weight", 0.0) + '}}',
      live_pc_multi_interest_b_ensemble_weight = '{{' + ab("live_pc_multi_interest_b_ensemble_weight", 0.0) + '}}',
      live_pc_multi_interest_c_ensemble_weight = '{{' + ab("live_pc_multi_interest_c_ensemble_weight", 0.0) + '}}',
      live_mb_multi_interest_a_ensemble_weight = '{{' + ab("live_mb_multi_interest_a_ensemble_weight", 0.0) + '}}',
      live_mb_multi_interest_b_ensemble_weight = '{{' + ab("live_mb_multi_interest_b_ensemble_weight", 0.0) + '}}',
      live_mb_multi_interest_c_ensemble_weight = '{{' + ab("live_mb_multi_interest_c_ensemble_weight", 0.0) + '}}',
    ) \
    .inner_live_ecpm_enricher(
      input_table_name = "inner_live_item_table",
      enable_ecpm_prerank_live_separate_billing = '{{' + ab("enable_ecpm_prerank_live_separate_billing", False) + '}}',
      enable_live_target_separate_billing = '{{' + ab("enable_live_target_separate_billing", False) + '}}',
      enable_live_ecpm_prerank_nobid_exp = '{{' + ab("enable_live_ecpm_prerank_nobid_exp", False) + '}}',
      prerank_live_bid_price_ratio = '{{' + ab("prerank_live_bid_price_ratio", 1.0) + '}}',
      prerank_live_auto_bid_weight = '{{' + ab("prerank_live_auto_bid_weight", 1.0) + '}}',
      enable_live_goods_score_replace_ecpm = '{{' + ab("enable_live_goods_score_replace_ecpm", False) + '}}',
      enable_fix_prerank_v3_quota_v2 = '{{' + ab("enable_fix_prerank_v3_quota_v2", False) + '}}',
      enable_fix_prerank_v3_quota_v3 = '{{' + ab("enable_fix_prerank_v3_quota_v3", False) + '}}',
      enable_prerank_bs_inner_live = '{{' + ab("enable_prerank_bs_inner_live", False) + '}}',
      prerank_bs_bid_ratio_inner_live = '{{' + ab("prerank_bs_bid_ratio_inner_live", 1.0) + '}}',
      enable_ecpm_atv = '{{' + ab("enable_ecpm_atv", False) + '}}',
      scene_oriented_type = "scene_oriented_type",
    ) \
    .inner_live_gpm_enricher(
      input_table_name = "inner_live_item_table",
      enable_live_gpm_prerank = '{{' + ab("enable_live_gpm_prerank", False) + '}}',
      enable_live_gpm_prerank_v2 = '{{' + ab("enable_live_gpm_prerank_v2", False) + '}}',
      enable_fix_prerank_v3_quota_v2 = '{{' + ab("enable_fix_prerank_v3_quota_v2", False) + '}}',
      enable_fix_prerank_v3_quota_v3 = '{{' + ab("enable_fix_prerank_v3_quota_v3", False) + '}}',
      disable_gpm_prerank_reward = '{{' + ab("disable_gpm_prerank_reward_inner_live", False) + '}}',
      prerank_live_gpm_ecpm_weight = '{{' + ab("prerank_live_gpm_ecpm_weight", 1.0) + '}}',
    ) \
    .inner_live_fanstop_bonus_enricher(
      input_table_name = "inner_live_item_table",
      is_inspire_live = "is_inspire_live",
      enable_prerank_ctcvr_replace_e2e_mobile = '{{' + ab("enable_prerank_ctcvr_replace_e2e_mobile", False) + '}}',
      enable_mobile_live_prerank_add_ecpm = '{{' + ab("enable_mobile_live_prerank_add_ecpm", False) + '}}',
      prerank_mobile_live_ensemble_ecpm_weight = '{{' + ab("prerank_mobile_live_ensemble_ecpm_weight", 1.0) + '}}',
      prerank_mobile_live_ensemble_e2e_weight = '{{' + ab("prerank_mobile_live_ensemble_e2e_weight", 1.0) + '}}',
      pc_fans_follow_bonus_rate_thresh = '{{' + ab("pc_fans_follow_bonus_rate_thresh", 100) + '}}',
      enable_pc_fans_follow_bonus = '{{' + ab("enable_pc_fans_follow_bonus", False) + '}}',
      enable_mobile_fans_follow_bonus = '{{' + ab("enable_mobile_fans_follow_bonus", False) + '}}',
      enable_inspire_live_pc_fans_follow_bonus = '{{' + ab("enable_inspire_live_pc_fans_follow_bonus", False) + '}}',
      enable_inspire_live_mobile_fans_follow_bonus = '{{' + ab("enable_inspire_live_mobile_fans_follow_bonus", False) + '}}',
      mobile_fans_follow_bonus_score = '{{' + ab("mobile_fans_follow_bonus_score", 1.0) + '}}',
    ) \
    .if_("%s %s"%(ab("enable_retarget_enricher_up", False), "== 1")) \
    .prerank_retarget_tag_enricher(
      input_table_name = "inner_live_item_table",
      enable_fix_action_cate3_tag = '{{' + ab("enable_fix_action_cate3_tag", False) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      enable_sign_ecpm_idx = '{{' + ab("enable_sign_ecpm_idx", False) + '}}',
      shorttime_action_gap = '{{' + ab("shorttime_action_gap", 0) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}',
      enable_sign_mix_photo_idx = '{{' + ab("enable_sign_mix_photo_idx", False) + '}}',
      enable_sign_mix_author_idx = '{{' + ab("enable_sign_mix_author_idx", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
    ) \
    .end_if_() \
    .inner_live_one_step_score(
      input_table_name = "inner_live_item_table",
      enable_prerank_bs_inner_live = '{{' + ab("enable_prerank_bs_inner_live", False) + '}}',
      prerank_bs_bid_ratio_inner_live = '{{' + ab("prerank_bs_bid_ratio_inner_live", 1.0) + '}}',
    ) \
    .inner_live_mix_ensemble_score(
      input_table_name = "inner_live_item_table",
      storewide_roas_live_prerank_boost_ratio = '{{' + ab("storewide_roas_live_prerank_boost_ratio", 1.0) + '}}',
      new_cus_live_prerank_boost_ratio = '{{' + ab("new_cus_live_prerank_boost_ratio", 1.0) + '}}',
      new_cus_live_prerank_boost_ratio_v2 = '{{' + ab("new_cus_live_prerank_boost_ratio_v2", 1.0) + '}}',
      new_cus_live_prerank_boost_ratio_v2_2 = '{{' + ab("new_cus_live_prerank_boost_ratio_v2_2", 1.0) + '}}',
      daily_new_cus_live_prerank_boost_ratio_v2 = '{{' + ab("daily_new_cus_live_prerank_boost_ratio_v2", 1.0) + '}}',
      daily_new_cus_live_prerank_boost_ratio_v2_2 = '{{' + ab("daily_new_cus_live_prerank_boost_ratio_v2_2", 1.0) + '}}',
      new_cus_live_neworder_prerank_boost_ratio = '{{' + ab("new_cus_live_neworder_prerank_boost_ratio", 1.0) + '}}',
      enable_newcus_neworder_prerank_adjust = '{{' + ab("enable_newcus_neworder_prerank_adjust", False) + '}}',
      enable_inner_cold_start_adjust = '{{' + ab("enable_inner_cold_start_adjust_live", False) + '}}',
      enable_filter_jili_page_v2 = '{{' + ab("enable_filter_jili_page_v2", False) + '}}',
      enable_inner_qcpx_live_prerank_adjust = '{{' + ab("enable_inner_qcpx_live_prerank_adjust", False) + '}}',
      enable_shelf_live_prerank_ensemble_weight = '{{' + ab("enable_shelf_live_prerank_ensemble_weight", False) + '}}',
      enable_pure_sort_es = '{{' + ab("enable_pure_sort_es_inner_live", False) + '}}'
    )
    return self

  @module()
  def inner_live_layer_manager(self):
    current_flow() \
    .inner_live_layer_manager_enricher(
      live_prerank_max_quota = '{{' + ab("live_prerank_max_quota", 60) + '}}',
      enable_explore = "enable_rank_quota_explore_inner_hard_photo",
      append_ab_tag = '{{' + ab("prerank_append_strategy_amd_live", "base") + '}}',
      outer_live_append_ab_tag = '{{' + ab("outer_live_append_ab_tag", "") + '}}',
      inspire_live_prerank_max_quota = '{{' + ab("inspire_live_prerank_max_quota", 100) + '}}',
      inspire_merchant_base_line_quota = '{{' + ab("inspire_merchant_base_line_quota", 60) + '}}',
      follow_fanstop_rank_quota = '{{' + ab("follow_fanstop_rank_quota", 450) + '}}',
      follow_live_inner_live_quota = '{{' + ab("follow_live_inner_live_quota", 200) + '}}',
      enable_biz_name= False,
      enable_individual_quota_for_inspire_merchant = '{{' + ab("enable_individual_quota_for_inspire_merchant", False) + '}}',
      enable_individual_quota_for_fanstop_inspire = '{{' + ab("enable_individual_quota_for_fanstop_inspire", False) + '}}',
      enable_modify_expand_quota_u_level = '{{' + ab("enable_modify_expand_quota_u_level", False) + '}}',
      outer_live_rank_num = '{{' + ab("outer_live_rank_num", 5) + '}}',
      fanstop_rank_quota = '{{' + ab("fanstop_rank_quota", 130) + '}}',
      fanstop_inspire_rank_quota = '{{' + ab("fanstop_inspire_rank_quota", 130) + '}}',
      live_target_rank_quota = '{{' + ab("live_target_rank_quota", 25) + '}}',
      inspire_live_target_rank_quota = '{{' + ab("inspire_live_target_max_quota", 25) + '}}',
      drop_down_invert_ab = '{{' + ab("drop_down_invert_ab", False) + '}}',
      drop_down_invert_ratio = '{{' + ab("drop_down_invert_ratio", 1.0) + '}}',
      enable_not_drop_down = '{{' + ab("enable_not_drop_down", False) + '}}',
      enable_all_outer_drop_down = '{{' + ab("enable_all_outer_drop_down", False) + '}}',
      enable_interest_adjust = '{{' + ab("enable_interest_adjust", False) + '}}',
      high_interest_adjust_ratio = '{{' + ab("high_interest_adjust_ratio", 1.0) + '}}',
      low_interest_adjust_ratio = '{{' + ab("low_interest_adjust_ratio", 1.0) + '}}',
      is_inspire_merchant = "is_merchant_inspire",
      is_inspire_live = "is_inspire_live",
      app_id = "app_id",
      user_gmv = "user_gmv",
      biz_name = "amd_live",
      layer_table_name = "inner_live_layer_table",
      enable_expand_ratio_cliff = '{{' + ab("enable_expand_ratio_cliff_inner_live", False) + '}}',
      inner_live_photo_expand_cliff_ratio = '{{' + ab("inner_live_photo_expand_cliff_ratio", 1.0) + '}}',
      enable_prerank_rank_ica_live = '{{' + ab("enable_prerank_rank_ica_live", False) + '}}',
      inner_live_quota_split = '{{' + ab("inner_live_quota_split", "0.33,0.42,0.25") + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      inner_buyer_exp_name = '{{' + ab("inner_buyer_exp_name", "") + '}}',
      enable_prerank_local_life_dui_tou_quota_limit = '{{' + ab("enable_prerank_local_life_dui_tou_quota_limit", False) + '}}',
      enable_inner_buyer_rank_quota = '{{' + ab("enable_inner_buyer_rank_quota", False) + '}}',
      enable_shelf_live_rank_quota = '{{' + ab("enable_shelf_live_rank_quota", False) + '}}',
      shelf_live_rank_quota = '{{' + ab("shelf_live_rank_quota", 100) + '}}',
    ) \
    .inner_live_layer_mix_effect(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_fanstop_prerank_ocpx_drop_dup = '{{' + ab("enable_fanstop_prerank_ocpx_drop_dup", False) + '}}',
      enable_nobid_item_ocpx_drop_fix = '{{' + ab("enable_nobid_item_ocpx_drop_fix", False) + '}}',
      nobid_item_ocpx_quota = '{{' + ab("nobid_item_ocpx_quota", 1) + '}}',
      enable_ocpx_dup_filter_recall = '{{' + ab("enable_ocpx_dup_filter_recall", False) + '}}',
      prerank_live_diversity_live_id_thresh = '{{' + ab("prerank_mix_live_diversity_live_id_thresh", 60) + '}}',
      prerank_live_diversity_unit_thresh = '{{' + ab("prerank_mix_live_diversity_unit_thresh", 10) + '}}',
      prerank_live_diversity_account_thresh = '{{' + ab("prerank_mix_live_diversity_account_thresh", 60) + '}}',
      prerank_live_diversity_creative_thresh = '{{' + ab("prerank_mix_live_diversity_creative_thresh", 2) + '}}',
      live_ocpx_pc_thresh = '{{' + ab("live_ocpx_mix_thresh", 1) + '}}',
      follow_live_ocpx_thresh = '{{' + ab("follow_live_ocpx_thresh", 100) + '}}',
      fanstop_live_thresh = '{{' + ab("fanstop_live_thresh", 50) + '}}',
      enable_one_step_to_dragon = '{{' + ab("enable_one_step_to_dragon", False) + '}}',
      prerank_live_diversity_cate3_thresh = '{{' + ab("prerank_live_diversity_cate3_thresh", 0) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
     ) \
    .layer_ecology_enricher(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_prerank_buyer_shallow_baosong = '{{' + ab("enable_prerank_buyer_shallow_baosong", False) + '}}',
      enable_prerank_lsp_green_channel = '{{' + ab("enable_prerank_lsp_green_channel", False) + '}}',
      prerank_buyer_shallow_baosong_type = '{{' + ab("prerank_buyer_shallow_baosong_type", "U4+") + '}}',
      enable_prerank_new_account_support = '{{' + ab("enable_prerank_new_account_support", False) + '}}',
      enable_update_prerank_new_account_support = '{{' + ab("enable_update_prerank_new_account_support", False) + '}}',
      enable_update_mobile_support = '{{' + ab("enable_update_mobile_support", False) + '}}',
      enable_all_coldstart_flink = '{{' + ab("enable_all_coldstart_flink", False) + '}}',
      enable_topk_live_prerank = '{{' + ab("enable_topk_live_prerank", False) + '}}',
      enable_inner_cold_start_adjust = '{{' + ab("enable_inner_cold_start_adjust", False) + '}}',
      enable_lsp_live_search_retarget_sign = '{{' + ab("enable_lsp_live_search_retarget_sign", False) + '}}',
      disable_prerank_reward_mingtou_support = '{{' + ab("disable_prerank_reward_mingtou_support", False) + '}}',
      enable_prerank_original_photo_support = '{{' + ab("enable_prerank_original_photo_support", False) + '}}',
      enable_prerank_new_item_support = '{{' + ab("enable_prerank_new_item_support", False) + '}}',
      enable_lsp_search_retarget_sign = '{{' + ab("enable_lsp_search_retarget_sign", False) + '}}',
      enable_lsp_geo_live_retarget_sign = '{{' + ab("enable_lsp_geo_live_retarget_sign", False) + '}}',
      new_item_recall_tag = '{{' + ab("new_item_recall_tag", 0) + '}}',
      enable_layer_selected_opt = '{{' + ab("enable_layer_selected_opt_live", False) + '}}',
      buyer_effective_type = "buyer_effective_type",
      follow_userid_list = "follow_userid_list",
      is_inner_rewarded_mingtou_ads = "is_inner_rewarded_mingtou_ads",
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      ad_strategy_tag = "ad_strategy_tag",
      is_new_model_creative = "is_new_model_creative",
      new_creative_tag_realtime = "new_creative_tag_realtime",
      is_original_photo = "is_original_photo",
      account_type = "account_type",
      is_esp = "is_esp",
      ocpx_action_type = "ocpx_action_type",
      author_id = "author_id",
      is_new_merchant = "is_new_merchant",
      is_smb_account = "is_smb_account",
      account_create_source = "account_create_source",
      multi_retrieval_tag = "multi_retrieval_tag",
      real_seller_main_category_id = "real_seller_main_category_id",
      ast_30d_pay_order_amt = "ast_30d_pay_order_amt",
      strategy_crowd_info = "strategy_crowd_info",
      enable_fanstop_small_c_live_baosong = '{{' + ab("enable_fanstop_small_c_live_baosong", False) + '}}',
      enable_prerank_mobile_live_support = '{{' + ab("enable_prerank_mobile_live_support", False) + '}}',
      enable_nearline_multi_recall = '{{' + ab("enable_nearline_multi_recall", False) + '}}',
      campaign_type = "campaign_type",
    ) \
    .layer_guaranteed_enricher(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_mobile_hard_photo_guaranteed_preranking = '{{' + ab("enable_mobile_hard_photo_guaranteed_preranking", False) + '}}',
      mobile_hard_photo_guaranteed_preranking_tag = '{{' + ab("mobile_hard_photo_guaranteed_preranking_tag", 200) + '}}',
      enable_mobile_hard_p2l_guaranteed_preranking = '{{' + ab("enable_mobile_hard_p2l_guaranteed_preranking", False) + '}}',
      mobile_hard_p2l_guaranteed_preranking_tag = '{{' + ab("mobile_hard_p2l_guaranteed_preranking_tag", 42) + '}}',
      ensemble_score = "ensemble_score",
      enable_search_retarget_guaranteed_preranking = '{{' + ab("enable_search_retarget_guaranteed_preranking", False) + '}}',
      enable_inner_prerank_search_append = '{{' + ab("enable_inner_prerank_search_append_new", False) + '}}',
      enable_layer_selected_opt = '{{' + ab("enable_layer_selected_opt_live", False) + '}}',
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      account_type = "account_type",
      multi_retrieval_tag = "multi_retrieval_tag",
      follow_userid_list = "follow_userid_list",
      author_id = "author_id",
      campaign_type = "campaign_type",
      enable_inner_white_list_all_guaranteed_preranking = '{{' + ab("enable_inner_white_list_all_guaranteed_preranking", False) + '}}',
      enable_inner_white_list_guaranteed_preranking = '{{' + ab("enable_inner_white_list_guaranteed_preranking", False) + '}}',
    ) \
    .layer_nomal_enricher(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      merchant_prerank_tdm_tag = '{{' + ab("merchant_prerank_tdm_tag", 0) + '}}',
      enable_whole_roi_append = '{{' + ab("enable_whole_roi_append_dragon", False) + '}}',
      enable_whole_roi_append_qhc = '{{' + ab("enable_whole_roi_append_qhc", False) + '}}',
      enable_qcpx_live_prerank = '{{' + ab("enable_qcpx_live_prerank", False) + '}}',
      enable_qcpx_photo_prerank = '{{' + ab("enable_qcpx_photo_prerank", False) + '}}',
      enable_prerank_lsp_append = '{{' + ab("enable_prerank_lsp_append", False) + '}}',
      enable_prerank_lsp_append_skip_follow = '{{' + ab("enable_prerank_lsp_append_skip_follow", False) + '}}',
      enable_prerank_t7_roi_append = '{{' + ab("enable_prerank_t7_roi_append", False) + '}}',
      pr_recall_tag = '{{' + ab("pr_recall_tag", 0) + '}}',
      merchant_prerank_recall_channel_tag = '{{' + ab("merchant_prerank_recall_channel_tag", 0) + '}}',
      enable_layer_selected_opt = '{{' + ab("enable_layer_selected_opt_live", False) + '}}',
      enable_new_cus_support = '{{' + ab("enable_new_cus_support", False) + '}}',
      enable_filter_jili_page = '{{' + ab("enable_filter_jili_page", False) + '}}',
      enable_new_cus_support_v2 = '{{' + ab("enable_new_cus_support_v2", False) + '}}',
      enable_smb_coldstart_support = '{{' + ab("enable_smb_coldstart_support", False) + '}}',
      enable_smb_coldstart_support_two = '{{' + ab("enable_smb_coldstart_support_two", False) + '}}',
      enable_c0c1_support = '{{' + ab("enable_c0c1_support", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      multi_retrieval_tag = "multi_retrieval_tag",
    ) \
    .layer_nondelivery_enricher(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_nondelivery", False) + '}}',
      enable_ensemble_score_model = '{{' + ab("enable_ensemble_score_model", False) + '}}',
      enable_ecom_click_action_retarget = '{{' + ab("enable_ecom_click_action_retarget", False) + '}}',
      enable_ecom_follow_action_retarget = '{{' + ab("enable_ecom_follow_action_retarget", False) + '}}',
      enable_ecom_purchase_action_retarget = '{{' + ab("enable_ecom_purchase_action_retarget", False) + '}}',
      enable_ecom_comment_action_retarget = '{{' + ab("enable_ecom_comment_action_retarget", False) + '}}',
      enable_ecom_zc_interact_author_retarget = '{{' + ab("enable_ecom_zc_interact_author_retarget", False) + '}}',
      enable_ecom_zc_long_play_action_retarget = '{{' + ab("enable_ecom_zc_long_play_action_retarget", False) + '}}',
      enable_ecom_reco_action_author_retarget = '{{' + ab("enable_ecom_reco_action_author_retarget", False) + '}}',
      enable_coldstart_retarget_support = '{{' + ab("enable_coldstart_retarget_support", False) + '}}',
      enable_high_grade_recall_path_support = '{{' + ab("enable_high_grade_recall_path_support", False) + '}}',
      enable_coldstart_author_fans_support = '{{' + ab("enable_coldstart_author_fans_support", False) + '}}',
      enable_inner_sid_support = '{{' + ab("enable_inner_sid_support", False) + '}}',
      enable_inner_t7_roi_support = '{{' + ab("enable_inner_t7_roi_support", False) + '}}',
      enable_fix_click_action_sign = '{{' + ab("enable_fix_click_action_sign", False) + '}}',
      enable_fix_action_cate3_sign = '{{' + ab("enable_fix_action_cate3_sign", False) + '}}',
      enable_ecom_action_cate3_retarget = '{{' + ab("enable_ecom_action_cate3_retarget", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}',
      enable_limit_cate3_nums = '{{' + ab("enable_limit_cate3_nums", False) + '}}',
      cate3_length_threshold = '{{' + ab("cate3_length_threshold", 0) + '}}',
      cate3_time_threshold = '{{' + ab("cate3_time_threshold", 0) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      short_action_up_weight = '{{' + ab("short_action_up_weight", 1.0) + '}}',
      enable_ecom_interest = '{{' + ab("enable_ecom_interest", False) + '}}',
      merchant_prerank_interest_tag = '{{' + ab("merchant_prerank_interest_tag", "") + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
      multi_retrieval_tag = "multi_retrieval_tag",
    ) \
    .layer_operational_enricher(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_layer_selected_opt = '{{' + ab("enable_layer_selected_opt_live", False) + '}}',
      enable_live_prerank_white_baosong = '{{' + ab("enable_live_prerank_white_baosong", False) + '}}',
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_coldstart_unshared_quota_support = '{{' + ab("enable_coldstart_unshared_quota_support", False) + '}}',
      enable_coldstart_author_fans_new_support = '{{' + ab("enable_coldstart_author_fans_new_support", False) + '}}',
      enable_all_coldstart_live_support = '{{' + ab("enable_all_coldstart_live_support", False) + '}}',
    ) \
    .layer_special_support_f1_enricher(
      input_table_name = "inner_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_inner_ceiling_preranking = '{{' + ab("enable_inner_ceiling_preranking", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      live_creative_type = "live_creative_type",
    ) \
    .layer_supplement_enricher(
      input_table_name = "inner_live_item_table",
      enable_prerank_layer_supplement_v2 = '{{' + ab("enable_prerank_layer_supplement_live", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      live_target_exp_name = '{{' + ab("live_target_exp_name", "unknown") + '}}',
    ) \
    .inner_live_guaranteed_sort_enricher(
      input_table_name = "inner_live_item_table",
      enable_inner_live_guaranteed_sort_preranking = '{{' + ab("enable_inner_live_guaranteed_sort_preranking", False) + '}}',
      ad_select_type = "ad_select_type",
    ) \
    .if_("%s %s"%(ab("enable_retarget_enricher_up", False), "== 0")) \
    .prerank_retarget_tag_enricher(
      input_table_name = "inner_live_item_table",
      enable_fix_action_cate3_tag = '{{' + ab("enable_fix_action_cate3_tag", False) + '}}',
      longtime_action_gap = '{{' + ab("longtime_action_gap", 0) + '}}',
      shorttime_action_gap = '{{' + ab("shorttime_action_gap", 0) + '}}',
      enable_sign_click_seller_id = '{{' + ab("enable_sign_click_seller_id", False) + '}}',
      enable_clk_retarget_opt_exp = '{{' + ab("enable_clk_retarget_opt_exp", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_page_tag_fc = '{{' + ab("enable_page_tag_fc", False) + '}}',
      enable_page_tag_fc_rs = '{{' + ab("enable_page_tag_fc_rs", False) + '}}'
    ) \
    .end_if_() \

    return self

  @module()
  def outer_live_rank_score(self):
    current_flow() \
    .outer_auto_cpa_bid_enricher(
      input_table_name = "outer_live_item_table",
      enable_prerank_roas_twin_mcb = '{{' + ab("enable_prerank_roas_twin_mcb", False) + '}}',
      prerank_roas_twin_deep_upper_bound = '{{' + ab("prerank_roas_twin_deep_upper_bound", 100.0) + '}}',
      prerank_roi_mcb_q_init = '{{' + ab("prerank_roi_mcb_q_init", 1.0) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite_price = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite_price", False) + '}}',
      mh_tcpl_auto_cpa_bid_seed_str = '{{' + ab("mh_tcpl_auto_cpa_bid_seed_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_tag", 0) + '}}',
      mh_tcpl_autobid_multiplier_str = '{{' + ab("mh_tcpl_autobid_multiplier_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_price_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_price_tag", 0) + '}}',
      enable_rta_modify_bid = '{{' + ab("enable_rta_modify_bid", False) + '}}',
      rta_modify_bid_up_ratio = '{{' + ab("rta_modify_bid_up_ratio", 1.0) + '}}',
      rta_modify_bid_down_ratio = '{{' + ab("rta_modify_bid_down_ratio", 1.0) + '}}',
      enable_prerank_roas_v202303 = '{{' + ab("enable_prerank_roas_v202303", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_first_day_v202303 = '{{' + ab("enable_prerank_first_day_v202303", False) + '}}',
      pre_sc_k0_ltv1 = '{{' + ab("pre_sc_k0_ltv1", 0.0) + '}}',
      pre_sc_k0_ltv7 = '{{' + ab("pre_sc_k0_ltv7", 0.0) + '}}',
      item_table = "outer_live_item_table",
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_cid_roas_modify_mock_ltv = '{{' + ab("enable_cid_roas_modify_mock_ltv", False) + '}}',
      enable_preranking_cpc_bid_for_ltr = '{{' + ab("enable_preranking_cpc_bid_for_ltr", False) + '}}',
      enable_rta_mcb_dy_bidding = '{{' + ab("enable_rta_mcb_dy_bidding", False) + '}}',
      cid_roas_mock_cvr = '{{' + ab("cid_roas_mock_cvr", 1.0) + '}}',
      cid_roas_mock_ltv = '{{' + ab("cid_roas_mock_ltv", 0.001) + '}}',
    ) \
    .outer_cpm_ltr_enricher(
      input_table_name = "outer_live_item_table",
      enable_cpm_ltr_add_log_bid = '{{' + ab("enable_cpm_ltr_add_log_bid", False) + '}}',
      enable_cpm_ltr_no_bid = '{{' + ab("enable_cpm_ltr_no_bid", False) + '}}',
      enable_rewarded_ltr_use_bid = '{{' + ab("enable_rewarded_ltr_use_bid", False) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      preranking_thorough_bid_price_ratio = '{{' + ab("preranking_thorough_bid_price_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_cpm_ltr_cmd_new='{{' + ab("enable_prerank_cpm_ltr_cmd_new", False) + '}}',
      enable_outer_live_fan_boost = '{{' + ab("enable_outer_live_fan_boost", False) + '}}',
      outer_live_fan_ecpc_exp_tag = '{{' + ab("outer_live_fan_ecpc_exp_tag", "") + '}}',
      enable_prerank_iaa_roas_ltr_support = '{{' + ab("enable_prerank_iaa_roas_ltr_support", False) + '}}',
      bidding_iaa_roas_prerank_ecpm_ltr_ratio = '{{' + ab("bidding_iaa_roas_prerank_ltr_support_ratio", 1.0) + '}}',
      enable_playlet_explore_bid = '{{' + ab("enable_playlet_explore_bid", False) + '}}',
      enable_playlet_explore_bid_iaa = '{{' + ab("enable_playlet_explore_bid_iaa", False) + '}}',
      enable_main_increment_explore_prerank = '{{' + ab("enable_main_increment_explore_prerank", False) + '}}',
      enable_ocpx_increment_explore_prerank = '{{' + ab("enable_ocpx_increment_explore_prerank", False) + '}}',
    ) \
    .outer_delivery_enricher(
      input_table_name = "outer_live_item_table",
      enable_prerank_ctcvr_fix_bid_diff = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff", False) + '}}',
      enable_delivery_rate_ctcvr_new = '{{' + ab("enable_delivery_rate_ctcvr_new", False) + '}}',
      enable_prerank_ctcvr_fix_cpm_accuracy = '{{' + ab("enable_prerank_ctcvr_fix_cpm_accuracy", False) + '}}',
      preranking_thorough_bid_price_ratio = '{{' + ab("preranking_thorough_bid_price_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_ad_roas = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_ad_roas", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      enable_deprecate_outer_prerank_ctcvr_cmd = '{{' + ab("enable_deprecate_outer_prerank_ctcvr_cmd", False) + '}}',
    ) \
    .if_("disable_outer_ecpm == 0") \
    .outer_ecpm_enricher(
      input_table_name = "outer_live_item_table",
      enable_prerank_dsp_live_ab = '{{' + ab("enable_prerank_dsp_live_ab", False) + '}}',
      enable_prerank_purchase_conversion = '{{' + ab("enable_prerank_purchase_conversion", False) + '}}',
      enable_prerank_retention_to_middle_platform = '{{' + ab("enable_prerank_retention_to_middle_platform", False) + '}}',
      enable_high_quality_photo_awake = '{{' + ab("enable_high_quality_photo_awake", False) + '}}',
      high_quality_photo_adjust = '{{' + ab("high_quality_photo_adjust", 1.0) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      enable_prerank_seven_day_pay_times = '{{' + ab("enable_prerank_seven_day_pay_times", False) + '}}',
      enable_prerank_seven_day_pay_times_support = '{{' + ab("enable_prerank_seven_day_pay_times_support", False) + '}}',
      prerank_seven_day_pay_times_support_ratio = '{{' + ab("prerank_seven_day_pay_times_support_ratio", 1.0) + '}}',
      enable_prerank_other_v202305 = '{{' + ab("enable_prerank_other_v202305", False) + '}}',
      enable_prerank_lps_v202305 = '{{' + ab("enable_prerank_lps_v202305", False) + '}}',
      enable_prerank_ecpm2ctcvr = '{{' + ab("enable_prerank_ecpm2ctcvr", False) + '}}',
      enable_prerank_leads_v202505 = '{{' + ab("enable_prerank_leads_v202505", False) + '}}',
      enable_prerank_msg_v202506 = '{{' + ab("enable_prerank_msg_v202506", False) + '}}',
      prerank_ecpm_chain_type ='{{' + ab("prerank_ecpm_chain_type", 0) + '}}',
      enable_prerank_iaa_roas_ecpm_support = '{{' + ab("enable_prerank_iaa_roas_ecpm_support", False) + '}}',
      bidding_iaa_roas_prerank_ecpm_support_ratio = '{{' + ab("bidding_iaa_roas_prerank_ecpm_support_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      enable_playlet_explore_bid = '{{' + ab("enable_playlet_explore_bid", False) + '}}',
      enable_playlet_explore_bid_iaa = '{{' + ab("enable_playlet_explore_bid_iaa", False) + '}}',
      enable_main_increment_explore_prerank = '{{' + ab("enable_main_increment_explore_prerank", False) + '}}',
      enable_ocpx_increment_explore_prerank = '{{' + ab("enable_ocpx_increment_explore_prerank", False) + '}}',
    ) \
    .end_if_() \
    .outer_ensemble_score_enricher(
      input_table_name = "outer_live_item_table",
      prerank_ensemble_ecpm_weight = '{{' + ab("prerank_ensemble_ecpm_weight_kuaishou_feature", 1.2) + '}}',
      prerank_ecpm_score_idx_weight = '{{' + ab("prerank_ecpm_score_idx_weight", 0.0) + '}}',
      prerank_e2e_ensemble_weight = '{{' + ab("prerank_e2e_ensemble_weight", 1.0) + '}}',
      prerank_e2e_score_weight = '{{' + ab("prerank_e2e_score_weight", 0.0) + '}}',
      prerank_e2e_score_idx_weight = '{{' + ab("prerank_e2e_score_idx_weight", 0.0) + '}}',
      prerank_cpm_ltr_ensemble_weight = '{{' + ab("prerank_cpm_ltr_ensemble_weight", 1.0) + '}}',
      prerank_cpm_ltr_score_weight = '{{' + ab("prerank_cpm_ltr_score_weight", 0.0) + '}}',
      prerank_cpm_ltr_score_idx_weight = '{{' + ab("prerank_cpm_ltr_score_idx_weight", 0.0) + '}}',
      enable_fill_prerank_weight = '{{' + ab("enable_fill_prerank_weight", False) + '}}',
      prerank_cpm_ltr_ensemble_weight_new = '{{' + ab("prerank_cpm_ltr_ensemble_weight_new", 1.0) + '}}',
      prerank_e2e_ensemble_weight_new = '{{' + ab("prerank_e2e_ensemble_weight_new", 1.0) + '}}',
      prerank_ensemble_ecpm_weight_kuaishou_nebula = '{{' + ab("prerank_ensemble_ecpm_weight_kuaishou_nebula", 1.5) + '}}',
      prerank_ecpm_ensemble_weight_rewarded = '{{' + ab("prerank_ecpm_ensemble_weight_rewarded", 1.0) + '}}',
      prerank_e2e_ensemble_weight_rewarded = '{{' + ab("prerank_e2e_ensemble_weight_rewarded", 1.0) + '}}',
      prerank_cpm_ltr_ensemble_weight_rewarded = '{{' + ab("prerank_cpm_ltr_ensemble_weight_rewarded", 1.0) + '}}',
      close_outer_live_prerank_delivery_ecpm_rewarded = '{{' + ab("close_outer_live_prerank_delivery_ecpm_rewarded", False) + '}}',
      local_live_prerank_score_bonus = '{{' + ab("local_live_prerank_score_bonus", False) + '}}',
      local_live_prerank_score_bonus_weight = '{{' + ab("local_live_prerank_score_bonus_weight", 1.0) + '}}',
    ) \

    return self

  @module()
  def outer_live_layer_manager(self):
    current_flow() \
    .outer_live_layer_effect_enricher(
      input_table_name = "outer_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      product_select_pid_count = '{{' + ab("product_select_pid_count", 0) + '}}',
      product_select_pid_ratio = '{{' + ab("product_select_pid_ratio", 0.0) + '}}',
      enable_prerank_account_limit_outloop = '{{' + ab("enable_prerank_account_limit_outloop", False) + '}}',
      enable_record_prerank_return_idx = '{{' + ab("enable_record_prerank_return_idx", False) + '}}',
      enable_prerank_industry_live_diversity = '{{' + ab("enable_prerank_industry_live_diversity", False) + '}}',
      enable_prerank_industry_live_limit_outloop = '{{' + ab("enable_prerank_industry_live_limit_outloop", False) + '}}',
      prerank_per_industry_live_ad_num_outloop = '{{' + ab("prerank_per_industry_live_ad_num_outloop", 2) + '}}',
      prerank_per_account_ad_num_outloop = '{{' + ab("prerank_per_account_ad_num_outloop", 5) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
    ) \
    .layer_outer_live_support_enricher(
      input_table_name = "outer_live_item_table",
      input_layer_table_name = "inner_live_layer_table",
      enable_outer_live_guarentee_append = '{{' + ab("enable_outer_live_guarentee_append", False) + '}}',
      enable_outerloop_universe_retarget_prerank = '{{' + ab("enable_outerloop_universe_retarget_prerank", False) + '}}',
      enable_outerloop_nc_retrieval_tag_prerank = '{{' + ab("enable_outerloop_nc_retrieval_tag_prerank", False) + '}}',
    ) \

    return self

  @module()
  def outer_hard_photo_rank_score(self):
    current_flow() \
    .if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_outer == 0 or enable_prerank_rank_ica == 0") \
    .if_("is_rewarded == 0") \
    .dcaf_redis(
      enable_biz_name=True,
      biz_name="outer_hard",
      sub_page_id="sub_page_id",
      version_id = '{{' + ab("dcaf_rank_version_outer_hard", "default") + '}}',
    ) \
    .else_() \
    .dcaf_redis(
      enable_biz_name = '{{' + ab("enable_dcaf_rank_outer_hard_rewarded", False) + '}}',
      biz_name = "outer_hard_rewarded",
      sub_page_id = "sub_page_id",
      version_id = '{{' + ab("dcaf_rank_version_outer_hard_rewarded", "default") + '}}',
    ) \
    .end_if_() \
    .end_if_() \
    .ecpm_ratio_enricher(
      item_table = "outer_hard_photo_item_table",
      enhance_ecpm_strategy_default_tag = '{{' + ab("enhance_ecpm_strategy_default_tag", 0) + '}}',
      enhance_ecpm_strategy_exp_tag = '{{' + ab("enhance_ecpm_strategy_exp_tag", 0) + '}}',
      enhance_ecpm_strategy_exp_ratio_up = '{{' + ab("enhance_ecpm_strategy_exp_ratio_up", 100.0) + '}}',
      enhance_ecpm_strategy_exp_ratio_down = '{{' + ab("enhance_ecpm_strategy_exp_ratio_down", 0.01) + '}}',
      enable_cid_spu_strategy_exp = '{{' + ab("enable_cid_spu_strategy_exp", 0) + '}}',
      enable_cid_corp_default_strategy_exp = '{{' + ab("enable_cid_corp_default_strategy_exp", 0) + '}}',
      cid_corp_default_bid_ratio = '{{' + ab("cid_corp_default_bid_ratio", 1.0) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_cid_bid_boost_roas_exp = '{{' + ab("enable_cid_bid_boost_roas_exp", 0) + '}}',
      enable_purchase_pay_boost_exp = '{{' + ab("enable_purchase_pay_boost_exp", False) + '}}',
      purchase_pay_default_bid_ratio = '{{' + ab("purchase_pay_default_bid_ratio", 1.0) + '}}',
      enable_cid_quality_strategy_exp = '{{' + ab("enable_cid_quality_strategy_exp", 0) + '}}',
      cid_spu_strategy_tag = '{{' + ab("cid_spu_strategy_tag", 0) + '}}',
      cid_quality_strategy_tag = '{{' + ab("cid_quality_strategy_tag", 0) + '}}',
    ) \
    .outer_auto_cpa_bid_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_prerank_deep_min_bid_coef = '{{' + ab("enable_prerank_deep_min_bid_coef", False) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_prerank_fuse_adjust_cpa_bid = '{{' + ab("enable_prerank_fuse_adjust_cpa_bid", False) + '}}',
      enable_prerank_fuse_purchase = '{{' + ab("enable_prerank_fuse_purchase", False) + '}}',
      prerank_fuse_adjust_coef = '{{' + ab("prerank_fuse_adjust_coef", 1.0) + '}}',
      enable_prerank_roas_twin_mcb = '{{' + ab("enable_prerank_roas_twin_mcb", False) + '}}',
      prerank_roas_twin_deep_upper_bound = '{{' + ab("prerank_roas_twin_deep_upper_bound", 100.0) + '}}',
      prerank_roi_mcb_q_init = '{{' + ab("prerank_roi_mcb_q_init", 1.0) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite_price = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite_price", False) + '}}',
      mh_tcpl_auto_cpa_bid_seed_str = '{{' + ab("mh_tcpl_auto_cpa_bid_seed_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_tag", 0) + '}}',
      mh_tcpl_autobid_multiplier_str = '{{' + ab("mh_tcpl_autobid_multiplier_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_price_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_price_tag", 0) + '}}',
      enable_rta_modify_bid = '{{' + ab("enable_rta_modify_bid", False) + '}}',
      rta_modify_bid_up_ratio = '{{' + ab("rta_modify_bid_up_ratio", 1.0) + '}}',
      rta_modify_bid_down_ratio = '{{' + ab("rta_modify_bid_down_ratio", 1.0) + '}}',
      enable_prerank_roas_v202303 = '{{' + ab("enable_prerank_roas_v202303", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_first_day_v202303 = '{{' + ab("enable_prerank_first_day_v202303", False) + '}}',
      pre_sc_k0_ltv1 = '{{' + ab("pre_sc_k0_ltv1", 0.0) + '}}',
      pre_sc_k0_ltv7 = '{{' + ab("pre_sc_k0_ltv7", 0.0) + '}}',
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_cid_roas_modify_mock_ltv = '{{' + ab("enable_cid_roas_modify_mock_ltv", False) + '}}',
      enable_preranking_cpc_bid_for_ltr = '{{' + ab("enable_preranking_cpc_bid_for_ltr", False) + '}}',
      enable_rta_mcb_dy_bidding = '{{' + ab("enable_rta_mcb_dy_bidding", False) + '}}',
      cid_roas_mock_cvr = '{{' + ab("cid_roas_mock_cvr", 1.0) + '}}',
      cid_roas_mock_ltv = '{{' + ab("cid_roas_mock_ltv", 0.001) + '}}',
      enable_prerank_iaa_roas_predict = '{{' + ab("enable_prerank_iaa_roas_predict", False) + '}}',
      enable_prerank_roas_iaap_predict = '{{' + ab("enable_prerank_roas_iaap_predict", False) + '}}',
      prerank_iaap_roas_ad_ltv_default = '{{'+ ab("prerank_iaap_roas_ad_ltv_default", 1.0) + '}}',
      mini_game_prerank_puchase_ltv_ratio = '{{'+ ab("mini_game_prerank_puchase_ltv_ratio", 1.0) + '}}',
      mini_game_prerank_puchase_ltv_bias = '{{'+ ab("mini_game_prerank_puchase_ltv_bias", 0.0) + '}}',
      mini_game_prerank_adv_ltv_ratio = '{{'+ ab("mini_game_prerank_adv_ltv_ratio", 1.0) + '}}',
      mini_game_prerank_adv_ltv_bias = '{{'+ ab("mini_game_prerank_adv_ltv_bias", 0.0) + '}}',
      enable_prerank_iaap_roas_ad_ltv = '{{'+ ab("enable_prerank_iaap_roas_ad_ltv", False) + '}}',
      enable_prerank_iaa_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_roas_ad_ltv", False) + '}}',
      enable_prerank_iaa_7d_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_7d_roas_ad_ltv", False) + '}}',
      enable_prerank_iaap_roas_fix_ltv = '{{' + ab("enable_prerank_iaap_roas_fix_ltv", False) + '}}',
      enable_mini_game_prerank_7r_bid = '{{' + ab("enable_mini_game_prerank_7r_bid", False) + '}}',
      prerank_iaap_roas_ltv_isonitic_ratio = '{{'+ ab("prerank_iaap_roas_ltv_isonitic_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_roas_ceiling = '{{' + ab("enable_prerank_fiction_iaa_roas_ceiling", False) + '}}',
      prerank_fiction_iaa_ceiling_ratio = '{{'+ ab("prerank_fiction_iaa_ceiling_ratio", 1.0) + '}}',
      enable_prerank_fiction_iap_roas_ceiling = '{{' + ab("enable_prerank_fiction_iap_roas_ceiling", False) + '}}',
      prerank_fiction_iap_ceiling_ratio = '{{'+ ab("prerank_fiction_iap_ceiling_ratio", 1.0) + '}}',
      enable_prerank_fiction_iap_not_na_bid = '{{' + ab("enable_prerank_fiction_iap_not_na_bid", False) + '}}',
      prerank_fiction_iap_not_na_bid_ratio = '{{'+ ab("prerank_fiction_iap_not_na_bid_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_not_na_bid = '{{' + ab("enable_prerank_fiction_iaa_not_na_bid", False) + '}}',
      prerank_fiction_iaa_not_na_bid_ratio = '{{'+ ab("prerank_fiction_iaa_not_na_bid_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_soft_ceiling = '{{' + ab("enable_prerank_fiction_iaa_soft_ceiling", False) + '}}',
      prerank_fiction_iaa_soft_ceiling_ratio = '{{'+ ab("prerank_fiction_iaa_soft_ceiling_ratio", 1.0) + '}}',
      enable_prerank_game_iaa_roas_ceiling = '{{' + ab("enable_prerank_game_iaa_roas_ceiling", False) + '}}',
      prerank_game_iaa_ceiling_ratio = '{{' + ab("prerank_game_iaa_ceiling_ratio", 1.0) + '}}',
      enable_prerank_playlet_iaa_predict_cmd = '{{' + ab("enable_prerank_playlet_iaa_predict_cmd", False) + '}}',
      enable_prerank_playlet_iap_predict_cmd_normal = '{{' + ab("enable_prerank_playlet_iap_predict_cmd_normal", False) + '}}',
      prerank_iaa_adjust_coef = '{{' + ab("prerank_iaa_adjust_coef", 1.0) + '}}',
      enable_iap_prerank_ecpc_by_bounus_coef = '{{' + ab("enable_iap_prerank_ecpc_by_bounus_coef", False) + '}}',
      prerank_bonus_coef_range_list_str = '{{' + ab("prerank_bonus_coef_range_list_str", "3,4,1.05;4,5,1.1") + '}}',
      prerank_iaa_7r_adjust_coef = '{{' + ab("prerank_iaa_7r_adjust_coef", 1.0) + '}}',
      prerank_playlet_iap_roas_ltv_cali_ratio = '{{'+ ab("prerank_playlet_iap_roas_ltv_cali_ratio", 1.0) + '}}',
      enable_preank_big_r_explore_iaap = '{{' + ab("enable_preank_big_r_explore_iaap", False) + '}}',
      enable_preank_big_r_explore_iap = '{{' + ab("enable_preank_big_r_explore_iap", False) + '}}',
      enable_preank_big_r_explore_iaa = '{{' + ab("enable_preank_big_r_explore_iaa", False) + '}}',
      prerank_big_r_explore_ratio = '{{' + ab("prerank_big_r_explore_ratio", 1.0) + '}}',
      big_r_exlore_ctrl_group_tag = '{{' + ab("big_r_exlore_ctrl_group_tag", "default") + '}}',
      big_r_exlore_ctrl_dim_tag = '{{' + ab("big_r_exlore_ctrl_dim_tag", "iap") + '}}',
      enable_kgame_big_r_explore_ctrl_pid = '{{' + ab("enable_kgame_big_r_explore_ctrl_pid", False) + '}}',
      enable_mini_game_big_r_ee_strategy = '{{' + ab("enable_mini_game_big_r_ee_strategy", False) + '}}',
      mini_game_big_r_ee_explore_k = '{{'+ ab("mini_game_big_r_ee_explore_k", 1.0) + '}}',
      mini_game_big_r_ee_explore_b = '{{'+ ab("mini_game_big_r_ee_explore_b", 0.0) + '}}',
      enable_mini_game_new_big_r_tag = '{{' + ab("enable_mini_game_new_big_r_tag", False) + '}}',
      prerank_big_r_explore_bias = '{{'+ ab("prerank_big_r_explore_bias", 1.0) + '}}',
      enable_prerank_auto_cpa_bid_exp = '{{'+ ab("enable_prerank_auto_cpa_bid_exp", False) + '}}',
      enable_rank_adlist_cache = '{{' + ab("enable_rank_adlist_cache", False) + '}}',
      enable_rank_adlist_cache_skip_prerank_sort = '{{' + ab("enable_rank_adlist_cache_skip_prerank_sort", False) + '}}',
    ) \
    .if_("enable_rank_adlist_cache == 0 or is_hit_cache == 0 or enable_rank_adlist_cache_skip_prerank_sort == 0") \
    .outer_cpm_ltr_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_cpm_ltr_add_log_bid = '{{' + ab("enable_cpm_ltr_add_log_bid", False) + '}}',
      enable_rewarded_ltr_use_bid = '{{' + ab("enable_rewarded_ltr_use_bid", False) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      preranking_thorough_bid_price_ratio = '{{' + ab("preranking_thorough_bid_price_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_cpm_ltr_cmd_new = '{{' + ab("enable_prerank_cpm_ltr_cmd_new", False) + '}}',
      enable_prerank_iaa_roas_ltr_support = '{{' + ab("enable_prerank_iaa_roas_ltr_support", False) + '}}',
      bidding_iaa_roas_prerank_ecpm_ltr_ratio = '{{' + ab("bidding_iaa_roas_prerank_ltr_support_ratio", 1.0) + '}}',
      enable_playlet_explore_bid = '{{' + ab("enable_playlet_explore_bid", False) + '}}',
      enable_playlet_explore_bid_iaa = '{{' + ab("enable_playlet_explore_bid_iaa", False) + '}}',
      enable_main_increment_explore_prerank = '{{' + ab("enable_main_increment_explore_prerank", False) + '}}',
      enable_ocpx_increment_explore_prerank = '{{' + ab("enable_ocpx_increment_explore_prerank", False) + '}}',
      enable_no_ltv_bid_ltr_rewarded = '{{' + ab("enable_no_ltv_bid_ltr_rewarded", False) + '}}',
      enable_no_ltv_bid_ltr_flow = '{{' + ab("enable_no_ltv_bid_ltr_flow", False) + '}}',
    ) \
    .outer_dnc_ltv_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_cal_dnc_ltv_idx = '{{' + ab("enable_cal_dnc_ltv_idx", False) + '}}',
      enable_prerank_dnc_ltv_up = '{{' + ab("enable_prerank_dnc_ltv_up", False) + '}}',
      enable_prerank_dnc_sort_by_obj_target = '{{' + ab("enable_prerank_dnc_sort_by_obj_target", False) + '}}',
      dnc_ltv_roi_avg_cpa_bid = '{{' + ab("dnc_ltv_roi_avg_cpa_bid", 1000.0) + '}}',
      dnc_ltv_cpm_weight = '{{' + ab("dnc_ltv_cpm_weight", 0.05) + '}}',
      prerank_dnc_ltv_exp_group = '{{' + ab("prerank_dnc_ltv_exp_group", "") + '}}',
      enable_stop_potential_nc_user = '{{' + ab("enable_stop_potential_nc_user", False) + '}}',
      enable_stop_low_active_user = '{{' + ab("enable_stop_low_active_user", False) + '}}',
      enable_dnc_ltr_model = '{{' + ab("enable_dnc_ltr_model", False) + '}}',
      enable_dnc_ltr_bid_aware = '{{' + ab("enable_dnc_ltr_bid_aware", False) + '}}',
    ) \
    .if_("enable_prerank_skip_superfluous_processor_outer_hard == 0") \
    .outer_delivery_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_prerank_ctcvr_fix_bid_diff = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff", False) + '}}',
      enable_delivery_rate_ctcvr_new = '{{' + ab("enable_delivery_rate_ctcvr_new", False) + '}}',
      enable_prerank_llm = '{{' + ab("enable_prerank_llm", False) + '}}',
      enable_prerank_llm_bid = '{{' + ab("enable_prerank_llm_bid", False) + '}}',
      enable_prerank_llm_rw = '{{' + ab("enable_prerank_llm_rw", False) + '}}',
      enable_prerank_llm_bid_rw = '{{' + ab("enable_prerank_llm_bid_rw", False) + '}}',
      enable_prerank_ctcvr_fix_cpm_accuracy = '{{' + ab("enable_prerank_ctcvr_fix_cpm_accuracy", False) + '}}',
      preranking_thorough_bid_price_ratio = '{{' + ab("preranking_thorough_bid_price_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_ad_roas = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_ad_roas", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      enable_deprecate_outer_prerank_ctcvr_cmd='{{' + ab("enable_deprecate_outer_prerank_ctcvr_cmd", False) + '}}',
      enable_outer_prerank_log_use_real_action='{{' + ab("enable_outer_prerank_log_use_real_action", False) + '}}',
      enable_outer_prerank_real_action_log='{{' + ab("enable_outer_prerank_real_action_log", False) + '}}',
      enable_outer_prerank_real_action_log_fix='{{' + ab("enable_outer_prerank_real_action_log_fix", False) + '}}',
    ) \
    .end_if_() \
    .if_("disable_outer_ecpm == 0") \
    .outer_ecpm_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_prerank_dsp_live_ab = '{{' + ab("enable_prerank_dsp_live_ab", False) + '}}',
      enable_prerank_purchase_conversion = '{{' + ab("enable_prerank_purchase_conversion", False) + '}}',
      enable_high_quality_photo_awake = '{{' + ab("enable_high_quality_photo_awake", False) + '}}',
      high_quality_photo_adjust = '{{' + ab("high_quality_photo_adjust", 1.0) + '}}',
      enable_prerank_retention_to_middle_platform = '{{' + ab("enable_prerank_retention_to_middle_platform", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      enable_prerank_seven_day_pay_times = '{{' + ab("enable_prerank_seven_day_pay_times", False) + '}}',
      enable_prerank_seven_day_pay_times_support = '{{' + ab("enable_prerank_seven_day_pay_times_support", False) + '}}',
      prerank_seven_day_pay_times_support_ratio = '{{' + ab("prerank_seven_day_pay_times_support_ratio", 1.0) + '}}',
      enable_prerank_other_v202305 = '{{' + ab("enable_prerank_other_v202305", False) + '}}',
      enable_prerank_lps_v202305 = '{{' + ab("enable_prerank_lps_v202305", False) + '}}',
      enable_prerank_ecpm2ctcvr = '{{' + ab("enable_prerank_ecpm2ctcvr", False) + '}}',
      enable_prerank_leads_v202505 = '{{' + ab("enable_prerank_leads_v202505", False) + '}}',
      enable_prerank_msg_v202506 = '{{' + ab("enable_prerank_msg_v202506", False) + '}}',
      prerank_ecpm_chain_type ='{{' + ab("prerank_ecpm_chain_type", 0) + '}}',
      enable_prerank_iaa_roas_ecpm_support = '{{' + ab("enable_prerank_iaa_roas_ecpm_support", False) + '}}',
      bidding_iaa_roas_prerank_ecpm_support_ratio = '{{' + ab("bidding_iaa_roas_prerank_ecpm_support_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      enable_playlet_explore_bid = '{{' + ab("enable_playlet_explore_bid", False) + '}}',
      enable_playlet_explore_bid_iaa = '{{' + ab("enable_playlet_explore_bid_iaa", False) + '}}',
      enable_main_increment_explore_prerank = '{{' + ab("enable_main_increment_explore_prerank", False) + '}}',
      enable_ocpx_increment_explore_prerank = '{{' + ab("enable_ocpx_increment_explore_prerank", False) + '}}',
      enable_outer_ecpm_to_auto_bid = '{{' + ab("enable_outer_ecpm_to_auto_bid", False) + '}}',
      ) \
    .end_if_() \
    .trigger_item_relative_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_trigger_relative_sort_with_bid = '{{' + ab("enable_trigger_relative_sort_with_bid", False) + '}}',
      enable_ltr_relative_score_ensemble = '{{' + ab("enable_ltr_relative_score_ensemble", False) + '}}',
      enable_ltr_score_sigmoid = '{{' + ab("enable_ltr_score_sigmoid", False) + '}}',
      relative_score_weight = '{{' + ab("relative_score_weight_outer_hard", 0.0) + '}}',
      ltr_score_weight = '{{' + ab("ltr_score_weight_outer_hard", 0.0) + '}}',
      ltr_score_scale = '{{' + ab("ltr_score_scale_outer_hard", 1.0) + '}}',
      prerank_relative_ensemble_exp_tag = '{{' + ab("prerank_relative_ensemble_exp_tag", "") + '}}',
      enable_relative_ltr_inner_soft_fix = '{{' + ab("enable_relative_ltr_inner_soft_fix", False) + '}}',
      enable_relative_score_mul_bid = '{{' + ab("enable_relative_score_mul_bid", False) + '}}',
      enable_relative_score_sigmoid = '{{' + ab("enable_relative_score_sigmoid", False) + '}}',
      relative_score_scale = '{{' + ab("relative_score_scale_outer_hard", 1.0) + '}}',
      enable_inner_explore_ltr_sim_score_calc = '{{' + ab("enable_inner_explore_ltr_sim_score_calc", False) + '}}',
      ) \
    .outer_ensemble_score_enricher(
      input_table_name = "outer_hard_photo_item_table",
      prerank_ensemble_ecpm_weight = '{{' + ab("prerank_ensemble_ecpm_weight_kuaishou_feature", 1.2) + '}}',
      prerank_ecpm_score_idx_weight = '{{' + ab("prerank_ecpm_score_idx_weight", 0.0) + '}}',
      prerank_e2e_ensemble_weight = '{{' + ab("prerank_e2e_ensemble_weight", 1.0) + '}}',
      prerank_e2e_score_weight = '{{' + ab("prerank_e2e_score_weight", 0.0) + '}}',
      prerank_e2e_score_idx_weight = '{{' + ab("prerank_e2e_score_idx_weight", 0.0) + '}}',
      prerank_cpm_ltr_ensemble_weight = '{{' + ab("prerank_cpm_ltr_ensemble_weight", 1.0) + '}}',
      prerank_cpm_ltr_score_weight = '{{' + ab("prerank_cpm_ltr_score_weight", 0.0) + '}}',
      prerank_cpm_ltr_score_idx_weight = '{{' + ab("prerank_cpm_ltr_score_idx_weight", 0.0) + '}}',
      prerank_real_action_weight ='{{' + ab("prerank_real_action_weight", 0.0) + '}}',
      enable_prerank_simple_ensemble = '{{' + ab("enable_prerank_simple_ensemble", False) + '}}',
      enable_prerank_simple_ensemble_v2all ='{{' + ab("enable_prerank_simple_ensemble_v2all", False) + '}}',
      enable_prerank_simple_ensemble_v2only ='{{' + ab("enable_prerank_simple_ensemble_v2only", False) + '}}',
      enable_prerank_no_ecpm = '{{' + ab("enable_prerank_no_ecpm", False) + '}}',
      enable_pure_sort_es = '{{' + ab("enable_pure_sort_es_outer_hard", False) + '}}',
      prerank_llm_ensemble_weight = '{{' + ab("prerank_llm_ensemble_weight", 0.0) + '}}',
      cim_ensemble_mode = '{{' + ab("cim_ensemble_mode", 0) + '}}',
      llm_ensemble_mode = '{{' + ab("llm_ensemble_mode", 0) + '}}',
      cim_ensemble_mode_rw = '{{' + ab("cim_ensemble_mode_rw", 0) + '}}',
      llm_ensemble_mode_rw = '{{' + ab("llm_ensemble_mode_rw", 0) + '}}',
      enable_fill_prerank_weight = '{{' + ab("enable_fill_prerank_weight", False) + '}}',
      enable_prerank_outer_ensemble_sort_new_weight = '{{' + ab("enable_prerank_outer_ensemble_sort_new_weight", False) + '}}',
      prerank_cpm_ltr_ensemble_weight_new = '{{' + ab("prerank_cpm_ltr_ensemble_weight_new", 1.0) + '}}',
      prerank_e2e_ensemble_weight_new = '{{' + ab("prerank_e2e_ensemble_weight_new", 1.0) + '}}',
      enable_hyperbolic_ensemble_score = '{{' + ab("enable_hyperbolic_ensemble_score_outer_hard", False) + '}}',
      enable_hyperbolic_ensemble_score_v2 = '{{' + ab("enable_hyperbolic_ensemble_score_v2_outer_hard", False) + '}}',
      enable_es_v2_quantile = '{{' + ab("enable_es_v2_quantile_outer_hard", False) + '}}',
      es_v2_quantile = '{{' + ab("es_v2_quantile_outer_hard", 0.5) + '}}',
      enable_reward_es_unify = '{{' + ab("enable_reward_es_unify_outer_hard", False) + '}}',
      enable_mean_multiplier = '{{' + ab("enable_mean_multiplier_outer_hard", False) + '}}',
      prerank_ensemble_score_exp_name = '{{' + ab("prerank_ensemble_score_exp_name_outer_hard", "") + '}}',
      prerank_ensemble_score_exp_name_reward = '{{' + ab("prerank_ensemble_score_exp_name_reward_outer_hard", "") + '}}',
      prerank_ensemble_ecpm_weight_kuaishou_nebula = '{{' + ab("prerank_ensemble_ecpm_weight_kuaishou_nebula", 1.5) + '}}',
      prerank_ecpm_ensemble_weight_rewarded = '{{' + ab("prerank_ecpm_ensemble_weight_rewarded", 1.0) + '}}',
      prerank_cim_ensemble_weight_rewarded = '{{' + ab("prerank_cim_ensemble_weight_rewarded", 0.0) + '}}',
      prerank_e2e_ensemble_weight_rewarded = '{{' + ab("prerank_e2e_ensemble_weight_rewarded", 1.0) + '}}',
      prerank_cpm_ltr_ensemble_weight_rewarded = '{{' + ab("prerank_cpm_ltr_ensemble_weight_rewarded", 1.0) + '}}',
      enable_wanhe_prerank_ratio_opt = '{{' + ab("enable_wanhe_prerank_ratio_opt", False) + '}}',
      wanhe_prerank_opt_exp_tag = '{{' + ab("wanhe_prerank_opt_exp_tag", "unknown") + '}}',
      enable_inner_explore_prerank_ratio_opt = '{{' + ab("enable_inner_explore_prerank_ratio_opt", False) + '}}',
      inner_explore_prerank_opt_exp_tag = '{{' + ab("inner_explore_prerank_opt_exp_tag", "unknown") + '}}',
      enable_outerloop_prerank_nc_strategy = '{{' + ab("enable_outerloop_prerank_nc_strategy", False) + '}}',
      enable_outerloop_prerank_nc_strategy_nc_limit = '{{' + ab("enable_outerloop_prerank_nc_strategy_nc_limit", False) + '}}',
      enable_nc_model_adjust_prerank = '{{' + ab("enable_nc_model_adjust_prerank", False) + '}}',
      enable_prerank_dnc_ltv_score = '{{' + ab("enable_prerank_dnc_ltv_score", False) + '}}',
      enable_iaa_one_model = '{{' + ab("enable_iaa_one_model", False) + '}}',
      disable_mini_game_one_model = '{{' + ab("disable_mini_game_one_model", False) + '}}',
      enable_iaa_one_model_dot = '{{' + ab("enable_iaa_one_model_dot", False) + '}}',
      enable_reward_one_model = '{{' + ab("enable_reward_one_model", False) + '}}',
      enable_flow_one_model = '{{' + ab("enable_flow_one_model", False) + '}}',
      enable_dnc_ltv_use_value_score = '{{' + ab("enable_dnc_ltv_use_value_score", False) + '}}',
      enable_prerank_dnc_ltv_use_add = '{{' + ab("enable_prerank_dnc_ltv_use_add", False) + '}}',
      enable_prerank_dnc_model_support_iaa_ocpx = '{{' + ab("enable_prerank_dnc_model_support_iaa_ocpx", False) + '}}',
      enable_prerank_dnc_model_support_all_ocpx = '{{' + ab("enable_prerank_dnc_model_support_all_ocpx", False) + '}}',
      enable_fix_dnc_ltr_model_setting = '{{' + ab("enable_fix_dnc_ltr_model_setting", False) + '}}',
      enable_dnc_ltr_model = '{{' + ab("enable_dnc_ltr_model", False) + '}}',
      enable_nc_model_adjust_prerank_update = '{{' + ab("enable_nc_model_adjust_prerank_update", False) + '}}',
      nc_model_adjust_prerank_ratio_control = '{{' + ab("nc_model_adjust_prerank_ratio_control", 1.0) + '}}',
      prerank_dac_ltv_weight = '{{' + ab("prerank_dac_ltv_weight", 1.0) + '}}',
      prerank_dnc_ltv_weight = '{{' + ab("prerank_dnc_ltv_weight", 1.0) + '}}',
      dnc_ltv_value_bound = '{{' + ab("dnc_ltv_value_bound", 1000.0) + '}}',
      nc_model_adjust_prerank_lower_bound = '{{' + ab("nc_model_adjust_prerank_lower_bound", 1.0) + '}}',
      nc_model_adjust_prerank_upper_bound = '{{' + ab("nc_model_adjust_prerank_upper_bound", 1.2) + '}}',
      enable_outerloop_prerank_nc_strategy_ocpx_limit = '{{' + ab("enable_outerloop_prerank_nc_strategy_ocpx_limit", False) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w1 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w1", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w2 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w2", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w3 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w3", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w4 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w4", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w5 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w5", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_default = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_default", 1.0) + '}}',
      enable_outerloop_prerank_nc_strategy_fix = '{{' + ab("enable_outerloop_prerank_nc_strategy_fix", False) + '}}',
      cpm_ltr_topk_protect_cnt = '{{' + ab("cpm_ltr_topk_protect_outer_hard_photo_cnt", 0) + '}}',
      cpm_ltr_topk_protect_ratio = '{{' + ab("cpm_ltr_topk_protect_outer_hard_photo_ratio", 0.0) + '}}',
      enable_deprecate_prerank_rewarded_ctcvr = '{{' + ab("enable_deprecate_prerank_rewarded_ctcvr", False) + '}}',
      enable_deprecate_prerank_rewarded_ecpm = '{{' + ab("enable_deprecate_prerank_rewarded_ecpm", False) + '}}',
      enable_use_old_es_score_for_value = '{{' + ab("enable_use_old_es_score_for_value_outer_hard", False) + '}}',
      enable_ltr_topk_v2 = '{{' + ab("enable_ltr_topk_v2_outer_hard", False) + '}}',
      ltr_boost_deboost_range = '{{' + ab("ltr_boost_deboost_range_outer_hard", "0.1,0.1") + '}}',
      ltr_boost_deboost_strength = '{{' + ab("ltr_boost_deboost_strength_outer_hard", "1.0,1.0") + '}}',
      enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank", False) + '}}',
      prerank_trigger_relative_score_ensemble_weight = '{{' + ab("prerank_trigger_relative_score_ensemble_weight_outer_hard", 0.0) + '}}',
      trigger_relative_score_request_times_threshold = '{{' + ab("trigger_relative_score_request_times_threshold", 0) + '}}',
      enable_relative_weight_dec_with_request_times = '{{' + ab("enable_relative_weight_dec_with_request_times", False) + '}}',
      enable_prerank_trigger_item_relative_score_boost = '{{' + ab("enable_prerank_trigger_item_relative_score_boost", False) + '}}',
      prerank_trigger_item_relative_score_boost_threshold = '{{' + ab("prerank_trigger_item_relative_score_boost_threshold", 0.0) + '}}',
      prerank_trigger_item_relative_score_boost_ratio = '{{' + ab("prerank_trigger_item_relative_score_boost_ratio_outer_hard", 1.0) + '}}',
      enable_prerank_class_dist_reform = '{{' + ab("enable_prerank_class_dist_reform_outer_hard", False) + '}}',
      enable_class_dist_adp = '{{' + ab("enable_class_dist_adp_outer_hard", False) + '}}',
      prerank_class_dist_reform_param = '{{' + ab("prerank_class_dist_reform_param_outer_hard", "1.0,1.0,1.0,1.0,1.0,1.0,1.0") + '}}',
      enable_hybrid_ltr_path = '{{' + ab("enable_hybrid_ltr_path_outer_hard", False) + '}}',
      enable_hybrid_ltr_bid = '{{' + ab("enable_hybrid_ltr_bid_outer_hard", False) + '}}',
      hybrid_ltr_mul_params = '{{' + ab("hybrid_ltr_mul_params_outer_hard", "1.0,1.0,1.0") + '}}',
      prerank_trigger_item_relative_idx_boost_threshold = '{{' + ab("prerank_trigger_item_relative_idx_boost_threshold", 0) + '}}',
      prerank_inner_explore_relative_boost_ltr_idx = '{{' + ab("prerank_inner_explore_relative_boost_ltr_idx", 0) + '}}',
      enable_inner_explore_fix_boost_outer_ltr_idx = '{{' + ab("enable_inner_explore_fix_boost_outer_ltr_idx", False) + '}}',
      enable_prerank_ensemble_score_presonal_cem = '{{' + ab("enable_prerank_ensemble_score_presonal_cem_outer_hard", False) + '}}',
    ) \
    .end_if_() \

    return self

  @module()
  def outer_soft_photo_rank_score(self):
    current_flow() \
    .if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_outer == 0 or enable_prerank_rank_ica == 0") \
    .dcaf_redis(
      enable_biz_name = '{{' + ab("enable_dcaf_rank_outer_soft", False) + '}}',
      biz_name="outer_soft",
      sub_page_id="sub_page_id",
      version_id = '{{' + ab("dcaf_rank_version_outer_soft", "default") + '}}',
      ) \
    .end_if_() \
    .ecpm_ratio_enricher(
      item_table = "outer_soft_photo_item_table",
      enhance_ecpm_strategy_default_tag = '{{' + ab("enhance_ecpm_strategy_default_tag", 0) + '}}',
      enhance_ecpm_strategy_exp_tag = '{{' + ab("enhance_ecpm_strategy_exp_tag", 0) + '}}',
      enhance_ecpm_strategy_exp_ratio_up = '{{' + ab("enhance_ecpm_strategy_exp_ratio_up", 100.0) + '}}',
      enhance_ecpm_strategy_exp_ratio_down = '{{' + ab("enhance_ecpm_strategy_exp_ratio_down", 0.01) + '}}',
      enable_cid_spu_strategy_exp = '{{' + ab("enable_cid_spu_strategy_exp", 0) + '}}',
      enable_cid_corp_default_strategy_exp = '{{' + ab("enable_cid_corp_default_strategy_exp", 0) + '}}',
      cid_corp_default_bid_ratio = '{{' + ab("cid_corp_default_bid_ratio", 1.0) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_cid_bid_boost_roas_exp = '{{' + ab("enable_cid_bid_boost_roas_exp", 0) + '}}',
      enable_purchase_pay_boost_exp = '{{' + ab("enable_purchase_pay_boost_exp", False) + '}}',
      purchase_pay_default_bid_ratio = '{{' + ab("purchase_pay_default_bid_ratio", 1.0) + '}}',
      enable_cid_quality_strategy_exp = '{{' + ab("enable_cid_quality_strategy_exp", 0) + '}}',
      cid_spu_strategy_tag = '{{' + ab("cid_spu_strategy_tag", 0) + '}}',
      cid_quality_strategy_tag = '{{' + ab("cid_quality_strategy_tag", 0) + '}}',
    ) \
    .outer_auto_cpa_bid_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_prerank_deep_min_bid_coef = '{{' + ab("enable_prerank_deep_min_bid_coef", False) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_prerank_fuse_adjust_cpa_bid = '{{' + ab("enable_prerank_fuse_adjust_cpa_bid", False) + '}}',
      enable_prerank_fuse_purchase = '{{' + ab("enable_prerank_fuse_purchase", False) + '}}',
      prerank_fuse_adjust_coef = '{{' + ab("prerank_fuse_adjust_coef", 1.0) + '}}',
      enable_prerank_roas_twin_mcb = '{{' + ab("enable_prerank_roas_twin_mcb", False) + '}}',
      prerank_roas_twin_deep_upper_bound = '{{' + ab("prerank_roas_twin_deep_upper_bound", 100.0) + '}}',
      prerank_roi_mcb_q_init = '{{' + ab("prerank_roi_mcb_q_init", 1.0) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite_price = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite_price", False) + '}}',
      mh_tcpl_auto_cpa_bid_seed_str = '{{' + ab("mh_tcpl_auto_cpa_bid_seed_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_tag", 0) + '}}',
      mh_tcpl_autobid_multiplier_str = '{{' + ab("mh_tcpl_autobid_multiplier_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_price_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_price_tag", 0) + '}}',
      enable_rta_modify_bid = '{{' + ab("enable_rta_modify_bid", False) + '}}',
      rta_modify_bid_up_ratio = '{{' + ab("rta_modify_bid_up_ratio", 1.0) + '}}',
      rta_modify_bid_down_ratio = '{{' + ab("rta_modify_bid_down_ratio", 1.0) + '}}',
      enable_prerank_roas_v202303 = '{{' + ab("enable_prerank_roas_v202303", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_first_day_v202303 = '{{' + ab("enable_prerank_first_day_v202303", False) + '}}',
      pre_sc_k0_ltv1 = '{{' + ab("pre_sc_k0_ltv1", 0.0) + '}}',
      pre_sc_k0_ltv7 = '{{' + ab("pre_sc_k0_ltv7", 0.0) + '}}',
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_cid_roas_modify_mock_ltv = '{{' + ab("enable_cid_roas_modify_mock_ltv", False) + '}}',
      enable_preranking_cpc_bid_for_ltr = '{{' + ab("enable_preranking_cpc_bid_for_ltr", False) + '}}',
      enable_rta_mcb_dy_bidding = '{{' + ab("enable_rta_mcb_dy_bidding", False) + '}}',
      cid_roas_mock_cvr = '{{' + ab("cid_roas_mock_cvr", 1.0) + '}}',
      cid_roas_mock_ltv = '{{' + ab("cid_roas_mock_ltv", 0.001) + '}}',
      enable_prerank_iaa_roas_predict = '{{' + ab("enable_prerank_iaa_roas_predict", False) + '}}',
      enable_prerank_roas_iaap_predict = '{{' + ab("enable_prerank_roas_iaap_predict", False) + '}}',
      prerank_iaap_roas_ad_ltv_default = '{{'+ ab("prerank_iaap_roas_ad_ltv_default", 1.0) + '}}',
      mini_game_prerank_puchase_ltv_ratio = '{{'+ ab("mini_game_prerank_puchase_ltv_ratio", 1.0) + '}}',
      mini_game_prerank_puchase_ltv_bias = '{{'+ ab("mini_game_prerank_puchase_ltv_bias", 0.0) + '}}',
      mini_game_prerank_adv_ltv_ratio = '{{'+ ab("mini_game_prerank_adv_ltv_ratio", 1.0) + '}}',
      mini_game_prerank_adv_ltv_bias = '{{'+ ab("mini_game_prerank_adv_ltv_bias", 0.0) + '}}',
      enable_prerank_iaap_roas_ad_ltv = '{{'+ ab("enable_prerank_iaap_roas_ad_ltv", False) + '}}',
      enable_prerank_iaa_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_roas_ad_ltv", False) + '}}',
      enable_prerank_iaap_roas_fix_ltv = '{{' + ab("enable_prerank_iaap_roas_fix_ltv", False) + '}}',
      enable_mini_game_prerank_7r_bid = '{{' + ab("enable_mini_game_prerank_7r_bid", False) + '}}',
      prerank_iaap_roas_ltv_isonitic_ratio = '{{'+ ab("prerank_iaap_roas_ltv_isonitic_ratio", 1.0) + '}}',
      enable_prerank_iaa_7d_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_7d_roas_ad_ltv", False) + '}}',
      prerank_iaa_7r_adjust_coef = '{{' + ab("prerank_iaa_7r_adjust_coef", 1.0) + '}}',
      enable_prerank_fiction_iaa_roas_ceiling = '{{' + ab("enable_prerank_fiction_iaa_roas_ceiling", False) + '}}',
      prerank_fiction_iaa_ceiling_ratio = '{{'+ ab("prerank_fiction_iaa_ceiling_ratio", 1.0) + '}}',
      enable_prerank_fiction_iap_roas_ceiling = '{{' + ab("enable_prerank_fiction_iap_roas_ceiling", False) + '}}',
      prerank_fiction_iap_ceiling_ratio = '{{'+ ab("prerank_fiction_iap_ceiling_ratio", 1.0) + '}}',
      enable_prerank_fiction_iap_not_na_bid = '{{' + ab("enable_prerank_fiction_iap_not_na_bid", False) + '}}',
      prerank_fiction_iap_not_na_bid_ratio = '{{'+ ab("prerank_fiction_iap_not_na_bid_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_not_na_bid = '{{' + ab("enable_prerank_fiction_iaa_not_na_bid", False) + '}}',
      prerank_fiction_iaa_not_na_bid_ratio = '{{'+ ab("prerank_fiction_iaa_not_na_bid_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_soft_ceiling = '{{' + ab("enable_prerank_fiction_iaa_soft_ceiling", False) + '}}',
      prerank_fiction_iaa_soft_ceiling_ratio = '{{'+ ab("prerank_fiction_iaa_soft_ceiling_ratio", 1.0) + '}}',
      enable_prerank_game_iaa_roas_ceiling = '{{' + ab("enable_prerank_game_iaa_roas_ceiling", False) + '}}',
      prerank_game_iaa_ceiling_ratio = '{{' + ab("prerank_game_iaa_ceiling_ratio", 1.0) + '}}',
      enable_prerank_playlet_iaa_predict_cmd = '{{' + ab("enable_prerank_playlet_iaa_predict_cmd", False) + '}}',
      enable_prerank_playlet_iap_predict_cmd_normal = '{{' + ab("enable_prerank_playlet_iap_predict_cmd_normal", False) + '}}',
      prerank_iaa_adjust_coef = '{{' + ab("prerank_iaa_adjust_coef", 1.0) + '}}',
      enable_iap_prerank_ecpc_by_bounus_coef = '{{' + ab("enable_iap_prerank_ecpc_by_bounus_coef", False) + '}}',
      prerank_bonus_coef_range_list_str = '{{' + ab("prerank_bonus_coef_range_list_str", "3,4,1.05;4,5,1.1") + '}}',
      prerank_playlet_iap_roas_ltv_cali_ratio = '{{'+ ab("prerank_playlet_iap_roas_ltv_cali_ratio", 1.0) + '}}',
      enable_preank_big_r_explore_iaap = '{{' + ab("enable_preank_big_r_explore_iaap", False) + '}}',
      enable_preank_big_r_explore_iap = '{{' + ab("enable_preank_big_r_explore_iap", False) + '}}',
      enable_preank_big_r_explore_iaa = '{{' + ab("enable_preank_big_r_explore_iaa", False) + '}}',
      prerank_big_r_explore_ratio = '{{' + ab("prerank_big_r_explore_ratio", 1.0) + '}}',
      big_r_exlore_ctrl_group_tag = '{{' + ab("big_r_exlore_ctrl_group_tag", "default") + '}}',
      big_r_exlore_ctrl_dim_tag = '{{' + ab("big_r_exlore_ctrl_dim_tag", "iap") + '}}',
      enable_kgame_big_r_explore_ctrl_pid = '{{' + ab("enable_kgame_big_r_explore_ctrl_pid", False) + '}}',
      enable_mini_game_big_r_ee_strategy = '{{' + ab("enable_mini_game_big_r_ee_strategy", False) + '}}',
      mini_game_big_r_ee_explore_k = '{{'+ ab("mini_game_big_r_ee_explore_k", 1.0) + '}}',
      mini_game_big_r_ee_explore_b = '{{'+ ab("mini_game_big_r_ee_explore_b", 0.0) + '}}',
      enable_mini_game_new_big_r_tag = '{{' + ab("enable_mini_game_new_big_r_tag", False) + '}}',
      prerank_big_r_explore_bias = '{{'+ ab("prerank_big_r_explore_bias", 1.0) + '}}',
      enable_prerank_auto_cpa_bid_exp = '{{'+ ab("enable_prerank_auto_cpa_bid_exp", False) + '}}',
      enable_rank_adlist_cache = '{{' + ab("enable_rank_adlist_cache", False) + '}}',
      enable_rank_adlist_cache_skip_prerank_sort = '{{' + ab("enable_rank_adlist_cache_skip_prerank_sort", False) + '}}',
    ) \
    .if_("enable_rank_adlist_cache == 0 or is_hit_cache == 0 or enable_rank_adlist_cache_skip_prerank_sort == 0") \
    .outer_cpm_ltr_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_cpm_ltr_add_log_bid = '{{' + ab("enable_cpm_ltr_add_log_bid", False) + '}}',
      enable_rewarded_ltr_use_bid = '{{' + ab("enable_rewarded_ltr_use_bid", False) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      preranking_thorough_bid_price_ratio = '{{' + ab("preranking_thorough_bid_price_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_cpm_ltr_cmd_new='{{' + ab("enable_prerank_cpm_ltr_cmd_new", False) + '}}',
      enable_prerank_iaa_roas_ltr_support = '{{' + ab("enable_prerank_iaa_roas_ltr_support", False) + '}}',
      bidding_iaa_roas_prerank_ecpm_ltr_ratio = '{{' + ab("bidding_iaa_roas_prerank_ltr_support_ratio", 1.0) + '}}',
      enable_playlet_explore_bid = '{{' + ab("enable_playlet_explore_bid", False) + '}}',
      enable_playlet_explore_bid_iaa = '{{' + ab("enable_playlet_explore_bid_iaa", False) + '}}',
      enable_main_increment_explore_prerank = '{{' + ab("enable_main_increment_explore_prerank", False) + '}}',
      enable_ocpx_increment_explore_prerank = '{{' + ab("enable_ocpx_increment_explore_prerank", False) + '}}',
      enable_no_ltv_bid_ltr_rewarded = '{{' + ab("enable_no_ltv_bid_ltr_rewarded", False) + '}}',
      enable_no_ltv_bid_ltr_flow = '{{' + ab("enable_no_ltv_bid_ltr_flow", False) + '}}',
    ) \
    .outer_dnc_ltv_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_cal_dnc_ltv_idx = '{{' + ab("enable_cal_dnc_ltv_idx", False) + '}}',
      enable_prerank_dnc_ltv_up = '{{' + ab("enable_prerank_dnc_ltv_up", False) + '}}',
      enable_prerank_dnc_sort_by_obj_target = '{{' + ab("enable_prerank_dnc_sort_by_obj_target", False) + '}}',
      dnc_ltv_roi_avg_cpa_bid = '{{' + ab("dnc_ltv_roi_avg_cpa_bid", 1000.0) + '}}',
      dnc_ltv_cpm_weight = '{{' + ab("dnc_ltv_cpm_weight", 0.05) + '}}',
      prerank_dnc_ltv_exp_group = '{{' + ab("prerank_dnc_ltv_exp_group", "") + '}}',
      enable_stop_potential_nc_user = '{{' + ab("enable_stop_potential_nc_user", False) + '}}',
      enable_stop_low_active_user = '{{' + ab("enable_stop_low_active_user", False) + '}}',
      enable_dnc_ltr_model = '{{' + ab("enable_dnc_ltr_model", False) + '}}',
      enable_dnc_ltr_bid_aware = '{{' + ab("enable_dnc_ltr_bid_aware", False) + '}}',
    ) \
    .if_("enable_prerank_skip_superfluous_processor_outer_soft == 0") \
    .outer_delivery_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_prerank_ctcvr_fix_bid_diff = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff", False) + '}}',
      enable_delivery_rate_ctcvr_new = '{{' + ab("enable_delivery_rate_ctcvr_new", False) + '}}',
      enable_prerank_llm = '{{' + ab("enable_prerank_llm", False) + '}}',
      enable_prerank_llm_bid = '{{' + ab("enable_prerank_llm_bid", False) + '}}',
      enable_prerank_llm_rw = '{{' + ab("enable_prerank_llm_rw", False) + '}}',
      enable_prerank_llm_bid_rw = '{{' + ab("enable_prerank_llm_bid_rw", False) + '}}',
      enable_prerank_ctcvr_fix_cpm_accuracy = '{{' + ab("enable_prerank_ctcvr_fix_cpm_accuracy", False) + '}}',
      preranking_thorough_bid_price_ratio = '{{' + ab("preranking_thorough_bid_price_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_ad_roas = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_ad_roas", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_event_pay_first_day", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_event_7_day_pay_times", False) + '}}',
      enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas = '{{' + ab("enable_prerank_ctcvr_fix_bid_diff_ad_seven_day_roas", False) + '}}',
      enable_deprecate_outer_prerank_ctcvr_cmd='{{' + ab("enable_deprecate_outer_prerank_ctcvr_cmd", False) + '}}',
      enable_outer_prerank_log_use_real_action='{{' + ab("enable_outer_prerank_log_use_real_action", False) + '}}',
      enable_outer_prerank_real_action_log='{{' + ab("enable_outer_prerank_real_action_log", False) + '}}',
      enable_outer_prerank_real_action_log_fix='{{' + ab("enable_outer_prerank_real_action_log_fix", False) + '}}',
    ) \
    .end_if_() \
    .trigger_item_relative_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_prerank_trigger_item_relative_score_cmd = '{{' + ab("enable_prerank_trigger_item_relative_score_cmd", False) + '}}',
      enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank", False) + '}}',
      enable_prerank_valid_hetu_emb_req_relative = '{{' + ab("enable_prerank_valid_hetu_emb_req_relative", False) + '}}',
      enable_prerank_zhuanqian_sta = '{{' + ab("enable_prerank_zhuanqian_sta", False) + '}}',
      enable_trigger_relative_sort_with_bid = '{{' + ab("enable_trigger_relative_sort_with_bid", False) + '}}',
      enable_ltr_relative_score_ensemble = '{{' + ab("enable_ltr_relative_score_ensemble", False) + '}}',
      enable_ltr_score_sigmoid = '{{' + ab("enable_ltr_score_sigmoid", False) + '}}',
      relative_score_weight = '{{' + ab("relative_score_weight_outer_soft", 0.0) + '}}',
      ltr_score_weight = '{{' + ab("ltr_score_weight_outer_soft", 0.0) + '}}',
      ltr_score_scale = '{{' + ab("ltr_score_scale_outer_soft", 1.0) + '}}',
      prerank_relative_ensemble_exp_tag = '{{' + ab("prerank_relative_ensemble_exp_tag", "") + '}}',
      enable_relative_ltr_inner_soft_fix = '{{' + ab("enable_relative_ltr_inner_soft_fix", False) + '}}',
      enable_relative_score_mul_bid = '{{' + ab("enable_relative_score_mul_bid", False) + '}}',
      enable_relative_score_sigmoid = '{{' + ab("enable_relative_score_sigmoid", False) + '}}',
      relative_score_scale = '{{' + ab("relative_score_scale_outer_soft", 1.0) + '}}',
      enable_inner_explore_ltr_sim_score_calc = '{{' + ab("enable_inner_explore_ltr_sim_score_calc", False) + '}}',
    ) \
    .outer_ecpm_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_prerank_dsp_live_ab = '{{' + ab("enable_prerank_dsp_live_ab", False) + '}}',
      enable_prerank_purchase_conversion = '{{' + ab("enable_prerank_purchase_conversion", False) + '}}',
      enable_high_quality_photo_awake = '{{' + ab("enable_high_quality_photo_awake", False) + '}}',
      high_quality_photo_adjust = '{{' + ab("high_quality_photo_adjust", 1.0) + '}}',
      enable_prerank_retention_to_middle_platform = '{{' + ab("enable_prerank_retention_to_middle_platform", False) + '}}',
      bidding_7days_pay_times_prerank_ratio = '{{' + ab("bidding_7days_pay_times_prerank_ratio", 1.0) + '}}',
      enable_bidding_7days_pay_times_prerank = '{{' + ab("enable_bidding_7days_pay_times_prerank", False) + '}}',
      enable_prerank_seven_day_pay_times = '{{' + ab("enable_prerank_seven_day_pay_times", False) + '}}',
      enable_prerank_seven_day_pay_times_support = '{{' + ab("enable_prerank_seven_day_pay_times_support", False) + '}}',
      prerank_seven_day_pay_times_support_ratio = '{{' + ab("prerank_seven_day_pay_times_support_ratio", 1.0) + '}}',
      enable_prerank_other_v202305 = '{{' + ab("enable_prerank_other_v202305", False) + '}}',
      enable_prerank_lps_v202305 = '{{' + ab("enable_prerank_lps_v202305", False) + '}}',
      enable_prerank_ecpm2ctcvr = '{{' + ab("enable_prerank_ecpm2ctcvr", False) + '}}',
      enable_prerank_leads_v202505 = '{{' + ab("enable_prerank_leads_v202505", False) + '}}',
      enable_prerank_msg_v202506 = '{{' + ab("enable_prerank_msg_v202506", False) + '}}',
      prerank_ecpm_chain_type ='{{' + ab("prerank_ecpm_chain_type", 0) + '}}',
      enable_prerank_iaa_roas_ecpm_support = '{{' + ab("enable_prerank_iaa_roas_ecpm_support", False) + '}}',
      bidding_iaa_roas_prerank_ecpm_support_ratio = '{{' + ab("bidding_iaa_roas_prerank_ecpm_support_ratio", 1.0) + '}}',
      prerank_bs_bid_ratio_outer = '{{' + ab("prerank_bs_bid_ratio_outer", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_live = '{{' + ab("prerank_bs_bid_ratio_outer_live", 1.0) + '}}',
      prerank_bs_bid_ratio_outer_soft = '{{' + ab("prerank_bs_bid_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_bs_outer = '{{' + ab("enable_prerank_bs_outer", False) + '}}',
      enable_playlet_explore_bid = '{{' + ab("enable_playlet_explore_bid", False) + '}}',
      enable_playlet_explore_bid_iaa = '{{' + ab("enable_playlet_explore_bid_iaa", False) + '}}',
      enable_main_increment_explore_prerank = '{{' + ab("enable_main_increment_explore_prerank", False) + '}}',
      enable_ocpx_increment_explore_prerank = '{{' + ab("enable_ocpx_increment_explore_prerank", False) + '}}',
      enable_outer_ecpm_to_auto_bid = '{{' + ab("enable_outer_ecpm_to_auto_bid", False) + '}}',
      ) \
    .outer_ensemble_score_enricher(
      input_table_name = "outer_soft_photo_item_table",
      prerank_ensemble_ecpm_weight = '{{' + ab("prerank_ensemble_ecpm_weight_kuaishou_feature", 1.2) + '}}',
      prerank_ecpm_score_idx_weight = '{{' + ab("prerank_ecpm_score_idx_weight", 0.0) + '}}',
      prerank_e2e_ensemble_weight = '{{' + ab("prerank_e2e_ensemble_weight", 1.0) + '}}',
      prerank_e2e_score_weight = '{{' + ab("prerank_e2e_score_weight", 0.0) + '}}',
      prerank_e2e_score_idx_weight = '{{' + ab("prerank_e2e_score_idx_weight", 0.0) + '}}',
      prerank_cpm_ltr_ensemble_weight = '{{' + ab("prerank_cpm_ltr_ensemble_weight", 1.0) + '}}',
      prerank_cpm_ltr_score_weight = '{{' + ab("prerank_cpm_ltr_score_weight", 0.0) + '}}',
      prerank_cpm_ltr_score_idx_weight = '{{' + ab("prerank_cpm_ltr_score_idx_weight", 0.0) + '}}',
      prerank_real_action_weight = '{{' + ab("prerank_real_action_weight", 0.0) + '}}',
      enable_prerank_simple_ensemble = '{{' + ab("enable_prerank_simple_ensemble", False) + '}}',
      enable_prerank_simple_ensemble_v2all ='{{' + ab("enable_prerank_simple_ensemble_v2all", False) + '}}',
      enable_prerank_simple_ensemble_v2only ='{{' + ab("enable_prerank_simple_ensemble_v2only", False) + '}}',
      enable_prerank_no_ecpm = '{{' + ab("enable_prerank_no_ecpm", False) + '}}',
      enable_pure_sort_es = '{{' + ab("enable_pure_sort_es_outer_soft", False) + '}}',
      prerank_llm_ensemble_weight = '{{' + ab("prerank_llm_ensemble_weight", 0.0) + '}}',
      cim_ensemble_mode = '{{' + ab("cim_ensemble_mode", 0) + '}}',
      llm_ensemble_mode = '{{' + ab("llm_ensemble_mode", 0) + '}}',
      cim_ensemble_mode_rw = '{{' + ab("cim_ensemble_mode_rw", 0) + '}}',
      llm_ensemble_mode_rw = '{{' + ab("llm_ensemble_mode_rw", 0) + '}}',
      enable_hyperbolic_ensemble_score = '{{' + ab("enable_hyperbolic_ensemble_score_outer_soft", False) + '}}',
      enable_hyperbolic_ensemble_score_v2 = '{{' + ab("enable_hyperbolic_ensemble_score_v2_outer_soft", False) + '}}',
      enable_es_v2_quantile = '{{' + ab("enable_es_v2_quantile_outer_soft", False) + '}}',
      es_v2_quantile = '{{' + ab("es_v2_quantile_outer_soft", 0.5) + '}}',
      enable_reward_es_unify = '{{' + ab("enable_reward_es_unify_outer_soft", False) + '}}',
      enable_mean_multiplier = '{{' + ab("enable_mean_multiplier_outer_soft", False) + '}}',
      prerank_ensemble_score_exp_name = '{{' + ab("prerank_ensemble_score_exp_name_outer_soft", "") + '}}',
      prerank_ensemble_score_exp_name_reward = '{{' + ab("prerank_ensemble_score_exp_name_reward_outer_soft", "") + '}}',
      enable_fill_prerank_weight = '{{' + ab("enable_fill_prerank_weight", False) + '}}',
      enable_prerank_outer_ensemble_sort_new_weight = '{{' + ab("enable_prerank_outer_ensemble_sort_new_weight", False) + '}}',
      prerank_cpm_ltr_ensemble_weight_new = '{{' + ab("prerank_cpm_ltr_ensemble_weight_new", 1.0) + '}}',
      prerank_e2e_ensemble_weight_new = '{{' + ab("prerank_e2e_ensemble_weight_new", 1.0) + '}}',
      prerank_ensemble_ecpm_weight_kuaishou_nebula = '{{' + ab("prerank_ensemble_ecpm_weight_kuaishou_nebula", 1.5) + '}}',
      prerank_ecpm_ensemble_weight_rewarded = '{{' + ab("prerank_ecpm_ensemble_weight_rewarded", 1.0) + '}}',
      prerank_cim_ensemble_weight_rewarded = '{{' + ab("prerank_cim_ensemble_weight_rewarded", 0.0) + '}}',
      prerank_e2e_ensemble_weight_rewarded = '{{' + ab("prerank_e2e_ensemble_weight_rewarded", 1.0) + '}}',
      prerank_cpm_ltr_ensemble_weight_rewarded = '{{' + ab("prerank_cpm_ltr_ensemble_weight_rewarded", 1.0) + '}}',
      enable_outerloop_prerank_nc_strategy = '{{' + ab("enable_outerloop_prerank_nc_strategy", False) + '}}',
      enable_outerloop_prerank_nc_strategy_nc_limit = '{{' + ab("enable_outerloop_prerank_nc_strategy_nc_limit", False) + '}}',
      enable_nc_model_adjust_prerank = '{{' + ab("enable_nc_model_adjust_prerank", False) + '}}',
      enable_prerank_dnc_ltv_score = '{{' + ab("enable_prerank_dnc_ltv_score", False) + '}}',
      enable_iaa_one_model = '{{' + ab("enable_iaa_one_model", False) + '}}',
      disable_mini_game_one_model = '{{' + ab("disable_mini_game_one_model", False) + '}}',
      enable_iaa_one_model_dot = '{{' + ab("enable_iaa_one_model_dot", False) + '}}',
      enable_reward_one_model = '{{' + ab("enable_reward_one_model", False) + '}}',
      enable_flow_one_model = '{{' + ab("enable_flow_one_model", False) + '}}',
      enable_dnc_ltv_use_value_score = '{{' + ab("enable_dnc_ltv_use_value_score", False) + '}}',
      enable_prerank_dnc_ltv_use_add = '{{' + ab("enable_prerank_dnc_ltv_use_add", False) + '}}',
      enable_fix_dnc_ltr_model_setting = '{{' + ab("enable_fix_dnc_ltr_model_setting", False) + '}}',
      enable_dnc_ltr_model = '{{' + ab("enable_dnc_ltr_model", False) + '}}',
      enable_prerank_dnc_model_support_iaa_ocpx = '{{' + ab("enable_prerank_dnc_model_support_iaa_ocpx", False) + '}}',
      enable_prerank_dnc_model_support_all_ocpx = '{{' + ab("enable_prerank_dnc_model_support_all_ocpx", False) + '}}',
      enable_nc_model_adjust_prerank_update = '{{' + ab("enable_nc_model_adjust_prerank_update", False) + '}}',
      nc_model_adjust_prerank_ratio_control = '{{' + ab("nc_model_adjust_prerank_ratio_control", 1.0) + '}}',
      prerank_dac_ltv_weight = '{{' + ab("prerank_dac_ltv_weight", 1.0) + '}}',
      prerank_dnc_ltv_weight = '{{' + ab("prerank_dnc_ltv_weight", 1.0) + '}}',
      dnc_ltv_value_bound = '{{' + ab("dnc_ltv_value_bound", 1000.0) + '}}',
      nc_model_adjust_prerank_lower_bound = '{{' + ab("nc_model_adjust_prerank_lower_bound", 1.0) + '}}',
      nc_model_adjust_prerank_upper_bound = '{{' + ab("nc_model_adjust_prerank_upper_bound", 1.2) + '}}',
      enable_outerloop_prerank_nc_strategy_ocpx_limit = '{{' + ab("enable_outerloop_prerank_nc_strategy_ocpx_limit", False) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w1 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w1", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w2 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w2", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w3 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w3", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w4 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w4", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_w5 = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_w5", 1.0) + '}}',
      prerank_real_action_weight_ratio_nc_strategy_default = '{{' + ab("prerank_real_action_weight_ratio_nc_strategy_default", 1.0) + '}}',
      enable_outerloop_prerank_nc_strategy_fix = '{{' + ab("enable_outerloop_prerank_nc_strategy_fix", False) + '}}',
      cpm_ltr_topk_protect_cnt = '{{' + ab("cpm_ltr_topk_protect_outer_soft_photo_cnt", 0) + '}}',
      cpm_ltr_topk_protect_ratio = '{{' + ab("cpm_ltr_topk_protect_outer_soft_photo_ratio", 0.0) + '}}',
      enable_use_old_es_score_for_value = '{{' + ab("enable_use_old_es_score_for_value_outer_soft", False) + '}}',
      enable_ltr_topk_v2 = '{{' + ab("enable_ltr_topk_v2_outer_soft", False) + '}}',
      ltr_boost_deboost_range = '{{' + ab("ltr_boost_deboost_range_outer_soft", "0.1,0.1") + '}}',
      ltr_boost_deboost_strength = '{{' + ab("ltr_boost_deboost_strength_outer_soft", "1.0,1.0") + '}}',
      enable_trigger_item_relative_score_prerank = '{{' + ab("enable_trigger_item_relative_score_prerank", False) + '}}',
      prerank_trigger_relative_score_ensemble_weight = '{{' + ab("prerank_trigger_relative_score_ensemble_weight_outer_soft", 0.0) + '}}',
      trigger_relative_score_request_times_threshold = '{{' + ab("trigger_relative_score_request_times_threshold", 0) + '}}',
      enable_relative_score_hc_ratio_prerank = '{{' + ab("enable_relative_score_hc_ratio_prerank", False) + '}}',
      enable_up_refresh_relative_hc_only = '{{' + ab("enable_up_refresh_relative_hc_only", False) + '}}',
      enable_relative_weight_dec_with_request_times = '{{' + ab("enable_relative_weight_dec_with_request_times", False) + '}}',
      enable_prerank_trigger_item_relative_score_boost = '{{' + ab("enable_prerank_trigger_item_relative_score_boost", False) + '}}',
      prerank_trigger_item_relative_score_boost_threshold = '{{' + ab("prerank_trigger_item_relative_score_boost_threshold", 0.0) + '}}',
      prerank_trigger_item_relative_score_boost_ratio = '{{' + ab("prerank_trigger_item_relative_score_boost_ratio_outer_soft", 1.0) + '}}',
      enable_prerank_class_dist_reform = '{{' + ab("enable_prerank_class_dist_reform_outer_soft", False) + '}}',
      enable_class_dist_adp = '{{' + ab("enable_class_dist_adp_outer_soft", False) + '}}',
      prerank_class_dist_reform_param = '{{' + ab("prerank_class_dist_reform_param_outer_soft", "1.0,1.0,1.0,1.0,1.0,1.0,1.0") + '}}',
      enable_hybrid_ltr_path = '{{' + ab("enable_hybrid_ltr_path_outer_soft", False) + '}}',
      enable_hybrid_ltr_bid = '{{' + ab("enable_hybrid_ltr_bid_outer_soft", False) + '}}',
      hybrid_ltr_mul_params = '{{' + ab("hybrid_ltr_mul_params_outer_soft", "1.0,1.0,1.0") + '}}',
      prerank_trigger_item_relative_idx_boost_threshold = '{{' + ab("prerank_trigger_item_relative_idx_boost_threshold", 0) + '}}',
      prerank_inner_explore_relative_boost_ltr_idx = '{{' + ab("prerank_inner_explore_relative_boost_ltr_idx", 0) + '}}',
      enable_inner_explore_fix_boost_outer_ltr_idx = '{{' + ab("enable_inner_explore_fix_boost_outer_ltr_idx", False) + '}}',
      enable_prerank_ensemble_score_presonal_cem = '{{' + ab("enable_prerank_ensemble_score_presonal_cem_outer_soft", False) + '}}',
    ) \
    .end_if_() \
    .if_("%s %s"%(ab("enable_write_info_to_redis", False, biz_name = "THANOS_RECO"), "== 1")) \
    .write_info_to_redis_enricher(
      input_table_name = "outer_soft_photo_item_table",
      info_to_redis_cluster_name = '{{' + ab("info_to_redis_cluster_name", "recoAdNativeCoopStrategy") + '}}',
      info_to_redis_ttl = '{{' + ab("info_to_redis_ttl", 259200) + '}}',
      info_to_redis_max_photo_id_num = '{{' + ab("info_to_redis_max_photo_id_num", 500) + '}}',
      info_to_redis_key_prefix = '{{' + ab("info_to_redis_key_prefix", "a4rout_") + '}}',
    ) \
    .end_if_() \

    return self

  @module()
  def outer_photo_layer_manager(self):
    current_flow() \
    .if_("enable_skip_dcaf_module == 0 or enable_prerank_rank_ica_outer == 0 or enable_prerank_rank_ica == 0") \
    .if_("is_rewarded == 0") \
    .dcaf(
      enable_explore = "enable_rank_quota_explore_outer_hard",
      dcaf_degrade_ratio = '{{' + ab("dcaf_degrade_ratio_outer", 1.0) + '}}',
      version_id = '{{' + ab("dcaf_rank_version_outer_hard", "default") + '}}',
      disable_dcaf_with_constant = '{{' + ab("disable_dcaf_with_constant", False) + '}}',
      enable_rank_rt_exp_quota = '{{' + ab("enable_rank_rt_exp_quota", False) + '}}',
      enable_biz_name = True,
      biz_name = "outer_hard",
      fake_type = "fake_type",
    ) \
    .dcaf(
    enable_explore = "enable_rank_quota_explore_outer_soft",
    dcaf_degrade_ratio = '{{' + ab("dcaf_degrade_ratio_outer", 1.0) + '}}',
    version_id = '{{' + ab("dcaf_rank_version_outer_soft", "default") + '}}',
    disable_dcaf_with_constant = '{{' + ab("disable_dcaf_with_constant", False) + '}}',
    enable_rank_rt_exp_quota = '{{' + ab("enable_rank_rt_exp_quota", False) + '}}',
    enable_biz_name = '{{' + ab("enable_dcaf_rank_outer_soft", False) + '}}',
    biz_name = "outer_soft",
    fake_type = "fake_type",
    ) \
    .else_() \
    .dcaf(
      enable_explore = "enable_rank_quota_explore_outer_hard_rewarded",
      dcaf_degrade_ratio = '{{' + ab("dcaf_degrade_ratio_outer", 1.0) + '}}',
      version_id = '{{' + ab("dcaf_rank_version_outer_hard_rewarded", "default") + '}}',
      disable_dcaf_with_constant = '{{' + ab("disable_dcaf_with_constant", False) + '}}',
      enable_rank_rt_exp_quota = '{{' + ab("enable_rank_rt_exp_quota", False) + '}}',
      enable_biz_name = '{{' + ab("enable_dcaf_rank_outer_hard_rewarded", False) + '}}',
      biz_name = "outer_hard_rewarded",
      fake_type = "fake_type",
    ) \
    .end_if_() \
    .end_if_() \
    .outer_photo_layer_manager_enricher(
      enable_rewarded_dcaf = '{{' + ab("enable_dcaf_rank_outer_hard_rewarded", False) + '}}',
      enable_soft_dcaf = '{{' + ab("enable_dcaf_rank_outer_soft", False) + '}}',
      enable_unify_outer_hard_truncate = '{{' + ab("enable_unify_outer_hard_truncate", False) + '}}',
      enable_user_group_quota_strategy = '{{' + ab("enable_user_group_quota_strategy", False) + '}}',
      enable_user_quota_for_inner = '{{' + ab("enable_user_quota_for_inner", False) + '}}',
      layer_table_name = "outer_photo_layer_table",
      append_ab_tag = '{{' + ab("prerank_append_strategy", "base") + '}}',
      new_append_ab_tag = '{{' + ab("new_prerank_append_strategy", "base") + '}}',
      user_quota_tag = '{{' + ab("user_quota_tag", "") + '}}',
      append_ab_tag_rewarded = '{{' + ab("prerank_append_strategy_rewarded", "") + '}}',
      soft_append_ab_tag = '{{' + ab("prerank_append_stretagy_outer_soft_photo", "outer_soft_append") + '}}',
      feed_degrade_ab = '{{' + ab("feed_degrade_ab", False) + '}}',
      outer_soft_rank_base_line_num = '{{' + ab("outer_soft_rank_base_line_num", 0) + '}}',
      outer_soft_rank_base_line_num_v2 = '{{' + ab("outer_soft_rank_base_line_num_v2", 0) + '}}',
      outer_soft_rank_base_line_num_v3 = '{{' + ab("outer_soft_rank_base_line_num_v3", 0) + '}}',
      enable_outer_soft_quota_v3 = '{{' + ab("enable_outer_soft_quota_v3", False) + '}}',
      outer_hard_main_upper = '{{' + ab("outer_hard_main_upper", 300) + '}}',
      outer_hard_degrade_upper = '{{' + ab("outer_hard_degrade_upper", 300) + '}}',
      outer_soft_main_upper = '{{' + ab("outer_soft_main_upper", 300) + '}}',
      rewarded_max_prerank_num = '{{' + ab("rewarded_max_prerank_num", 0) + '}}',
      outer_hard_minus_quota_num = '{{' + ab("outer_hard_minus_quota_num", 0) + '}}',
      rank_ad_size_reveal_config = '{{' + ab("rank_ad_size_reveal_config", "default") + '}}',
      enable_not_drop_down = '{{' + ab("enable_not_drop_down", False) + '}}',
      drop_down_invert_ab = '{{' + ab("drop_down_invert_ab", False) + '}}',
      enable_all_outer_drop_down = '{{' + ab("enable_all_outer_drop_down", False) + '}}',
      enable_expand_quota_degrade = '{{' + ab("enable_expand_quota_degrade", False) + '}}',
      enable_append_quota_tag = '{{' + ab("enable_append_quota_tag", False) + '}}',
      enable_outer_photo_expand_ratio = '{{' + ab("enable_outer_photo_expand_ratio", False) + '}}',
      drop_down_invert_ratio = '{{' + ab("drop_down_invert_ratio", 1.0) + '}}',
      quota_ratio_outer_hard = '{{' + ab("quota_ratio_outer_hard", 1.0) + '}}',
      quota_ratio_outer_soft = '{{' + ab("quota_ratio_outer_soft", 1.0) + '}}',
      rank_quota_exp_name_outer_hard = '{{' + ab("rank_quota_exp_name_outer_hard", "") + '}}',
      rank_quota_exp_name_outer_soft = '{{' + ab("rank_quota_exp_name_outer_soft", "") + '}}',
      quota_limit_ratio = '{{' + ab("quota_limit_ratio_outer_hard", 1.0) + '}}',
      quota_limit_ratio_soft = '{{' + ab("quota_limit_ratio_outer_soft", 1.0) + '}}',
      enable_quota_limit = '{{' + ab("enable_quota_limit_outer_hard", False) + '}}',
      enable_quota_limit_new = '{{' + ab("enable_quota_limit_new_outer_hard", False) + '}}',
      enable_quota_limit_by_hour = '{{' + ab("enable_quota_limit_by_hour", False) + '}}',
      quota_limit_ratio_exp_tag = '{{' + ab("quota_limit_ratio_exp_tag", "") + '}}',
      enable_expand_ratio_cliff = '{{' + ab("enable_expand_ratio_cliff_outer", False) + '}}',
      is_quit_expand_cliff_outer_soft = '{{' + ab("is_quit_expand_cliff_outer_soft", False) + '}}',
      outer_photo_expand_cliff_ratio = '{{' + ab("outer_photo_expand_cliff_ratio", 1.0) + '}}',
      enable_expand_ratio_cliff_page = '{{' + ab("enable_expand_ratio_cliff_page_outer", False) + '}}',
      expand_ratio_cliff_page_str = '{{' + ab("expand_ratio_cliff_page_str_outer", "") + '}}',
      enable_outer_expand_efficiency_test = '{{' + ab("enable_outer_expand_efficiency_test", False) + '}}',
      enable_outer_hard_minus_quota_test = '{{' + ab("enable_outer_hard_minus_quota_test", False) + '}}',
      outer_expand_efficiency_test_ratio = '{{' + ab("outer_expand_efficiency_test_ratio", 1.0) + '}}',
      quota_limit_ratio_feed_explore = '{{' + ab("quota_limit_ratio_oh_feed_explore", 1.0) + '}}',
      enable_quota_limit_feed_explore = '{{' + ab("enable_quota_limit_oh_feed_explore", False) + '}}',
      enable_prerank_local_life_dui_tou_quota_limit = '{{' + ab("enable_prerank_local_life_dui_tou_quota_limit", False) + '}}',
      enable_prerank_rank_ica_outer = '{{' + ab("enable_prerank_rank_ica_outer", False) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      none_one_model_quota_drop_down_ratio = '{{' + ab("none_one_model_quota_drop_down_ratio_outer", 1.0) + '}}'
    ) \
    .outer_photo_layer_effect_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      layer_name = "OuterHardPhotoEffect",
      product_select_pid_count = '{{' + ab("product_select_pid_count", 0) + '}}',
      product_select_pid_ratio = '{{' + ab("product_select_pid_ratio", 0.0) + '}}',
      enable_prerank_account_limit_outloop = '{{' + ab("enable_prerank_account_limit_outloop", False) + '}}',
      enable_record_prerank_return_idx = '{{' + ab("enable_record_prerank_return_idx", False) + '}}',
      enable_prerank_industry_live_diversity = '{{' + ab("enable_prerank_industry_live_diversity", False) + '}}',
      enable_prerank_industry_live_limit_outloop = '{{' + ab("enable_prerank_industry_live_limit_outloop", False) + '}}',
      prerank_per_industry_live_ad_num_outloop = '{{' + ab("prerank_per_industry_live_ad_num_outloop", 2) + '}}',
      prerank_per_account_ad_num_outloop = '{{' + ab("prerank_per_account_ad_num_outloop", 5) + '}}',
      prerank_nextstay_threshold_discount = '{{' + ab("prerank_nextstay_threshold_discount", 1.0) + '}}',
      extra_outer_hard_photo_effect_quota = '{{' + ab("extra_outer_hard_photo_effect_quota", 8) + '}}',
      prerank_nextstay_threshold_adjust_ratio = '{{' + ab("prerank_nextstay_threshold_adjust_ratio", 1.0) + '}}',
      enable_use_ensemble_score_for_product_select_strategy='{{' + \
          ab("enable_use_ensemble_score_for_product_select_strategy", False) + '}}',
      enable_use_ltr_score_for_product_select_strategy='{{' + \
          ab("enable_use_ltr_score_for_product_select_strategy", False) + '}}',
      enable_use_interest_score_for_product_select_strategy='{{' + \
          ab("enable_use_interest_score_for_product_select_strategy", False) + '}}',
      disable_prerank_nextstay_filter='{{' + \
          ab("disable_prerank_nextstay_filter2", False) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      disable_prerank_nextstay_filter_ocpm='{{' + \
          ab("disable_prerank_nextstay_filter_ocpm", False) + '}}',
      enable_close_threshold_filter = '{{' + ab("enable_close_threshold_filter_outer_hard", False) + '}}',
    ) \
    .outer_photo_layer_effect_enricher(
      input_table_name = "outer_soft_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      layer_name = "OuterSoftPhotoEffect",
      product_select_pid_count = '{{' + ab("product_select_pid_count", 0) + '}}',
      product_select_pid_ratio = '{{' + ab("product_select_pid_ratio", 0.0) + '}}',
      enable_prerank_account_limit_outloop = '{{' + ab("enable_prerank_account_limit_outloop", False) + '}}',
      enable_record_prerank_return_idx = '{{' + ab("enable_record_prerank_return_idx", False) + '}}',
      enable_prerank_industry_live_diversity = '{{' + ab("enable_prerank_industry_live_diversity", False) + '}}',
      enable_prerank_industry_live_limit_outloop = '{{' + ab("enable_prerank_industry_live_limit_outloop", False) + '}}',
      prerank_per_industry_live_ad_num_outloop = '{{' + ab("prerank_per_industry_live_ad_num_outloop", 2) + '}}',
      prerank_per_account_ad_num_outloop = '{{' + ab("prerank_per_account_ad_num_outloop", 5) + '}}',
      enable_use_ensemble_score_for_product_select_strategy='{{' + \
          ab("enable_use_ensemble_score_for_product_select_strategy", False) + '}}',
      enable_use_ltr_score_for_product_select_strategy='{{' + \
          ab("enable_use_ltr_score_for_product_select_strategy", False) + '}}',
      enable_use_interest_score_for_product_select_strategy='{{' + \
          ab("enable_use_interest_score_for_product_select_strategy", False) + '}}',
      disable_prerank_nextstay_filter='{{' + \
          ab("disable_prerank_nextstay_filter2", False) + '}}',
      target_prerank_ica_exp_tag = '{{' + ab("target_prerank_ica_exp_tag", "") + '}}',
      disable_prerank_nextstay_filter_ocpm='{{' + \
          ab("disable_prerank_nextstay_filter_ocpm", False) + '}}',
      enable_close_threshold_filter = '{{' + ab("enable_close_threshold_filter_outer_soft", False) + '}}',
    ) \
    .layer_outer_soft_photo_support_enricher(
      input_table_name = "outer_soft_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_soft_photo_duanju_support = '{{' + ab("enable_outer_soft_photo_duanju_support", False) + '}}',
      enable_outerloop_universe_retarget_prerank = '{{' + ab("enable_outerloop_universe_retarget_prerank", False) + '}}',
      enable_outer_native_fiction_soft_prerank = '{{' + ab("enable_outer_native_fiction_soft_prerank", False) + '}}', 
      enable_fiction_dpa_prerank_score_v2 = '{{' + ab("enable_fiction_dpa_prerank_score_v2", False) + '}}',
      enable_outerloop_nc_retrieval_tag_prerank = '{{' + ab("enable_outerloop_nc_retrieval_tag_prerank", False) + '}}',
      enable_outer_soft_support_fiction_iaa_prerank = '{{' + ab("enable_outer_soft_support_fiction_iaa_prerank", False) + '}}', 
      enable_outer_medical_soft_sign = '{{' + ab("enable_outer_medical_soft_sign", False) + '}}', 
    ) \
    .if_("%s %s"%(ab("enable_close_layer_outer", False), "== 0")) \
    .layer_ecology_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_wechat_game_tmp_trans = '{{' + ab("enable_wechat_game_tmp_trans", False) + '}}',
      enable_duanju_ecology_support = '{{' + ab("enable_duanju_ecology_support", False) + '}}',
      enable_lsp_native_sign = '{{' + ab("enable_lsp_native_sign", False) + '}}',
      enable_outer_medical_sign = '{{' + ab("enable_outer_medical_sign", False) + '}}',
      enable_game_interest_retarget_papo = '{{' + ab("enable_game_interest_retarget_papo", False) + '}}',
      close_wechat_papo_for_game_interest = '{{' + ab("close_wechat_papo_for_game_interest", False) + '}}',
      enable_outerloop_nc_target_strategy = '{{' + ab("enable_outerloop_nc_target_strategy", False) + '}}',
      enable_prerank_skip_nc_user_limit = '{{' + ab("enable_prerank_skip_nc_user_limit", False) + '}}',
      enable_outerloop_interest_retarget_papo = '{{' + ab("enable_outerloop_interest_retarget_papo", False) + '}}',
      enable_outerloop_nc_app_list_papo = '{{' + ab("enable_outerloop_nc_app_list_papo", False) + '}}',
      enable_outerloop_nc_limit_ocpx = '{{' + ab("enable_outerloop_nc_limit_ocpx", False) + '}}',
      enable_outerloop_nc_fix_bug = '{{' + ab("enable_outerloop_nc_fix_bug", False) + '}}',
      enable_outerloop_nc_product_papo = '{{' + ab("enable_outerloop_nc_product_papo", False) + '}}',
      enable_outerloop_nc_retrieval_papo = '{{' + ab("enable_outerloop_nc_retrieval_papo", False) + '}}',
      enable_outer_applist_papo_fix = '{{' + ab("enable_outer_applist_papo_fix", False) + '}}',
      enable_add_kwai_minigame_papo = '{{' + ab("enable_add_kwai_minigame_papo", False) + '}}',
      enable_fresh_outer_product_explore_prerank_boost = '{{' + ab("enable_fresh_outer_product_explore_prerank_boost", False) + '}}',
      enable_fresh_inner_product_explore_prerank_boost = '{{' + ab("enable_fresh_inner_product_explore_prerank_boost", False) + '}}',
      enable_outerloop_cross_ind_enhance_prerank_strategy = '{{' + ab("enable_outerloop_cross_ind_enhance_prerank_strategy", False) + '}}',
      enable_outerloop_ac_enhance_prerank_new_strategy = '{{' + ab("enable_outerloop_ac_enhance_prerank_new_strategy", False) + '}}',
      enable_outerloop_ac_converted_industry_limit = '{{' + ab("enable_outerloop_ac_converted_industry_limit", False) + '}}',
      enable_outerloop_universe_retarget_prerank = '{{' + ab("enable_outerloop_universe_retarget_prerank", False) + '}}',
      enable_outerloop_nc_retrieval_tag_prerank = '{{' + ab("enable_outerloop_nc_retrieval_tag_prerank", False) + '}}',
      enable_outerloop_leads_car_industry_prerank_support = '{{' + ab("enable_outerloop_leads_car_industry_prerank_support", False) + '}}',
      enable_inner_explore_sim_ltr_append = '{{' + ab("enable_inner_explore_sim_ltr_append", False) + '}}',
      inner_explore_sim_ltr_append_idx = '{{' + ab("inner_explore_sim_ltr_append_idx", 0) + '}}',
      inner_explore_sim_ltr_append_score = '{{' + ab("inner_explore_sim_ltr_append_score", 0.0) + '}}',
    ) \
    .layer_guaranteed_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      outer_native_galaxy_recall_tag = '{{' + ab("outer_native_galaxy_recall_tag", 132) + '}}',
      enable_whitelist_guaranteed_preranking = '{{' + ab("enable_whitelist_guaranteed_preranking", False) + '}}',
      enable_outer_prerank_green_channel = '{{' + ab("enable_outer_prerank_green_channel", False) + '}}',
      mobile_hard_photo_guaranteed_preranking_tag = '{{' + ab("mobile_hard_photo_guaranteed_preranking_tag", 200) + '}}',
      enable_prophet_guaranteed_preranking = '{{' + ab("enable_prophet_guaranteed_preranking", False) + '}}',
      enable_search_retarget_guaranteed_preranking = '{{' + ab("enable_search_retarget_guaranteed_preranking", False) + '}}',
      force_impression_multi_tagid_list = '{{' + ab("force_impression_multi_tagid_list", "66,88") + '}}',
    ) \
    .layer_nomal_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_auto_campaign = '{{' + ab("enable_auto_campaign", False) + '}}',
      enable_game_retarget_append = '{{' + ab("enable_game_retarget_append", False) + '}}',
      enable_game_retarget_append_new = '{{' + ab("enable_game_retarget_append_new", False) + '}}',
      enable_fiction_retarget_generalize = '{{' + ab("enable_fiction_retarget_generalize", False) + '}}',
      enable_layer_normal_support_fiction_retarget = '{{' + ab("enable_layer_normal_support_fiction_retarget", False) + '}}',
      layer_normal_fiction_retarget_score_weight = '{{' + ab("layer_normal_fiction_retarget_score_weight", 1.0) + '}}',
      fiction_retarget_resort_num = '{{' + ab("fiction_retarget_resort_num", 0) + '}}',
      enable_ual_narrowtarget_prerank_support = '{{' + ab("enable_ual_narrowtarget_prerank_support", False) + '}}',
      enable_daodian_prerank_support = '{{' + ab("enable_daodian_prerank_support", False) + '}}',
      enable_new_cid_prerank_support = '{{' + ab("enable_new_cid_prerank_support", False) + '}}',
      enable_private_message_sent_prerank_support = '{{' + ab("enable_private_message_sent_prerank_support", False) + '}}',
      enable_leads_submit_prerank_support = '{{' + ab("enable_leads_submit_prerank_support", False) + '}}',
      enable_wechat_connected_prerank_support = '{{' + ab("enable_wechat_connected_prerank_support", False) + '}}',
      close_wechat_game_append = '{{' + ab("close_wechat_game_append", False) + '}}',
      append_only_wechat_game_bugfix = '{{' + ab("append_only_wechat_game_bugfix", False) + '}}',
      enable_inner_trigger_item_append_wanhe = '{{' + ab("enable_inner_trigger_item_append_wanhe", False) + '}}',
      enable_inner_trigger_item_path_append = '{{' + ab("enable_inner_trigger_item_path_append", False) + '}}',
      enable_inner_trigger_item_tag_append = '{{' + ab("enable_inner_trigger_item_tag_append", False) + '}}',
      inner_trigger_item_strategy_tag = '{{' + ab("inner_trigger_item_strategy_tag", "") + '}}',
      inner_trigger_item_strategy_tag_thr = '{{' + ab("inner_trigger_item_strategy_tag_thr", 1.0) + '}}',
      enable_kai_game_iap_append = '{{' + ab("enable_kai_game_iap_append", False) + '}}',
      enable_kai_game_iaap_append = '{{' + ab("enable_kai_game_iaap_append", False) + '}}',
      enable_game_retarget_skip_kai = '{{' + ab("enable_game_retarget_skip_kai", False) + '}}',
      enbale_game_retarget_explode = '{{' + ab("enbale_game_retarget_explode", False) + '}}',
      enable_game_retarget_unify_resort = '{{' + ab("enable_game_retarget_unify_resort", False) + '}}',
      enable_iaap_game_unify_resort = '{{' + ab("enable_iaap_game_unify_resort", False) + '}}',
      iaap_game_unify_resort_boost_ratio = '{{' + ab("iaap_game_unify_resort_boost_ratio", 1.0) + '}}',
      iaap_game_unify_resort_boost_bias = '{{' + ab("iaap_game_unify_resort_boost_bias", 0.0) + '}}',
      enable_fiction_iaa_prerank_sort_boost = '{{' + ab("enable_fiction_iaa_prerank_sort_boost", False) + '}}',
      fiction_iaa_unify_resort_boost_ratio = '{{' + ab("fiction_iaa_unify_resort_boost_ratio", 1.0) + '}}',
      fiction_iaa_unify_resort_boost_bias = '{{' + ab("fiction_iaa_unify_resort_boost_bias", 0.0) + '}}',
      enable_game_iaa_prerank_sort_boost = '{{' + ab("enable_game_iaa_prerank_sort_boost", False) + '}}',
      game_iaa_unify_resort_boost_ratio = '{{' + ab("game_iaa_unify_resort_boost_ratio", 1.0) + '}}',
      enable_mini_game_big_r_ee_strategy_append = '{{' + ab("enable_mini_game_big_r_ee_strategy_append", False) + '}}',
      mini_game_big_r_ee_explore_k_append = '{{'+ ab("mini_game_big_r_ee_explore_k_append", 1.0) + '}}',
      mini_game_big_r_ee_explore_b_append = '{{'+ ab("mini_game_big_r_ee_explore_b_append", 0.0) + '}}',
      game_iaa_unify_resort_boost_bias = '{{' + ab("game_iaa_unify_resort_boost_bias", 0.0) + '}}',
      game_retarget_status_tag = '{{' + ab("game_retarget_status_tag", "") + '}}',
      playlet_retarget_tag = '{{' + ab("playlet_retarget_tag", 101) + '}}',
      outer_native_fiction_retarget_tag = '{{' + ab("outer_native_fiction_retarget_tag", 101) + '}}',
      enable_duanju_hot_playlet_append = '{{' + ab("enable_duanju_hot_playlet_append", False) + '}}',
      enable_duanju_hot_playlet_append_series_id = '{{' + ab("enable_duanju_hot_playlet_append_series_id", False) + '}}',
      enable_inner_explore_relative_recall_tag_support = '{{' + ab("enable_inner_explore_relative_recall_tag_support", False) + '}}',
      enable_inner_explore_relative_score_support = '{{' + ab("enable_inner_explore_relative_score_support", False) + '}}',
      relative_score_support_threshold = '{{'+ ab("relative_score_support_threshold", 1.0) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
      ad_select_type = "ad_select_type",
      multi_retrieval_tag = "multi_retrieval_tag",
      playlet_name_hash = "playlet_name_hash",
      series_id = "series_id",
    ) \
    .layer_outer_sdpa_support_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_sdpa_prerank_support = '{{' + ab("enable_outer_sdpa_prerank_support", False) + '}}',
    ) \
    .layer_outer_ceiling_support_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_ceiling_prerank_support = '{{' + ab("enable_outer_ceiling_prerank_support", False) + '}}',
    ) \
    .layer_outer_ceiling_support_enricher(
      input_table_name = "outer_soft_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_ceiling_prerank_support = '{{' + ab("enable_outer_ceiling_prerank_support", False) + '}}',
    ) \
    .layer_outer_medtrans_support_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_medtrans_prerank_support = '{{' + ab("enable_outer_medtrans_prerank_support", False) + '}}',
    ) \
    .layer_outer_medtrans_support_enricher(
      input_table_name = "outer_soft_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_medtrans_prerank_support = '{{' + ab("enable_outer_medtrans_prerank_support", False) + '}}',
    ) \
    .layer_operational_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_prerank_append_operation = '{{' + ab("enable_prerank_append_operation", False) + '}}',
    ) \
    .layer_special_support_f1_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_native_special_support_f1_preranking = '{{' + ab("enable_outer_native_special_support_f1_preranking", False) + '}}',
      enable_target_industry_explore_append = '{{' + ab("enable_target_industry_explore_append", False) + '}}',
      enable_corporation_ceiling_preranking = '{{' + ab("enable_corporation_ceiling_preranking", False) + '}}',
      enable_photo_ceiling_preranking = '{{' + ab("enable_photo_ceiling_preranking", False) + '}}',
      enable_photo_ceiling_compare = '{{' + ab("enable_photo_ceiling_compare", False) + '}}',
      enable_outer_industry_boost = '{{' + ab("enable_outer_industry_boost_preranking", False) + '}}',
      enable_outer_native_fiction_append_f1 = '{{' + ab("enable_outer_native_fiction_append_f1", False) + '}}',
      enable_fiction_dpa_prerank_score = '{{' + ab("enable_fiction_dpa_prerank_score", False) + '}}',
      enable_fiction_dpa_prerank_score_v2 = '{{' + ab("enable_fiction_dpa_prerank_score_v2", False) + '}}',
      enable_fiction_ecpm_prerank_score = '{{' + ab("enable_fiction_ecpm_prerank_score", False) + '}}',
      enable_fiction_retarget_generalize = '{{' + ab("enable_fiction_retarget_generalize", False) + '}}',
      generalized_fiction_prerank_score_weight = '{{' + ab("generalized_fiction_prerank_score_weight", 1.0) + '}}',
      game_prerank_tag = '{{' + ab("game_prerank_tag", "default") + '}}',
      enable_game_iaa_append = '{{' + ab("enable_game_iaa_append", False) + '}}',
    ) \
    .layer_special_support_f3_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      outer_native_galaxy_recall_tag = '{{' + ab("outer_native_galaxy_recall_tag", 132) + '}}',
      enable_search_retarget_guaranteed_preranking = '{{' + ab("enable_search_retarget_guaranteed_preranking", False) + '}}',
      force_impression_multi_tagid_list = '{{' + ab("force_impression_multi_tagid_list", "66,88") + '}}',
      enable_outer_high_cost_guaranteed_preranking = '{{' + ab("enable_outer_high_cost_guaranteed_preranking", False) + '}}',
      enable_outer_aigc_photo_append = '{{' + ab("enable_outer_aigc_photo_append", False) + '}}',
      enable_outer_prerank_search_append = '{{' + ab("enable_outer_prerank_search_append", False) + '}}',
    ) \
    .layer_outer_aigc_support_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_aigc_photo_guarentee_append = '{{' + ab("enable_outer_aigc_photo_guarentee_append", False) + '}}',
      enable_outer_aigc_photo_guarentee_append_new = '{{' + ab("enable_outer_aigc_photo_guarentee_append_new", False) + '}}',
    ) \
    .layer_galaxy_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
    ) \
    .layer_outer_galaxy_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_outer_merchant_guaranteed_preranking_v2 = '{{' + ab("enable_outer_merchant_guaranteed_preranking_v2", False) + '}}',
      enable_outer_merchant_guaranteed_antou_preranking = '{{' + ab("enable_outer_merchant_guaranteed_antou_preranking", False) + '}}',
      enable_prerank_layer_sort_opt = '{{' + ab("enable_prerank_layer_sort_opt", False) + '}}',
      enable_layer_selected_opt = '{{' + ab("enable_layer_selected_opt", False) + '}}',
    ) \
    .layer_special_support_f4_enricher(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_guaranteed_preranking_f4_white = '{{' + ab("enable_guaranteed_preranking_f4_white", False) + '}}',
      enable_guaranteed_preranking_f4_wechat_game= '{{' + ab("enable_guaranteed_preranking_f4_wechat_game", False) + '}}',
      enable_guaranteed_preranking_f4_duanju = '{{' + ab("enable_guaranteed_preranking_f4_duanju", False) + '}}',
      enable_guaranteed_preranking_f4_playlet_explore = '{{' + ab("enable_guaranteed_preranking_f4_playlet_explore", False) + '}}',
      enable_guaranteed_preranking_f4_duanju_search_ecpc = '{{' + ab("enable_guaranteed_preranking_f4_duanju_search_ecpc", False) + '}}',
      enable_upgrade_industry_id_v5 = '{{' + ab("enable_upgrade_industry_id_v5", False) + '}}',
      enable_purchase_roi_prerank_f4_support = '{{' + ab("enable_purchase_roi_prerank_f4_support", False) + '}}',
      append_only_wechat_game_bugfix = '{{' + ab("append_only_wechat_game_bugfix", False) + '}}',
      enable_psp_prerank_f4 = '{{' + ab("enable_psp_prerank_f4", False) + '}}',
    ) \
    .layer_industry_explore(
      input_table_name = "outer_hard_photo_item_table",
      input_layer_table_name = "outer_photo_layer_table",
      enable_dragon_industry_explore = '{{' + ab("enable_dragon_industry_explore", False) + '}}',
      explore_layer_name = "OuterHardPhotoEffect"
    ) \
    .end_if_() \
    .layer_supplement_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_prerank_layer_supplement_v2 = '{{' + ab("enable_prerank_layer_supplement_outer", False) + '}}',
      ensemble_score = "ensemble_score",
      layer_select_type = "layer_select_type",
    ) \

    return self


# live end

# module begin
  @module()
  def inner_photo_soft_prerank(self, name):
    current_flow() \
      .if_("is_prerank == 1") \
        .inner_photo_soft_predict() \
      .end_if_() \
      .inner_photo_soft_feature_bid() \
      .if_("is_prerank == 1") \
        .inner_photo_soft_predict_callback() \
      .end_if_() \
      .inner_photo_soft_bid_guard() \
      .inner_photo_soft_remote_and_diff() \
      .inner_photo_soft_filter() \
      .inner_photo_soft_sort() \
      .inner_photo_soft_layer()
    return self

  @module()
  def inner_photo_hard_prerank(self, name):
    current_flow() \
      .if_("enable_rank_adlist_cache == 0 or is_hit_cache == 0") \
        .inner_photo_hard_predict() \
      .end_if_() \
      .inner_photo_hard_feature_bid()\
      .inner_photo_hard_predict_callback() \
      .inner_photo_hard_bid_guard() \
      .inner_photo_hard_remote_and_diff() \
      .inner_photo_hard_filter() \
      .inner_photo_hard_sort() \
      .inner_photo_hard_layer()
    return self

  @module()
  def live_prerank(self, name):
    current_flow() \
      .live_predict() \
      .live_feature_bid()\
      .live_predict_callback() \
      .live_bid_guard() \
      .live_remote_and_diff() \
      .live_filter() \
      .inner_live_rank_score() \
      .inner_live_layer_manager() \
      .outer_live_rank_score() \
      .outer_live_layer_manager()
    return self

  @module()
  def outer_photo_prerank(self, name):
    current_flow() \
      .if_("enable_rank_adlist_cache == 0 or is_hit_cache == 0") \
        .outer_predict() \
      .end_if_() \
      .outer_photo_feature_bid()\
      .if_("enable_rank_adlist_cache == 0 or is_hit_cache == 0") \
        .outer_predict_callback() \
      .end_if_() \
      .outer_bid_guard() \
      .outer_photo_remote_and_diff() \
      .outer_hard_photo_rank_score() \
      .outer_soft_photo_rank_score() \
      .outer_photo_layer_manager()
    return self

  def calc_model_predict_extra_timeout(self, kconf_param, ab_param):
    """
    * kconf 参数 != 0 时，kconf 参数生效
    * kconf 参数 == 0 时，ab 参数生效
    """
    self \
    .get_kconf_params(
      kconf_configs=[{
        "kconf_key": kconf_param,
        "default_value": 0,
        "value_type": "int",
        "export_common_attr": "model_predict_extra_timeout"
      }]
    ) \
    .if_("model_predict_extra_timeout == 0") \
      .get_abtest_params(
        biz_name = "AD_DSP",
        ab_params = [{
          "param_name": ab_param,
          "default_value": 0,
          "attr_name": "model_predict_extra_timeout"
        }]
      ) \
    .end_if_()
    return self

  @module()
  def inner_soft_branch(self, **kwargs):
    with data_manager:
      current_flow() \
    .inner_photo_soft_prerank(name = "inner_soft_photo") \
    .if_("enable_prerank_move_to_ad_server == 1 or enable_trace_base == 1") \
      .prerank_trace_log_context_mixer(
        is_trace_log = "is_trace_log",
        trace_log_name = "inner_soft_trace_log",
        target_trace_type = 24
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "inner_photo_soft_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "inner_soft_trace_log",
        need_record = True
      ) \
    .end_if_() \
    .color_debug_creative_enricher(
       item_table = "inner_photo_soft_item_table",
       debug_creative_id_common_attr = "debug_creative_id",
       debug_creative_reason_common_attr = "debug_creative_reason",
    ) \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "inner_photo_soft_item_table",
      enable_filter_zero_auto_bid = '{{' + ab("enable_filter_zero_auto_bid", False) + '}}',
      enable_feature_bid_filter_zero = '{{' + ab("enable_feature_bid_filter_zero", False) + '}}'
    ) \
    .if_("enable_new_fields_from_forward_diff == 1") \
      .new_forward_fields_diff(
        input_table_name = "inner_photo_soft_item_table",
        diff_columns = all_new_fields
    ).end_if_() \
    .copy_item_meta_info(
      item_table="inner_photo_soft_item_table",
      save_reason_to_attr="prerank_filter_reason",
      save_item_seq_to_attr="prerank_seq",
    )
    return self

  @module()
  def inner_hard_branch(self, **kwargs):
    with data_manager:
      current_flow() \
    .inner_photo_hard_prerank(name = "inner_hard_photo") \
    .if_("enable_prerank_redis_cache == 1 and enable_rank_adlist_cache == 0") \
      .prepare_ad_list_cache_mixer(
        p2l_quota = '{{' + ab("inner_p2l_hard_cache_quota", 200) + '}}',
        photo_quota = '{{' + ab("inner_photo_hard_cache_quota", 20) + '}}',
        session_cache_quota = '{{' + ab("session_cache_quota", 200) + '}}',
        input_table_name = "inner_photo_hard_item_table",
        cache_adlist_name = "inner_cache_adlist"
      ) \
      .write_ad_list_cache_mixer(
        redis_cluster_name = "adEnginePrerankCache",
        biz = "inner_hard",
        redis_ttl = '{{' + ab("write_ad_list_redis_ttl", 90) + '}}',
        skip_redis_ttl = '{{' + ab("skip_redis_ttl", False) + '}}',
        cache_adlist_name = "inner_cache_adlist",
        input_table_name = "inner_photo_hard_item_table",
        dcaf_rank_version_cache = '{{' + ab("dcaf_rank_version_cache", "default") + '}}',
      ) \
    .end_if_() \
    .if_("enable_prerank_move_to_ad_server == 1 or enable_trace_base == 1") \
      .prerank_trace_log_context_mixer(
        is_trace_log = "is_trace_log",
        trace_log_name = "inner_hard_trace_log",
        target_trace_type = 24
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "inner_photo_hard_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "inner_hard_trace_log",
        need_record = True
      ) \
    .end_if_() \
    .color_debug_creative_enricher(
      item_table = "inner_photo_hard_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    ) \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "inner_photo_hard_item_table",
      enable_filter_zero_auto_bid = '{{' + ab("enable_filter_zero_auto_bid", False) + '}}',
      enable_feature_bid_filter_zero = '{{' + ab("enable_feature_bid_filter_zero", False) + '}}'
    ) \
    .if_("enable_new_fields_from_forward_diff == 1") \
      .new_forward_fields_diff(
        input_table_name = "inner_photo_hard_item_table",
        diff_columns = all_new_fields
    ).end_if_() \
    .copy_item_meta_info(
      item_table="inner_photo_hard_item_table",
      save_reason_to_attr="prerank_filter_reason",
      save_item_seq_to_attr="prerank_seq",
    )
    return self

  @module()
  def live_branch(self, **kwargs):
    with data_manager:
      current_flow() \
    .live_prerank(name = "live") \
    .prerank_sample_mixer(
      input_table_name = "outer_live_item_table",
      candidate_type = 9,
      is_prerank_sample = "is_prerank_sample"
    ) \
    .if_("enable_prerank_move_to_ad_server == 1 or enable_trace_base == 1") \
      .prerank_trace_log_context_mixer(
        is_trace_log = "is_trace_log",
        trace_log_name = "live_trace_log",
        target_trace_type = 23
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "one_step_live_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "live_trace_log",
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "outer_live_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "live_trace_log",
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "inner_live_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "live_trace_log",
        need_record = True
      ) \
    .end_if_() \
    .color_debug_creative_enricher(
      item_table = "inner_live_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    ) \
    .color_debug_creative_enricher(
      item_table = "outer_live_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    ) \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "inner_live_item_table",
      enable_filter_zero_auto_bid = '{{' + ab("enable_filter_zero_auto_bid", False) + '}}',
      enable_feature_bid_filter_zero = '{{' + ab("enable_feature_bid_filter_zero", False) + '}}'
    )  \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "outer_live_item_table"
    )  \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "one_step_live_item_table",
      enable_filter_zero_auto_bid = '{{' + ab("enable_filter_zero_auto_bid", False) + '}}',
      enable_feature_bid_filter_zero = '{{' + ab("enable_feature_bid_filter_zero", False) + '}}'
    )  \
    .if_("enable_adjust_native_live == 1")  \
      .native_live_adjust_mixer(
        input_table_name = "inner_live_item_table",
        enable_buyer_homepage_live_soft_queue = '{{' + ab("enable_buyer_homepage_live_soft_queue", False) + '}}',
        enable_rewarded_soft_ad = '{{' + ab("enable_rewarded_soft_ad", False) + '}}',
        enable_soft_photo_for_inspire = '{{' + ab("enable_soft_photo_for_inspire", False) + '}}',
      )  \
      .native_live_adjust_mixer(
        input_table_name = "one_step_live_item_table",
        enable_buyer_homepage_live_soft_queue = '{{' + ab("enable_buyer_homepage_live_soft_queue", False) + '}}',
        enable_rewarded_soft_ad = '{{' + ab("enable_rewarded_soft_ad", False) + '}}',
        enable_soft_photo_for_inspire = '{{' + ab("enable_soft_photo_for_inspire", False) + '}}',
      ) \
    .end_if_() \
    .if_("enable_adjust_esp_mobile == 1") \
      .esp_mobile_live_to_hard(
        input_table_name = "inner_live_item_table",
        enable_buyer_homepage_live_soft_queue = '{{' + ab("enable_buyer_homepage_live_soft_queue", False) + '}}',
        enable_esp_mobile_live_to_hard = '{{' + ab("enable_esp_mobile_live_to_hard", False) + '}}'
      )  \
      .esp_mobile_live_to_hard(
        input_table_name = "one_step_live_item_table",
        enable_buyer_homepage_live_soft_queue = '{{' + ab("enable_buyer_homepage_live_soft_queue", False) + '}}',
        enable_esp_mobile_live_to_hard = '{{' + ab("enable_esp_mobile_live_to_hard", False) + '}}'
      ) \
    .end_if_()  \
    .if_("enable_adjust_fanstop_live == 1") \
      .fanstop_live_to_hard_mixer(
        input_table_name = "inner_live_item_table",
        enable_buyer_homepage_live_soft_queue = '{{' + ab("enable_buyer_homepage_live_soft_queue", False) + '}}',
        enable_esp_mobile_live_to_hard = '{{' + ab("enable_esp_mobile_live_to_hard", False) + '}}',
        copy_fanstop_live_to_hard_quota = '{{' + ab("copy_fanstop_live_to_hard_quota", 5) + '}}'
      )  \
      .fanstop_live_to_hard_mixer(
        input_table_name = "one_step_live_item_table",
        enable_buyer_homepage_live_soft_queue = '{{' + ab("enable_buyer_homepage_live_soft_queue", False) + '}}',
        enable_esp_mobile_live_to_hard = '{{' + ab("enable_esp_mobile_live_to_hard", False) + '}}',
        copy_fanstop_live_to_hard_quota = '{{' + ab("copy_fanstop_live_to_hard_quota", 5) + '}}'
      ) \
    .end_if_()  \
    .if_("enable_adjust_outer_live == 1") \
      .outer_live_native_to_soft_mixer(
        input_table_name = "outer_live_item_table",
        ab_switch_NativeSourceAdFansLiveProc = '{{' + ab("ab_switch_NativeSourceAdFansLiveProc", False) + '}}'
      ) \
    .end_if_() \
    .if_("enable_new_fields_from_forward_diff == 1") \
      .new_forward_fields_diff(
        input_table_name = "inner_live_item_table",
        diff_columns = all_new_fields
      ) \
      .new_forward_fields_diff(
        input_table_name = "outer_live_item_table",
        diff_columns = all_new_fields
      ) \
      .new_forward_fields_diff(
        input_table_name = "one_step_live_item_table",
        diff_columns = all_new_fields
      ) \
    .end_if_() \
    .copy_item_meta_info(
      item_table="inner_live_item_table",
      save_reason_to_attr="prerank_filter_reason",
      save_item_seq_to_attr="prerank_seq",
    )  \
    .copy_item_meta_info(
      item_table="outer_live_item_table",
      save_reason_to_attr="prerank_filter_reason",
      save_item_seq_to_attr="prerank_seq",
    )
    return self

  @module()
  def outer_branch(self, **kwargs):
    with data_manager:
      current_flow() \
    .outer_photo_prerank(name = "outer_photo")\
    .if_("enable_prerank_redis_cache == 1 and enable_rank_adlist_cache == 0") \
      .prepare_ad_list_cache_mixer(
        outer_quota = '{{' + ab("outer_photo_soft_cache_quota", 20) + '}}',
        session_cache_quota = '{{' + ab("session_cache_quota", 200) + '}}',
        input_table_name = "outer_soft_photo_item_table",
        cache_adlist_name = "outer_cache_adlist"
      ) \
      .prepare_ad_list_cache_mixer(
        outer_quota = '{{' + ab("outer_photo_hard_cache_quota", 200) + '}}',
        session_cache_quota = '{{' + ab("session_cache_quota", 200) + '}}',
        input_table_name = "outer_hard_photo_item_table",
        cache_adlist_name = "outer_cache_adlist"
      ) \
      .write_ad_list_cache_mixer(
        redis_cluster_name = "adEnginePrerankCache",
        biz = "outer",
        redis_ttl = '{{' + ab("write_ad_list_redis_ttl", 90) + '}}',
        skip_redis_ttl = '{{' + ab("skip_redis_ttl", False) + '}}',
        cache_adlist_name = "outer_cache_adlist",
        dcaf_rank_version_cache = '{{' + ab("dcaf_rank_version_cache", "default") + '}}',
      ) \
    .end_if_() \
    .prerank_sample_mixer(
      input_table_name = "outer_hard_photo_item_table",
      candidate_type = 3,
      is_prerank_sample = "is_prerank_sample"
    ) \
    .prerank_sample_mixer(
      input_table_name = "outer_soft_photo_item_table",
      candidate_type = 4,
      is_prerank_sample = "is_prerank_sample"
    ) \
    .if_("enable_prerank_move_to_ad_server == 1 or enable_trace_base == 1") \
      .prerank_trace_log_context_mixer(
        is_trace_log = "is_trace_log",
        trace_log_name = "outer_trace_log",
        target_trace_type = 20
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "outer_soft_photo_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "outer_trace_log",
      ) \
      .prerank_trace_log_item_mixer(
        input_table_name = "outer_hard_photo_item_table",
        is_trace_log = "is_trace_log",
        trace_log_name = "outer_trace_log",
        need_record = True
      ) \
    .end_if_() \
    .color_debug_creative_enricher(
      item_table = "outer_soft_photo_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    ) \
    .color_debug_creative_enricher(
      item_table = "outer_hard_photo_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    ) \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "outer_soft_photo_item_table"
    )  \
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "outer_hard_photo_item_table"
    ) \
    .if_("enable_new_fields_from_forward_diff == 1") \
      .new_forward_fields_diff(
        input_table_name = "outer_soft_photo_item_table",
        diff_columns = all_new_fields
      ) \
      .new_forward_fields_diff(
        input_table_name = "outer_hard_photo_item_table",
        diff_columns = all_new_fields
    ).end_if_() \
    .copy_item_meta_info(
      item_table="outer_soft_photo_item_table",
      save_reason_to_attr="prerank_filter_reason",
      save_item_seq_to_attr="prerank_seq",
    )  \
    .copy_item_meta_info(
      item_table="outer_hard_photo_item_table",
      save_reason_to_attr="prerank_filter_reason",
      save_item_seq_to_attr="prerank_seq",
    )
    return self

  def ad_router_common_data_prepare(self):
    self.if_("prerank_direct_predict_flag & 31 > 0 and enable_prerank_user_info_bs_truncate == 0") \
        .ad_router_bs_trans_enricher(save_async_status_to = "bs_trans_async_status") \
      .end_if_()
    return self

  def ad_router_predict_process_outer(self):
    self.if_("prerank_direct_predict_flag & outer_predict_enum > 0") \
      .ad_router_request_prepare(
        predict_request_attr_name = "outer_predict_request",
        arena_attr_name = "outer_rpc_arena",
        predict_scene = "outer"
      ) \
      .ad_router_send_request_mixer(
        predict_request_attr_name = "outer_predict_request",
        arena_attr_name = "outer_rpc_arena",
        cmd_item_tables = [
          {
            "cmd_table" : "outer_soft_photo_cmd_table",
            "item_table" : "outer_soft_photo_item_table"
          },
          {
            "cmd_table" : "outer_hard_photo_cmd_table",
            "item_table" : "outer_hard_photo_item_table"
          }
        ],
        item_feature_tables = ["outer_soft_photo_item_feature_table", "outer_hard_photo_item_feature_table"],
        user_feature_table_name = "prerank_user_feature",
        context_feature_table_name = "outer_context_feature",
        predict_response_attr_name = "outer_predict_response",
        save_async_status_to = "outer_predict_async_status",
        enable_debug = "{{prerank_direct_predict_flag}}",
        output_debug_sub_reqeust_name = "outer_sub_request_map_exp",
        enable_cmdkey_list_group = "prerank_direct_predict_cmdkey_group",
        async_status_attr = "bs_trans_async_status",
        enable_mix_rank_hist_page_info_fea = False,
        use_data_converter_v2 = "{{use_data_converter_v2_prerank}}",
        is_truncate = "{{enable_prerank_user_info_bs_truncate}}",
        is_compress = "{{enable_prerank_user_info_bs_compress}}"
      ) \
      .end_if_()
    return self

  def ad_router_predict_wait_outer(self):
    self.if_("prerank_direct_predict_flag & outer_predict_enum > 1") \
        .ad_router_response_proc(
          cmd_item_tables = [
            {
              "cmd_table" : "outer_soft_photo_cmd_table",
              "item_table" : "outer_soft_photo_item_table"
            },
            {
              "cmd_table" : "outer_hard_photo_cmd_table",
              "item_table" : "outer_hard_photo_item_table"
            }
          ],
          predict_response_attr_name = "outer_predict_response",
          async_status_attr = "outer_predict_async_status"
        ) \
      .end_if_()
    return self

  def ad_router_predict_process_live(self):
    self.if_("prerank_direct_predict_flag & live_predict_enum > 0") \
      .ad_router_request_prepare(
        predict_request_attr_name = "live_predict_request",
        arena_attr_name = "live_rpc_arena",
        predict_scene = "live"
      ) \
      .ad_router_send_request_mixer(
        predict_request_attr_name = "live_predict_request",
        arena_attr_name = "live_rpc_arena",
        cmd_item_tables = [
          {
            "cmd_table" : "inner_live_cmd_table",
            "item_table" : "inner_live_item_table"
          },
          {
            "cmd_table" : "outer_live_cmd_table",
            "item_table" : "outer_live_item_table"
          }
        ],
        item_feature_tables = ["inner_live_item_feature_table", "outer_live_item_feature_table"],
        user_feature_table_name = "prerank_user_feature",
        context_feature_table_name = "live_context_feature",
        predict_response_attr_name = "live_predict_response",
        save_async_status_to = "live_predict_async_status",
        enable_debug = "{{prerank_direct_predict_flag}}",
        output_debug_sub_reqeust_name = "live_sub_request_map_exp",
        enable_cmdkey_list_group = "prerank_direct_predict_cmdkey_group",
        async_status_attr = "bs_trans_async_status",
        enable_mix_rank_hist_page_info_fea = True,
        use_data_converter_v2 = "{{use_data_converter_v2_prerank}}",
        is_truncate = "{{enable_prerank_user_info_bs_truncate}}",
        is_compress = "{{enable_prerank_user_info_bs_compress}}"
      ) \
      .end_if_()
    return self

  def ad_router_predict_wait_live(self):
    self.if_("prerank_direct_predict_flag & live_predict_enum > 1") \
      .ad_router_response_proc(
        cmd_item_tables = [
          {
            "cmd_table" : "inner_live_cmd_table",
            "item_table" : "inner_live_item_table"
          },
          {
            "cmd_table" : "outer_live_cmd_table",
            "item_table" : "outer_live_item_table"
          }
        ],
        predict_response_attr_name = "live_predict_response",
        async_status_attr = "live_predict_async_status"
      ) \
      .end_if_()
    return self

  def ad_router_predict_process_inner_soft(self):
    self.if_("prerank_direct_predict_flag & inner_soft_predict_enum > 0") \
      .ad_router_request_prepare(
        predict_request_attr_name = "inner_soft_predict_request",
        arena_attr_name = "inner_soft_rpc_arena",
        predict_scene = "inner_soft"
      ) \
      .ad_router_send_request_mixer(
        predict_request_attr_name = "inner_soft_predict_request",
        arena_attr_name = "inner_soft_rpc_arena",
        cmd_item_tables = [
          {
            "cmd_table" : "inner_photo_soft_cmd_table",
            "item_table" : "inner_photo_soft_item_table"
          }
        ],
        item_feature_tables = ["inner_photo_soft_item_feature_table"],
        user_feature_table_name = 'prerank_user_feature',
        context_feature_table_name = 'prerank_inner_photo_soft_context_feature',
        predict_response_attr_name = 'inner_soft_predict_response',
        save_async_status_to = "inner_soft_predict_async_status",
        enable_debug = "{{prerank_direct_predict_flag}}",
        output_debug_sub_reqeust_name = "inner_soft_sub_request_map_exp",
        enable_cmdkey_list_group = "prerank_direct_predict_cmdkey_group",
        async_status_attr = "bs_trans_async_status",
        enable_mix_rank_hist_page_info_fea = False,
        use_data_converter_v2 = "{{use_data_converter_v2_prerank}}",
        is_truncate = "{{enable_prerank_user_info_bs_truncate}}",
        is_compress = "{{enable_prerank_user_info_bs_compress}}"
      ) \
      .end_if_()
    return self

  def ad_router_predict_wait_inner_soft(self):
    self.if_("prerank_direct_predict_flag & inner_soft_predict_enum > 1") \
      .ad_router_response_proc(
        cmd_item_tables = [
          {
            "cmd_table" : "inner_photo_soft_cmd_table",
            "item_table" : "inner_photo_soft_item_table"
          }
        ],
        predict_response_attr_name = 'inner_soft_predict_response',
        async_status_attr = "inner_soft_predict_async_status"
      ) \
      .end_if_()
    return self

  
  def ad_router_predict_process_inner_hard(self):
    self.if_("prerank_direct_predict_flag & inner_hard_predict_enum > 0") \
      .ad_router_request_prepare(
        predict_request_attr_name = "inner_hard_predict_request",
        arena_attr_name = "inner_hard_rpc_arena",
        predict_scene = "inner_hard"
      ) \
      .ad_router_send_request_mixer(
        predict_request_attr_name = "inner_hard_predict_request",
        arena_attr_name = "inner_hard_rpc_arena",
        cmd_item_tables = [
          {
            "cmd_table" : "inner_photo_hard_cmd_table",
            "item_table" : "inner_photo_hard_item_table"
          }
        ],
        item_feature_tables = ["inner_photo_hard_item_feature_table"],
        user_feature_table_name = 'prerank_user_feature',
        context_feature_table_name = 'prerank_context_feature',
        predict_response_attr_name = 'inner_hard_predict_response',
        save_async_status_to = "inner_hard_predict_async_status",
        enable_debug = "{{prerank_direct_predict_flag}}",
        output_debug_sub_reqeust_name = "inner_hard_sub_request_map_exp",
        enable_cmdkey_list_group = "prerank_direct_predict_cmdkey_group",
        async_status_attr = "bs_trans_async_status",
        enable_mix_rank_hist_page_info_fea = False,
        use_data_converter_v2 = "{{use_data_converter_v2_prerank}}",
        is_truncate = "{{enable_prerank_user_info_bs_truncate}}",
        is_compress = "{{enable_prerank_user_info_bs_compress}}"
      ) \
      .end_if_()
    return self

  def ad_router_predict_wait_inner_hard(self):
    self.if_("prerank_direct_predict_flag & inner_hard_predict_enum > 1") \
      .ad_router_response_proc(
        cmd_item_tables = [
          {
            "cmd_table" : "inner_photo_hard_cmd_table",
            "item_table" : "inner_photo_hard_item_table"
          }
        ],
        predict_response_attr_name = 'inner_hard_predict_response',
        async_status_attr = "inner_hard_predict_async_status"
      ) \
      .end_if_()
    return self

  def ad_router_predict_diff(self, scene):
    self.if_(f"prerank_direct_predict_flag & {scene}_predict_enum == 1") \
      .ad_router_request_diff_reporter(
        base_request_attr_name = f"{scene}_sub_request_map_base",
        exp_request_attr_name = f"{scene}_sub_request_map_exp",
        to_file_folder = "./difflog",
        to_file_name = "request_diff",
        print_diff_num_limit = 100,
        print_same_num_limit = 100,
        origin_request_attr_name = f"{scene}_origin_router_request",
        predict_request_attr_name = f"{scene}_predict_request"
      ) \
      .end_if_()
    return self

  @module()
  def one_model_inner_hard_predict(self: Self):
    (self
    .context_feature_build_enricher(
      enable_fill_inner_trigger_emb_feature = '{{' + ab("enable_fill_inner_trigger_emb_feature", False) + '}}'
    )
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "inner_photo_hard_item_table",
          item_feature_table_name = "inner_photo_hard_item_feature_table"
        )
    ])
    .user_feature_build_enricher()
    .one_model_inner_hard_cmd_manager( )
    .model_register(
      register_table = [
        dict(
          item_table_name = "inner_photo_hard_item_table",
          cmd_table_name = "inner_photo_hard_cmd_table"
        )
    ])
    .ad_router_predict_process_inner_hard()
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "prerank_context_feature",
      item_feature_tables = ["inner_photo_hard_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "inner_photo_hard_cmd_table",
          "item_table" : "inner_photo_hard_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "inner_hard"
    )

    )
    return self

  @module()
  def one_model_inner_soft_predict(self):
    (self
    .default_prerank_inner_photo_soft_context_feature_build_enricher(
      enable_fill_inner_trigger_emb_feature_inner_soft = '{{' + ab("enable_fill_inner_trigger_emb_feature_inner_soft", False) + '}}'
    )
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "inner_photo_soft_item_table",
          item_feature_table_name = "inner_photo_soft_item_feature_table"
        )
    ])
    .user_feature_build_enricher()
    .one_model_inner_soft_cmd_manager()
    .model_register(
      register_table = [
        dict(
          item_table_name = "inner_photo_soft_item_table",
          cmd_table_name = "inner_photo_soft_cmd_table"
        )
    ])
    .ad_router_predict_process_inner_soft()
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "prerank_inner_photo_soft_context_feature",
      item_feature_tables = ["inner_photo_soft_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "inner_photo_soft_cmd_table",
          "item_table" : "inner_photo_soft_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "inner_soft"
    )

    )
    return self

  @module()
  def one_model_outer_predict(self):
    (self
    .context_feature_build_enricher(
      output_context_feature_table_name = "outer_context_feature",
      enable_outer_prerank_add_candidate_top_feature = '{{' + ab("enable_outer_prerank_add_candidate_top_feature", False) + '}}',
      enable_outer_prerank_candidate_feature_hard_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_hard_sample_count", 0) + '}}',
      enable_outer_prerank_candidate_feature_soft_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_soft_sample_count", 0) + '}}',
      enable_fill_inner_trigger_emb_feature = '{{' + ab("enable_fill_inner_trigger_emb_feature", False) + '}}'
    )
    .item_feature_build_enricher(
      register_table = [
        dict(
          item_table_name = "outer_soft_photo_item_table",
          item_feature_table_name = "outer_soft_photo_item_feature_table"
        ),
        dict(
          item_table_name = "outer_hard_photo_item_table",
          item_feature_table_name = "outer_hard_photo_item_feature_table"
        )
    ])
    .user_feature_build_enricher(
      enable_outer_prerank_add_candidate_feature = '{{' + ab("enable_outer_prerank_add_candidate_feature", False) + '}}',
      enable_outer_prerank_candidate_feature_hard_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_hard_sample_count", 0) + '}}',
      enable_outer_prerank_candidate_feature_soft_sample_count = '{{' + ab("enable_outer_prerank_candidate_feature_soft_sample_count", 0) + '}}'
    )
    .one_model_outer_cmd_manager(
      item_table = "outer_hard_photo_item_table",
      cmd_table_name = "outer_hard_photo_cmd_table",
    )
    .one_model_outer_cmd_manager(
      item_table = "outer_soft_photo_item_table",
      cmd_table_name = "outer_soft_photo_cmd_table",
    )
    .model_register(
      register_table = [
        dict(
          item_table_name = "outer_soft_photo_item_table",
          cmd_table_name = "outer_soft_photo_cmd_table"
        ),
        dict(
          item_table_name = "outer_hard_photo_item_table",
          cmd_table_name = "outer_hard_photo_cmd_table"
        )
    ])
    .ad_router_predict_process_outer()
    .model_predict(
      user_feature_table_name = "prerank_user_feature",
      context_feature_table_name = "outer_context_feature",
      item_feature_tables = ["outer_soft_photo_item_feature_table", "outer_hard_photo_item_feature_table"],
      cmd_item_tables = [
        {
          "cmd_table" : "outer_soft_photo_cmd_table",
          "item_table" : "outer_soft_photo_item_table"
        },
        {
          "cmd_table" : "outer_hard_photo_cmd_table",
          "item_table" : "outer_hard_photo_item_table"
        }
      ],
      router_name = "ad-predict-router-dsp-retrieval",
      dragon_async = True,
      save_async_status_to = "prerank_predict",
      enable_prerank_negtive_predict_value = '{{' + ab("enable_prerank_negtive_predict_value", False) + '}}',
      predict_scene = "outer"
    )
    )

    return self

  @module()
  def one_model_inner_hard_photo_sort(self: Self, is_nearline: bool):
    inner_hard_quota_ab = (ab('one_model_nearline_prerank_inner_hard_quota', 150)
                           if is_nearline 
                           else ab('one_model_prerank_inner_hard_quota', 10))
    (self
    .one_model_inner_hard_calc_ltr_enricher(
      item_table="inner_photo_hard_item_table",
      u0_pow_ratio_ltr = get_dynamic_param(ab('u0_pow_ratio_ltr', 1.0))
    )
    .one_model_sort_enricher(
      item_table="inner_photo_hard_item_table",
      ltr_attr="delivery_rate"
    )
    .one_model_truncate_enricher(
      item_table="inner_photo_hard_item_table",
      quota=get_dynamic_param(inner_hard_quota_ab)
    )
    )

    return self

  @module()
  def one_model_inner_soft_photo_sort(self: Self, is_nearline: bool):
    inner_soft_quota_ab = (ab('one_model_nearline_prerank_inner_soft_quota', 150)
                           if is_nearline 
                           else ab('one_model_prerank_inner_soft_quota', 10))
    (self
    .one_model_inner_soft_calc_ltr_enricher(
      item_table="inner_photo_soft_item_table",
      default_soft_mobile_roi = get_dynamic_param(ab("default_soft_mobile_roi", 0.5))
    )
    .one_model_sort_enricher(
      item_table="inner_photo_soft_item_table",
      ltr_attr="prerank_score"
    )
    .one_model_truncate_enricher(
      item_table="inner_photo_soft_item_table",
      quota=get_dynamic_param(inner_soft_quota_ab)
    )
    )

    return self

  @module()
  def one_model_outer_sort(self: Self, is_nearline: bool):
    outer_soft_quota_ab = (ab('one_model_nearline_prerank_outer_soft_quota', 150)
                           if is_nearline 
                           else ab('one_model_prerank_outer_soft_quota', 10))
    outer_hard_quota_ab = (ab('one_model_nearline_prerank_outer_hard_quota', 150)
                           if is_nearline 
                           else ab('one_model_prerank_outer_hard_quota', 10))

    (self.if_(f'{ab("enable_one_model_auto_cpa_bid_fix", False)} > 0').
      outer_auto_cpa_bid_enricher(
      input_table_name = "outer_hard_photo_item_table",
      enable_prerank_deep_min_bid_coef = '{{' + ab("enable_prerank_deep_min_bid_coef", False) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_prerank_fuse_adjust_cpa_bid = '{{' + ab("enable_prerank_fuse_adjust_cpa_bid", False) + '}}',
      enable_prerank_fuse_purchase = '{{' + ab("enable_prerank_fuse_purchase", False) + '}}',
      prerank_fuse_adjust_coef = '{{' + ab("prerank_fuse_adjust_coef", 1.0) + '}}',
      enable_prerank_roas_twin_mcb = '{{' + ab("enable_prerank_roas_twin_mcb", False) + '}}',
      prerank_roas_twin_deep_upper_bound = '{{' + ab("prerank_roas_twin_deep_upper_bound", 100.0) + '}}',
      prerank_roi_mcb_q_init = '{{' + ab("prerank_roi_mcb_q_init", 1.0) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite_price = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite_price", False) + '}}',
      mh_tcpl_auto_cpa_bid_seed_str = '{{' + ab("mh_tcpl_auto_cpa_bid_seed_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_tag", 0) + '}}',
      mh_tcpl_autobid_multiplier_str = '{{' + ab("mh_tcpl_autobid_multiplier_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_price_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_price_tag", 0) + '}}',
      enable_rta_modify_bid = '{{' + ab("enable_rta_modify_bid", False) + '}}',
      rta_modify_bid_up_ratio = '{{' + ab("rta_modify_bid_up_ratio", 1.0) + '}}',
      rta_modify_bid_down_ratio = '{{' + ab("rta_modify_bid_down_ratio", 1.0) + '}}',
      enable_prerank_roas_v202303 = '{{' + ab("enable_prerank_roas_v202303", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_first_day_v202303 = '{{' + ab("enable_prerank_first_day_v202303", False) + '}}',
      pre_sc_k0_ltv1 = '{{' + ab("pre_sc_k0_ltv1", 0.0) + '}}',
      pre_sc_k0_ltv7 = '{{' + ab("pre_sc_k0_ltv7", 0.0) + '}}',
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_cid_roas_modify_mock_ltv = '{{' + ab("enable_cid_roas_modify_mock_ltv", False) + '}}',
      enable_preranking_cpc_bid_for_ltr = '{{' + ab("enable_preranking_cpc_bid_for_ltr", False) + '}}',
      enable_rta_mcb_dy_bidding = '{{' + ab("enable_rta_mcb_dy_bidding", False) + '}}',
      cid_roas_mock_cvr = '{{' + ab("cid_roas_mock_cvr", 1.0) + '}}',
      cid_roas_mock_ltv = '{{' + ab("cid_roas_mock_ltv", 0.001) + '}}',
      enable_prerank_iaa_roas_predict = '{{' + ab("enable_prerank_iaa_roas_predict", False) + '}}',
      enable_prerank_roas_iaap_predict = '{{' + ab("enable_prerank_roas_iaap_predict", False) + '}}',
      prerank_iaap_roas_ad_ltv_default = '{{'+ ab("prerank_iaap_roas_ad_ltv_default", 1.0) + '}}',
      mini_game_prerank_puchase_ltv_ratio = '{{'+ ab("mini_game_prerank_puchase_ltv_ratio", 1.0) + '}}',
      mini_game_prerank_puchase_ltv_bias = '{{'+ ab("mini_game_prerank_puchase_ltv_bias", 0.0) + '}}',
      mini_game_prerank_adv_ltv_ratio = '{{'+ ab("mini_game_prerank_adv_ltv_ratio", 1.0) + '}}',
      mini_game_prerank_adv_ltv_bias = '{{'+ ab("mini_game_prerank_adv_ltv_bias", 0.0) + '}}',
      enable_prerank_iaap_roas_ad_ltv = '{{'+ ab("enable_prerank_iaap_roas_ad_ltv", False) + '}}',
      enable_prerank_iaa_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_roas_ad_ltv", False) + '}}',
      enable_prerank_iaa_7d_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_7d_roas_ad_ltv", False) + '}}',
      enable_prerank_iaap_roas_fix_ltv = '{{' + ab("enable_prerank_iaap_roas_fix_ltv", False) + '}}',
      enable_mini_game_prerank_7r_bid = '{{' + ab("enable_mini_game_prerank_7r_bid", False) + '}}',
      prerank_iaap_roas_ltv_isonitic_ratio = '{{'+ ab("prerank_iaap_roas_ltv_isonitic_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_roas_ceiling = '{{' + ab("enable_prerank_fiction_iaa_roas_ceiling", False) + '}}',
      prerank_fiction_iaa_ceiling_ratio = '{{'+ ab("prerank_fiction_iaa_ceiling_ratio", 1.0) + '}}',
      enable_prerank_fiction_iap_roas_ceiling = '{{' + ab("enable_prerank_fiction_iap_roas_ceiling", False) + '}}',
      prerank_fiction_iap_ceiling_ratio = '{{'+ ab("prerank_fiction_iap_ceiling_ratio", 1.0) + '}}',
      enable_prerank_fiction_iap_not_na_bid = '{{' + ab("enable_prerank_fiction_iap_not_na_bid", False) + '}}',
      prerank_fiction_iap_not_na_bid_ratio = '{{'+ ab("prerank_fiction_iap_not_na_bid_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_not_na_bid = '{{' + ab("enable_prerank_fiction_iaa_not_na_bid", False) + '}}',
      prerank_fiction_iaa_not_na_bid_ratio = '{{'+ ab("prerank_fiction_iaa_not_na_bid_ratio", 1.0) + '}}',
      enable_prerank_fiction_iaa_soft_ceiling = '{{' + ab("enable_prerank_fiction_iaa_soft_ceiling", False) + '}}',
      prerank_fiction_iaa_soft_ceiling_ratio = '{{'+ ab("prerank_fiction_iaa_soft_ceiling_ratio", 1.0) + '}}',
      enable_prerank_game_iaa_roas_ceiling = '{{' + ab("enable_prerank_game_iaa_roas_ceiling", False) + '}}',
      prerank_game_iaa_ceiling_ratio = '{{' + ab("prerank_game_iaa_ceiling_ratio", 1.0) + '}}',
      enable_prerank_playlet_iaa_predict_cmd = '{{' + ab("enable_prerank_playlet_iaa_predict_cmd", False) + '}}',
      enable_prerank_playlet_iap_predict_cmd_normal = '{{' + ab("enable_prerank_playlet_iap_predict_cmd_normal", False) + '}}',
      prerank_iaa_adjust_coef = '{{' + ab("prerank_iaa_adjust_coef", 1.0) + '}}',
      enable_iap_prerank_ecpc_by_bounus_coef = '{{' + ab("enable_iap_prerank_ecpc_by_bounus_coef", False) + '}}',
      prerank_bonus_coef_range_list_str = '{{' + ab("prerank_bonus_coef_range_list_str", "3,4,1.05;4,5,1.1") + '}}',
      prerank_iaa_7r_adjust_coef = '{{' + ab("prerank_iaa_7r_adjust_coef", 1.0) + '}}',
      prerank_playlet_iap_roas_ltv_cali_ratio = '{{'+ ab("prerank_playlet_iap_roas_ltv_cali_ratio", 1.0) + '}}',
      enable_preank_big_r_explore_iaap = '{{' + ab("enable_preank_big_r_explore_iaap", False) + '}}',
      enable_preank_big_r_explore_iap = '{{' + ab("enable_preank_big_r_explore_iap", False) + '}}',
      enable_preank_big_r_explore_iaa = '{{' + ab("enable_preank_big_r_explore_iaa", False) + '}}',
      prerank_big_r_explore_ratio = '{{' + ab("prerank_big_r_explore_ratio", 1.0) + '}}',
      big_r_exlore_ctrl_group_tag = '{{' + ab("big_r_exlore_ctrl_group_tag", "default") + '}}',
      big_r_exlore_ctrl_dim_tag = '{{' + ab("big_r_exlore_ctrl_dim_tag", "iap") + '}}',
      enable_kgame_big_r_explore_ctrl_pid = '{{' + ab("enable_kgame_big_r_explore_ctrl_pid", False) + '}}',
      enable_mini_game_big_r_ee_strategy = '{{' + ab("enable_mini_game_big_r_ee_strategy", False) + '}}',
      mini_game_big_r_ee_explore_k = '{{'+ ab("mini_game_big_r_ee_explore_k", 1.0) + '}}',
      mini_game_big_r_ee_explore_b = '{{'+ ab("mini_game_big_r_ee_explore_b", 0.0) + '}}',
      enable_mini_game_new_big_r_tag = '{{' + ab("enable_mini_game_new_big_r_tag", False) + '}}',
      prerank_big_r_explore_bias = '{{'+ ab("prerank_big_r_explore_bias", 1.0) + '}}',
      enable_prerank_auto_cpa_bid_exp = '{{'+ ab("enable_prerank_auto_cpa_bid_exp", False) + '}}',
      enable_rank_adlist_cache = '{{' + ab("enable_rank_adlist_cache", False) + '}}',
      enable_rank_adlist_cache_skip_prerank_sort = '{{' + ab("enable_rank_adlist_cache_skip_prerank_sort", False) + '}}',
    ).outer_auto_cpa_bid_enricher(
      input_table_name = "outer_soft_photo_item_table",
      enable_prerank_deep_min_bid_coef = '{{' + ab("enable_prerank_deep_min_bid_coef", False) + '}}',
      enable_purchase_pay_set_first_industry = '{{' + ab("enable_purchase_pay_set_first_industry", False) + '}}',
      enable_prerank_fuse_adjust_cpa_bid = '{{' + ab("enable_prerank_fuse_adjust_cpa_bid", False) + '}}',
      enable_prerank_fuse_purchase = '{{' + ab("enable_prerank_fuse_purchase", False) + '}}',
      prerank_fuse_adjust_coef = '{{' + ab("prerank_fuse_adjust_coef", 1.0) + '}}',
      enable_prerank_roas_twin_mcb = '{{' + ab("enable_prerank_roas_twin_mcb", False) + '}}',
      prerank_roas_twin_deep_upper_bound = '{{' + ab("prerank_roas_twin_deep_upper_bound", 100.0) + '}}',
      prerank_roi_mcb_q_init = '{{' + ab("prerank_roi_mcb_q_init", 1.0) + '}}',
      enable_preranking_thorough_billing_separation = '{{' + ab("enable_preranking_thorough_billing_separation", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite", False) + '}}',
      enable_ptds_auto_cpa_bid_rewrite_price = '{{' + ab("enable_ptds_auto_cpa_bid_rewrite_price", False) + '}}',
      mh_tcpl_auto_cpa_bid_seed_str = '{{' + ab("mh_tcpl_auto_cpa_bid_seed_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_tag", 0) + '}}',
      mh_tcpl_autobid_multiplier_str = '{{' + ab("mh_tcpl_autobid_multiplier_str", "") + '}}',
      ptds_auto_cpa_bid_rewrite_price_tag = '{{' + ab("ptds_auto_cpa_bid_rewrite_price_tag", 0) + '}}',
      enable_rta_modify_bid = '{{' + ab("enable_rta_modify_bid", False) + '}}',
      rta_modify_bid_up_ratio = '{{' + ab("rta_modify_bid_up_ratio", 1.0) + '}}',
      rta_modify_bid_down_ratio = '{{' + ab("rta_modify_bid_down_ratio", 1.0) + '}}',
      enable_prerank_roas_v202303 = '{{' + ab("enable_prerank_roas_v202303", False) + '}}',
      enable_prerank_7roas_v202303 = '{{' + ab("enable_prerank_7roas_v202303", False) + '}}',
      enable_prerank_credit_new = '{{' + ab("enable_prerank_credit_new", False) + '}}',
      prerank_live_ltv_support_twin_bid = '{{' + ab("prerank_live_ltv_support_twin_bid", False) + '}}',
      enable_prerank_first_day_v202303 = '{{' + ab("enable_prerank_first_day_v202303", False) + '}}',
      pre_sc_k0_ltv1 = '{{' + ab("pre_sc_k0_ltv1", 0.0) + '}}',
      pre_sc_k0_ltv7 = '{{' + ab("pre_sc_k0_ltv7", 0.0) + '}}',
      enable_cid_roas_modify_bid = '{{' + ab("enable_cid_roas_modify_bid", False) + '}}',
      enable_cid_roas_modify_mock_ltv = '{{' + ab("enable_cid_roas_modify_mock_ltv", False) + '}}',
      enable_preranking_cpc_bid_for_ltr = '{{' + ab("enable_preranking_cpc_bid_for_ltr", False) + '}}',
      enable_rta_mcb_dy_bidding = '{{' + ab("enable_rta_mcb_dy_bidding", False) + '}}',
      cid_roas_mock_cvr = '{{' + ab("cid_roas_mock_cvr", 1.0) + '}}',
      cid_roas_mock_ltv = '{{' + ab("cid_roas_mock_ltv", 0.001) + '}}',
      enable_prerank_iaa_roas_predict = '{{' + ab("enable_prerank_iaa_roas_predict", False) + '}}',
      enable_prerank_roas_iaap_predict = '{{' + ab("enable_prerank_roas_iaap_predict", False) + '}}',
      prerank_iaap_roas_ad_ltv_default = '{{'+ ab("prerank_iaap_roas_ad_ltv_default", 1.0) + '}}',
      enable_prerank_iaap_roas_ad_ltv = '{{'+ ab("enable_prerank_iaap_roas_ad_ltv", False) + '}}',
      enable_prerank_iaa_roas_ad_ltv = '{{' + ab("enable_prerank_iaa_roas_ad_ltv", False) + '}}',
      prerank_iaa_adjust_coef = '{{' + ab("prerank_iaa_adjust_coef", 1.0) + '}}',
    ).end_if_()    
    .one_model_outer_calc_ltr_enricher(
      item_table="outer_soft_photo_item_table",
    )
    .one_model_outer_calc_ltr_enricher(
      item_table="outer_hard_photo_item_table",
    )
    .one_model_sort_enricher(
      item_table="outer_soft_photo_item_table",
      ltr_attr="prerank_cpm_ltr",
    )
    .one_model_sort_enricher(
      item_table="outer_hard_photo_item_table",
      ltr_attr="prerank_cpm_ltr",
    ).outer_prerank_diversity_filter
    (
      item_table="outer_soft_photo_item_table",
      enable_prerank_account_limit_outloop_one_model = '{{' + ab("enable_prerank_account_limit_outloop_one_model", False) + '}}',
      prerank_per_account_ad_num_outloop_one_model = '{{' + ab("prerank_per_account_ad_num_outloop_one_model", 5) + '}}',
    )
    .outer_prerank_diversity_filter
    (
      item_table="outer_hard_photo_item_table",
      enable_prerank_account_limit_outloop_one_model = '{{' + ab("enable_prerank_account_limit_outloop_one_model", False) + '}}',
      prerank_per_account_ad_num_outloop_one_model = '{{' + ab("prerank_per_account_ad_num_outloop_one_model", 5) + '}}',
    )
    .one_model_truncate_enricher(
      item_table="outer_soft_photo_item_table",
      quota=get_dynamic_param(outer_soft_quota_ab)
    )
    .one_model_truncate_enricher(
      item_table="outer_hard_photo_item_table",
      quota=get_dynamic_param(outer_hard_quota_ab)
    )

    )
    return self

  @module()
  def one_model_inner_hard_flow(self, is_nearline: bool, **kwargs):
    with data_manager:
      (current_flow()
    .one_model_inner_hard_predict()
    .inner_photo_hard_feature_bid()
    .inner_photo_hard_predict_callback()
    .inner_photo_hard_bid_guard()
    .inner_photo_hard_remote_and_diff()
    .one_model_inner_hard_photo_sort(is_nearline=is_nearline)
    .prerank_trace_log_context_mixer(
      is_trace_log = "is_trace_log",
      trace_log_name = "inner_hard_trace_log",
      target_trace_type = 24
    )
    .prerank_trace_log_item_mixer(
      input_table_name = "inner_photo_hard_item_table",
      is_trace_log = "is_trace_log",
      trace_log_name = "inner_hard_trace_log",
      need_record = True
    )
    .color_debug_creative_enricher(
      item_table = "inner_photo_hard_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    )
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "inner_photo_hard_item_table",
      enable_filter_zero_auto_bid = '{{' + ab("enable_filter_zero_auto_bid", False) + '}}',
      enable_feature_bid_filter_zero = '{{' + ab("enable_feature_bid_filter_zero", False) + '}}')

      )
    return self

  @module()
  def one_model_inner_soft_flow(self, is_nearline: bool, **kwargs):
    with data_manager:
      (current_flow()
    .one_model_inner_soft_predict()
    .inner_photo_soft_feature_bid()
    .inner_photo_soft_predict_callback()
    .inner_photo_soft_bid_guard()
    .inner_photo_soft_remote_and_diff()
    .one_model_inner_soft_photo_sort(is_nearline=is_nearline)
    .prerank_trace_log_context_mixer(
      is_trace_log = "is_trace_log",
      trace_log_name = "inner_soft_trace_log",
      target_trace_type = 24
    )
    .prerank_trace_log_item_mixer(
      input_table_name = "inner_photo_soft_item_table",
      is_trace_log = "is_trace_log",
      trace_log_name = "inner_soft_trace_log",
      need_record = True
    )
    .color_debug_creative_enricher(
      item_table = "inner_photo_soft_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    )
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "inner_photo_soft_item_table",
      enable_filter_zero_auto_bid = '{{' + ab("enable_filter_zero_auto_bid", False) + '}}',
      enable_feature_bid_filter_zero = '{{' + ab("enable_feature_bid_filter_zero", False) + '}}')

      )
    return self

  @module()
  def one_model_outer_flow(self, is_nearline: bool, **kwargs):
    with data_manager:
      (current_flow()
    .one_model_outer_predict()
    .outer_photo_feature_bid()
    .outer_predict_callback()
    .outer_bid_guard()
    .outer_photo_remote_and_diff()
    .one_model_outer_sort(is_nearline=is_nearline)
    .prerank_trace_log_context_mixer(
      is_trace_log = "is_trace_log",
      trace_log_name = "outer_trace_log",
      target_trace_type = 20
    )
    .prerank_trace_log_item_mixer(
      input_table_name = "outer_hard_photo_item_table",
      is_trace_log = "is_trace_log",
      trace_log_name = "outer_trace_log",
    )
    .prerank_trace_log_item_mixer(
      input_table_name = "outer_soft_photo_item_table",
      is_trace_log = "is_trace_log",
      trace_log_name = "outer_trace_log",
      need_record = True
    )
    .color_debug_creative_enricher(
      item_table = "outer_soft_photo_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    )
    .color_debug_creative_enricher(
      item_table = "outer_hard_photo_item_table",
      debug_creative_id_common_attr = "debug_creative_id",
      debug_creative_reason_common_attr = "debug_creative_reason",
    )
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "outer_soft_photo_item_table"
    )
    .prerank_truncate_mixer(
      is_trace_log = "is_trace_log",
      input_table_name = "outer_hard_photo_item_table"
    )

      )
    return self

  @module()
  def inner_hard_nearline_flow(self, is_nearline: bool, **kwargs):
    with data_manager:
      (current_flow()
    .inner_photo_hard_feature_bid()
      )
    return self

  @module()
  def inner_live_nearline_flow(self, is_nearline: bool, **kwargs):
    with data_manager:
      (current_flow()
    .live_feature_bid()
      )
    return self


#=//==================================================== Flow Define Start =====================================================
inner_hard_flow = AdPrerankFlow(name="inner_hard")

with inner_hard_flow, data_manager:
  inner_hard_flow \
  .common_prepare(name="inner_hard_prerank_prepare") \
  .if_("has_inner_hard_photo == 1") \
    .inner_hard_branch(name="IHB") \
  .end_()

inner_soft_flow = AdPrerankFlow(name="inner_soft")

with inner_soft_flow, data_manager:
  inner_soft_flow \
  .common_prepare(name="inner_soft_prerank_prepare") \
  .if_("has_inner_soft_photo == 1") \
    .inner_soft_branch(name="ISB") \
  .end_()

live_flow = AdPrerankFlow(name="live")

with live_flow, data_manager:
  live_flow \
  .common_prepare(name="live_prerank_prepare") \
  .if_("has_live == 1") \
    .live_branch(name="LB") \
  .end_()

outer_flow = AdPrerankFlow(name="outer")

with outer_flow, data_manager:
  outer_flow \
  .common_prepare(name="outer_prerank_prepare") \
  .if_("has_outer_photo == 1") \
    .outer_branch(name="OB") \
  .end_()

one_model_inner_hard_nearline_flow = AdPrerankFlow(name="one_model_inner_hard_nearline")
with one_model_inner_hard_nearline_flow, data_manager:
  (one_model_inner_hard_nearline_flow
  .common_prepare(name="one_model_nearline_inner_hard_prerank_prepare")
  .if_("has_inner_hard_photo == 1")
    .one_model_inner_hard_flow(name="OM_IH_N", is_nearline=True)
  .end_()
  )

one_model_inner_soft_nearline_flow = AdPrerankFlow(name="one_model_inner_soft_nearline")
with one_model_inner_soft_nearline_flow, data_manager:
  (one_model_inner_soft_nearline_flow
  .common_prepare(name="one_model_nearline_inner_soft_prerank_prepare")
  .if_("has_inner_soft_photo == 1")
    .one_model_inner_soft_flow(name="OM_IS_N", is_nearline=True)
  .end_()
  )

one_model_outer_nearline_flow = AdPrerankFlow(name="one_model_outer_nearline")
with one_model_outer_nearline_flow, data_manager:
  (one_model_outer_nearline_flow
  .common_prepare(name="one_model_nearline_outer_prerank_prepare")
  .if_("has_outer_photo == 1")
    .one_model_outer_flow(name="OM_O_N",is_nearline=True)
  .end_()
  )

one_model_inner_hard_flow = AdPrerankFlow(name="one_model_inner_hard")
with one_model_inner_hard_flow, data_manager:
  (one_model_inner_hard_flow
  .common_prepare(name="one_model_inner_hard_prerank_prepare")
  .if_("has_inner_hard_photo == 1")
    .one_model_inner_hard_flow(name="OM_IH", is_nearline=False)
  .end_()
  )

one_model_inner_soft_flow = AdPrerankFlow(name="one_model_inner_soft")
with one_model_inner_soft_flow, data_manager:
  (one_model_inner_soft_flow
  .common_prepare(name="one_model_inner_soft_prerank_prepare")
  .if_("has_inner_soft_photo == 1")
    .one_model_inner_soft_flow(name="OM_IS", is_nearline=False)
  .end_()
  )

one_model_outer_flow = AdPrerankFlow(name="one_model_outer")
with one_model_outer_flow, data_manager:
  (one_model_outer_flow
  .common_prepare(name="one_model_outer_prerank_prepare")
  .if_("has_outer_photo == 1")
    .one_model_outer_flow(name="OM_O", is_nearline=False)
  .end_()
  )

inner_hard_nearline_flow = AdPrerankFlow(name="inner_hard_nearline")
with inner_hard_nearline_flow, data_manager:
  (inner_hard_nearline_flow
  .ad_common_prepare(name="inner_hard_nearline_prerank_prepare")
  .if_("has_inner_hard_photo == 1")
    .inner_hard_nearline_flow(name="IH_N",is_nearline=True)
  .end_()
  )

inner_live_nearline_flow = AdPrerankFlow(name="inner_live_nearline")
with inner_live_nearline_flow, data_manager:
  (inner_live_nearline_flow
  .ad_common_prepare(name="inner_live_nearline_prerank_prepare")
  .if_("has_live == 1")
    .inner_live_nearline_flow(name="IL_N",is_nearline=True)
  .end_()
  )


# Service definition
# 定义service维度的一些配置, add_leaf_flows 方法将各个 flow 绑定到对应的 request_type 下
#default_flow = AdPrerankFlow(name="default").default_flow()

def CookService(service):
  service.return_common_attrs([
  ])
  service.return_item_attrs([
  ])
  service.common_attrs_from_request=["llsid", "deep_group_tag", "group_tag_enum", "page_id", "bid_service_failed", "is_one_model_nearline_flow",
     "sub_page_id", "request_flow_type", "deep_coef_groug_tag", "flow_type", "pos_id", "is_rewarded", "is_shelf_merchant_traffic",
     "target_deploy", "enable_dragon_predict", "is_prerank", "is_prerank_ps_succ", "is_inspire_live",
     "is_inspire_mix", "is_follow_traffic", "is_buyer_home_page_traffic", "inner_loop_fanstop_retrieval",
     "gender", "age_segment", "inner_buyer_ui_type", "is_hit_cache", "is_unlogin_user", "enable_prerank_rank_ica", "buyer_effective_type", "consum_power_tag", "enable_prerank_move_to_ad_server",
     "ad_router_ad_fantop_action","ad_router_ad_user_realtime_action_attr_map_pb", "ad_router_ad_user_realtime_action_attr_map_bs", "ad_router_ad_user_realtime_action_pb",
     "ad_router_ad_user_realtime_action_bs", "ad_router_eb_ad_user_realtime_action_pb", "ad_router_ad_context", "ad_router_user_info_bin", "serialized_reco_user_info",
     "ad_router_truncated_merge_user_info_bs"]
  service.item_attrs_from_request=["unit_id", "creative_id", "live_creative_type", "auto_cpa_bid", "cpa_bid_coef", "item_type", "auto_dark_control", "payment_per_order", "first_industry_id","new_spu_tag",
    "project_ocpx_action_type", "creative_material_type"]
  service.IGNORE_NO_SOURCE_ATTR=["unit_id", "creative_id", "live_creative_type", "is_inner_ad",
              "ad_queue_type", "is_fan_follow", "is_no_sale_live_stream_ad", "cpa_bid_coef","new_spu_tag","project_ocpx_action_type", "creative_material_type",
              "user_id_for_ab_param", "fanstop_flow_type", "photo_id", "auto_dark_control", "payment_per_order", "first_industry_id"] + bid_info_columns
  service.CHECK_UNUSED_ATTR = False
  service.AUTO_INJECT_META_DATA = False
  service.ENABLE_PROCESSOR_STABLE_NAME=True
  current_folder = os.path.dirname(os.path.abspath(__file__))
  service.add_leaf_flows(leaf_flows=[inner_hard_flow], request_type="inner_hard") \
         .add_leaf_flows(leaf_flows=[inner_soft_flow], request_type="inner_soft") \
         .add_leaf_flows(leaf_flows=[live_flow], request_type="live") \
         .add_leaf_flows(leaf_flows=[outer_flow], request_type="outer") \
         .add_leaf_flows(leaf_flows=[one_model_inner_hard_flow], request_type="one_model_inner_hard") \
         .add_leaf_flows(leaf_flows=[one_model_inner_soft_flow], request_type="one_model_inner_soft") \
         .add_leaf_flows(leaf_flows=[one_model_outer_flow], request_type="one_model_outer") \
         .add_leaf_flows(leaf_flows=[one_model_inner_hard_nearline_flow], request_type="one_model_inner_hard_nearline") \
         .add_leaf_flows(leaf_flows=[one_model_inner_soft_nearline_flow], request_type="one_model_inner_soft_nearline") \
         .add_leaf_flows(leaf_flows=[one_model_outer_nearline_flow], request_type="one_model_outer_nearline") \
         .add_leaf_flows(leaf_flows=[inner_hard_nearline_flow], request_type="inner_hard_nearline") \
         .add_leaf_flows(leaf_flows=[inner_live_nearline_flow], request_type="inner_live_nearline") \
         .draw(to_dragonfly_viz = True, mode = "remote")

service = LeafService(kess_name="ad-dragon-prerank-server")
CookService(service)

gen_service_graph_def(service, mod = "remote")
#gen_service_graph_def(service)

ad_config = {
  "grpc" : {
    "test" : False,
    "server" : {
      "kess_name" : "USE_KSN_AS_SERVICE",  # kess_name 可以自动扩展为 kess_name + "_" + ksp 分组, default 不扩展
      "port" : 20182,
      "kcs_grpc_port_key" : "AUTO_PORT1",
      "thread_num": 200,  ## default cpu num
      "grpc_cq_num": 4,
      "quit_wait_seconds" : 85,
      "start_warmup_seconds" : 10, #默认 10s
    },
    "client_map": {
      "ad-bid-service-test": "ad-bid-service-test",
      "ad-dragon-bid-service": "ad-dragon-bid-service",
      "ad_bid_client": "ad-bid-service",
      "preranking_predict_client": "ad-predict-router-dsp-retrieval",
      "ranking_prepare_predict_client_other": "ad-predict-router",
      "universe_predict_client": "ad-predict-router-universe"
    }
  }
}

current_folder = os.path.dirname(os.path.abspath(__file__))
service.build(output_file=os.path.join(current_folder, "pub/ad-dragon-prerank-server/config/config.json"),
                extra_fields=ad_config)


