#pragma once
#include <malloc.h>
#include <stdio.h>

#include <algorithm>
#include <atomic>
#include <fstream>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "base/file/file_util.h"
#include "base/thread/thread_pool.h"
#include "base/time/timestamp.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "infra/perfutil/src/perfutil/perfutil.h"
#include "serving_base/jansson/json.h"
#include "serving_base/util/array.h"
#include "teams/ad/ad_base/src/container/concurrent_queue.h"
#include "teams/ad/ad_nn/model/embedding_storage.h"
#include "teams/ad/ad_nn/model/lru_cache.h"
#include "teams/ad/ad_nn/model/tuning_util.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/reco-arch/embedding_manager/utils/memory_util.h"
#include "third_party/boost/boost/lockfree/queue.hpp"
#include "third_party/folly/ThreadCachedInt.h"

namespace ks {
namespace ad_nn {
DECLARE_bool(enable_fixed_cache);
DECLARE_bool(enable_mem_interleave);
DECLARE_uint64(fixed_cache_max_field_size);
DECLARE_int32(fixed_cache_update_interval);
DECLARE_int32(fixed_cache_flush_interval);
DECLARE_uint64(fixed_cache_max_reserve_size);
DECLARE_int32(lru_cache_flush_interval);
DECLARE_int32(embedding_cache_expire_time);
DECLARE_string(shm_cache_root_path);
DECLARE_bool(btq_enable_hash_field_opt);
DECLARE_int32(evict_interval_base);
DECLARE_double(max_fix_capacity_factor);
DECLARE_double(min_fix_capacity_factor);
DECLARE_bool(debug_embedding);

DECLARE_bool(use_idt);
DECLARE_int32(debug_embedding_lookup_slot);

extern const std::unordered_set<uint64_t> hybrid_check_sign_sets;

template <uint32_t EMBEDDING_LEN>
struct EmbeddingNodeTpl {
  float data[EMBEDDING_LEN];
  void Initialize() {
    // 这个函数只有在更新参数发生逐出时会调用，由于逐出后会马上填充
    // 所以这个函数可以不做任何事情，只是兼容下 lru_cache 的调用逻辑
  }
};
// 该结构用于 fix cache map
template <uint32_t EMBEDDING_LEN>
struct FixedCacheMemItem {
  uint64_t timestamp = 0;
  EmbeddingNodeTpl<EMBEDDING_LEN> embedding;
};
// 该结构用于 fix cache 队列以及序列化和反序列化
template <uint32_t EMBEDDING_LEN>
struct FixedCacheItem {
  uint32_t field;
  uint64_t sign;
  FixedCacheMemItem<EMBEDDING_LEN> embedding;
};

enum class EmbeddingOperation {
  SUM,
  CONCAT,
};
struct FieldEmbeddingInfo {
  FieldEmbeddingInfo() {}
  FieldEmbeddingInfo(uint32_t size, uint64_t capacity, uint32_t embedding_len,
                     uint32_t slot, const std::string &op_str = "SUM",
                     uint32_t concat_len = 0)
      : field_size(size),
        field_capacity(capacity),
        embedding_len(embedding_len),
        field_slot(slot),
        concat_len(concat_len) {
    if (op_str == "SUM") {
      op = EmbeddingOperation::SUM;
    } else if (op_str == "CONCAT") {
      op = EmbeddingOperation::CONCAT;
    } else {
      op = EmbeddingOperation::SUM;
    }
  }
  uint32_t field_size = 0;
  uint64_t field_capacity = 0;
  uint32_t embedding_len = 0;
  uint32_t field_slot = 0;
  EmbeddingOperation op = EmbeddingOperation::SUM;
  uint32_t concat_len = 0;
};
using FieldEmbeddingInfoMap = std::map<uint32_t, FieldEmbeddingInfo>;

enum class EmbCacheType {
  HASH_FIELD_CACHE,
  FIXED_CACHE,
  LRU_CACHE,
  ZERO_CACHE,
};

class HybridEmbCache {
 public:
  using EmbFlushCallback = std::function<void(uint64_t version)>;
  virtual void Init(const std::map<int, FieldEmbeddingInfo> &slot_infos,
                    const std::shared_ptr<RandValue> &random_values, uint64_t capacity,
                    uint32_t load_factor, const std::string &mmap_path,
                    const std::string &embedding_cache_type, uint32_t loader_id) = 0;
  virtual const float *GetW(const size_t &field, const uint64_t &sign) const = 0;
  virtual const float *GetWDirectly(const size_t slot, const uint64_t sign) const = 0;
  virtual const float *GetWDirectly(const size_t slot, const uint32_t sign) const = 0;
  virtual bool GetBatchW(const size_t slot, const uint64_t *batch_signs,
                         uint32_t batch_size,
                         std::vector<const float *> *embedding_results) = 0;
  virtual bool GetBatchW(const size_t slot, const uint32_t *batch_signs,
                         uint32_t batch_size,
                         std::vector<const float *> *embedding_results) = 0;
  virtual void Put(const size_t &field, const uint64_t &sign,
                   const base::ConstArray<float> &embedding, uint64_t timestamp = 0) = 0;
  virtual void UpdateStats() const = 0;
  virtual uint64_t GetEmbeddingCount() const = 0;
  virtual void UpdateVersion(uint64_t version) = 0;
  virtual void SetFlushCallback(EmbFlushCallback callback) = 0;

  virtual void ClearZeroCache() = 0;
  virtual void ClearHashFieldCache() = 0;
  virtual void ClearFixedCache() = 0;
  virtual void ClearLruCache() = 0;
  // idt 相关接口
  virtual bool GetOneFieldSignSize(EmbCacheType cache_type, uint32_t field_index,
                                   uint64_t &sign_size) = 0;  // NOLINT
  virtual bool GetNextValidField(EmbCacheType cache_type, uint32_t field_index,
                                 uint32_t &next_field) = 0;  // NOLINT
  virtual bool GetOneFieldWeights(EmbCacheType cache_type, uint32_t field_index,
                                  uint64_t sign_index, uint64_t sign_size, uint64_t *id,
                                  float *val, uint64_t *timestamp) = 0;
  virtual bool PutOneFieldWeights(EmbCacheType cache_type, uint32_t field_index,
                                  uint64_t sign_index, uint64_t sign_size,
                                  const uint64_t *id, const float *val,
                                  const uint64_t *timestamp) = 0;
  virtual uint64_t GetIdtUpdateEmbeddingCount() = 0;
  virtual void ResetIdtUpdateEmbeddingCount() = 0;
  virtual ~HybridEmbCache() {}
};

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
class HybridEmbCacheTpl : public HybridEmbCache {
  // 对于 size <= fixed_cache_max_field_size 的 field 采用双 buffer cache
  // 其他 size 较大的 field 采用 lru cache
  using EmbLruCache = LruCache<uint64_t, EmbeddingNodeTpl<EMBEDDING_LEN>, LruBuffer>;
  using EmbFixedCache = absl::flat_hash_map<uint64_t, FixedCacheMemItem<EMBEDDING_LEN>>;

 public:
  HybridEmbCacheTpl()
      : cache_thread_pool_(2), fixed_cache_queue_(kFixCacheQueueCapacity) {}

  ~HybridEmbCacheTpl() {
    LOG(INFO) << "thread pool stop";
    is_running_ = false;
    cache_thread_pool_.StopAll();
    if (fixed_cache_flush_buffer_ != nullptr) {
      free(fixed_cache_flush_buffer_);
    }
    LOG(INFO) << "thread pool stoped";
  }

  void Init(const std::map<int, FieldEmbeddingInfo> &slot_infos,
            const std::shared_ptr<RandValue> &random_values, uint64_t capacity,
            uint32_t load_factor, const std::string &mmap_path,
            const std::string &embedding_cache_type, uint32_t loader_id = 0) override;
  void InitZeroCache(size_t zero_cache_size);
  void InitHashFields(const std::vector<uint64_t> &hash_field_sizes);
  void InitFixCache();
  void UpdateFixedCache();
  bool FlushFixCache();
  void FlushLruCache();
  void ClearZeroCache() {
    LOG(INFO) << "start to clear zero cache"
              << ", current embedding_count: " << zero_cache_embedding_count_;
    if (zero_cache_ == nullptr || zero_cache_capacity_ <= 0) {
      LOG(INFO) << "zero cache is empty";
      return;
    }
    for (size_t idx = 0; idx < zero_cache_capacity_; ++idx) {
      zero_cache_[idx].timestamp = 0;
      std::fill_n(zero_cache_[idx].embedding.data, EMBEDDING_LEN, 0.0);
    }
    zero_cache_embedding_count_ = 0;
    LOG(INFO) << "clear zero cache success";
  }
  void ClearHashFieldCache() {
    LOG(INFO) << "start to clear hash field cache"
              << ", current embedding_count: " << hash_field_embedding_count_;
    if (hash_field_cache_.empty() || hash_field_sizes_.size() <= 0) {
      LOG(INFO) << "hash field cache is empty";
      return;
    }
    for (size_t idx = 0; idx < hash_field_sizes_.size(); ++idx) {
      if (hash_field_sizes_[idx] <= 0) {
        continue;
      }
      for (size_t i = 0; i < hash_field_sizes_[idx]; ++i) {
        hash_field_cache_[idx][i].timestamp = 0;
        std::fill_n(hash_field_cache_[idx][i].embedding.data, EMBEDDING_LEN, 0.0);
      }
    }
    hash_field_embedding_count_ = 0;
    LOG(INFO) << "clear hash field cache success";
  }
  // 清空 fixed cache 和 lru cache 的接口，防止写入时触发逐出
  void ClearFixedCache() {
    uint64_t total_count = 0;
    auto &cur_cache = fixed_cache_[1 - current_fixed_cache_];
    for (auto &field_cache : cur_cache) {
      if (field_cache) {
        total_count += field_cache->size();
      }
    }
    LOG(INFO) << "start to clear fixed cache"
              << ", current embedding_count: " << total_count;
    for (size_t i = 0; i < 2; ++i) {
      if (fixed_cache_[i].empty()) {
        LOG(INFO) << "fixed_cache[" << i << "] is empty";
        continue;
      }
      for (auto &iter : fixed_cache_[i]) {
        if (iter == nullptr) {
          continue;
        }
        iter->erase(iter->begin(), iter->end());
      }
      LOG(INFO) << "after clear, fixed_cache[" << i << "] size "
                << fixed_cache_[i].size();
    }
  }

  void ClearLruCache() {
    if (lru_cache_) {
      LOG(INFO) << "start to clear lru cache"
                << ", current embedding_count: " << lru_cache_->Size();
      if (lru_cache_->Clear()) {
        LOG(INFO) << "clear lru cache success";
        return;
      }
      LOG(WARNING) << "clear lru cache failed";
    }
  }
  void UpdateStats() const override {
    falcon::Set(falcon_size_key_.c_str(), GetEmbeddingCount(), falcon::kNonAdditiveGauge);
    if (lru_cache_) {
      LOG_EVERY_N(INFO, 1000) << "embedding length " << EMBEDDING_LEN << " lru size "
                              << lru_cache_->Size() << " capacity "
                              << lru_cache_->Capacity() << " index capacity "
                              << lru_cache_->IndexCapacity();
    }
  }
  uint64_t GetEmbeddingCount() const override {
    uint64_t total_count = hash_field_embedding_count_;
    total_count += zero_cache_embedding_count_;
    if (lru_cache_) {
      total_count += lru_cache_->Size();
    }
    auto &cur_cache = fixed_cache_[1 - current_fixed_cache_];
    for (auto &field_cache : cur_cache) {
      if (field_cache) {
        total_count += field_cache->size();
      }
    }
    return total_count;
  }
  void UpdateVersion(uint64_t version) override { batch_version_ms_ = version; }
  void SetFlushCallback(EmbFlushCallback callback) override {
    flush_callback_ = callback;
  }

  const float *GetW(const size_t &field, const uint64_t &sign) const override;
  const float *GetWDirectly(const size_t slot, const uint64_t sign) const override;
  const float *GetWDirectly(const size_t slot, const uint32_t sign) const override;
  bool GetBatchW(const size_t slot, const uint64_t *batch_signs, uint32_t batch_size,
                 std::vector<const float *> *embedding_results) override;
  bool GetBatchW(const size_t slot, const uint32_t *batch_signs, uint32_t batch_size,
                 std::vector<const float *> *embedding_results) override;
  void Put(const uint64_t &field, const uint64_t &sign,
           const base::ConstArray<float> &embedding, uint64_t timestap = 0) override;

  // idt 相关接口
  bool GetOneFieldSignSize(EmbCacheType cache_type, uint32_t field_index,
                           uint64_t &sign_size) override;
  bool GetNextValidField(EmbCacheType cache_type, uint32_t field_index,
                         uint32_t &next_field) override;
  bool GetOneFieldWeights(EmbCacheType cache_type, uint32_t field_index,
                          uint64_t sign_index, uint64_t sign_size, uint64_t *id,
                          float *val, uint64_t *timestamp) override;
  bool PutOneFieldWeights(EmbCacheType cache_type, uint32_t field_index,
                          uint64_t sign_index, uint64_t sign_size, const uint64_t *id,
                          const float *val, const uint64_t *timestamp) override;
  uint64_t GetIdtUpdateEmbeddingCount() override {
    return idt_update_embedding_count_.load(std::memory_order_relaxed);
  }
  void ResetIdtUpdateEmbeddingCount() override { idt_update_embedding_count_ = 0; }

 private:
  template <typename SignType>
  bool GetBatchWImpl(const size_t slot, const SignType *batch_signs, uint32_t batch_size,
                     std::vector<const float *> *embedding_results);
  template <typename SignType>
  const float *GetWDirectlyImpl(const size_t slot, const SignType batch_signs) const;

  static const float kDefaultEmbedding[EMBEDDING_LEN];
  static const uint64_t kFixCacheQueueCapacity = 65534;  // boost fix size 最大值

  std::atomic_uint64_t zero_cache_embedding_count_ = ATOMIC_VAR_INIT(0);
  FixedCacheMemItem<EMBEDDING_LEN> *zero_cache_;  // 存放 sign 为 0, 下标对应 field id

  // 双 buffer fixed cache 相关结构
  std::atomic_uint current_fixed_cache_ = ATOMIC_VAR_INIT(0);
  std::vector<uint64_t> slot2capacity_;   // 下标为 slot
  std::vector<uint64_t> slot2embed_len_;  // slot -> real embedding len
  // 用于统计 hash_field_cache 有效 embedding 总数
  std::atomic_uint64_t hash_field_embedding_count_ = ATOMIC_VAR_INIT(0);
  std::vector<uint64_t> hash_field_sizes_;  // 下标对应 field id
  std::vector<FixedCacheMemItem<EMBEDDING_LEN> *> hash_field_cache_;  // 下标对应 field id
  std::vector<std::unique_ptr<EmbFixedCache>> fixed_cache_[2];  // 下标对应 field id
  ::thread::ThreadPool cache_thread_pool_;
  boost::lockfree::queue<FixedCacheItem<EMBEDDING_LEN>,
                         boost::lockfree::fixed_sized<true>>
      fixed_cache_queue_;
  reco_arch::utils::MallocArena malloc_arena_;

  std::unique_ptr<EmbLruCache> lru_cache_;
  std::shared_ptr<RandValue> random_values_ = nullptr;
  uint64_t zero_cache_capacity_ = 0;
  uint64_t fix_cache_capacity_ = 0;
  uint64_t lru_capacity_ = 0;

  // for idt
  std::atomic_uint64_t idt_update_embedding_count_ = ATOMIC_VAR_INIT(0);
  // 持久化路径
  std::string zero_cache_path_;
  std::string hash_field_path_;
  std::string fix_file_path_;
  std::string lru_file_path_;
  std::string embedding_cache_type_;
  EmbFlushCallback flush_callback_ = nullptr;
  std::atomic_uint64_t batch_version_ms_ = ATOMIC_VAR_INIT(0);
  static const uint64_t kFixCacheFlushSize = 1 << 18;
  static const uint64_t kFixCacheFlushCapacity =
      kFixCacheFlushSize * sizeof(FixedCacheItem<EMBEDDING_LEN>);
  FixedCacheItem<EMBEDDING_LEN> *fixed_cache_flush_buffer_ = nullptr;

  // 统计相关
  mutable uint64_t hash_field_miss_count_ = 0;
  mutable uint64_t hash_field_put_count_ = 0;
  mutable uint64_t fix_miss_count_ = 0;
  mutable uint64_t lru_miss_count_ = 0;
  mutable std::vector<uint64_t> slot_miss_count_;
  std::string falcon_hash_field_miss_key_;
  std::string falcon_hash_field_put_key_;
  std::string falcon_fix_miss_key_;
  std::string falcon_lru_miss_key_;
  std::string falcon_fix_put_key_;
  std::string falcon_lru_put_key_;
  std::string falcon_queue_key_;
  std::string falcon_size_key_;
  std::string falcon_fix_size_key_;
  std::string falcon_fix_evict_interval_key_;
  std::string falcon_fix_evict_count_key_;
  std::string falcon_hash_field_cache_idt_update_count_;
  std::string falcon_fixed_cache_idt_update_count_;
  std::string falcon_lru_cache_idt_update_count_;
  std::string falcon_zero_cache_idt_update_count_;
  uint32_t loader_id_ = 0;
  bool is_running_ = true;
};

// embedding 合法性校验
inline bool CheckEmbeddingValue(uint64_t slot, uint64_t sign, const float *src,
                                size_t embed_len, size_t embed_cache_size) {
  static std::string model_cmd_name = ks::ad_nn::GetKcsModelCmd();
  if (KS_UNLIKELY(src == nullptr)) {
    LOG_EVERY_N(WARNING, 100000) << "embedding src is nullptr, embed_len " << embed_len
                                 << ", slot " << slot << ", sign " << sign;
    return false;
  }
  if (KS_UNLIKELY(embed_len > embed_cache_size || embed_len <= 0)) {
    ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "embed_len_invalid",
                                       model_cmd_name);
    LOG_EVERY_N(ERROR, 1000000)
        << "Invalid embedding len " << embed_len << ", embed cache size "
        << embed_cache_size << ", sign " << sign << ", slot " << slot;
    return false;
  }
  float val = 0;
  for (size_t i = 0; i < embed_len; ++i) {
    val = *(src + i);
    if (KS_UNLIKELY(std::isnan(val))) {
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "nan_embedding_val_cnt",
                                         model_cmd_name);
      LOG_EVERY_N(ERROR, 100000) << "nan embedding value " << val << " for slot " << slot
                                 << ", sign " << sign << ", embedding_len " << embed_len;
      return false;
    }
    if (KS_UNLIKELY(std::isinf(val))) {
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "inf_embedding_val_cnt",
                                         model_cmd_name);
      LOG_EVERY_N(ERROR, 100000)
          << "embedding value " << val << " is inf for slot " << slot << ", sign " << sign
          << ", embedding_len " << embed_len;
      return false;
    }
  }
  return true;
}

inline bool GetShardFieldConfig(const base::Json &config, const std::string &shard,
                                FieldEmbeddingInfoMap *field_embeddings) {
  auto shard_config = config.Get(shard);
  if (shard_config == nullptr) {
    LOG(ERROR) << "no field config found for shard " << shard;
    return false;
  }

#define GET_AND_CHECK_KEY(key)                            \
  auto key = field_config->GetInt(#key, -1);              \
  if (key < 0) {                                          \
    LOG(ERROR) << "Invalid " << #key << " value " << key; \
    return false;                                         \
  }

  for (const auto &field_config : shard_config->array()) {
    GET_AND_CHECK_KEY(field);
    GET_AND_CHECK_KEY(size);
    GET_AND_CHECK_KEY(embedding_len);
    GET_AND_CHECK_KEY(capacity);
    auto slot = field_config->GetInt("slot", field);
    auto op_str = field_config->GetString("op", "SUM");
    auto concat_len = field_config->GetInt("concat_len", 0);
    field_embeddings->emplace(field, FieldEmbeddingInfo(size, capacity, embedding_len,
                                                        slot, op_str, concat_len));
  }
#undef GET_AND_CHECK_KEY
  return true;
}

class HybridEmbedding : public EmbeddingStorage {
 public:
  HybridEmbedding() : thread_pool_(1) {}
  virtual ~HybridEmbedding() {
    LOG(INFO) << "Release HybridEmbedding";
    is_running_ = false;
    slot_caches_.clear();
    embed_len_caches_.clear();
    LOG(INFO) << "cache cleared";
    thread_pool_.StopAll();
    LOG(INFO) << "Thread pool stoped";
  }
  void SetCapacity(uint64_t capacity) {
    total_capacity_ = capacity >> 2;  // 转化为以 float 为单位
  }
  int64_t batch_version_ms() { return batch_version_ms_; }
  // 数据加载，从 btq 读取所有 embedding
  // field_slots: 商业化 field 到 kuiba slot 的映射，商业化可传空 vector
  // zero_for_miss_sign: 对于 miss 的 sign 是否返回全零，默认返回随机值
  // kuiba 模型需要设置 zero_for_miss_sign 为 true
  // 商业化模型采用默认值即可
  int Load(const std::string &model_root_path,
           const FieldEmbeddingInfoMap &field_embedding_infos,
           const std::string &embedding_cache_type, float min_fill_ratio,
           bool zero_for_miss_sign = false, uint32_t loader_id = 0);
  // 更新 callback
  void UpdateVersion(int64 batch_version_ms, bool is_batch_end, bool rollback = false);
  void SetVersion(int64 batch_version_ms) {
    if (batch_version_ms < 0) {
      return;
    }
    batch_version_ms_ = batch_version_ms;
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->UpdateVersion(batch_version_ms);
      }
    }
  }
  float GetMinFillRatio() { return min_fill_ratio_; }
  uint64_t GetFullEmbeddingCount() { return total_embeddings_; }
  uint64_t GetEmbeddingTotalCount() {
    uint64_t filled_count = 0;
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        filled_count += cache->GetEmbeddingCount();
      }
    }
    return filled_count;
  }
  // 数据读取，Override EmbeddingStorage
  const float *GetW(size_t slot, const uint64_t &sign) const override;

  // 为 kai 有哈希模型暴露直接取 embedding 的接口
  inline const float *GetWDirectly(const size_t field, const uint64_t sign) const {
    // kai 有哈希模型 field -> slot
    auto &slot = field_slots_[field];
    if (KS_UNLIKELY(slot >= slot_caches_.size() || !slot_caches_[slot])) {
      LOG_EVERY_N(ERROR, 10000) << "slot out of range! slot: " << slot
                                << ", cache size: " << slot_caches_.size()
                                << ", sign: " << sign << ", field: " << field;
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
      return nullptr;
    }
    const float *target = slot_caches_[slot]->GetWDirectly(slot, sign);
    if (KS_UNLIKELY(!target)) {
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
    }
    return target;
  }

  inline const float *GetWDirectly(const size_t field, const uint32_t sign) const {
    // kai 有哈希模型 field -> slot
    auto &slot = field_slots_[field];
    if (KS_UNLIKELY(slot >= slot_caches_.size() || !slot_caches_[slot])) {
      LOG_EVERY_N(ERROR, 10000) << "slot out of range! slot: " << slot
                                << ", cache size: " << slot_caches_.size()
                                << ", sign: " << sign << ", field: " << field;
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
      return nullptr;
    }
    const float *target = slot_caches_[slot]->GetWDirectly(slot, sign);
    if (KS_UNLIKELY(!target)) {
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
    }
    return target;
  }

  inline bool GetBatchW(const size_t field, const uint64_t *batch_signs,
                        uint32_t batch_size,
                        std::vector<const float *> *embedding_results) {
    // kai 有哈希模型 field -> slot
    auto &slot = field_slots_[field];
    // slot 只用检查一次
    if (KS_UNLIKELY((slot >= slot_caches_.size() || !slot_caches_[slot]))) {
      LOG_EVERY_N(ERROR, 10000) << "slot out of range! slot: " << slot
                                << ", cache size: " << slot_caches_.size()
                                << ", batch_size: " << batch_size << ", field: " << field;
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
      return false;
    }
    auto ret =
        slot_caches_[slot]->GetBatchW(slot, batch_signs, batch_size, embedding_results);
    if (KS_UNLIKELY(!ret)) {
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
    }
    return ret;
  }

  // uint32
  inline bool GetBatchW(const size_t field, const uint32_t *batch_signs,
                        uint32_t batch_size,
                        std::vector<const float *> *embedding_results) {
    // kai 有哈希模型 field -> slot
    auto &slot = field_slots_[field];
    // slot 只用检查一次
    if (KS_UNLIKELY((slot >= slot_caches_.size() || !slot_caches_[slot]))) {
      LOG_EVERY_N(ERROR, 10000) << "slot out of range! slot: " << slot
                                << ", cache size: " << slot_caches_.size()
                                << ", batch_size: " << batch_size << ", field: " << field;
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
      return false;
    }
    auto ret =
        slot_caches_[slot]->GetBatchW(slot, batch_signs, batch_size, embedding_results);
    if (KS_UNLIKELY(!ret)) {
      ks::infra::PerfUtil::CountLogStash(1, "dnn_predict_server", "get_w_nullptr",
                                         model_cmd_);
    }
    return ret;
  }

  void FlushCallback(uint64_t batch_version_ms) {
    if (batch_version_ms == batch_version_ms_) {
      if (++flush_cache_count_ >= embed_cache_count_) {
        std::fstream fs(embedding_path_ + "/version", std::ios::trunc | std::ios::out);
        fs << batch_version_ms_;
        flush_cache_count_ = 0;

        LOG(INFO) << "embedding flush version update to " << batch_version_ms_;
      }
    }
  }

  uint64_t GetRecoverVersion() {
    std::fstream fs(embedding_path_ + "/version");
    uint64_t version = 0;
    if (fs.is_open()) {
      fs >> version;
      LOG(INFO) << "version read from file is " << version;
      uint64_t now_ms = base::GetTimestamp() / 1000;
      if (!IsValidModelVersion(version, now_ms)) {
        LOG(INFO) << "recovered version " << version << "is invalid and will not be used";
        return 0;
      }
    }

    return version;
  }
  void RecoverVersion() {
    uint64_t version = GetRecoverVersion();
    if (version > 0) {
      uint64_t now_ms = base::GetTimestamp() / 1000;
      if (now_ms > version &&
          now_ms - version > FLAGS_embedding_cache_expire_time * 1000) {
        LOG(INFO) << "recovered version " << version << "is too old and will not be used";
        return;
      }
      uint64_t filled_count = 0;
      for (auto &cache : embed_len_caches_) {
        if (cache) {
          filled_count += cache->GetEmbeddingCount();
        }
      }
      LOG(INFO) << "expected total count " << total_embeddings_ << " fill count "
                << filled_count;
      if (filled_count < total_embeddings_ * min_fill_ratio_) {
        LOG(INFO) << "recovered count does not match min ratio and will not be used";
        return;
      }
    }
    batch_version_ms_ = version;
    LOG(INFO) << "embedding version recovered to " << batch_version_ms_;
  }
  void ClearEmbeddingCache() {
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->ClearZeroCache();
        cache->ClearHashFieldCache();
        cache->ClearFixedCache();
        cache->ClearLruCache();
      }
    }
  }
  void ClearZeroCache() {
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->ClearZeroCache();
      }
    }
  }
  void ClearHashFieldCache() {
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->ClearHashFieldCache();
      }
    }
  }
  void ClearFixedCache() {
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->ClearFixedCache();
      }
    }
  }
  void ClearLruCache() {
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->ClearLruCache();
      }
    }
  }
  // idt 相关接口
  uint32_t GetKMaxEmbeddingLenLg() const { return kMaxEmbeddingLenLg; }
  bool GetOneFieldSignSize(uint32_t embedding_len, EmbCacheType cache_type,
                           uint32_t field_index, uint64_t &sign_size) {  // NOLINT
    if (embedding_len >= embed_len_caches_.size()) {
      LOG(ERROR) << "Invalid embedding length " << embedding_len << "exceed max length "
                 << embed_len_caches_.size();
      return false;
    }
    if (embed_len_caches_[embedding_len] == nullptr) {
      LOG(ERROR) << "embed_len_caches_[" << embedding_len << "] is empty";
      return false;
    }
    return embed_len_caches_[embedding_len]->GetOneFieldSignSize(cache_type, field_index,
                                                                 sign_size);
  }

  bool GetNextValidField(uint32_t embedding_len, EmbCacheType cache_type,
                         uint32_t field_index, uint32_t &next_field) {  // NOLINT
    if (embedding_len >= embed_len_caches_.size()) {
      LOG(WARNING) << "Invalid embedding length " << embedding_len << "exceed max length "
                   << embed_len_caches_.size();
      return false;
    }
    if (embed_len_caches_[embedding_len] == nullptr) {
      LOG(WARNING) << "embed_len_caches_[" << embedding_len << "] is empty";
      return false;
    }
    return embed_len_caches_[embedding_len]->GetNextValidField(cache_type, field_index,
                                                               next_field);
  }

  bool GetOneFieldWeights(uint32_t embedding_len, EmbCacheType cache_type,
                          uint32_t field_index, uint64_t sign_index, uint64_t sign_size,
                          uint64_t *id, float *val, uint64_t *timestamp) {
    if (KS_UNLIKELY(embedding_len <= 0)) {
      LOG(ERROR) << "invalid embedding size " << embedding_len;
      return false;
    }
    if (embedding_len >= embed_len_caches_.size()) {
      LOG(ERROR) << "Invalid embedding length " << embedding_len << "exceed max length "
                 << embed_len_caches_.size();
      return false;
    }
    if (embed_len_caches_[embedding_len] == nullptr) {
      LOG(ERROR) << "embed_len_caches_[" << embedding_len << "] is empty";
      return false;
    }
    return embed_len_caches_[embedding_len]->GetOneFieldWeights(
        cache_type, field_index, sign_index, sign_size, id, val, timestamp);
  }
  bool PutOneFieldWeights(uint32_t embedding_len, EmbCacheType cache_type,
                          uint32_t field_index, uint64_t sign_index, uint64_t sign_size,
                          const uint64_t *id, const float *val,
                          const uint64_t *timestamp) {
    if (KS_UNLIKELY(embedding_len <= 0)) {
      LOG(ERROR) << "invalid embedding size " << embedding_len;
      return false;
    }
    if (embedding_len >= embed_len_caches_.size()) {
      LOG(ERROR) << "Invalid embedding length " << embedding_len << "exceed max length "
                 << embed_len_caches_.size();
      return false;
    }
    if (embed_len_caches_[embedding_len] == nullptr) {
      LOG(ERROR) << "embed_len_caches_[" << embedding_len << "] is empty";
      return false;
    }
    return embed_len_caches_[embedding_len]->PutOneFieldWeights(
        cache_type, field_index, sign_index, sign_size, id, val, timestamp);
  }
  bool SetIdtServerEmbeddingVersion(uint64_t embed_version) {
    // 用上锁嘛
    if (idt_server_embedding_version_ == 0) {
      idt_server_embedding_version_ = embed_version;
    }
    return true;
  }

  bool SetIdtServerEmbeddingCount(uint64_t embed_count) {
    if (idt_server_embedding_count_ == 0) {
      idt_server_embedding_count_ = embed_count;
    }
    return true;
  }
  uint64_t GetIdtServerEmbeddingVersion() { return idt_server_embedding_version_; }
  uint64_t GetIdtServerEmbeddingCount() { return idt_server_embedding_count_; }

  // 获取当前 hybrid 的 Idt update Embedding 总数
  uint64_t GetTotalIdtUpdateEmbeddingCount() {
    uint64_t filled_count = 0;
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        filled_count += cache->GetIdtUpdateEmbeddingCount();
      }
    }
    return filled_count;
  }
  void ResetIdtUpdateEmbeddingCount() {
    for (auto &cache : embed_len_caches_) {
      if (cache) {
        cache->ResetIdtUpdateEmbeddingCount();
      }
    }
  }
  void AcquireInUsageFlag() {
    in_usage_flag_ = true;
    LOG(INFO) << "set in_usage_flag to " << in_usage_flag_;
    // sleep 5 s 让 idt 请求都能回去
    std::this_thread::sleep_for(std::chrono::seconds(5));
  }
  bool IsInUsage() { return in_usage_flag_; }
  void ReleaseInUsageFlag() {
    in_usage_flag_ = false;
    LOG(INFO) << "release in_usage_flag to " << in_usage_flag_;
  }

 private:
  void Init(const std::string &model_root_path,
            const FieldEmbeddingInfoMap &field_embedding_infos,
            const std::string &embedding_cache_type, bool zero_for_miss_sign = false,
            uint32_t loader_id = 0, const base::Json *json_config = nullptr);

  // 初始化 embed_len_caches_ 的辅助函数
  // 请参照 https://stackoverflow.com/questions/48913092/constexpr-in-for-statement
  template <template <typename> class LruBuffer, uint32_t... Is>
  void InitCacheHelper(std::integer_sequence<uint32_t, Is...> const &) {
    using unused = int[];
    (void)unused{(embed_len_caches_[(1 << Is)] =
                      std::make_shared<HybridEmbCacheTpl<LruBuffer, (1 << Is)>>(),
                  0)...};
  }
  template <template <typename> class LruBuffer, uint32_t T>
  void InitCache() {
    embed_len_caches_.resize(kMaxEmbeddingLen + 1);
    InitCacheHelper<LruBuffer>(std::make_integer_sequence<uint32_t, T + 1>{});
  }
  void EmbeddingLoadDelayReportThread();

 private:
  float min_fill_ratio_ = 0.65;  // embedding 填充比例大于该值才能服务

  bool started_ = false;
  std::atomic_uint64_t batch_version_ms_ = ATOMIC_VAR_INIT(0);
  uint64_t total_capacity_ = 1lu << 35;  // 32G float = 128G
  uint64_t total_embeddings_ = 0;        // 预估的总的 embedding 数量
  uint32_t embed_cache_count_ = 0;
  std::atomic_uint flush_cache_count_ = ATOMIC_VAR_INIT(0);
  std::string embedding_path_;
  std::shared_ptr<RandValue> random_values_;

 protected:
  std::string model_cmd_ = "";
  uint32_t loader_id_ = 0;
  static const uint32_t kMaxEmbeddingLenLg = 11;
  static const uint32_t kMaxEmbeddingLen = 1 << kMaxEmbeddingLenLg;
  struct FieldDefaultSignEmbedding {
    float data[kMaxEmbeddingLen] = {0.0};
    bool valid = false;
  };
  // 用来定时上报 embedding 延迟
  ::thread::ThreadPool thread_pool_;
  bool is_running_ = true;
  // 下标是 field id, value 是对应的 cache
  std::vector<std::shared_ptr<HybridEmbCache>> slot_caches_;
  // 下标是 embeding 长度, value 是对应的 cache
  std::vector<std::shared_ptr<HybridEmbCache>> embed_len_caches_;
  std::vector<uint32_t> field_sizes_;
  std::vector<uint64_t> field_slots_;
  FieldEmbeddingInfoMap field_embedding_infos_;

  std::vector<uint32_t> slot_sizes_;
  std::atomic_bool in_usage_flag_{false};
  // for idt
  std::uint64_t idt_server_embedding_count_ = 0;
  std::uint64_t idt_server_embedding_version_ = 0;
};

// 声明
void DebugPrintEmbeddingValue(uint64_t slot, uint64_t sign, const float *embedding,
                              size_t embedding_len);
bool LoopGetW(HybridEmbedding *hybrid_embedding, const uint32_t embedding_len,
              const size_t field_index, const uint64_t *batch_signs, uint32_t batch_size,
              std::vector<const float *> *embedding_results);
bool GetBatchW(HybridEmbedding *hybrid_embedding, const uint32_t embedding_len,
               const size_t field_index, const uint64_t *batch_signs, uint32_t batch_size,
               std::vector<const float *> *embedding_results);

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
const float
    HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::kDefaultEmbedding[EMBEDDING_LEN] = {0.0};

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::Init(
    const std::map<int, FieldEmbeddingInfo> &slot_infos,
    const std::shared_ptr<RandValue> &random_values, uint64_t capacity,
    uint32_t load_factor, const std::string &mmap_path,
    const std::string &embedding_cache_type, uint32_t loader_id) {
  LOG(INFO) << "embedding len " << EMBEDDING_LEN << " load factor " << load_factor;
  malloc_arena_.InitArenas(1, 10000);
  random_values_ = random_values;
  loader_id_ = loader_id;
  std::string suffix;
  if (loader_id_ != 0) {
    suffix = base::StringPrintf("_for_loader_%u", loader_id_);
  }
  falcon_hash_field_miss_key_ =
      base::StringPrintf("predict_server.hybrid_embed_hash_field_cache_miss_%u",
                         EMBEDDING_LEN) +
      suffix;
  falcon_hash_field_put_key_ =
      base::StringPrintf("predict_server.hybrid_embed_hash_field_cache_put_%u",
                         EMBEDDING_LEN) +
      suffix;
  falcon_fix_miss_key_ =
      base::StringPrintf("predict_server.hybrid_embed_fix_cache_miss_%u", EMBEDDING_LEN) +
      suffix;
  falcon_lru_miss_key_ =
      base::StringPrintf("predict_server.hybrid_embed_lru_cache_miss_%u", EMBEDDING_LEN) +
      suffix;
  falcon_fix_put_key_ =
      base::StringPrintf("predict_server.hybrid_embed_fix_cache_put_%u", EMBEDDING_LEN) +
      suffix;
  falcon_lru_put_key_ =
      base::StringPrintf("predict_server.hybrid_embed_lru_cache_put_%u", EMBEDDING_LEN) +
      suffix;
  falcon_size_key_ =
      base::StringPrintf("predict_server.hybrid_embed_cache_size_%u", EMBEDDING_LEN) +
      suffix;
  falcon_fix_size_key_ =
      base::StringPrintf("predict_server.hybrid_embed_fix_cache_size_%u", EMBEDDING_LEN) +
      suffix;
  falcon_queue_key_ =
      base::StringPrintf("predict_server.hybrid_embed_queue_len_%u", EMBEDDING_LEN) +
      suffix;
  falcon_fix_evict_interval_key_ =
      base::StringPrintf("predict_server.hybrid_embed_fix_evict_interval_%u",
                         EMBEDDING_LEN) +
      suffix;
  falcon_fix_evict_count_key_ =
      base::StringPrintf("predict_server.hybrid_embed_fix_evict_count_%u",
                         EMBEDDING_LEN) +
      suffix;
  // idt 相关监控
  falcon_hash_field_cache_idt_update_count_ =
      base::StringPrintf(
          "predict_server.hybrid_embed_hash_field_cache_idt_update_count_%u",
          EMBEDDING_LEN) +
      suffix;
  falcon_fixed_cache_idt_update_count_ =
      base::StringPrintf("predict_server.hybrid_embed_fixed_cache_idt_update_count_%u",
                         EMBEDDING_LEN) +
      suffix;
  falcon_lru_cache_idt_update_count_ =
      base::StringPrintf("predict_server.hybrid_embed_lru_cache_idt_update_count_%u",
                         EMBEDDING_LEN) +
      suffix;
  falcon_zero_cache_idt_update_count_ =
      base::StringPrintf("predict_server.hybrid_embed_zero_cache_idt_update_count_%u",
                         EMBEDDING_LEN) +
      suffix;
  slot_miss_count_.resize(slot_infos.rbegin()->first + 1);
  size_t fixed_fields = 0;
  size_t hash_fields = 0;
  size_t hash_field_capacity = 0;
  if (!slot_infos.empty()) {
    zero_cache_capacity_ = slot_infos.rbegin()->first + 1;
    slot2capacity_.resize(zero_cache_capacity_, 0);
    slot2embed_len_.resize(zero_cache_capacity_, 0);
    if (FLAGS_enable_fixed_cache) {
      fixed_cache_[0].resize(slot_infos.rbegin()->first + 1);
      fixed_cache_[1].resize(slot_infos.rbegin()->first + 1);
    }
    if (FLAGS_btq_enable_hash_field_opt) {
      hash_field_sizes_.resize(slot_infos.rbegin()->first + 1, 0);
    }
    for (const auto &iter : slot_infos) {
      auto slot_capacity = iter.second.field_capacity;
      auto slot_size = iter.second.field_size;
      slot2capacity_[iter.first] = slot_capacity;
      slot2embed_len_[iter.first] = iter.second.embedding_len;
      LOG(INFO) << "slot2embed_len, slot: " << iter.first
                << ", embed len: " << iter.second.embedding_len;
      if (FLAGS_btq_enable_hash_field_opt && slot_size > 0) {
        if (slot_size > slot_capacity) {
          LOG(FATAL) << "Invalid hash slot found: slot size " << slot_size
                     << " is larger than "
                     << " slot capacity " << slot_capacity << " slot id " << iter.first
                     << ", loader " << loader_id_;
        }
        hash_field_sizes_[iter.first] = slot_capacity;
        hash_field_capacity += slot_capacity;
        ++hash_fields;
        continue;
      }
      if (!FLAGS_enable_fixed_cache || slot_capacity > FLAGS_fixed_cache_max_field_size) {
        continue;
      }
      fixed_cache_[0][iter.first].reset(new EmbFixedCache());
      fixed_cache_[1][iter.first].reset(new EmbFixedCache());
      auto reserve_size = slot_capacity;
      if (reserve_size > FLAGS_fixed_cache_max_reserve_size) {
        reserve_size = FLAGS_fixed_cache_max_reserve_size;
      }
      fixed_cache_[0][iter.first]->reserve(reserve_size);
      fixed_cache_[1][iter.first]->reserve(reserve_size);
      fix_cache_capacity_ += slot_capacity;

      ++fixed_fields;
      LOG(INFO) << "slot " << iter.first << " with size " << slot_capacity
                << " embedding len " << EMBEDDING_LEN << " will use fixed cache"
                << ", loader " << loader_id_;
    }
    cache_thread_pool_.AddTask(NewCallback(
        this, &HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::UpdateFixedCache));
    LOG(INFO) << " embedding len " << EMBEDDING_LEN << " fixed cache field count "
              << fixed_fields << " total capacity " << fix_cache_capacity_
              << " init succeeds"
              << ", loader " << loader_id_;
  }
  capacity -= fix_cache_capacity_ + hash_field_capacity;

  lru_capacity_ = capacity;
  auto lru_fields = slot_infos.size() - fixed_fields - hash_fields;
  LOG(INFO) << " embedding len " << EMBEDDING_LEN << " lru cache capacity "
            << lru_capacity_ << " field count " << lru_fields << ", loader "
            << loader_id_;
  if (mmap_path.size() > 0) {
    zero_cache_path_ = mmap_path + "/zero_cache/";
    hash_field_path_ = mmap_path + "/hash_fields/";
    fix_file_path_ = base::StringPrintf("%s/fix/%d", mmap_path.c_str(), EMBEDDING_LEN);
    if (fixed_fields > 0) {
      if (fix_cache_capacity_ <= 0) {
        fix_cache_capacity_ = 16;
        LOG(WARNING) << " embedding len " << EMBEDDING_LEN
                     << " fix capacity is 0 and is set to " << fix_cache_capacity_
                     << ", loader " << loader_id_;
      }
      base::file_util::CreateDirectory(mmap_path + "/fix");
      InitFixCache();
    } else {
      // 清理遗漏的文件
      base::file_util::Delete(fix_file_path_, false);
      LOG(INFO) << "fix cache " << fix_file_path_ << " is cleared"
                << ", loader " << loader_id_;
    }

    lru_file_path_ = base::StringPrintf("%s/lru/%d", mmap_path.c_str(), EMBEDDING_LEN);
    if (lru_capacity_ > 0 && lru_fields > 0) {
      base::file_util::CreateDirectory(mmap_path + "/lru");
      cache_thread_pool_.AddTask(
          NewCallback(this, &HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::FlushLruCache));
    } else {
      // 清理遗漏的文件
      base::file_util::Delete(lru_file_path_, false);
    }
  }

  if (lru_capacity_ > 0 && lru_fields > 0) {
    embedding_cache_type_ = embedding_cache_type;
    lru_cache_ = std::make_unique<EmbLruCache>(capacity, load_factor, lru_file_path_);
  }
  InitZeroCache(zero_cache_capacity_);
  InitHashFields(hash_field_sizes_);
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::InitZeroCache(size_t zero_cache_size) {
  if (zero_cache_size <= 0) {
    LOG(INFO) << "zero_cache_size is zero, return directly"
              << ", loader " << loader_id_;
    return;
  }

  auto init_zero_cache = [&](FixedCacheMemItem<EMBEDDING_LEN> *start, size_t length) {
    if (!random_values_) {
      for (size_t idx = 0; idx < length; ++idx) {
        start[idx].timestamp = 0;
        std::fill_n(start[idx].embedding.data, EMBEDDING_LEN, 0.0);
      }
      return;
    }
    for (size_t idx = 0; idx < length; ++idx) {
      start[idx].timestamp = 0;
      std::copy_n(random_values_->Get(), EMBEDDING_LEN, start[idx].embedding.data);
    }
  };
  base::file_util::CreateDirectory(zero_cache_path_);

  size_t total_bytes = zero_cache_size * sizeof(FixedCacheMemItem<EMBEDDING_LEN>);
  if (zero_cache_path_.empty()) {
    zero_cache_ = static_cast<FixedCacheMemItem<EMBEDDING_LEN> *>(mmap(
        NULL, total_bytes, PROT_READ | PROT_WRITE, MAP_SHARED | MAP_ANONYMOUS, -1, 0));
    if (zero_cache_ == MAP_FAILED) {
      LOG(FATAL) << "mmap for zero_cache failed, msg: " << strerror(errno);
    }
    init_zero_cache(zero_cache_, zero_cache_size);
    LOG(INFO) << "zero_cache init succeeds with memory mode";
    return;  // continue;
  }

  bool need_recover = false;
  std::string zero_cache_file_path = zero_cache_path_ + std::to_string(EMBEDDING_LEN);
  if (base::file_util::PathExists(zero_cache_file_path)) {
    // 检查数据合法性并从文件中恢复
    int64_t file_size = 0;
    base::file_util::GetFileSize(zero_cache_file_path, &file_size);
    if (file_size > 0 && file_size == total_bytes) {
      need_recover = true;
    } else {
      LOG(ERROR) << "zero cache file " << zero_cache_file_path << " size " << file_size
                 << " is not valid, skip it";
    }
  }
  uint64_t flags = (O_RDWR | O_CREAT);
  if (!need_recover) {
    flags = (flags | O_TRUNC);
  }
  int fd = open(zero_cache_file_path.c_str(), flags, 0600);
  if (fd < 0) {
    LOG(FATAL) << "open file " << zero_cache_file_path
               << " failed, error msg:" << strerror(errno);
  }
  auto ret = posix_fallocate(fd, 0, total_bytes);
  if (0 != ret) {
    LOG(FATAL) << "allocate space for file " << zero_cache_file_path << " failed, error "
               << ret << " msg:" << strerror(errno);
  }
  zero_cache_ = static_cast<FixedCacheMemItem<EMBEDDING_LEN> *>(
      mmap(NULL, total_bytes, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0));
  if (zero_cache_ == MAP_FAILED) {
    LOG(FATAL) << "mmap for zero_cache failed, msg: " << strerror(errno);
  }
  if (!need_recover) {
    init_zero_cache(zero_cache_, zero_cache_size);
  } else {
    // 统计有效 Embedding 数
    for (size_t fidx = 0; fidx < zero_cache_size; ++fidx) {
      if (zero_cache_[fidx].timestamp > 0) {
        zero_cache_embedding_count_.fetch_add(1, std::memory_order_relaxed);
      }
    }
  }
  LOG(INFO) << "zero_cache init succeeds with file " << zero_cache_file_path
            << " recovered " << need_recover << " embedding count "
            << zero_cache_embedding_count_ << ", loader " << loader_id_;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::InitHashFields(
    const std::vector<uint64_t> &hash_field_sizes) {
  if (hash_field_sizes.empty()) {
    LOG(INFO) << "empty hash field sizes, return directly"
              << ", loader " << loader_id_;
    return;
  }

  auto init_hash_field = [&](FixedCacheMemItem<EMBEDDING_LEN> *start, size_t length) {
    if (!random_values_) {
      for (size_t idx = 0; idx < length; ++idx) {
        start[idx].timestamp = 0;
        std::fill_n(start[idx].embedding.data, EMBEDDING_LEN, 0.0);
      }
      return;
    }
    for (size_t idx = 0; idx < length; ++idx) {
      start[idx].timestamp = 0;
      std::copy_n(random_values_->Get(), EMBEDDING_LEN, start[idx].embedding.data);
    }
  };
  base::file_util::CreateDirectory(hash_field_path_);
  hash_field_cache_.resize(hash_field_sizes.size(), nullptr);
  for (size_t idx = 0; idx < hash_field_sizes.size(); ++idx) {
    if (hash_field_sizes[idx] <= 0) {
      continue;
    }

    size_t total_bytes = hash_field_sizes[idx] * sizeof(FixedCacheMemItem<EMBEDDING_LEN>);
    if (hash_field_path_.empty()) {
      hash_field_cache_[idx] = static_cast<FixedCacheMemItem<EMBEDDING_LEN> *>(mmap(
          NULL, total_bytes, PROT_READ | PROT_WRITE, MAP_SHARED | MAP_ANONYMOUS, -1, 0));
      if (hash_field_cache_[idx] == MAP_FAILED) {
        LOG(FATAL) << "mmap for hash field " << idx
                   << " failed, msg: " << strerror(errno);
      }
      init_hash_field(hash_field_cache_[idx], hash_field_sizes[idx]);
      LOG(INFO) << "slot " << idx << " init succeeds with memory mode";
      continue;
    }

    bool need_recover = false;
    std::string slot_file_path = hash_field_path_ + std::to_string(idx);
    if (base::file_util::PathExists(slot_file_path)) {
      // 检查数据合法性并从文件中恢复
      int64_t file_size = 0;
      base::file_util::GetFileSize(slot_file_path, &file_size);
      if (file_size > 0 && file_size == total_bytes) {
        need_recover = true;
      } else {
        LOG(ERROR) << "hash field cache file " << slot_file_path << " size " << file_size
                   << " is not valid, skip it";
      }
    }
    uint64_t flags = (O_RDWR | O_CREAT);
    if (!need_recover) {
      flags = (flags | O_TRUNC);
    }
    int fd = open(slot_file_path.c_str(), flags, 0600);
    if (fd < 0) {
      LOG(FATAL) << "open file " << slot_file_path
                 << " failed, error msg:" << strerror(errno);
    }
    auto ret = posix_fallocate(fd, 0, total_bytes);
    if (0 != ret) {
      LOG(FATAL) << "allocate space for file " << slot_file_path << " failed, error "
                 << ret << " msg:" << strerror(errno);
    }
    hash_field_cache_[idx] = static_cast<FixedCacheMemItem<EMBEDDING_LEN> *>(
        mmap(NULL, total_bytes, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0));
    if (hash_field_cache_[idx] == MAP_FAILED) {
      LOG(FATAL) << "mmap for hash field " << idx << " failed, msg: " << strerror(errno);
    }
    if (!need_recover) {
      init_hash_field(hash_field_cache_[idx], hash_field_sizes[idx]);
    } else {
      // 统计有效 Embedding 数
      for (size_t fidx = 0; fidx < hash_field_sizes[idx]; ++fidx) {
        if (hash_field_cache_[idx][fidx].timestamp > 0) {
          hash_field_embedding_count_.fetch_add(1, std::memory_order_relaxed);
        }
      }
    }
    LOG(INFO) << "slot " << idx << " init succeeds with file " << slot_file_path
              << " recovered " << need_recover << " embedding count "
              << hash_field_embedding_count_ << ", loader " << loader_id_;
  }
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::InitFixCache() {
  fixed_cache_flush_buffer_ = static_cast<FixedCacheItem<EMBEDDING_LEN> *>(
      memalign(4096, kFixCacheFlushCapacity));
  if (fixed_cache_flush_buffer_ == nullptr) {
    LOG(FATAL) << "allocate fix cache buffer failed, error msg: " << strerror(errno);
  }

  bool need_recover = false;
  int64_t file_size = 0;
  if (base::file_util::PathExists(fix_file_path_)) {
    // 检查数据合法性并从文件中恢复
    base::file_util::GetFileSize(fix_file_path_, &file_size);
    if (file_size > 0 && file_size % sizeof(FixedCacheItem<EMBEDDING_LEN>) != 0) {
      LOG(ERROR) << "fix cache file " << fix_file_path_ << " size " << file_size
                 << " is not valid, skip it";
    } else {
      need_recover = true;
    }
  }

  auto init_fix_cache_file = [&]() {
    // 预先分配足够的空间
    int fd = open(fix_file_path_.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0600);
    if (fd < 0) {
      LOG(FATAL) << "open file " << fix_file_path_
                 << " failed, error msg:" << strerror(errno);
    }
    if (0 != posix_fallocate(
                 fd, 0, fix_cache_capacity_ * sizeof(FixedCacheItem<EMBEDDING_LEN>))) {
      LOG(FATAL) << "allocate space for file " << fix_file_path_
                 << " failed, error msg:" << strerror(errno);
    }

    close(fd);
    LOG(INFO) << "fix cache file init succeeds for " << fix_file_path_;
  };
  if (!need_recover) {
    init_fix_cache_file();
    return;
  }

  LOG(INFO) << "Valid fix cache file " << fix_file_path_ << " found and start recover";
  int fd = open(fix_file_path_.c_str(), O_RDONLY);
  if (fd < 0) {
    LOG(FATAL) << "open fix cache file " << fix_file_path_
               << " failed, error msg:" << strerror(errno);
  }
  auto items = static_cast<FixedCacheItem<EMBEDDING_LEN> *>(
      mmap(NULL, file_size, PROT_READ, MAP_SHARED, fd, 0));
  if (items == MAP_FAILED) {
    LOG(FATAL) << "mmap file " << fix_file_path_
               << " failed, error msg:" << strerror(errno);
  }
  size_t item_count = file_size / sizeof(FixedCacheItem<EMBEDDING_LEN>);
  auto &cur_cache = fixed_cache_[current_fixed_cache_];
  for (size_t idx = 0; idx < item_count; ++idx) {
    if (items[idx].sign == 0) {
      LOG_EVERY_N(INFO, 10000) << "skip tail sign=0 data during recover";
      continue;
    }
    uint32_t field = items[idx].field;
    if (field > cur_cache.size() || !cur_cache[field]) {
      LOG(ERROR) << "Invalid field " << field
                 << " found, fix cache file might be corrupted,"
                 << " fix cache will not  recover";
      for (auto &field_cache : cur_cache) {
        if (field_cache) {
          field_cache
              ->clear();  // fix cache corrupt 或者读到了老的遗留数据，清空整个 cache
        }
      }
      init_fix_cache_file();
      return;
    }
    cur_cache[field]->emplace(items[idx].sign, items[idx].embedding);
    LOG_EVERY_N(INFO, 100000) << "fix cache recover field " << field << " sign "
                              << items[idx].sign << " embedding[0] "
                              << items[idx].embedding.embedding.data[0];
    /*if (hybrid_check_sign_sets.count(items[idx].sign) > 0) {
      string weights;
      for (size_t eidx = 0; eidx < EMBEDDING_LEN; ++eidx) {
        base::StringAppendF(&weights, " %f", items[idx].embedding.data[eidx]);
      }
      LOG(INFO) << "embedding len " << EMBEDDING_LEN << "field " << field << " sign " <<
    items[idx].sign
          << " ptr " << items[idx].embedding.data << " weights [" << weights << "]";
    }*/
  }
  close(fd);
  munmap(items, file_size);
  LOG(INFO) << "fix cache recover succeeds from file " << fix_file_path_ << ", loader "
            << loader_id_;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::UpdateFixedCache() {
  if (FLAGS_enable_mem_interleave) {
    // tuning::SetThreadMemoryInterleave();
  }

  malloc_arena_.SetThreadArena();
  auto last_flush_time = Clock::now();
  auto last_switch_time = Clock::now();
  bool need_copy_after_switch = true;
  uint32_t updated_count = 0;

  uint64_t max_fix_cache_capacity = 1.2 * (double)fix_cache_capacity_;
  uint64_t min_fix_cache_capacity = 1.1 * (double)fix_cache_capacity_;
  // 保证 max_factor 大于 min_factor
  if (FLAGS_max_fix_capacity_factor >= FLAGS_min_fix_capacity_factor) {
    max_fix_cache_capacity = FLAGS_max_fix_capacity_factor * fix_cache_capacity_;
    min_fix_cache_capacity = FLAGS_min_fix_capacity_factor * fix_cache_capacity_;
  }
  while (is_running_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(1));

    auto &load_cache = fixed_cache_[1 - current_fixed_cache_];
    auto &cur_cache = fixed_cache_[current_fixed_cache_];
    // 将 load_cache 同步为与 cur_cache 一致，然后更新
    if (need_copy_after_switch) {
      for (size_t idx = 0; idx < load_cache.size(); ++idx) {
        if (!load_cache[idx]) {
          continue;
        }

        auto &target = *(load_cache[idx]);
        for (auto &iter : *(cur_cache[idx])) {
          target[iter.first] = iter.second;
        }
      }
      need_copy_after_switch = false;

      uint64_t cur_total_count = 0;
      std::string suffix;
      if (loader_id_ != 0) {
        suffix = "_for_loader_" + std::to_string(loader_id_);
      }
      size_t field_idx = 0;
      for (auto &field_cache : load_cache) {
        if (field_cache) {
          cur_total_count += field_cache->size();
          auto size_falcon_key = "predict_server.hybrid_embed_" +
                                 std::to_string(EMBEDDING_LEN) + "_field_" +
                                 std::to_string(field_idx) + "_fixed_cache_size" + suffix;
          auto capacity_falcon_key =
              "predict_server.hybrid_embed_" + std::to_string(EMBEDDING_LEN) + "_field_" +
              std::to_string(field_idx) + "_fixed_cache_capacity" + suffix;
          falcon::Set(size_falcon_key.c_str(), field_cache->size(),
                      falcon::kNonAdditiveGauge);
          falcon::Set(capacity_falcon_key.c_str(), field_cache->capacity(),
                      falcon::kNonAdditiveGauge);
        }
        ++field_idx;
      }
      // do evict job
      if (cur_total_count > max_fix_cache_capacity) {
        // 排序出用于逐出的时间戳阈值
        auto get_evict_timestamp = [&]() {
          // timestamp -> count, 按 timestamp 从大到小排序
          thread_local static std::map<uint64_t, uint64_t, std::greater<uint64_t>>
              timestamp2count;
          timestamp2count.clear();
          for (size_t idx = 0; idx < load_cache.size(); ++idx) {
            if (!load_cache[idx]) {
              continue;
            }
            auto &target = *(load_cache[idx]);
            for (auto it = target.begin(); it != target.end(); it++) {
              ++timestamp2count[it->second.timestamp];
            }
          }
          // 默认值为逐出两个小时前的 key
          uint64_t evict_timestamp = base::GetTimestamp() / 1000 - 2 * 60 * 60 * 1000;
          uint64_t count = 0;
          auto iter = timestamp2count.begin();
          while (iter != timestamp2count.end()) {
            if (count >= min_fix_cache_capacity) {
              break;
            }
            evict_timestamp = iter->first;
            count += iter->second;
            iter++;
          }
          return evict_timestamp;
        };
        auto tmp_0 = base::GetTimestamp() / 1000;
        uint64_t evict_timestamp = get_evict_timestamp();
        auto tmp_1 = base::GetTimestamp() / 1000;
        LOG(INFO) << "time consuming for get_evict_timestamp " << tmp_1 - tmp_0
                  << " of loader " << loader_id_ << ", evict_timestamp "
                  << evict_timestamp;
        // fix evict interval 单位为秒
        falcon::Set(falcon_fix_evict_interval_key_.c_str(),
                    (base::GetTimestamp() / 1000 - evict_timestamp) / 1000,
                    falcon::kNonAdditiveGauge);
        uint64_t erase_count = cur_total_count - min_fix_cache_capacity;  // 要逐出的个数
        for (size_t idx = 0; idx < load_cache.size(); ++idx) {
          if (!load_cache[idx]) {
            continue;
          }
          auto &target = *(load_cache[idx]);
          for (auto it = target.begin(); it != target.end(); it++) {
            // timestamp 都很新的时候有可能逐出的数量不够
            if (it->second.timestamp < evict_timestamp) {
              target.erase(it);
              --erase_count;
              if (erase_count == 0) {
                break;
              }
              // 当该 field 已经被逐出至低于 capacity 时停止逐出
              if (target.size() < slot2capacity_[idx]) {
                break;
              }
            }
          }
          if (erase_count == 0) {
            break;
          }
        }
        uint64_t load_total_count = 0;
        for (auto &field_cache : load_cache) {
          if (field_cache) {
            load_total_count += field_cache->size();
          }
        }
        falcon::Set(falcon_fix_evict_count_key_.c_str(),
                    cur_total_count - load_total_count, falcon::kNonAdditiveGauge);
      }
    }
    FixedCacheItem<EMBEDDING_LEN> item;
    while (fixed_cache_queue_.pop(item) && is_running_) {
      // Put 的时候已经做过校验，此处假定 field 值都是需要更新的
      auto &target = (*load_cache[item.field])[item.sign];
      if (item.embedding.timestamp >= target.timestamp) {
        target = item.embedding;
        ++updated_count;
      } else {
        IsValidModelVersion(target.timestamp, item.embedding.timestamp);
        LOG_EVERY_N(INFO, 100000)
            << "skip older item " << item.sign << " version " << item.embedding.timestamp
            << " update, current version " << target.timestamp << ", loader "
            << loader_id_;
      }
    }

    if (GetSeconds(last_switch_time) < FLAGS_fixed_cache_update_interval) {
      continue;
    }
    // 切换 cache
    last_switch_time = Clock::now();
    current_fixed_cache_ = 1 - current_fixed_cache_;
    need_copy_after_switch = true;

    uint64_t total_count = 0;
    for (auto &field_cache : load_cache) {
      if (field_cache) {
        total_count += field_cache->size();
      }
    }
    falcon::Set(falcon_fix_size_key_.c_str(), total_count, falcon::kNonAdditiveGauge);
    falcon::Set(falcon_hash_field_miss_key_.c_str(), hash_field_miss_count_,
                falcon::kNonAdditiveGauge);
    falcon::Set(falcon_hash_field_put_key_.c_str(), hash_field_put_count_,
                falcon::kNonAdditiveGauge);
    falcon::Set(falcon_fix_miss_key_.c_str(), fix_miss_count_, falcon::kNonAdditiveGauge);
    falcon::Set(falcon_lru_miss_key_.c_str(), lru_miss_count_, falcon::kNonAdditiveGauge);
    for (size_t idx = 0; idx < slot_miss_count_.size(); ++idx) {
      if (slot_miss_count_[idx] <= 0) {
        continue;
      }
      std::string suffix;
      if (loader_id_ != 0) {
        suffix = "_for_loader_" + std::to_string(loader_id_);
      }
      auto falcon_key =
          "predict_server.hybrid_embed_slot_miss_count_" + std::to_string(idx) + suffix;
      falcon::Set(falcon_key.c_str(), slot_miss_count_[idx], falcon::kNonAdditiveGauge);
      slot_miss_count_[idx] = 0;
    }
    hash_field_miss_count_ = 0;
    hash_field_put_count_ = 0;
    fix_miss_count_ = 0;
    lru_miss_count_ = 0;
    LOG(INFO) << "fixed cache for embedding len " << EMBEDDING_LEN << " total count "
              << total_count << " update count " << updated_count << " switch to "
              << current_fixed_cache_ << ", loader " << loader_id_;
    LOG(INFO) << "hash field for embedding len " << EMBEDDING_LEN << " embedding count "
              << hash_field_embedding_count_ << ", loader " << loader_id_;

    if (fix_file_path_.size() > 0 &&
        GetSeconds(last_flush_time) > FLAGS_fixed_cache_flush_interval) {
      last_flush_time = Clock::now();
      if (batch_version_ms_ <= 0) {
        LOG_EVERY_N(INFO, 100) << "skip fix flush " << EMBEDDING_LEN
                               << " since no valid batch avaible"
                               << ", loader " << loader_id_;
        continue;
      }

      if (updated_count > 0) {
        LOG(INFO) << "fix cache update count " << updated_count << " will flush it"
                  << ", loader " << loader_id_;
        if (!FlushFixCache()) {
          LOG(ERROR) << "flush fix cache failed for " << fix_file_path_ << ", loader "
                     << loader_id_;
          continue;
        }
        updated_count = 0;
      }
      if (flush_callback_ != nullptr) {
        flush_callback_(batch_version_ms_);
      }
    }
  }
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::FlushFixCache() {
  LOG(INFO) << "flush fix cache start for " << fix_file_path_;

  int flags = O_WRONLY | O_CREAT;
  if (embedding_cache_type_ == "file") {
    flags |= O_DIRECT;
  }
  int fd = open(fix_file_path_.c_str(), flags, 0600);
  if (fd < 0) {
    LOG(ERROR) << "open file " << fix_file_path_
               << " failed, error msg:" << strerror(errno);
    return false;
  }

  size_t item_count = 0;
  auto &cur_cache = fixed_cache_[current_fixed_cache_];
  for (auto &field_cache : cur_cache) {
    if (!field_cache) {
      continue;
    }
    item_count += field_cache->size();
  }

  size_t buffer_idx = 0;
  memset(fixed_cache_flush_buffer_, 0, kFixCacheFlushCapacity);
  for (size_t field = 0; field < cur_cache.size(); ++field) {
    auto &field_cache = cur_cache[field];
    if (!field_cache) {
      continue;
    }
    LOG(INFO) << "fix cache field " << field << " embedding count "
              << field_cache->size();
    for (auto &iter : *field_cache) {
      fixed_cache_flush_buffer_[buffer_idx].field = field;
      fixed_cache_flush_buffer_[buffer_idx].sign = iter.first;
      fixed_cache_flush_buffer_[buffer_idx].embedding = iter.second;
      ++buffer_idx;
      if (buffer_idx == kFixCacheFlushSize) {
        if (write(fd, fixed_cache_flush_buffer_, kFixCacheFlushCapacity) < 0) {
          LOG(ERROR) << "flush fix cache addr " << fixed_cache_flush_buffer_ << " length "
                     << kFixCacheFlushCapacity << " failed for " << fix_file_path_
                     << " error msg: " << strerror(errno);
          close(fd);
          return false;
        }
        buffer_idx = 0;
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
      }
    }
  }
  if (buffer_idx > 0) {
    // flush 最后一个 buffer
    if (write(fd, fixed_cache_flush_buffer_, kFixCacheFlushCapacity) < 0) {
      LOG(ERROR) << "flush fix cache addr " << fixed_cache_flush_buffer_ << " length "
                 << kFixCacheFlushCapacity << " failed for " << fix_file_path_
                 << " error msg: " << strerror(errno);
      close(fd);
      return false;
    }
  }
  close(fd);

  LOG(INFO) << "flush fix cache item count " << item_count << " succeeds for "
            << fix_file_path_;
  return true;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::FlushLruCache() {
  uint32_t count = 0;
  while (is_running_) {
    if (count++ % FLAGS_lru_cache_flush_interval != 0) {
      std::this_thread::sleep_for(std::chrono::seconds(1));
      continue;
    }
    if (batch_version_ms_ <= 0) {
      LOG_EVERY_N(INFO, 100) << "skip lru flush " << EMBEDDING_LEN
                             << " since no valid batch avaible";
      continue;
    }
    lru_cache_->Flush();
  }
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
const float *HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetW(
    const size_t &field, const uint64_t &sign) const {
  if (sign == 0 && field < zero_cache_capacity_) {
    auto &target = zero_cache_[field];
#ifdef DEBUG_EMBEDDING
    DebugPrintEmbeddingValue(field, sign, target.embedding.data, EMBEDDING_LEN);
#endif
    return target.embedding.data;
  }
  if (field < hash_field_cache_.size() && hash_field_cache_[field] != nullptr) {
    if (KS_UNLIKELY(sign >= hash_field_sizes_[field])) {
      LOG_EVERY_N(ERROR, 10000) << "Invalid field sign " << sign << " exceeds field "
                                << field << " size " << hash_field_sizes_[field];
      return nullptr;
    }

    auto &target = hash_field_cache_[field][sign];
#ifdef DEBUG_EMBEDDING
    DebugPrintEmbeddingValue(field, sign, target.embedding.data, EMBEDDING_LEN);
#endif
    // Embedding 没有更新过，用的初始值，计算为一次 miss
    // hash_field_miss_count_ += (target.timestamp <= 0);
    // slot_miss_count_[field] += (target.timestamp <= 0);
    return target.embedding.data;
  }
  auto &cur_cache = fixed_cache_[current_fixed_cache_];
  if (field < cur_cache.size() && cur_cache[field]) {
    const auto &iter = cur_cache[field]->find(sign);
    if (iter == cur_cache[field]->end()) {
      ++fix_miss_count_;
      ++slot_miss_count_[field];
      if (random_values_) {
        return random_values_->Get();
      }
      return kDefaultEmbedding;
    }
#ifdef DEBUG_EMBEDDING
    DebugPrintEmbeddingValue(field, sign, iter->second.embedding.data, EMBEDDING_LEN);
#endif
    return iter->second.embedding.data;
  }

  const float *target = nullptr;
  auto get_handle = [&](const EmbeddingNodeTpl<EMBEDDING_LEN> &node) {
    target = node.data;
  };
  if (KS_UNLIKELY(lru_cache_ == nullptr)) {
    falcon::Inc("predict_server.invalid_getw_sign", 1);
    LOG(ERROR) << "Invalid request, field:" << field << " sign:" << sign
               << " not found in any cache";
    return target;
  }
  if (!lru_cache_->Get(sign, get_handle)) {
    ++slot_miss_count_[field];
    ++lru_miss_count_;
    if (random_values_) {
      target = random_values_->Get();
    } else {
      target = kDefaultEmbedding;
    }
  }
#ifdef DEBUG_EMBEDDING
  DebugPrintEmbeddingValue(field, sign, target, EMBEDDING_LEN);
#endif
  return target;
}

// 针对 kai 有哈希模型直接取数组中的 sign 值
template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetBatchW(
    const size_t slot, const uint64_t *batch_signs, uint32_t batch_size,
    std::vector<const float *> *embedding_results) {
  return GetBatchWImpl(slot, batch_signs, batch_size, embedding_results);
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetBatchW(
    const size_t slot, const uint32_t *batch_signs, uint32_t batch_size,
    std::vector<const float *> *embedding_results) {
  return GetBatchWImpl(slot, batch_signs, batch_size, embedding_results);
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
template <typename SignType>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetBatchWImpl(
    const size_t slot, const SignType *batch_signs, uint32_t batch_size,
    std::vector<const float *> *embedding_results) {
  // slot 只用检查一次
  if (KS_UNLIKELY(slot >= hash_field_cache_.size() ||
                  hash_field_cache_[slot] == nullptr)) {
    LOG_EVERY_N(ERROR, 10000) << "Invalid slot " << slot << " exceeds cache size "
                              << hash_field_cache_.size();
    return false;
  }
  // 缓存地址保存起来，避免反复寻址
  auto &hash_field_cache = hash_field_cache_[slot];
  auto &hash_field_size = hash_field_sizes_[slot];
  for (int i = 0; i < batch_size; ++i) {
    auto &sign = *batch_signs;
    if (KS_UNLIKELY(sign >= hash_field_size)) {
      LOG_EVERY_N(ERROR, 10000) << "Invalid sign " << sign << " exceeds slot " << slot
                                << " size " << hash_field_size;
      return false;
    }
    auto &target = hash_field_cache[sign];
    embedding_results->push_back(target.embedding.data);
    ++batch_signs;
#ifdef DEBUG_EMBEDDING
    DebugPrintEmbeddingValue(slot, sign, target.embedding.data, EMBEDDING_LEN);
#endif
  }

  return true;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
const float *HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetWDirectly(
    const size_t slot, const uint64_t sign) const {
  return GetWDirectlyImpl(slot, sign);
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
const float *HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetWDirectly(
    const size_t slot, const uint32_t sign) const {
  return GetWDirectlyImpl(slot, sign);
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
template <typename SignType>
const float *HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetWDirectlyImpl(
    const size_t slot, const SignType sign) const {
  if (KS_UNLIKELY(slot >= hash_field_cache_.size() ||
                  hash_field_cache_[slot] == nullptr)) {
    LOG_EVERY_N(ERROR, 10000) << "Invalid slot " << slot << " exceeds cache size "
                              << hash_field_cache_.size();
    return nullptr;
  }
  if (KS_UNLIKELY(sign >= hash_field_sizes_[slot])) {
    LOG_EVERY_N(ERROR, 10000) << "Invalid sign " << sign << " exceeds slot " << slot
                              << " size " << hash_field_sizes_[slot];
    return nullptr;
  }
  auto &target = hash_field_cache_[slot][sign];
#ifdef DEBUG_EMBEDDING
  DebugPrintEmbeddingValue(slot, sign, target.embedding.data, EMBEDDING_LEN);
#endif
  return target.embedding.data;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
void HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::Put(
    const uint64_t &field, const uint64_t &sign, const base::ConstArray<float> &embedding,
    const uint64_t timestamp) {
  // embedding 值校验失败直接返回
  // 只用校验 embedding.size 个 val
  if (KS_UNLIKELY(!CheckEmbeddingValue(field, sign, embedding.begin(), embedding.size,
                                       EMBEDDING_LEN))) {
    return;
  }
  auto &cur_cache = fixed_cache_[current_fixed_cache_];
  LOG_EVERY_N(INFO, 1000000) << "sign stats: embedding len " << embedding.size
                             << ", embed cache size " << EMBEDDING_LEN << ", sign "
                             << sign << ", field " << field << ", timestamp "
                             << timestamp;
  // sign 0 会同时放到 zero_cache 和 hash_field_cache 中
  if (sign == 0 && field < zero_cache_capacity_) {
    auto &target = zero_cache_[field];
    if (timestamp < target.timestamp) {
      IsValidModelVersion(target.timestamp, timestamp);
      LOG_EVERY_N(INFO, 100000) << "skip older item " << sign << " version " << timestamp
                                << " update, current version " << target.timestamp;
      return;
    }
    if (target.timestamp <= 0) {
      zero_cache_embedding_count_.fetch_add(1, std::memory_order_relaxed);
    }
    target.timestamp = timestamp;
    // 只需拷贝 embedding.size 个 val, 而不是 EMBEDDING_LEN 个
    std::copy_n(embedding.begin(), embedding.size, target.embedding.data);
    // ++zero_cache_put_count_;
  }
  if (field < hash_field_cache_.size() && hash_field_cache_[field] != nullptr) {
    if (KS_UNLIKELY(sign >= hash_field_sizes_[field])) {
      LOG_EVERY_N(ERROR, 100000) << "Invalid field sign " << sign << " exceeds field "
                                 << field << " size " << hash_field_sizes_[field];
      return;
    }
    auto &target = hash_field_cache_[field][sign];
    if (timestamp < target.timestamp) {
      IsValidModelVersion(target.timestamp, timestamp);
      LOG_EVERY_N(INFO, 100000) << "skip older item " << sign << " version " << timestamp
                                << " update, current version " << target.timestamp;
      return;
    }
    if (target.timestamp <= 0) {
      hash_field_embedding_count_.fetch_add(1, std::memory_order_relaxed);
    }
    target.timestamp = timestamp;
    std::copy_n(embedding.begin(), embedding.size, target.embedding.data);
    // LOG_EVERY_N(INFO, 100) << "Field: " << field << ", sign: " << sign;
    ++hash_field_put_count_;
    return;
  }
  if (field < cur_cache.size() && cur_cache[field]) {
    FixedCacheItem<EMBEDDING_LEN> item;
    item.field = field;
    item.sign = sign;
    item.embedding.timestamp = timestamp;
    std::copy_n(embedding.begin(), embedding.size, item.embedding.embedding.data);
    while (!fixed_cache_queue_.push(item)) {
      LOG_EVERY_N(INFO, 10000) << "fix cache queue is full, wait for some time";
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    falcon::Inc(falcon_fix_put_key_.c_str(), 1);
    return;
  }
  falcon::Inc(falcon_lru_put_key_.c_str(), 1);
  EmbeddingNodeTpl<EMBEDDING_LEN> *target = nullptr;
  auto update_handle = [&](EmbeddingNodeTpl<EMBEDDING_LEN> *node) { target = node; };
  if (KS_UNLIKELY(lru_cache_ == nullptr)) {
    falcon::Inc("predict_server.invalid_put_sign", 1);
    LOG_EVERY_N(ERROR, 1000) << "Invalid put, embedding_len:" << EMBEDDING_LEN
                             << " slot:" << field << " sign:" << sign
                             << " does not match any cache, skip it";
    return;
  }
  lru_cache_->Put(sign, update_handle, timestamp);
  if (target != nullptr) {
    std::copy_n(embedding.begin(), embedding.size, target->data);
  }
}

// idt 接口实现
template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetOneFieldSignSize(
    EmbCacheType cache_type, uint32_t field_index, uint64_t &sign_size) {  // NOLINT
  if (cache_type == EmbCacheType::HASH_FIELD_CACHE) {
    if (field_index < hash_field_cache_.size() &&
        hash_field_cache_[field_index] != nullptr) {
      sign_size = hash_field_sizes_[field_index];
      return true;
    }
  } else if (cache_type == EmbCacheType::FIXED_CACHE) {
    auto &cur_cache = fixed_cache_[current_fixed_cache_];
    if (field_index < cur_cache.size() && cur_cache[field_index]) {
      sign_size = cur_cache[field_index]->size();
      return true;
    }
  } else if (cache_type == EmbCacheType::LRU_CACHE) {
    if (lru_cache_ != nullptr) {
      sign_size = lru_cache_->Capacity();
      return true;
    }
    LOG(ERROR) << "lru_cache is empty";
  } else if (cache_type == EmbCacheType::ZERO_CACHE) {
    sign_size = zero_cache_capacity_;
    return true;
  }
  LOG_EVERY_N(WARNING, 1000) << "failed to get sign size,"
                             << " cache type " << (int)cache_type << " field index "
                             << field_index << " sign size " << sign_size;
  return false;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetNextValidField(
    EmbCacheType cache_type, uint32_t field_index, uint32_t &next_field) {  // NOLINT
  if (cache_type == EmbCacheType::HASH_FIELD_CACHE) {
    uint32_t cache_size = hash_field_cache_.size();
    LOG(INFO) << "cache_size " << cache_size << ", field_index " << field_index;
    if (field_index >= cache_size) {
      LOG(WARNING) << "invalid field index " << field_index
                   << " exceeds the size of cache " << hash_field_cache_.size();
      return false;
    }

    for (uint32_t idx = field_index; idx < cache_size; idx++) {
      if (hash_field_cache_[idx]) {
        next_field = idx;
        LOG(INFO) << "find next valid field " << next_field << " in hash cache";
        return true;
      }
    }
  } else if (cache_type == EmbCacheType::FIXED_CACHE) {
    auto &cur_cache = fixed_cache_[current_fixed_cache_];
    if (field_index >= cur_cache.size()) {
      LOG(WARNING) << "invalid field index " << field_index
                   << " exceeds the size of cache " << cur_cache.size();
      return false;
    }

    for (uint32_t idx = field_index; idx < cur_cache.size(); idx++) {
      if (cur_cache[idx]) {
        next_field = idx;
        LOG(INFO) << "find next valid field " << next_field << " in fix cache";
        return true;
      }
    }
  } else if (cache_type == EmbCacheType::LRU_CACHE) {
    if (lru_cache_ != nullptr) {
      next_field = 0;
      LOG(INFO) << "find next valid field " << next_field << " in lru cache";
      return true;
    }
  } else if (cache_type == EmbCacheType::ZERO_CACHE) {
    if (zero_cache_ == nullptr) {
      LOG(ERROR) << "zero_cache is empty";
      return false;
    }
    next_field = 0;
    LOG(INFO) << "find next valid field " << next_field << " in zero cache";
    return true;
  }
  LOG(WARNING) << "no more valid field";
  return false;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::GetOneFieldWeights(
    EmbCacheType cache_type, uint32_t field_index, uint64_t sign_index,
    uint64_t sign_size, uint64_t *id, float *val, uint64_t *timestamp) {
  if (cache_type == EmbCacheType::HASH_FIELD_CACHE) {
    if (field_index < hash_field_cache_.size() &&
        hash_field_cache_[field_index] != nullptr) {
      if (sign_index >= hash_field_sizes_[field_index]) {
        LOG_EVERY_N(WARNING, 100000)
            << "Invalid field sign " << sign_index << " exceeds field " << field_index
            << " size " << hash_field_sizes_[field_index];
        return false;
      }
      // 这个感觉没啥用，这两个值一般就是一样的
      uint64_t sign_limit =
          std::min((sign_index + sign_size), hash_field_sizes_[field_index]);
      LOG(INFO) << " sign_index " << sign_index << ", sign_size " << sign_size
                << ", total sign size of " << field_index << " is "
                << hash_field_sizes_[field_index] << ", sign_limit " << sign_limit;
      for (uint64_t idx = sign_index; idx < sign_limit; idx++) {
        auto &target = hash_field_cache_[field_index][idx];
        std::copy_n(target.embedding.data, EMBEDDING_LEN, val);
        LOG_FIRST_N(INFO, 50) << "hash_cache get sample: embedding_len " << EMBEDDING_LEN
                              << ", cache " << (int)cache_type << ", field "
                              << field_index << ", sign " << idx << ", val " << *val
                              << ", timestamp " << target.timestamp;
        val += EMBEDDING_LEN;

        *id = idx;
        id++;

        *timestamp = target.timestamp;
        timestamp++;
      }
      return true;
    }
  } else if (cache_type == EmbCacheType::FIXED_CACHE) {
    auto &cur_cache = fixed_cache_[current_fixed_cache_];
    if (field_index < cur_cache.size() && cur_cache[field_index]) {
      if (sign_index >= cur_cache[field_index]->size()) {
        LOG_EVERY_N(WARNING, 100000)
            << "Invalid field sign " << sign_index << " exceeds field " << field_index
            << " fixed cache size " << cur_cache[field_index]->size();
        return false;
      }
      auto iter = std::next(cur_cache[field_index]->begin(), sign_index);
      // 这个是有意义的，因为 fixed_cache 大小会变
      uint64_t sign_limit =
          std::min((sign_index + sign_size), cur_cache[field_index]->size());
      LOG(INFO) << " sign_index " << sign_index << ", sign_size " << sign_size
                << ", total sign size of " << field_index << ", cur_cache size "
                << cur_cache[field_index]->size() << ", sign_limit " << sign_limit;
      for (uint64_t idx = sign_index;
           idx < sign_limit && iter != cur_cache[field_index]->end(); idx++, iter++) {
        auto &target = iter->second;
        std::copy_n(target.embedding.data, EMBEDDING_LEN, val);
        LOG_FIRST_N(INFO, 50) << "fixed_cache get sample: embedding_len " << EMBEDDING_LEN
                              << ", cache " << (int)cache_type << ", field "
                              << field_index << ", sign " << iter->first << ", val "
                              << *val << ", timestamp " << target.timestamp;

        val += EMBEDDING_LEN;

        *id = iter->first;
        id++;

        *timestamp = target.timestamp;
        timestamp++;
      }
      return true;
    }
  } else if (cache_type == EmbCacheType::LRU_CACHE) {
    LOG(INFO) << " get embedding weights from lru_cache, embedding_len " << EMBEDDING_LEN
              << ", cache_type " << (int)cache_type << ", field_index " << field_index
              << ", sign_index " << sign_index << ", sign_size " << sign_size;
    auto get_embed_weights_handle = [&](const size_t &key,
                                        const EmbeddingNodeTpl<EMBEDDING_LEN> &node,
                                        const size_t &meta_timestamp) {
      std::copy_n(node.data, EMBEDDING_LEN, val);
      LOG_FIRST_N(INFO, 100) << "lru_cache get sample: embedding_len " << EMBEDDING_LEN
                             << ", cache " << (int)cache_type << ", field " << field_index
                             << ", slot " << (key >> 52) << ", sign " << key << ", val "
                             << *val << ", timestamp " << meta_timestamp;
      val += EMBEDDING_LEN;

      *id = key;
      id++;

      *timestamp = meta_timestamp;
      timestamp++;
      return true;
    };
    return lru_cache_->ForEachPart(sign_index, sign_size, get_embed_weights_handle);
  } else if (cache_type == EmbCacheType::ZERO_CACHE) {
    // 从 zero cahche 中获取 embedding weights
    // FixedCacheMemItem<EMBEDDING_LEN>* zero_cache_;  // 存放 sign 为 0 下标对应 field id
    // 此时输入的 field_index 无效，sign_index 指明 field id 下标，
    // sign_size 是存入的总数, id 存放的是 field id
    if (zero_cache_ == nullptr || sign_index >= zero_cache_capacity_) {
      LOG(ERROR) << "zero cache is nullptr or field id " << sign_index
                 << " exceeds zero_cache_capacity " << zero_cache_capacity_;
      return false;
    }

    uint64_t limit = std::min((sign_index + sign_size), zero_cache_capacity_);
    for (uint64_t idx = sign_index; idx < limit; idx++) {
      auto &target = zero_cache_[idx];
      std::copy_n(target.embedding.data, EMBEDDING_LEN, val);
      LOG(INFO) << "zero_cache get sample: embedding_len " << EMBEDDING_LEN << ", cache "
                << (int)cache_type << ", field " << idx << ", val " << *val
                << ", timestamp " << target.timestamp;
      val += EMBEDDING_LEN;

      *id = idx;
      id++;

      *timestamp = target.timestamp;
      timestamp++;
    }
    return true;
  }
  LOG(ERROR) << "get embedding weights failed, embedding_len " << EMBEDDING_LEN
             << ", cache_type " << (int)cache_type << ", field_index " << field_index
             << ", sign_index " << sign_index << ", sign_size " << sign_size;
  return false;
}

template <template <typename> class LruBuffer, uint32_t EMBEDDING_LEN>
bool HybridEmbCacheTpl<LruBuffer, EMBEDDING_LEN>::PutOneFieldWeights(
    EmbCacheType cache_type, uint32_t field_index, uint64_t sign_index,
    uint64_t sign_size, const uint64_t *id, const float *val, const uint64_t *timestamp) {
  if (cache_type == EmbCacheType::HASH_FIELD_CACHE) {
    if (field_index < hash_field_cache_.size() &&
        hash_field_cache_[field_index] != nullptr) {
      uint64_t sign = id[sign_size - 1];
      // todo 需要找到最后一个大于 0 的 sign 的值，因为后面可能会填充为 0
      // 并不需要，hash_field_cache 的下标是 sign，sign 值是线性增长的， 并不会突然少几个
      // sign
      if (sign >= hash_field_sizes_[field_index]) {
        LOG(WARNING) << "Invalid field sign " << sign << " exceeds field " << field_index
                     << " size " << hash_field_sizes_[field_index];
        return false;
      }
      for (uint64_t idx = 0; idx < sign_size; idx++, val += EMBEDDING_LEN) {
        if (timestamp[idx] >= 0) {
          sign = id[idx];
          auto &target = hash_field_cache_[field_index][sign];
          idt_update_embedding_count_.fetch_add(1, std::memory_order_relaxed);
          falcon::Inc(falcon_hash_field_cache_idt_update_count_.c_str(), 1);
          if (timestamp[idx] < target.timestamp) {
            LOG_EVERY_N(INFO, 100000)
                << "skip older item " << sign << " version " << timestamp[idx]
                << " update, current version " << target.timestamp;
            continue;
          }
          if (!CheckEmbeddingValue(field_index, sign, val, slot2embed_len_[field_index],
                                   EMBEDDING_LEN)) {
            return false;
          }
          // idt 拉取数据的时间戳为 0 时, hash_field_embedding_count_ 不做计数
          if (target.timestamp <= 0 && timestamp[idx] > 0) {
            hash_field_embedding_count_.fetch_add(1, std::memory_order_relaxed);
          }
          target.timestamp = timestamp[idx];
          std::copy_n(val, EMBEDDING_LEN, target.embedding.data);
          LOG_FIRST_N(INFO, 50)
              << "hash_cache put sample: embedding_len " << EMBEDDING_LEN << ", field "
              << field_index << ", sign " << sign << ", val " << *val << ", timestamp "
              << target.timestamp;
        } else {
          LOG_FIRST_N(WARNING, 50)
              << "hash_cache key timestamp " << timestamp[idx]
              << " less than 0: embedding_len " << EMBEDDING_LEN << ", cache "
              << (int)cache_type << ", field " << field_index;
        }
      }
      LOG(INFO) << "put one field weights success, cache_type " << (int)cache_type
                << ", field_index " << field_index << ", sign_size " << sign_size;
      return true;
    }
  } else if (cache_type == EmbCacheType::FIXED_CACHE) {
    auto &cur_cache = fixed_cache_[current_fixed_cache_];
    if (field_index < cur_cache.size() && cur_cache[field_index]) {
      uint64_t sign = 0;
      for (uint64_t idx = 0; idx < sign_size; idx++, val += EMBEDDING_LEN) {
        sign = id[idx];
        // fixed cache 有逐出逻辑，GetOneFieldWeights 可能没有取出 sign_size
        // 个，未取出部分填充为 0
        if (sign == 0) {
          continue;
        }
        if (!CheckEmbeddingValue(field_index, sign, val, slot2embed_len_[field_index],
                                 EMBEDDING_LEN)) {
          return false;
        }
        FixedCacheItem<EMBEDDING_LEN> item;
        item.field = field_index;
        item.sign = sign;
        item.embedding.timestamp = timestamp[idx];
        std::copy_n(val, EMBEDDING_LEN, item.embedding.embedding.data);

        while (!fixed_cache_queue_.push(item)) {
          LOG_EVERY_N(INFO, 10000) << "fix cache queue is full, wait for some time";
          std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        idt_update_embedding_count_.fetch_add(1, std::memory_order_relaxed);
        falcon::Inc(falcon_fixed_cache_idt_update_count_.c_str(), 1);
        LOG_FIRST_N(INFO, 50) << "fixed_cache put sample: embedding_len " << EMBEDDING_LEN
                              << ", field " << field_index << ", sign " << sign
                              << ", val " << *val << ", timestamp " << timestamp[idx];
      }
      LOG(INFO) << "put one field weights success, cache_type " << (int)cache_type
                << ", field_index " << field_index << ", sign_size " << sign_size;
      return true;
    }
  } else if (cache_type == EmbCacheType::LRU_CACHE) {
    EmbeddingNodeTpl<EMBEDDING_LEN> *target = nullptr;
    auto update_handle = [&](EmbeddingNodeTpl<EMBEDDING_LEN> *node) { target = node; };
    uint64_t sign = 0;
    for (uint64_t idx = 0; idx < sign_size; idx++, val += EMBEDDING_LEN) {
      sign = id[idx];
      if (sign == 0) {
        continue;
      }
      // 无 hash 模型的 embedding 参数无 slot 的概念, 因此 slot2embed_len_[field_index] 为
      // 0 故固定检查 EMBEDDING_LEN 长度的 embedding 参数值
      if (!CheckEmbeddingValue(0, sign, val, EMBEDDING_LEN, EMBEDDING_LEN)) {
        return false;
      }
      idt_update_embedding_count_.fetch_add(1, std::memory_order_relaxed);
      falcon::Inc(falcon_lru_cache_idt_update_count_.c_str(), 1);
      lru_cache_->Put(sign, update_handle, timestamp[idx]);
      if (target != nullptr) {
        std::copy_n(val, EMBEDDING_LEN, target->data);
      }
      target = nullptr;
      LOG_FIRST_N(INFO, 100) << "lru_cache put sample: embedding_len " << EMBEDDING_LEN
                             << ", field " << field_index << ", slot " << (sign >> 52)
                             << ", sign " << sign << ", val " << *val << ", timestamp "
                             << timestamp[idx];
    }
    LOG(INFO) << "put one field weights success, cache_type " << (int)cache_type
              << ", field_index " << field_index << ", sign_size " << sign_size;
    return true;
  } else if (cache_type == EmbCacheType::ZERO_CACHE) {
    // FixedCacheMemItem<EMBEDDING_LEN>* zero_cache_;  // 存放 sign 为 0 下标对应 field id
    // 此时输入的 field_index 无效，sign_size 是存入的总数, id 存放的是 field id
    uint64_t field_id = id[sign_size - 1];
    if (field_id >= zero_cache_capacity_) {
      LOG(WARNING) << "field id to be put into zero cache " << field_index
                   << " exceed zero_cache_capacity " << zero_cache_capacity_;
      return false;
    }
    for (uint64_t idx = 0; idx < sign_size; idx++, val += EMBEDDING_LEN) {
      if (timestamp[idx] >= 0) {
        field_id = id[idx];
        auto &target = zero_cache_[field_id];
        idt_update_embedding_count_.fetch_add(1, std::memory_order_relaxed);
        falcon::Inc(falcon_zero_cache_idt_update_count_.c_str(), 1);
        if (timestamp[idx] < target.timestamp) {
          LOG_EVERY_N(INFO, 100000)
              << "skip older item " << field_id << " version " << timestamp[idx]
              << " update, current version " << target.timestamp;
          continue;
        }
        // 有效 slot 才做校验
        if (slot2embed_len_[field_id] > 0 &&
            !CheckEmbeddingValue(field_id, 0, val, slot2embed_len_[field_id],
                                 EMBEDDING_LEN)) {
          return false;
        }
        // idt 拉取数据的时间戳为 0 时, zero_cache_embedding_count_ 不做计数
        if (target.timestamp <= 0 && timestamp[idx] > 0) {
          zero_cache_embedding_count_.fetch_add(1, std::memory_order_relaxed);
        }
        target.timestamp = timestamp[idx];
        std::copy_n(val, EMBEDDING_LEN, target.embedding.data);
        LOG(INFO) << "zero_cache put sample: embedding_len " << EMBEDDING_LEN
                  << ", field " << field_id << ", val " << *val << ", timestamp "
                  << timestamp[idx];
        // hash_field_cache 存在时，sign 0 也存入 hash_field_cache
        if (field_id < hash_field_cache_.size() &&
            hash_field_cache_[field_id] != nullptr && hash_field_sizes_[field_id] > 0) {
          auto &hash_field_target = hash_field_cache_[field_id][0];
          if (timestamp[idx] < hash_field_target.timestamp) {
            LOG_EVERY_N(INFO, 100000)
                << "skip to put sign 0 of field " << field_id
                << " update into hash_field_cache, version " << timestamp[idx]
                << ", current version " << hash_field_target.timestamp;
            continue;
          }
          hash_field_target.timestamp = timestamp[idx];
          std::copy_n(val, EMBEDDING_LEN, hash_field_target.embedding.data);
          LOG(INFO) << "sign zero put into hash_field_cache: embedding_len "
                    << EMBEDDING_LEN << ", field " << field_id << ", val " << *val
                    << ", timestamp " << timestamp[idx];
        }
      } else {
        LOG(WARNING) << "zero_cache key timestamp " << timestamp[idx]
                     << " less than 0, embedding_len " << EMBEDDING_LEN << ", field "
                     << id[idx];
      }
    }
    return true;
  }
  LOG(WARNING) << "put one field weights failed, cache_type " << (int)cache_type
               << ", field_index " << field_index << ", sign_size " << sign_size;
  return false;
}
}  // namespace ad_nn
}  // namespace ks
