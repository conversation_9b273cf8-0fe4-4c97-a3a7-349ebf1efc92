#include "teams/ad/ad_nn/feature_server/feature_server.h"
#include <google/protobuf/arena.h>
#include <gperftools/profiler.h>
#include <algorithm>
#include <cstdlib>
#include <functional>
#include <map>
#include <memory>
#include <regex>
#include <set>
#include <sstream>
#include <unordered_set>
#include <utility>
#include <vector>
#include "base/file/file_util.h"
#include "brpc/traceprintf.h"
#include "dragon/src/util/logging_util.h"
#include "flatbuffers/flatbuffers.h"
#include "infra/falcon_counter/src/falcon/counter.h"
#include "infra/kenv/src/kenv/kenv.h"
#include "infra/perfutil/src/perfutil/perfutil.h"
#include "kenv/service_meta.h"
#include "ks/cofea/flat_generated/flat_generated_helper.h"
#include "ks/cofea/flat_generated/samples_generated.h"
#include "ks/serving_util/dynamic_config.h"
#include "teams/ad/ad_algorithm/bs_feature/fast/frame/bs_leaf_util.h"
#include "teams/ad/ad_algorithm/feature_interface/fast_feature_interface.h"
#include "teams/ad/ad_algorithm/feature_interface/industry_v5_wrapper.h"
#include "teams/ad/ad_base/src/common/virtual_creative_hash.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_nn/bs_field_helper/bs_fields_enum_i18n.h"
#include "teams/ad/ad_nn/bs_kconf/bs_kconf_util.h"
#include "teams/ad/ad_nn/common_idt_server/common_idt_reader.h"
#include "teams/ad/ad_nn/common_idt_server/common_idt_utils.h"
#include "teams/ad/ad_nn/data_converter/common.h"
#include "teams/ad/ad_nn/data_converter/data_converter.h"
#include "teams/ad/ad_nn/feature/feature_stat_control.h"
#include "teams/ad/ad_nn/feature/feature_thread_resource_manager/feature_thread_resource_manager.h"
#include "teams/ad/ad_nn/feature/preprocess/common.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/attr_name_mapping.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/batched_samples_feature.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/dynamic_batched_samples.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/eval_context_interface.h"
#include "teams/ad/ad_nn/feature_extract/extract_framework/include/raw_buffer_feature.h"
#include "teams/ad/ad_nn/flatten_raw_feature/adlog_helper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/flat_wrapper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/flatten_helper.h"
#include "teams/ad/ad_nn/flatten_raw_feature/utils.h"
#include "teams/ad/ad_nn/flow_copier/infer_service_flow_copier.h"
#include "teams/ad/ad_nn/idt_server/idt_item_helper.h"
#include "teams/ad/ad_nn/model/cleanup.h"
#include "teams/ad/ad_nn/model/tuning_util.h"
#include "teams/ad/ad_nn/model/utils.h"
#include "teams/ad/ad_nn/monitor/monitor.h"
#include "teams/ad/ad_nn/router/utils/prometheus.h"
#include "teams/ad/ad_nn/service/debug_sample.h"
#include "teams/ad/ad_nn/service/kconf_util.h"
#include "teams/ad/ad_nn/service/predict_result_util.h"
#include "teams/ad/ad_nn/service/service_util.h"
#include "teams/ad/ad_nn/service/wait_service_ready.h"
#include "teams/ad/ad_nn/service_status/service_status.h"
#include "teams/ad/ad_nn/simplified_raw_fea/utils.h"
#include "teams/ad/ad_nn/utils/compress.h"
#include "teams/ad/ad_nn/utils/fake_item_counter.h"
#include "teams/ad/ad_proto/kuaishou/ad/algorithm/model/ad_joint_labeled_log_with_features.pb.h"
#include "teams/ad/picasso/sdk/proto/kv.pb.h"
#include "third_party/gflags/gflags/gflags.h"
#include "third_party/snappy/snappy.h"

DECLARE_int32(grpc_thread_num);
DECLARE_bool(enable_kv_item_value_empty);

using ks::ad_ps_router::prometheus_util::call_endpoint;
using ks::ad_ps_router::prometheus_util::call_service;
using kuaishou::ad::algorithm::AckStatus;
using kuaishou::ad::algorithm::AD_DSP;
using kuaishou::ad::algorithm::AdJointLabeledLog;
using kuaishou::ad::algorithm::FeatureRequest;
using kuaishou::ad::algorithm::PredictStatus_Name;
using kuaishou::ad::algorithm::UniversePredictRequest;
using kuaishou::ad::algorithm::UniversePredictResponse;
using kuaishou::ad::algorithm::UsedItem;

using tuning::CpuAffinity;
using tuning::ThreadTuning;

using ks::ad_algorithm::IdIndustryMapping;
using ks::ad_algorithm::IndustryMapping;
using ks::ad_algorithm::InitAllIndustryMapping;
using ks::infra::kenv::ServiceMeta;

using ks::ad_algorithm::GameProductNameMapping;
using kuaishou::ad::CommonTypeEnum;

DECLARE_int32(fake_log_freq);
DECLARE_bool(is_i18n);
DEFINE_string(item_type, "", "request item type");
DEFINE_bool(support_ack, false, "whether support ack");
DEFINE_bool(join_ack_from_redis, false, "whether join ack from redis");
DEFINE_int32(ad_log_kafka_tab_type, 0, "tabType_ of AdNLogKafkaQueue");
DEFINE_bool(enable_item_opt, false, "whether enable item opt");
DEFINE_int64(item_feature_cache_capacity, 0, "");
DEFINE_int32(item_feature_expire_time, 120, "in seconds");
DEFINE_bool(enable_item_pb_parse_opt, true, "");
DEFINE_int32(dump_result_freq, 1000000, "");
DEFINE_bool(dump_base64_result, false, "");
DEFINE_int64(item_embedding_feature_cache_capacity, 0, "");
DEFINE_int32(item_embedding_feature_cache_expire_time, 120, "in seconds");
DEFINE_bool(enable_embedding_feature_cache, false,
            "whether enable embedding feature cache");
DEFINE_int32(
    dump_request_freq, 0,
    "dump one request to local every dump_request_freq requests; set to 0 to disable it");
DEFINE_double(dump_value_threshold, 0.0f, "the threshold to dump req and res");
DEFINE_string(dump_request_path, "../debug_log", "dump request path");
DEFINE_int32(infer_service_timeout, 40, "infer service timeout in ms");
DEFINE_int32(debug_req_infer_service_timeout, 4000, "infer service timeout in ms");
DEFINE_int32(feature_req_arena_size, 1 << 22,
             "thread local feature request arena size");  // 4M
DEFINE_bool(enable_idt_item, false, "enable idt item");
DEFINE_bool(enable_cost_stat, false, "enable cost stat");

DEFINE_bool(enable_pb_cache, false, "whether to enable pb serialized cache");
DEFINE_int32(pb_cache_capacity, 5000000, "pb cache capacity");

DECLARE_bool(remap_add_sign_prefix);
DECLARE_bool(use_exist_fix_version);
DECLARE_bool(use_bs_reco_userinfo);
DECLARE_bool(fake_photo_id_for_empty);
namespace ks {
namespace ad_nn {

thread_local std::vector<std::vector<int32_t>> FeatureServer::req_idxes_;
thread_local std::vector<std::vector<uint64_t>> FeatureServer::item_ids_;
thread_local uint64_t FeatureServer::req_time_;
thread_local uint64_t FeatureServer::item_expire_time_;

FeatureServer::FeatureServer()
    : item_type_(ParseItemType(FLAGS_item_type)),
      item_tasks_(kTaskQueueCapacity),
      is_i18n_(FLAGS_is_i18n),
      counter_(0) {
  // 1. 读取模型配置
  std::string model_cmd = GetKcsModelCmd();
  auto kconf_model_config = ks::ad_nn::PredictServiceKconfUtil::GetModelConfig(model_cmd);
  if (!kconf_model_config) {
    std::ostringstream oss;
    oss << "no model config found for model " << model_cmd;
    ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
    LOG(FATAL) << "no model config found for model " << model_cmd;
  }
  model_config_ = kconf_model_config;
  FeatureThreadResourceManager::InitAll(
      model_config_->GetBoolean("auto_adjust_thread_pool", false));
  // 2. init base variable
  InitBaseVariable(model_cmd);
  // 3. init dragon
  InitDragon();
  // 4. init copy request
  ParseCopyReqParas(model_config_.get());
  // 5. init bs
  InitBsFastFeature();
  // 6. init feature cache
  InitFeatureCache();
  // 7. init predict service
  InitPredictService();
  // 8. init picasso
  InitPicasso();
  // 9. 特征提取模块初始化
  InitFeatureExtract();
  // 16. init preprocessor
  auto preprocessor_config = model_config_->Get("feature_process_config");
  if (preprocessor_config != nullptr) {
    feature_processor_ = std::make_unique<FeatureProcessor>();
    feature_processor_->Init(preprocessor_config, false);
    LOG(INFO) << "feature_process_config init success";
  } else {
    LOG(INFO) << "feature_process_config not found";
  }
  InitAttrId2Idx();
  // 10. 初始化 debugsample 配置
  DebugSample::GetInstance()->UpdateFeatureConfig(&feature_file_info_);
  // 11. init infer service timeout
  InferServiceTimeout();
  // 12. 初始化物料相关逻辑
  InitItem();
  // 13. init local feature
  auto enable_local_feature = model_config_->GetBoolean("enable_local_feature", false);
  LOG(INFO) << "enable_local_feature " << enable_local_feature;
  if (enable_local_feature) {
    // infer server 放本地时，不再需要做 sign diff
    enable_sign_diff_ = false;
    LOG(INFO) << "use local infer server, no need enable_sign_diff " << enable_sign_diff_;
  }
  LOG(INFO) << "enable_sign_diff_ is:" << enable_sign_diff_;
  if (!enable_local_feature) {
    WaitForInferServerReady();
  }
  // 14. init offline topk
  auto enable_offline_topk = model_config_->GetBoolean("enable_offline_topk", false);
  if (enable_offline_topk) {
    InitOfflineTopk(model_config_);
  }
  // 15. init dump request dir
  if (FLAGS_dump_request_freq > 0) {
    base::file_util::CreateDirectory(FLAGS_dump_request_path + "/req");
    base::file_util::CreateDirectory(FLAGS_dump_request_path + "/res");
    // 存储请求 infer server 的 FeatureRequest
    base::file_util::CreateDirectory(FLAGS_dump_request_path + "/infer_req");
  }
  // 17. init item parse
  InitItemParse();
  // 18. init industry_v5 mapping
  InitAllIndustryMapping(model_config_.get());
  ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().SetServerName(cmd_name_);
  ks::ad_algorithm::label_centre::LabelCentreMapping::Instance().Init();
  LOG(INFO) << "feature server init succeeds with actual service name "
            << actual_service_name_;

  InitCompressionConfig();
}

void FeatureServer::InitBaseVariable(const std::string& model_cmd) {
  // 1. init actual_service_name_ and is_i18n_
  actual_service_name_ =
      DynamicJsonConfig::GetConfig()->GetString("fail_switch_server_name", "");
  is_i18n_ = model_config_->GetBoolean("is_i18n", FLAGS_is_i18n);
  // 2. init item_type
  std::string item_type_str = model_config_->GetString("item_type", FLAGS_item_type);
  FLAGS_colossus_common_item_timeout_ms =
      model_config_->GetInt("colossus_common_item_timeout_ms", 20);
  FLAGS_colossus_common_item_compress_type =
      model_config_->GetInt("colossus_common_item_compress_type", -1);
  LOG(INFO) << "colossus_common_item_timeout_ms: "
            << FLAGS_colossus_common_item_timeout_ms
            << ", colossus_common_item_compress_type:"
            << FLAGS_colossus_common_item_compress_type;
  auto item_type = ParseItemType(item_type_str);
  if (item_type != kuaishou::ad::algorithm::UNKNOWN) {
    item_type_ = item_type;
  }
  LOG(INFO) << "item type: " << item_type_;
  // 3. 设置 arena
  ItemAdaptorBase::SetArenaOption(1 << 10);
  // 4. 线上禁止添加前缀
  FLAGS_remap_add_sign_prefix = false;
  // 5. 初始化预估值数量
  predict_value_num_ = model_config_->GetInt("predict_value_num", 1);
  // 6. 初始化是否统计 fake item 数据
  ignore_fake_item_monitor_ =
      model_config_->GetBoolean("ignore_fake_item_monitor", false);
  // 7. 初始化是否需要请求 infer
  need_req_infer_server_ = model_config_->GetBoolean("need_req_infer_server", true);
  use_brpc_ = model_config_->GetBoolean("feature_use_brpc", false);
  LOG(INFO) << "feature_server use_brpc set: " << use_brpc_;
  // 8. 初始化是否混排和加载 user 信息
  is_mix_rank_ = model_config_->GetBoolean("is_mix_rank", false);
  need_load_user_data_ =
      model_config_->GetBoolean("load_user_data", need_load_user_data_);
  skip_cmd_check_ = model_config_->GetBoolean("skip_cmd_check", false);
  if (is_mix_rank_) {
    skip_cmd_check_ = true;
    LOG(INFO) << "is mix rank, skip cmd check";
  }
  // 9. init DAG&bs switch config
  use_feature_extract_handler_ =
      model_config_->GetBoolean("use_feature_extract_handler", false);
  use_bs_fast_feature_ = model_config_->GetBoolean("use_bs_fast_feature", false);
  if (use_feature_extract_handler_ || use_bs_fast_feature_) {
    fake_bs_miss_item_ = model_config_->GetBoolean("fake_bs_miss_item", false);
    need_fake_user_ = model_config_->GetBoolean("need_fake_user", false);
    use_data_converter_ = model_config_->GetBoolean("use_data_converter", false);
    convert_in_local_ = model_config_->GetBoolean("convert_in_local", false);
    convert_real_time_item_context_ =
        model_config_->GetBoolean("convert_real_time_item_context", false);
    miss_when_item_context_nullptr_ =
        model_config_->GetBoolean("miss_when_item_context_nullptr", false);
    req_flatten_reco_user_info_ =
        model_config_->GetBoolean("req_flatten_reco_user_info", false);
    skip_router_req_ = model_config_->GetBoolean("skip_router_req", false);
    skip_req_item_server_ = model_config_->GetBoolean("skip_req_item_server", false);
    build_photo_item_id_from_request_ =
        model_config_->GetBoolean("build_photo_item_id_from_request", false);
    use_full_bs_user_info_ = model_config_->GetBoolean("use_full_bs_user_info", true);
    if (is_i18n_) use_i18n_opt_ = model_config_->GetBoolean("use_i18n_opt", false);
    if (use_i18n_opt_) FLAGS_dag_use_simple_raw_fea = true;
  }
  use_component_item_ = model_config_->GetBoolean("use_component_item", false);
  if (use_component_item_) {
    if (is_i18n_ && !use_i18n_opt_) {
      LOG(FATAL) << "i18n's component must open opt.";
    }
    FLAGS_fake_photo_id_for_empty =
        model_config_->GetBoolean("fake_photo_id_for_empty", true);
    use_component_item_debug_ =
        model_config_->GetBoolean("use_component_item_debug", false);
  }
  // 10. init dump log
  dump_log_ = model_config_->GetBoolean("dump_log", false);
  dump_log_freq_ = model_config_->GetInt("dump_log_freq", 1000);
  // 11. 初始化 bs 相关的变量
  need_eb_bs_redis_info_ = model_config_->GetBoolean("need_eb_bs_redis", false);
  bs_need_pb_maplist_ = model_config_->GetBoolean("bs_need_pb_maplist", true);
  // 12. init aa alias
  aa_alias_ = model_config_->GetString("aa_alias", "");
  // 13. init feature arena size
  FLAGS_feature_req_arena_size =
      model_config_->GetInt("feature_req_arena_size", FLAGS_feature_req_arena_size);
  // 14. init FLAGS_enable_feature_info_check
  auto model_name_white_list =
      ks::ad_nn::PredictServiceKconfUtil::adPredictFeatureFileCheckWhiteList();
  if (model_name_white_list) {
    if (model_name_white_list->find(model_cmd) != model_name_white_list->end()) {
      FLAGS_enable_feature_info_check = false;
    }
  }
  // 15. 初始化 item 反序列化优化
  FLAGS_enable_item_pb_parse_opt = model_config_->GetBoolean(
      "enable_item_pb_parse_opt", FLAGS_enable_item_pb_parse_opt);
  // 16. 初始化是否差分特征值
  enable_sign_diff_ = model_config_->GetBoolean("enable_sign_diff", false);
  // 17. 初始化是否强制解析 reco_user_info
  need_parse_reco_user_info_ =
      model_config_->GetBoolean("need_parse_reco_user_info", false);
  // 18. 初始化 feature processor 的并发线程数
  FLAGS_feature_processor_thread_count = model_config_->GetInt(
      "feature_processor_thread_count", FLAGS_feature_processor_thread_count);
  // 19. rank name 特征是否使用推理替换的信息（共享特征服务使用）
  ks::ad_algorithm::FLAGS_enable_infer_use_train =
      model_config_->GetBoolean("enable_infer_use_train", false);
  LOG(INFO) << "FLAGS_enable_infer_use_train: "
            << ks::ad_algorithm::FLAGS_enable_infer_use_train;
  // 20. debug 特征提取，关闭 user id 缓存
  debug_diff_ = model_config_->GetBoolean("debug_shared", false);
  debug_feature_value_v2_ = model_config_->GetBoolean("debug_feature_value_v2", false);
  ks::ad_algorithm::FLAGS_enable_shared_debug = debug_diff_;
  // 21. 标签中心 Redis 迁移 BTQ
  ks::ad_algorithm::FLAGS_use_btq_industry_v5_feature =
      model_config_->GetBoolean("use_btq_industry_v5_feature", false);
  // 22. 初始化 output
  if (!PredictServiceKconfUtil::GetOutputOpNames(*model_config_.get(),
                                                 &output_op_names_)) {
    LOG(FATAL) << "failed to get output_op_names from kconf, please refer to error log "
                  "for more information";
  }
  if (!PredictServiceKconfUtil::GetArrayWithDefaultValue<std::string>(
          *model_config_.get(), "vector_op_names", &vector_op_names_, false, 0, false,
          "")) {
    LOG(FATAL) << "failed to get vector_op_names from kconf, please refer to error log "
                  "for more information";
  }
  output_op_names_.insert(output_op_names_.end(), vector_op_names_.begin(),
                          vector_op_names_.end());
  for (size_t i = 0; i < output_op_names_.size(); i++) {
    LOG(INFO) << "output_op_names[" << i << "]=" << output_op_names_[i];
  }
  // 23. 初始化访问 ColossusDB AdItem 的相关配置
  clsdb_server_config_ = model_config_->GetString("clsdb_server_config", "");
  // 用于替代 item_server, 优先级高于 item_server_cmd
  if (clsdb_server_config_ != "") {
    clsdb_client_ = std::make_unique<colossusdb::KconfClient<colossusdb::ps::PsClient>>(
        clsdb_server_config_);
    if (clsdb_client_) access_clsdb_ad_item_ = true;
  }

  // 打开 dragon 多线程处理回调
  FLAGS_dragon_grpc_use_multi_eventloop =
      model_config_->GetBoolean("dragon_grpc_use_multi_eventloop", false);
  FLAGS_thread_pool_min_thread_num =
      model_config_->GetInt("thread_pool_min_thread_num", 16);
  FLAGS_thread_pool_max_thread_num =
      model_config_->GetInt("thread_pool_max_thread_num ", 128);
  FLAGS_enable_kv_item_value_empty =
      model_config_->GetBoolean("enable_kv_item_value_empty", false);
  // for i18n rawfea user info diff
  user_info_diff_ = model_config_->GetBoolean("user_info_diff", false);
}

void FeatureServer::InitDragon() {
  bool template_dragon = false;
  auto template_keys = model_config_->Get("template_pipeline_keys");
  if (template_keys != nullptr && template_keys->IsArray() && template_keys->size() > 0) {
    template_dragon = true;
  }
  enable_dragon_pipeline_ = model_config_->GetBoolean("enable_dragon_pipeline", false);
  if (enable_dragon_pipeline_) {
#ifdef BUILD_WITH_DRAGON
    if (template_dragon) {
      feature_dragon_.reset(new TemplateDragon());
    } else {
      feature_dragon_.reset(new FeatureDragon());
    }
    feature_dragon_->Init();
#else
    ks::ad_nn::ServiceStatus::Get()
        .GetModule(MODULE_DEFAULT)
        ->LogFail("dragon pipeline must run with BUILD_WITH_DRAGON build option set");
    LOG(FATAL) << "dragon pipeline must run with BUILD_WITH_DRAGON build option set";
#endif
  }
}

void FeatureServer::InitBsFastFeature() {
  transfer_type_ =
      static_cast<TransferType>(model_config_->GetInt("adlog_transfer_type", 0));
  LOG(INFO) << "Transfer type: " << transfer_type_;
  int raw_fea_perf_mode = model_config_->GetInt("rawfea_perf_mode", -1);
  if (use_bs_fast_feature_ && raw_fea_perf_mode != -1) {
    auto* instance = ks::ad_nn::AttrIdCounter::GetInstance();
    if (instance->Init(ks::ad_nn::SampleInterface::max_raw_fea_size(),
                       raw_fea_perf_mode)) {
      LOG(ERROR) << "RawFea perf init failed, mode: " << raw_fea_perf_mode;
    }
  }
  if (use_bs_fast_feature_ || use_i18n_opt_) {
    dragon_infer_kess_name_ = model_config_->GetString("dragon_infer_kess_name", "");
    if (dragon_infer_kess_name_ != "") {
      dragon_client_ = std::make_shared<DragonClient>();
    }
    FLAGS_enable_item_pb_parse_opt = false;
    remap_bs_field_enum_ = model_config_->GetBoolean("remap_bs_field_enum", false);
    async_build_bs_ = model_config_->GetBoolean("async_build_bs", true);
    opt_bs_item_ = model_config_->GetBoolean("opt_bs_item", true);
    if (use_component_item_ && !opt_bs_item_)
      LOG(FATAL) << "component item must use opt_bs_item.";
    prune_bs_attr_ = model_config_->GetBoolean("prune_bs_attr", true);
    use_opt_user_info_ = model_config_->GetBoolean("use_opt_user_info", true);
    use_opt_item_server_ = model_config_->GetBoolean("use_opt_item_server", false);
    if (opt_bs_item_ && use_opt_item_server_)
      cmd_name_ = model_config_->GetString("cmd_name", "");
    FLAGS_use_simplified_raw_fea =
        model_config_->GetBoolean("use_simplified_raw_fea", FLAGS_use_simplified_raw_fea);
    need_bs_proccessor_opt_ = model_config_->GetBoolean("need_bs_proccessor_opt", true);
    auto deploy_config = model_config_->Get("deploy");
    FLAGS_bs_feature_config_map =
        deploy_config->GetString("ps_root", "") + "/bs_feature_config_map";
    // when copy req client is not empty, disable some opt for bs
    if (copy_req_client_ != nullptr) {
      transfer_type_ =
          static_cast<TransferType>(model_config_->GetInt("adlog_transfer_type", 1));
      FLAGS_skip_process_pb = true;
      LOG(INFO) << "[Debug] copy client is not nullptr"
                << " async_build_bs: " << async_build_bs_
                << " opt_bs_item_: " << opt_bs_item_
                << " use_opt_user_info: " << use_opt_user_info_
                << " use_data_converter: " << use_data_converter_
                << " convert_in_local: " << convert_in_local_
                << " prune_bs_attr: " << prune_bs_attr_
                << " use simplified_raw_fea: " << FLAGS_use_simplified_raw_fea
                << " use_i18n_opt: " << use_i18n_opt_;
    }
    if (async_build_bs_) {
      bs_build_task_thread_count_ =
          model_config_->GetInt("bs_build_task_thread_count", 100);
      bs_build_item_concurrency_ = model_config_->GetInt("bs_build_item_concurrency", 2);
      bs_init_item_concurrency_ = model_config_->GetInt("bs_init_item_concurrency", 4);
      bs_item_async_init_threshold_ =
          model_config_->GetInt("bs_item_async_init_threshold", 25);
      LOG(INFO) << "Async build bs is enabled "
                << " bs_build_task_thread_count: " << bs_build_task_thread_count_
                << " bs_item_async_init_threshold: " << bs_item_async_init_threshold_
                << " bs_build_item_concurrency: " << bs_build_item_concurrency_
                << " bs_init_item_concurrency: " << bs_init_item_concurrency_
                << " bs_build_task queue capacity: " << kTaskQueueCapacity
                << " bs_init_task queue capacity: " << kTaskQueueCapacity;
    }
#ifdef REMAP_BS_FIELD_ENUM
    remap_bs_field_enum_ = true;
    LOG(INFO) << "Set remap_bs_field_enum to true because the REAMP_BS_FIELD_ENUM macro";
#endif
  }
  LOG(INFO) << " remap_bs_field_enum_: " << remap_bs_field_enum_
            << " fake bs miss item: " << fake_bs_miss_item_;
  if (copy_req_cmd_ != "") {
    transfer_bs_timeout_ = model_config_->GetInt("transfer_bs_timeout", 10000);
    WaitForTransferServerReady();
  }
}

void FeatureServer::InitFeatureCache() {
  // 1. init before embedding cache
  FLAGS_item_feature_cache_capacity = model_config_->GetInt(
      "item_feature_cache_capacity", FLAGS_item_feature_cache_capacity);
  FLAGS_item_feature_expire_time =
      model_config_->GetInt("item_feature_expire_time", FLAGS_item_feature_expire_time);
  // 新特征流暂时不支持 feature cache
  uint64_t item_feature_cache_capacity_in_gb =
      model_config_->GetInt("item_feature_cache_capacity_in_gb", 0);
  if ((FLAGS_item_feature_cache_capacity > 0 || item_feature_cache_capacity_in_gb > 0) &&
      (!use_feature_extract_handler_ || copy_req_cmd_ != "")) {
    const int32_t shard_count = model_config_->GetInt("item_feature_cache_shard_num", 1);
    if (item_feature_cache_capacity_in_gb > 0) {
      item_feature_cache_.reset(new CompactFeatureCache(
          item_feature_cache_capacity_in_gb * 1024 * 1024 * 1024,
          FLAGS_item_feature_expire_time, true, shard_count /* fixed mem mode */));
    } else {
      item_feature_cache_.reset(new CompactFeatureCache(FLAGS_item_feature_cache_capacity,
                                                        FLAGS_item_feature_expire_time,
                                                        false, shard_count));
    }
  }
  // 2. init after embedding cache
  FLAGS_item_embedding_feature_cache_capacity =
      model_config_->GetInt("item_embedding_feature_cache_capacity",
                            FLAGS_item_embedding_feature_cache_capacity);
  FLAGS_item_embedding_feature_cache_expire_time =
      model_config_->GetInt("item_embedding_feature_cache_expire_time",
                            FLAGS_item_embedding_feature_cache_expire_time);
  FLAGS_enable_embedding_feature_cache = model_config_->GetBoolean(
      "enable_embedding_feature_cache", FLAGS_enable_embedding_feature_cache);
  embedding_cache_listen_delete_disable_ =
      model_config_->GetBoolean("embedding_cache_listen_delete_disable", false);
  LOG(INFO) << "enable_embedding_feature_cache: " << FLAGS_enable_embedding_feature_cache;
  // 初始化 embedding 缓存
  if (FLAGS_enable_embedding_feature_cache) {
    item_embedding_feature_cache_.reset(
        new EmbeddingItemCache(FLAGS_item_embedding_feature_cache_capacity,
                               FLAGS_item_embedding_feature_cache_expire_time));
  }
}

void FeatureServer::InitPredictService() {
  predict_service_.reset(
      new SampleProvider(FLAGS_server_name, item_type_,
                         ThreadTuning::Instance().GetGrpcThreadNum() + 10, nullptr));
  LOG(INFO) << "Setskip item: " << NeedDumpAdLogForCompare();
  if (model_config_->GetBoolean("fill_ad_user_info", false)) {
    predict_service_->SetFillAddUserInfo(true);
    fill_ad_user_info_ = true;
    LOG(INFO) << "set for fill ad user info.";
  }
  if (NeedDumpAdLogForCompare()) {
    predict_service_->SetSkipItem(true);
  }
  if (!use_feature_extract_handler_) {
    auto item_func = std::bind(&FeatureServer::GetItem, this, std::placeholders::_1,
                               std::placeholders::_2, std::placeholders::_3);  // NOLINT
    predict_service_->SetCustomItemFunc(item_func);
  }
}

void FeatureServer::InitPicasso() {
  // 1. init picasso realtime
  if (model_config_->GetBoolean("need_picasso_realtime", false)) {
    CachedUserInfoClient::getInstance()->SetRealTimeActionFromPicassoWithCache(true);
  }
  if (is_i18n_ && model_config_->GetBoolean("enable_device_id2picasso", false)) {
    i18n_user_manager_.SetEnableDeviceId2Picasso(true);
  }
  // 2. 调用下游：kess picasso （用于 取 user info) 的注册
  ks::ad_picasso::sdk::PicassoOption opt;
  opt.client_tag = "ad_predict";
  opt.self_kess_server_name = ks::ad_nn::GetKcsKessName();
  opt.use_hash = true;
  if (ks::ad_nn::PredictServiceKconfUtil::adPicasspChangeGateWay()) {
    opt.picasso_kess_service = "grpc_adPicassoGatewayServiceUserInfo";
    LOG(INFO) << "Picasso gateway change to " << opt.picasso_kess_service;
  }
  if (!ks::ad_picasso::sdk::PicassoClient::GetInstance()->Init(opt)) {
    ks::ad_nn::ServiceStatus::Get()
        .GetModule(MODULE_DEFAULT)
        ->LogFail("init picasso client fail");
    LOG(FATAL) << "init picasso client fail";
  }
  // 3. 用于 bs 的 daily 获取
  auto use_picasso_batch_sdk = model_config_->GetBoolean("use_picasso_batch_sdk", false);
  if (use_picasso_batch_sdk) {
    ks::ad_picasso_batch::sdk::PicassoOption opt2;
    opt2.client_tag = "ad_predict";
    opt2.caller_kess_service = "ad_picasso_batch_stress";
    opt2.timeout_ms = 20;
    if (!ks::ad_picasso_batch::sdk::PicassoClient::GetInstance()->Init(opt2)) {
      ks::ad_nn::ServiceStatus::Get()
          .GetModule(MODULE_DEFAULT)
          ->LogFail("init picasso client new fail");
      LOG(FATAL) << "init picasso client new fail";
    }
  }
}

void FeatureServer::InitFeatureExtract() {
  // 2. init feature_file_info
  auto feature_path = model_config_->GetString("feature_path");
  if (feature_path.empty()) {
    ks::ad_nn::ServiceStatus::Get()
        .GetModule(MODULE_DEFAULT)
        ->LogFail("feature path is empty");
    LOG(FATAL) << "feature path is empty";
  }
  FLAGS_use_exist_fix_version = model_config_->GetBoolean("use_exist_fix_version", false);
  FLAGS_use_bs_reco_userinfo = model_config_->GetBoolean("use_bs_reco_userinfo", false);
  if (!use_feature_extract_handler_) {
    feature_file_info_.enable_field_capacity_check =
        !(model_config_->GetBoolean("mio_model", false));
    feature_file_info_.enable_feature_info_check =
        !(model_config_->GetBoolean("mio_model", false));
    feature_file_info_.need_check_feature_slot =
        model_config_->GetBoolean("need_check_feature_slot", true);
    if (0 != FeatureFileInfo::LoadFeatureFile(feature_path, use_bs_fast_feature_,
                                              &feature_file_info_, true, false)) {
      std::ostringstream oss;
      oss << "load feature file failed for: " << feature_path;
      ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
      LOG(FATAL) << "load feature file failed for: " << feature_path;
    }
#ifdef BUILD_WITH_DRAGON
    if (enable_dragon_pipeline_) {
      feature_dragon_->UpdateDenseConfig(feature_file_info_);
    }
#endif
    if (enable_dragon_pipeline_) {
      // 需要 item 都解析出来
      FLAGS_enable_item_pb_parse_opt = false;
      LOG(INFO) << "item_pb_parse_opt is disabled because dragon pipeline is found";
    }
  }
  // 3. init feature extractor
  if (use_feature_extract_handler_) {
    if (0 != FeatureFileInfo::LoadJsonConfigFile(feature_path, &feature_file_info_)) {
      std::ostringstream oss;
      oss << "load json config file failed for: " << feature_path;
      ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
      LOG(FATAL) << "load json config file failed for: " << feature_path;
    }
    dag_bs_extractor_.reset(new DagBSFeatureExtractor(feature_file_info_));
    feature_stat_.UpdateName2DenseFlag(dag_bs_extractor_->GetFeatureMetas());
  } else {
    feature_extractor_.reset(new ConcurrentFeatureExtractor(feature_file_info_));
    feature_extractor_->SetAddDefaultSignForDragon(
        model_config_->GetBoolean("add_default_sign_for_dragon_feature", false));
    feature_extractor_->SetFeatureCache(item_feature_cache_.get());
    if (item_embedding_feature_cache_) {
      feature_extractor_->SetItemEmbeddingFeatureCache(
          item_embedding_feature_cache_.get());
    }
    feature_extractor_->SetFeatureStat(&feature_stat_);
    feature_stat_.UpdateName2DenseFlag(feature_file_info_.features);
  }
  // init feature stat
  int stat_fea_value_mode =
      SetStatFeatureMode(model_config_->GetInt("stat_instance_num", 3));
  feature_stat_.SetStatFeaValueMode(cmd_name_, stat_fea_value_mode);
  feature_stat_.SetStatFeaWithCustomStrategy(*model_config_);
  feature_stat_.SetStatFeaValueFreq(
      model_config_->GetInt("stat_fea_value_frequence", 100));
  feature_stat_.SetStatFeaValueMaxItemCount(
      model_config_->GetInt("stat_fea_value_max_item_count", 10));
}

bool FeatureServer::InitOfflineTopk(std::shared_ptr<base::Json> model_config) {
  offline_topk_service_.reset(new OfflineTopkService(*model_config));
  auto req_func = [&](const UniversePredictRequest& req,
                      const EngineOfflineTargetInfo& msg) {
    ks::kess::rpc::grpc::ResponseWriter<UniversePredictResponse> res_writer(nullptr);
    auto req_context = std::make_shared<FeatureServerContext>(&req, res_writer, true);
    PredictWithContext(req_context);
    if (!req_context->Wait()) {
      falcon::Inc("predict_server.offline_topk_infer_fail", 1);
      return false;
    }
    return offline_topk_service_->ProcessResponse(*req_context->ad_log,
                                                  *req_context->response, msg);
  };
  offline_topk_service_->SetCmd(GetKcsModelCmd());
  offline_topk_service_->SetItemType(item_type_);
  offline_topk_service_->SetReqFunc(req_func);
  if (!offline_topk_service_->Start()) {
    LOG(FATAL) << "offline topk service init failed";
  }
  return true;
}

void FeatureServer::InitAttrId2Idx() {
  if (!use_i18n_opt_ && !use_bs_fast_feature_) {
    LOG(INFO) << "not need attrid2idx, return directly.";
    return;
  }

  if (use_bs_fast_feature_ && use_i18n_opt_) {
    LOG(FATAL) << "i18n should not open use_bs_fast_feature";
  }
  bs_sample_attr_id2idx_ = std::make_shared<AttrId2Idx>();
  bs_user_attr_id2idx_ = std::make_shared<AttrId2Idx>();
  bs_all_prune_attr_id2idx_ = std::make_shared<AttrId2Idx>();
  std::set<int32_t> attr_ids;
  if (use_i18n_opt_) {
    attr_ids = dag_bs_extractor_->GetAllAttrSet();
    auto json = ks::ad_nn::PredictServiceKconfUtil::featureConfigMap()->data;
    if (json && ks::ad_algorithm::BSInfoStatic::Instance().InitFromJson(*json)) {
      LOG(INFO) << "Success to init BSInfoStatic.";
    } else {
      LOG(FATAL) << "error to init BSInfoStatic.";
      return;
    }
  } else if (use_bs_fast_feature_) {
    auto fill_feature_file_info = [&](FeatureFileInfo& feature_file_info) {
      for (const auto& feature_config : feature_file_info.features) {
        if (feature_config.is_dragon) {
          LOG_EVERY_N(INFO, 10) << "dragon feature " << feature_config.class_name
                                << " no need prune, skip.";
          continue;
        }
        if (!feature_config.feature_in_sub_graph) {
          LOG(INFO) << "feature " << feature_config.class_name
                    << " is not in sub graph, no need prune.";
          continue;
        }
        auto bs_fea = reinterpret_cast<const ks::ad_algorithm::BSFastFeature*>(
            feature_config.feature);
        if (bs_fea != nullptr) {
          for (const auto& attr : bs_fea->AttrMetas()) {
            if (attr.id >= 0) attr_ids.insert(attr.id);
          }
        }
      }
    };

    if (shared_feature_server_) {
      for (auto& it : shared_feature_file_infos_) {
        fill_feature_file_info(it.second);
      }
    } else {
      fill_feature_file_info(feature_file_info_);
    }

    // for preprocessors
    if (feature_processor_) {
      feature_processor_->FillAllAttrMetas(&attr_ids);
    }

    // for dragon processors
    if (enable_dragon_pipeline_ &&
        !feature_dragon_->GetBsUsedAttr(model_config_, &attr_ids)) {
      LOG(FATAL) << "get dragon attr id failed";
    }
  }
  const auto& sample_attr_ids =
      ks::ad_algorithm::BSInfoStatic::Instance().sample_attr_ids();
  const auto& user_attr_ids = ks::ad_algorithm::BSInfoStatic::Instance().user_attr_ids();
  LOG(INFO) << "Sample attr id count: " << sample_attr_ids.size()
            << " user attr id count: " << user_attr_ids.size();
  for (auto id : attr_ids) {
    if (sample_attr_ids.count(id) > 0) {
      bs_pruned_sample_attr_ids_.insert(id);
    } else if (user_attr_ids.count(id) > 0) {
      bs_pruned_user_attr_ids_.insert(id);
    } else {
      LOG(FATAL) << "Unknown type attr id: " << id;
    }
  }
  // 强制加入一些基础字段
  auto& stat_data_info = feature_stat_.GetCustomDataInfo();
  if (stat_data_info.enum_id > 0) {
    if (user_attr_ids.count(stat_data_info.enum_id) > 0) {
      bs_pruned_user_attr_ids_.insert(stat_data_info.enum_id);
    } else {
      LOG(FATAL) << "now only support custom user attr for feature stat: "
                 << stat_data_info.enum_id;
    }
  }
  if (use_i18n_opt_) {
    bs_pruned_user_attr_ids_.insert(ks::ad_nn::i18n::BSFieldEnumI18N::adlog_user_info_id);
    bs_pruned_user_attr_ids_.insert(ks::ad_nn::i18n::BSFieldEnumI18N::adlog_time);
    bs_pruned_sample_attr_ids_.insert(ks::ad_nn::i18n::BSFieldEnumI18N::adlog_item_id);
  } else {
    bs_pruned_user_attr_ids_.insert(BSFieldEnum::adlog_user_info_id);
    bs_pruned_user_attr_ids_.insert(BSFieldEnum::adlog_time);
    bs_pruned_user_attr_ids_.insert(BSFieldEnum::adlog_user_info_exists);
    bs_pruned_user_attr_ids_.insert(BSFieldEnum::adlog_is_train);
    bs_pruned_user_attr_ids_.insert(BSFieldEnum::adlog_context_info_common_attr_key_438);
    bs_pruned_sample_attr_ids_.insert(BSFieldEnum::adlog_item_id);
    bs_pruned_sample_attr_ids_.insert(BSFieldEnum::adlog_item_build_time);
  }
  int32_t max_sample_attr_id = *(sample_attr_ids.rbegin());
  if (!bs_sample_attr_id2idx_->Init(max_sample_attr_id + 1)) {
    LOG(FATAL) << "bs_pruned_sample_attr_id2idx init failed: " << max_sample_attr_id + 1;
  }
  if (!bs_sample_attr_id2idx_->Insert(bs_pruned_sample_attr_ids_, SrcV::SRC_ITEM)) {
    LOG(FATAL) << "bs_pruned_sample_attr_id2idx insert failed";
  }
  int32_t max_user_attr_id = *(user_attr_ids.rbegin());
  if (!bs_user_attr_id2idx_->Init(max_user_attr_id + 1)) {
    LOG(FATAL) << "bs_pruned_user_attr_id2idx init failed: " << max_user_attr_id + 1;
  }
  if (!bs_user_attr_id2idx_->Insert(bs_pruned_user_attr_ids_, SrcV::SRC_USER)) {
    LOG(FATAL) << "bs_pruned_user_attr_id2idx insert failed: ";
  }
  int max_attr_id = std::max(max_sample_attr_id, max_user_attr_id);
  if (!bs_all_prune_attr_id2idx_->Init(max_attr_id + 1)) {
    LOG(FATAL) << "bs_all_prune_attr_id2idx_ init failed: " << max_attr_id + 1;
  }
  if (!bs_all_prune_attr_id2idx_->Insert(bs_pruned_user_attr_ids_, SrcV::SRC_USER,
                                         true)) {
    LOG(FATAL) << "bs_all_prune_attr_id2idx_ insert failed: ";
  }
  if (!bs_all_prune_attr_id2idx_->Insert(bs_pruned_sample_attr_ids_, SrcV::SRC_ITEM,
                                         true)) {
    LOG(FATAL) << "bs_all_prune_attr_id2idx_ insert failed";
  }
  LOG(INFO) << "bs_pruned_sample_attr_ids size: " << bs_pruned_sample_attr_ids_.size()
            << " bs_pruned_user_attr_ids size: " << bs_pruned_user_attr_ids_.size()
            << " bs_all_prune_attr_id2idx size: "
            << (bs_pruned_sample_attr_ids_.size() + bs_pruned_user_attr_ids_.size());
  LOG(INFO) << "bs_sample_attr_id2idx_ vaild count: "
            << bs_sample_attr_id2idx_->valid_index_cnt
            << " bs_user_attr_id2idx_ vaild count: "
            << bs_user_attr_id2idx_->valid_index_cnt
            << " bs_all_prune_attr_id2idx_ vaild count: "
            << bs_all_prune_attr_id2idx_->valid_index_cnt;

  ks::ad_nn::data_converter::DataConverter::InitConvertRecoMap();
  auto hard_code_set = ks::ad_nn::data_converter::DataConverter::GetRecoHardCodeSet();
  auto& reco_user_attr_ids =
      ks::ad_algorithm::BSInfoStatic::Instance().reco_user_attr_ids();
  for (auto& attr_id : bs_pruned_user_attr_ids_) {
    if (reco_user_attr_ids.find(attr_id) != reco_user_attr_ids.end() &&
        hard_code_set.find(attr_id) == hard_code_set.end()) {
      falcon::Inc("bs_hardcode_reco_attr_miss");
      LOG(FATAL) << "hardcode convert bs reco userinfo miss attr: " << attr_id;
    }
  }

  if (use_opt_item_server_ && cmd_name_ != "") {
    ks::ad_nn::RegisterBsAttr(bs_pruned_sample_attr_ids_, cmd_name_,
                              "ad.Item.adBsCmdItemAttrMap");
  }
  if (use_opt_user_info_ && cmd_name_ != "") {
    ks::ad_nn::RegisterBsAttr(bs_pruned_user_attr_ids_, cmd_name_,
                              "ad.router.adBsCmdUserAttrMap");
  }
  if (skip_router_req_) {
    FLAGS_read_kconf = true;
    std::map<std::string, int32_t> input_path2id_map;
    const auto& feature_config_map =
        ks::ad_algorithm::BSInfoStatic::Instance().GetFeatureMaps();
    for (auto& feature_map_iter : feature_config_map) {
      if (bs_pruned_sample_attr_ids_.find(feature_map_iter.second) !=
              bs_pruned_sample_attr_ids_.end() ||
          bs_pruned_user_attr_ids_.find(feature_map_iter.second) !=
              bs_pruned_user_attr_ids_.end()) {
        input_path2id_map.insert(
            std::make_pair(feature_map_iter.first, feature_map_iter.second));
      }
    }
    ks::ad_nn::data_converter::DataConverter::GetInstance()->Init(input_path2id_map,
                                                                  "sub_context", true);
  }
  return;
}

bool FeatureServer::LoadComponentItemConfigOfAllType() {
  const bool res = ItemConfigUtil::LoadComponentItemConfig(model_config_.get(),
                                                           &component_item_config_);
  if (!res || component_item_config_.empty()) {
    LOG(FATAL)
        << "[component] failed to load component config, please check kconf, res is "
        << res << ", config map size is " << component_item_config_.size();
  }
  for (const auto& type_conf : component_item_config_) {
    bool has_btq_conf = type_conf.second.has_btq_config();
    bool has_item_server_conf = type_conf.second.has_item_server_config();
    if (!has_btq_conf || !has_item_server_conf) {
      LOG(FATAL) << "type " << type_conf.first << " config has btq: " << has_btq_conf
                 << ", has item server: " << has_item_server_conf
                 << ", please check config";
      return false;
    }
  }
  return res;
}

bool FeatureServer::LoadOnlyFullItemServerConfigFromKconf() {
  auto full_config = &component_item_config_[ItemInfoType::FULL];
  const bool res = ItemConfigUtil::LoadItemServerConfig(model_config_.get(), full_config);
  LOG(INFO) << "load full component res: " << res
            << ", detail: " << full_config->DebugString();
  return res;
}

void FeatureServer::InitCompressionConfig() {
  int compress_type =
      model_config_->GetInt("infer_request_compress_type", (int)CompressType::SNAPPY);
  if (!CompressionUtil::IsCompressTypeValid(compress_type)) {
    LOG(FATAL) << "invalid comrpess type";
  }
  infer_request_compress_type_ = static_cast<CompressType>(compress_type);
  infer_request_compress_level_ =
      model_config_->GetInt("infer_request_compress_level", -20);

  const int kMaxCompressLevel = 50;
  const int kMinCompressLevel = -50;
  if (infer_request_compress_level_ > kMaxCompressLevel ||
      infer_request_compress_level_ < kMinCompressLevel) {
    LOG(FATAL) << "invalid compress level: " << infer_request_compress_level_;
  }
  LOG(INFO) << "infer request compress type is " << (int)infer_request_compress_type_
            << ", compress level is " << infer_request_compress_level_;
}

void FeatureServer::InitItem() {
  FeatureReader *reader = nullptr, *second_reader = nullptr;
  bool enable_skip_parse_item =
      model_config_->GetBoolean("enable_skip_parse_item", false);
  bool store_load_only_once = false;
  ReaderOption reader_option;
  StoreOption store_option;
  if ((use_feature_extract_handler_ || use_bs_fast_feature_) && copy_req_cmd_ == "") {
    reader_option.read_flatten = true;
    reader_option.validate_str_callback = [](const std::string& str) {
      return flatten::Verify<ks::cofea_fbs::BatchedSamples>(str.data(), str.size());
    };
    if (opt_bs_item_ || prune_bs_attr_) {
      store_option.type = StorageType::ST_BS_ITEM;
    } else {
      store_option.type = StorageType::ST_STRING;
    }
  } else {
    store_option.type = StorageType::ST_PROTOBUF;
  }
  GetFeatureStoreOptions(&reader_option, &store_option);
  std::string err_msg;
  if (!VerifyItemOptions(reader_option, store_option, &err_msg)) {
    LOG(FATAL) << "Item Options Invalid, ERR_MSG: " << err_msg;
  }
  if (copy_req_cmd_ != "") {
    reader_option.transfer_bs_callback =
        std::bind(&FeatureServer::TransferBSItem, this, std::placeholders::_1,
                  std::placeholders::_2);
  }

  uint64_t item_store_capacity = store_option.capacity;
  if (use_component_item_) {
    LoadComponentItemConfigOfAllType();
    if (use_component_item_debug_ &&
        !ks::ad_nn::data_online_checker::DataOnlineChecker::GetInstance()->Init(
            model_config_)) {
      LOG(FATAL) << "use component item debug, but init failed";
    }
    use_strict_item_info_ = model_config_->GetBoolean("use_strict_item_info", false);
    use_strict_vaild_info_ = model_config_->GetBoolean("use_strict_vaild_info", false);
    if (!InitBSComponentItemConfig(model_config_, &store_option, &reader_option,
                                   &reader)) {
      LOG(FATAL) << "use component but fail init config.";
    }
  } else {
    LoadOnlyFullItemServerConfigFromKconf();
    reader = feature_manager_.RegisterReader("item", reader_option);
    auto creative_store =
        feature_manager_.RegisterFeatureStore<RawFeatureStore>("item", store_option);
    creative_store->Subscribe(reader);
    item_component_server_map_[ItemInfoType::FULL].item_store = creative_store;
  }

  if (store_option.type == StorageType::ST_BS_ITEM) {
    SetBSItemOptConfig();
  }

  // second reader
  reader_option.full_source_name = model_config_->GetString("second_btq_full_topic", "");
  reader_option.incr_source_name = model_config_->GetString("second_btq_incr_topic", "");
  if (!reader_option.full_source_name.empty() ||
      !reader_option.incr_source_name.empty()) {
    err_msg.clear();
    if (!VerifyItemOptions(reader_option, store_option, &err_msg)) {
      LOG(FATAL) << "Item Options Invalid, ERR_MSG: " << err_msg;
    }
    second_reader = feature_manager_.RegisterReader("second_item", reader_option);
    auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
    if (KS_UNLIKELY(info_ptr == nullptr)) {
      LOG(FATAL) << "get component config failed.";
      return;
    }
    info_ptr->item_store->Subscribe(second_reader);
  }

  bool enable_pb_cache =
      model_config_->GetBoolean("enable_pb_cache", FLAGS_enable_pb_cache);
  if (enable_pb_cache) {
    store_option.capacity =
        model_config_->GetInt("pb_cache_lru_capacity", FLAGS_pb_cache_capacity);
    if ((use_feature_extract_handler_ || use_bs_fast_feature_) && copy_req_cmd_ == "") {
      store_option.type = StorageType::ST_COMPRESSED_STRING;
    } else {
      store_option.type = StorageType::ST_SERIALIZED_PROTOBUF;
    }

    serialized_item_store_ =
        feature_manager_.RegisterFeatureStore<RawFeatureStore>("ser_item", store_option);
    serialized_item_store_->Subscribe(reader);
    if (second_reader) {
      serialized_item_store_->Subscribe(second_reader);
    }
    auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
    if (KS_UNLIKELY(info_ptr == nullptr)) {
      LOG(FATAL) << "get component config failed.";
      return;
    }
    info_ptr->item_store->SetLoadOnlyOnce(true);
    store_load_only_once = true;
  }

  use_dist_item_service_ = model_config_->GetBoolean("use_dist_item_service", false);
  LOG(INFO) << "use dist item server: " << use_dist_item_service_;
  if (use_dist_item_service_) {
    if (!InitItemServerConfig(model_config_)) {
      LOG(FATAL) << "dist item service config init fail.";
    }
    store_load_only_once = true;
    if (NeedDumpAdLogForCompare()) {
      std::string compare_item_server_cmd =
          model_config_->GetString("compare_item_server_cmd", "");
      if (!compare_item_server_cmd.empty()) {
        auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
        if (KS_UNLIKELY(info_ptr == nullptr)) {
          LOG(FATAL) << "get component config failed.";
          return;
        }
        if (use_brpc_) {
          WaitServiceReady<IndexService>(
              &compare_item_server_cmd, &info_ptr->item_service_timeout,
              &info_ptr->item_server_shard_num, &info_ptr->item_server_shard_config,
              &info_ptr->item_server_clients, &info_ptr->brpc_item_server_clients,
              ks::ad_nn::GetItemServerKessName);
        } else {
          WaitServiceReady<IndexService>(
              &compare_item_server_cmd, &info_ptr->item_service_timeout,
              &info_ptr->item_server_shard_num, &info_ptr->item_server_shard_config,
              &info_ptr->item_server_clients, ks::ad_nn::GetItemServerKessName);
        }
      }
    }
  }

  if ((!use_feature_extract_handler_ || copy_req_cmd_ != "") && item_feature_cache_) {
    auto update_cb = [&](const std::vector<FeatureData>& items, Operation op) {
      if (op == Operation::DELETE && !use_bs_fast_feature_) {
        // 现在被 shard filter 的 item 也会调用，暂时忽略 Delete 消息
        return;
      }
      for (auto& item : items) {
        item_feature_cache_->Erase(item.item_id);
      }
      LOG_EVERY_N(INFO, 100000)
          << "clean " << items.size() << " updated items from item feature cache";
      falcon::Set("predict_server.item_feature_cache_total", item_feature_cache_->Size(),
                  falcon::kNonAdditiveGauge);
    };
    LOG(INFO) << "[component] main_type value is :" << main_type_;
    if (use_component_item_) {
      feature_manager_.GetReader(ItemInfoTypeStr(main_type_))
          ->AddSubscriber("item_feature_cache", update_cb);
    } else {
      reader->AddSubscriber("item_feature_cache", update_cb);
    }
    if (second_reader) second_reader->AddSubscriber("item_feature_cache", update_cb);
  }

  // embedding 缓存监听 Btq 消息删除过期数据
  if (item_embedding_feature_cache_ && !embedding_cache_listen_delete_disable_) {
    auto update_cb = [&](const std::vector<FeatureData>& items, Operation op) {
      // 无论什么消息，均表示 embedding 缓存过期
      for (auto& item : items) {
        item_embedding_feature_cache_->Erase(item.item_id);
      }
      LOG_EVERY_N(INFO, 100000) << "clean " << items.size()
                                << " updated items from item embedding feature cache";
      falcon::Set("predict_server.item_embedding_feature_cache_total",
                  item_embedding_feature_cache_->Size(), falcon::kNonAdditiveGauge);
    };
    reader->AddSubscriber("item_embedding_feature_cache", update_cb);
    if (second_reader)
      second_reader->AddSubscriber("item_embedding_feature_cache", update_cb);
  }

  // idt item
  auto enable_idt_item =
      model_config_->GetBoolean("enable_idt_item", FLAGS_enable_idt_item);
  if (use_component_item_) {
    enable_idt_item = false;
  }
  auto idt_wait_time = model_config_->GetInt("idt_wait_time", 300);
  auto idt_service_timeout_ms = model_config_->GetInt("idt_service_timeout_ms", 1000);
  ks::ad_nn::IdtDataSources::GetInstance().Put("item_data_source", &feature_manager_);
  bool idt_success = false;
  while (enable_idt_item) {
    auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
    if (KS_UNLIKELY(info_ptr == nullptr)) {
      LOG(FATAL) << "get component config failed.";
      return;
    }
    auto& info = *info_ptr;
    LOG(INFO) << "before idt , item_store stores " << info.item_store->Size() << " items";
    base::JsonObject idt_config;
    std::string idt_server_kess_name = ::ks::infra::KEnv::GetServiceName();
    std::string my_pod_name = ks::ad_nn::GetPodName();
    idt_config.set("idt_server_kess_name", idt_server_kess_name);
    idt_config.set("pod_name", my_pod_name);
    idt_config.set("wait_time", idt_wait_time);
    idt_config.set("idt_service_timeout_ms", idt_service_timeout_ms);
    LOG(INFO) << "enable idt item, idt_server_kess_name: " << idt_server_kess_name
              << ", my pod name: " << my_pod_name << ", idt_service_timeout_ms "
              << idt_service_timeout_ms << "ms"
              << ", wait_time: " << idt_wait_time << "s";
    idt_reader_.SetReadCallback(std::bind(&ks::ad_nn::IdtItemHelper::PutIdtDataCallback,
                                          info.item_store, reader->GetItemVersions(),
                                          std::placeholders::_1, std::placeholders::_2));
    // common idt
    bool use_common_idt_service =
        model_config_->GetBoolean("use_common_idt_service", false);
    base::JsonObject common_idt_config;
    std::string data_type = "item";
    if (!GetOneIdtHandlerConfig(model_config_.get(), data_type, &common_idt_config)) {
      break;
    }
    CommonIdtReader common_idt_reader;
    auto call_idt_service = [&]() {
      if (use_common_idt_service) {
        return common_idt_reader.Start(common_idt_config);
      } else {
        return idt_reader_.Start(idt_config);
      }
    };
    info.item_store->SetIdtStatus(true);
    if (!call_idt_service()) {
      ks::ad_nn::ServiceStatus::Get()
          .GetModule(MODULE_DEFAULT)
          ->Log(
              "idt reader start failed, couldn't get data from other server through idt "
              "service",
              WARN);
      LOG(WARNING) << "idt reader start failed, couldn't get data from other server "
                      "through idt service";
    } else {
      LOG(INFO) << "after idt , item_store stores " << info.item_store->Size()
                << " items";
      // item_store 无法通过 idt 达到 reader_to_serve 的状态
      // 故，如果 idt item 能拉取 90% 的物料数据时即认定 idt item 成功了
      size_t store_size = store_option.capacity_in_mem ? info.item_store->UsedMemory()
                                                       : info.item_store->Size();
      if (store_size > item_store_capacity * 0.9) {
        auto item_versions = reader->GetItemVersions();
        uint64_t idt_item_version = 0;
        for (auto iter : *item_versions) {
          if (iter.second > idt_item_version) {
            idt_item_version = iter.second;
          }
        }
        reader->SetIdtItemVersion(idt_item_version);
        idt_success = true;
        LOG(INFO) << "idt item success, item_store stores " << info.item_store->Size()
                  << " items"
                  << ", item_store_capacity " << item_store_capacity
                  << ", idt_item_version " << idt_item_version;
      }
      if (info.item_store->ReadyToServe()) {
        ks::ad_nn::ServiceStatus::Get()
            .GetModule(MODULE_DEFAULT)
            ->Log("idt item success, item store is ready to serve", INFO);
        LOG(INFO) << "idt item success, item store is ready to serve";
      }
    }
    if (idt_success) {
      falcon::Set(("common_idt.success_" + data_type).c_str(), 1,
                  falcon::kNonAdditiveGauge);
    } else {
      falcon::Set(("common_idt.failed_" + data_type).c_str(), 1,
                  falcon::kNonAdditiveGauge);
    }
    break;
  }

  if (use_component_item_) {
    if (item_component_server_map_.size() == 0) LOG(FATAL) << "no component register.";
    for (auto& item_component : item_component_server_map_) {
      if (item_component.second.item_store != nullptr) {
        item_component.second.item_store->SetIdtStatus(false);
      } else {
        LOG(FATAL) << item_component.first << " must has store but nullptr.";
      }
    }
  } else {
    auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
    if (KS_UNLIKELY(info_ptr == nullptr)) {
      LOG(FATAL) << "get component config failed.";
      return;
    }
    info_ptr->item_store->SetIdtStatus(false);
    if (!use_feature_extract_handler_) {
      if (shared_feature_server_) {
        // 共享特征服务特征提取模块
        shared_feature_extractor_->SetItemStore(info_ptr->item_store);
      } else {
        // 特征服务特征提取模块
        feature_extractor_->SetItemStore(info_ptr->item_store);
      }
    }
  }
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_ITEM)->LogStart();
  if (!feature_manager_.Start()) {
    ks::ad_nn::ServiceStatus::Get()
        .GetModule(MODULE_DEFAULT)
        ->LogFail("item reader start failed");
    LOG(FATAL) << "item reader start failed";
  }

  if (item_component_server_map_.size() > 0) {
    if (enable_skip_parse_item && store_load_only_once) {
      feature_manager_.WaitUntilStoresReady();
      reader->SkipParseItem(true);
      if (second_reader) second_reader->SkipParseItem(true);
    } else {
      feature_manager_.WaitUntilReady();
    }
    ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_ITEM)->LogFinish();
    LOG(INFO) << "wait feature manager init success";
  } else {
    ks::ad_nn::ServiceStatus::Get()
        .GetModule(MODULE_ITEM)
        ->Log("wait item loading...", INFO);
    ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_ITEM)->LogFinish();
  }
  // init item miss monitor
  std::string item_server_name = model_config_->GetString("item_server_cmd", "");
  monitor_name_ = item_server_name;
  ks::ad_nn::monitor::Monitor::Instance()->Register(monitor_name_, &monitor_handler_);
  LOG(INFO) << "wait item init complete success.";
}

void FeatureServer::WaitForTransferServerReady() {
  std::string transfer_service_name = "grpc_adLogConvertRestService";
  std::ostringstream oss;
  oss << "waiting for transfer service " << transfer_service_name << " to be ready";
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->Log(oss.str(), INFO);
  do {
    transfer_client_ = ks::ad_base::AdKessClient::ClientOfKey<AdLogConverterRestService>(
                           transfer_service_name)
                           .second;
    if (transfer_client_ != nullptr) {
      break;
    }
    LOG_EVERY_N(WARNING, 10) << "waiting for transfer service " << transfer_service_name
                             << " to be ready";
    std::this_thread::sleep_for(std::chrono::seconds(1));
  } while (true);
  LOG(INFO) << "register transfer server " << transfer_service_name << " succeeds";
  oss.str("");
  oss.clear();
  oss << "register transfer server " << transfer_service_name << " succeeds";
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->Log(oss.str(), INFO);
}

void FeatureServer::WaitForInferServerReady() {
  infer_service_name_ = GetInferServerKessName("");
  bool kess_test = ks::DynamicJsonConfig::GetConfig()->GetBoolean("kess_test", false);
  use_raw_kess_client_ = model_config_->GetBoolean("use_raw_kess_client", false);
  raw_kess_client_event_loop_num_ = model_config_->GetInt(
      "raw_kess_client_event_loop_num", raw_kess_client_event_loop_num_);
  LOG(INFO) << "use_raw_kess_client: " << use_raw_kess_client_
            << " raw_kess_client_event_loop_num: " << raw_kess_client_event_loop_num_;

  std::ostringstream oss;
  oss << "waiting for infer service " << infer_service_name_ << " to be ready";
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->Log(oss.str(), INFO);
  do {
    if (use_raw_kess_client_) {
      raw_kess_client_ = ks::kess::rpc::grpc::GrpcClientBuilder(infer_service_name_)
                             .SetAsyncThreadNum(raw_kess_client_event_loop_num_)
                             .Build();
    } else {
      client_ = ks::ad_base::AdKessClient::ClientOfKey<InferService>(infer_service_name_)
                    .second;
    }
    if (client_ != nullptr || raw_kess_client_ != nullptr) {
      break;
    }
    LOG_EVERY_N(WARNING, 10) << "waiting for infer service " << infer_service_name_
                             << " to be ready";
    std::this_thread::sleep_for(std::chrono::seconds(1));
  } while (true);

  oss.str("");
  oss.clear();
  oss << "register infer server " << infer_service_name_ << " succeeds";
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->Log(oss.str(), INFO);

  LOG(INFO) << "register infer server " << infer_service_name_ << " succeeds";
}

void FeatureServer::WaitForCopyReqServerReady() {
  std::ostringstream oss;
  oss << "waiting for copy service " << copy_req_kess_name_ << " to be ready";
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->Log(oss.str(), INFO);
  do {
    copy_req_client_ =
        ks::ad_base::AdKessClient::ClientOfKey<
            kuaishou::ad::algorithm::kess::UniversePredictService>(copy_req_kess_name_)
            .second;
    if (copy_req_client_ != nullptr) {
      break;
    }
    LOG_EVERY_N(WARNING, 10) << "waiting for copy service " << copy_req_kess_name_
                             << " to be ready";
    std::this_thread::sleep_for(std::chrono::seconds(1));
  } while (true);

  oss.str("");
  oss.clear();
  oss << "register copy server " << copy_req_kess_name_ << " succeeds";
  ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->Log(oss.str(), INFO);

  LOG(INFO) << "register copy server " << copy_req_kess_name_ << " succeeds";
}

kuaishou::ad::algorithm::PredictStatus FeatureServer::CheckLegal(
    const UniversePredictRequest* request) {
  static const std::string model_cmd = GetKcsModelCmd();
  if (request->llsid() == 0) {
    LOG(INFO) << "checkLegal failed; empty llsid: " << request->llsid();
    return kuaishou::ad::algorithm::LLSID_EMPTY;
  }
  if (request->cmd_size() != 1) {
    // TODO(yangjialin) 多模型共享支持
    LOG_EVERY_N(ERROR, 10000) << "invalid request: only 1 cmd is supporte, req cmd count "
                              << request->cmd_size();
    return kuaishou::ad::algorithm::INTERNAL_ERROR;
  }

  // 混排模型不经过 router，cmd 字段实际填的是 kess name
  // 自动调参模型，压测时，cmd 字段实际是 base cmd
  if (!skip_cmd_check_ && request->cmd(0) != model_cmd && request->cmd(0) != aa_alias_) {
    LOG_EVERY_N(ERROR, 10000)
        << "cmd in request is not equal to the model cmd. cmd in request: "
        << request->cmd(0) << ", the model cmd: " << model_cmd;
    return kuaishou::ad::algorithm::INTERNAL_ERROR;
  }

  if (!need_fake_user_ && !predict_service_->IsUnloginUser(*request) &&
      request->user_id() <= 0) {
    LOG_EVERY_N(ERROR, 10000) << "invalid user_id " << request->user_id();
    return kuaishou::ad::algorithm::INTERNAL_ERROR;
  }
  return kuaishou::ad::algorithm::STATUS_OK;
}

void FeatureServer::FillPredictResultForFakedItem(
    const UniversePredictRequest* request,
    const std::unordered_set<int>& faked_item_indices, size_t predict_value_count,
    UniversePredictResponse* response) {
  if (faked_item_indices.empty() || request == nullptr || response == nullptr ||
      predict_value_count < 1) {
    return;
  }
  ::google::protobuf::RepeatedPtrField<::kuaishou::ad::algorithm::PredictResult>
      new_predict_result;
  auto old_predict_result = response->mutable_predict_result();
  uint32_t offset = 0;

  for (uint32_t item_idx = 0; item_idx < request->item_id_size(); ++item_idx) {
    auto fake = (faked_item_indices.find(item_idx) != faked_item_indices.end());
    auto predict_result = new_predict_result.Add();
    if (fake) {
      predict_result->set_item_id(request->item_id(item_idx));
      for (int i = 0; i < predict_value_count; ++i) {
        predict_result->add_value(0);
      }
    } else {
      predict_result->Swap(old_predict_result->Mutable(offset++));
    }
  }
  old_predict_result->Swap(&new_predict_result);
}

bool FeatureServer::ReturnDefaultResponse(
    std::shared_ptr<FeatureServerContext> req_context) {
  std::unordered_set<int> faked_item_indices;
  const auto request = req_context->request;
  auto ad_log = req_context->ad_log;

  faked_item_indices.reserve(request->item_id_size());
  for (int i = 0; i < request->item_id_size(); ++i) {
    faked_item_indices.insert(i);
  }
  FillPredictResultForFakedItem(request, faked_item_indices, predict_value_num_,
                                req_context->response);

  req_context->finish(kuaishou::ad::algorithm::STATUS_OK);
  return true;
}

bool FeatureServer::GetInferResult(
    const InferFeatureList& features, Arena* arena,
    std::shared_ptr<FeatureServerContext> req_context) {  // NOLINT
  if (!need_req_infer_server_) {
    const auto request = req_context->request;
    if (request->is_debug_req()) {
      FeatureUtils::OutputInferFeatureList(feature_file_info_, features);
    }
    return ReturnDefaultResponse(req_context);
  }
  auto now = Clock::now();
  req_context->infer_start = now;
  auto request = req_context->request;
  auto item_cnt = item_cnt_;
  if (req_context->item_id_idxs != nullptr) {
    item_cnt = req_context->item_id_idxs->size();
  }
  if ((use_feature_extract_handler_ || use_bs_fast_feature_) &&
      (req_context->faked_flatten_item_indices.size() == request->item_id_size() &&
       !fake_bs_miss_item_)) {
    LOG(WARNING) << "all flatten items are fake and fake_bs_miss_item is false";
    InferResponseCb(req_context, req_context->response);
    return true;
  }
#ifdef INFER_SERVER
  /**
   * 把上下文中的 embedding 缓存相关信息传入到local infer server
   */
  if (local_infer_server_ != nullptr) {
    thread_local FeatureRequest fake_req;
    fake_req.set_is_debug_req(request->is_debug_req());
    fake_req.set_llsid(request->llsid());
    fake_req.set_user_id(request->user_id());

    grpc::Status ret;
    falcon::Inc("dnn_predict_server.predict_local", 1);
    falcon::Inc("dnn_predict_server.predict", 1);

    if (ks::ad_nn::IsOfflineTraffic()) {
      falcon::Inc("dnn_predict_server.predict_cmdkey_offline", 1);
    } else if (request->cmdkey_size() > 0) {
      if (request->cmdkey_end_pos_size() > 0) {
        fake_req.mutable_cmdkey_list()->CopyFrom(request->cmdkey());
        fake_req.mutable_cmdkey_end_pos()->CopyFrom(request->cmdkey_end_pos());
        for (const auto& cmdkey : request->cmdkey()) {
          falcon::Inc(
              base::StringPrintf("dnn_predict_server.predict_cmdkey_%s", cmdkey.c_str())
                  .c_str(),
              1);
        }
      } else {
        fake_req.set_cmdkey(request->cmdkey(0));
      }
    }
    if (request->need_predict_embedding_size() > 0) {
      if (opt_bs_item_ && !fake_bs_miss_item_) {
        for (int i = 0; i < request->need_predict_embedding_size(); i++) {
          if (!req_context->faked_flatten_item_indices.count(i)) {
            fake_req.add_need_predict_embedding(request->need_predict_embedding(i));
          }
        }
      } else {
        fake_req.mutable_need_predict_embedding()->CopyFrom(
            request->need_predict_embedding());
      }
    }
    const auto start = Clock::now();
    if (item_embedding_feature_cache_) {
      ret = local_infer_server_->Predict(fake_req, features, arena, req_context->response,
                                         req_context->embedding_map,
                                         req_context->combine_feature_start);
    } else {
      ret =
          local_infer_server_->Predict(fake_req, features, arena, req_context->response);
    }
    falcon::Stat("dnn_predict_server.predict_local_latency", GetMicroseconds(start));
    if (!ret.ok()) {
      falcon::Inc("feature_server.infer_fail_count", 1);
      LOG(WARNING) << "local infer server Predict failed, error_code=" << ret.error_code()
                   << " error_msg=" << ret.error_message();
    }
    InferResponseCb(req_context, req_context->response);
    return true;
  }
#endif

  auto feature_req = Arena::CreateMessage<FeatureRequest>(arena);
  feature_req->set_llsid(request->llsid());
  feature_req->set_cmd(request->cmd(req_context->cmd_index));
  feature_req->set_user_id(request->user_id());
  feature_req->set_is_debug_req(request->is_debug_req());
  feature_req->set_enable_sign_diff(enable_sign_diff_);
  if (request->cmdkey_size() > req_context->cmd_index) {
    feature_req->set_cmdkey(request->cmdkey(req_context->cmd_index));
  }
  if (!shared_feature_server_ && request->cmdkey_end_pos_size() > 0) {
    feature_req->mutable_cmdkey_list()->CopyFrom(request->cmdkey());
    feature_req->mutable_cmdkey_end_pos()->CopyFrom(request->cmdkey_end_pos());
  }
  // 共享特征服务直接使用 cmd item mapping 中的信息
  if (shared_feature_server_) {
    auto& cmd_item_mapping = request->cmd_item_mapping(req_context->cmd_index);
    if (cmd_item_mapping.cmdkey_end_pos_size() > 0) {
      feature_req->mutable_cmdkey_list()->CopyFrom(cmd_item_mapping.cmdkey());
      feature_req->mutable_cmdkey_end_pos()->CopyFrom(cmd_item_mapping.cmdkey_end_pos());
    }
  }
  if (request->need_predict_embedding_size() > 0) {
    if (opt_bs_item_ && !fake_bs_miss_item_) {
      for (int i = 0; i < request->need_predict_embedding_size(); i++) {
        if (!req_context->faked_flatten_item_indices.count(i)) {
          feature_req->add_need_predict_embedding(request->need_predict_embedding(i));
        }
      }
    } else {
      feature_req->mutable_need_predict_embedding()->CopyFrom(
          request->need_predict_embedding());
    }
  }
  auto serialized_features = feature_req->mutable_serialized_features();
  thread_local std::string features_str;
  features_str.clear();
  features.SerializeToString(&features_str);
  req_context->features_str = &features_str;
  req_context->feature_req = feature_req;
  if (req_context->fake_debug) {
    req_context->debug_feature_req = std::make_shared<std::string>();
    req_context->feature_req->SerializeToString(req_context->debug_feature_req.get());
  }
  const auto compress_start = Clock::now();
  CompressionUtil::Compress(infer_request_compress_type_, features_str,
                            serialized_features, infer_request_compress_level_);
  falcon::Stat("feature_server.infer_req_compress_time", GetMicroseconds(compress_start));
  feature_req->set_features_compress_type((int)infer_request_compress_type_);

  falcon::Stat("feature_server.infer_req_build_time", GetMicroseconds(now));

  std::string peer, host, port;
  auto infer_client = client_;
  std::string infer_service_name = infer_service_name_;
  if (!req_context->infer_service_name.empty() && req_context->infer_client != nullptr) {
    infer_service_name = req_context->infer_service_name;
    infer_client = req_context->infer_client;
  }

  std::unique_ptr<InferService::Stub> stub = nullptr;

  if (use_raw_kess_client_) {
    auto ch = raw_kess_client_->All()->SelectOne();
    peer = ch->GetPeer();
    stub = std::make_unique<InferService::Stub>(ch);
  } else {
    stub =
        std::make_unique<InferService::Stub>(std::move(infer_client->SelectOne(&peer)));
  }

  if (!GetHostAndPort(peer, &host, &port)) {
    LOG_EVERY_N(WARNING, 10000) << "Get host and port from peer failed, peer: " << peer;
  }

  if (host.empty()) {
    // dummy channel，找不到下游的情况
    req_context->endpoint = absl::Substitute("$0:nonode", infer_service_name);
  } else {
    req_context->endpoint = absl::Substitute("$0:$1", host, port);
  }
  falcon::Stat("feature_server.infer_req_size", serialized_features->size());

  // 这段代码作用是 给压测流量的上游独立成一个固定名字。
  bool is_kscale_stress = false;
  std::string real_service;
  if (req_context->grpc_context || req_context->brpc_cntl) {
    // 获取上游服务名字
    std::string upstream_name = "";
    if (req_context->grpc_context) {
      upstream_name = ks::kess::rpc::grpc::ServerContextHelper::GetCallerService(
          req_context->grpc_context);
    } else if (req_context->brpc_cntl) {
      upstream_name = ks::kess::rpc::brpc::ServerContextHelper::GetCallerService(
          req_context->brpc_cntl);
    }
    LOG_EVERY_N(INFO, 100000) << "upstream_name: " << upstream_name;
    // 这里是临时处理，只有压测流量没有上游名字同时没有 grpc_context。
    // 后续需要建设压测标识。
    if (upstream_name.empty()) {
      is_kscale_stress = true;
      real_service = ServiceMeta::GetService();
      ServiceMeta::SetService("kscale_stress");
    }
  }
  auto async_cb = [&, item_cnt, req_context, is_kscale_stress, infer_service_name](
                      const ::grpc::Status& status,
                      UniversePredictResponse* response) mutable {
    falcon::Stat("feature_server.infer_req_total_time",
                 GetMicroseconds(req_context->infer_start));
    req_context->AddCostStat("infer_req_cb", req_context->infer_start);
    if (!status.ok()) {
      LOG_EVERY_N(ERROR, 100) << "infer service predict failed, error_code "
                              << status.error_code() << " error_msg "
                              << status.error_message();
      req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR,
                          is_kscale_stress ? false : true);
    } else if (response->status() != kuaishou::ad::algorithm::STATUS_OK) {
      LOG_EVERY_N(WARNING, 100) << "infer service predict failed, status "
                                << PredictStatus_Name(response->status());
      falcon::Inc("feature_server.infer_fail_count", 1);
      req_context->finish(response->status());
    } else {
      InferResponseCb(req_context, response);
    }
    int fail_count = (status.error_code() != 0) ? 1 : 0;
    if (!IsAucTest()) {
      call_service(infer_service_name, 1, fail_count, item_cnt);
      call_endpoint(req_context->endpoint, 1, fail_count, item_cnt);
    }
  };

  auto timeout = FLAGS_infer_service_timeout;
  if (request->is_debug_req()) {
    timeout = FLAGS_debug_req_infer_service_timeout;
  } else if (shared_feature_server_) {
    // 共享特征服务直接用配置值
    timeout = infer_service_timeout_;
  } else {
    timeout = InferServiceTimeout();
  }
  falcon::Stat("feature_server.infer_get_client", GetMicroseconds(now));

  if (shared_feature_server_) {
    req_context->batch_waiter_.Add(
        stub->AsyncPredict(ks::ad_base::OptionsFromMilli(timeout), *feature_req,
                           req_context->response, event_loops_->SelectOne()),
        async_cb);
  } else if (req_context->use_brpc) {
    auto brpc_cli = ks::ad_base::AdKessClient::Instance()
                        .GetKessClient(infer_service_name, true)
                        .brpc;
    if (!brpc_cli) {
      LOG_EVERY_N(ERROR, 100) << "get brpc client fail.";
      return false;
    }
    auto brpc_stub =
        std::make_unique<::kuaishou::ad::algorithm::generic_rpc::InferService::Stub>(
            brpc_cli->All()->SelectOne().get());
    if (!brpc_stub) {
      LOG_EVERY_N(ERROR, 100) << "get brpc stub fail.";
      return false;
    }
    req_context->infer_brpc_cntl.reset(new brpc::Controller());
    req_context->infer_brpc_cntl->set_timeout_ms(timeout);
    std::function<void()> brpc_lambda = [req_context, async_cb]() mutable {
      static auto grpcFail = grpc::Status(grpc::StatusCode::INTERNAL, "TIMEOUT");
      if (req_context->infer_brpc_cntl->Failed()) {
        async_cb(grpcFail, req_context->response);
      } else {
        async_cb(grpc::Status::OK, req_context->response);
      }
      if (req_context->done != nullptr) req_context->done->Run();
    };
    if (req_context->done_guard != nullptr) req_context->done_guard->release();
    brpc_stub->Predict(req_context->infer_brpc_cntl.get(), feature_req,
                       req_context->response,
                       brpc::NewCallback(BrpcCallback, brpc_lambda));
  } else if (dragon_client_ != nullptr) {
    auto common_req = std::make_shared<ks::platform::CommonRecoRequest>();

    auto common_res = std::make_shared<ks::platform::CommonRecoResponse>();
    if (common_req == nullptr || common_res == nullptr) {
      LOG(ERROR) << "generate common_req and common_res failed.";
      return false;
    }
    if (!dragon_client_->ConstructRequest(feature_req, common_req.get(), false)) {
      LOG_EVERY_N(WARNING, 1000) << "Build dragon request failed";
      return false;
    }

    std::string peer;
    auto client =
        ks::ad_base::AdKessClient::ClientOfKey<ks::platform::kess::CommonRecoLeafService>(
            dragon_infer_kess_name_)
            .second;
    ks::platform::kess::CommonRecoLeafService::Stub stub(nullptr);
    stub = client->SelectOne(&peer);

    auto rpc_cb = [this, feature_req, common_res, common_req, req_context, async_cb,
                   peer](const ::grpc::Status& status,
                         ks::platform::CommonRecoResponse* res) mutable {
      falcon::Inc("feature_server.req_cb_count", 1);
      if (!dragon_client_->FillResponse(status, *res, feature_req, req_context->response,
                                        false)) {
        LOG_EVERY_N(WARNING, 1000) << "fill response failed";
        falcon::Inc("feature_server.req_cb_failed", 1);
      }
      async_cb(status, req_context->response);
    };
    stub->AsyncRecommend(ks::ad_base::OptionsFromMilli(timeout), *common_req,
                         common_res.get(), client->GetEventLoop())
        .Submit(rpc_cb);
  } else {
    if (use_raw_kess_client_) {
      stub->AsyncPredict(ks::ad_base::OptionsFromMilli(timeout), *feature_req,
                         req_context->response)
          .Submit(async_cb);

    } else {
      stub->AsyncPredict(ks::ad_base::OptionsFromMilli(timeout), *feature_req,
                         req_context->response, infer_client->GetEventLoop())
          .Submit(async_cb);
    }
  }

  InferServiceFlowCopier::Get()->CopyFlowForStressing(
      infer_service_name, feature_req, timeout, item_cnt, SINGLE_STRESS_KCONF);
  InferServiceFlowCopier::Get()->CopyFlowForStressing(
      infer_service_name, feature_req, timeout, item_cnt, TEST_STRESS_KCONF);

  if (is_kscale_stress) ServiceMeta::SetService(std::move(real_service));
  return true;
}

void FeatureServer::AsyncCopyPredict(
    FeatureServerContext* context, std::shared_ptr<UniversePredictRequest> copy_req,
    std::shared_ptr<UniversePredictResponse> copy_response,
    std::shared_ptr<std::promise<bool>> copy_promise) {
  auto source_req = context->request;
  copy_response->set_status(kuaishou::ad::algorithm::INTERNAL_ERROR);
  copy_req->CopyFrom(*source_req);
  predict_service_->SetRankCmd(copy_req->cmd(0), copy_req_cmd_,
                               copy_req->mutable_context());
  copy_req->mutable_cmd(0)->assign(copy_req_cmd_);
  copy_req->set_fill_feature_info(compare_feature_info_);
  if (compare_item_meta_info_) {
    copy_req->set_fill_item_meta_info(true);
  }
  if (fill_item_info_in_req_) {
    copy_req->set_fill_item_info_from_req(true);
    LOG_EVERY_N(INFO, 1000) << "[Debug] adlog item size: " << context->ad_log->item_size()
                            << " request item id size: " << source_req->item_id_size();
    for (int i = 0; i < context->ad_log->item_size(); ++i) {
      auto debug_info = copy_req->add_debug_raw_data();
      debug_info->set_pay_load_type(kuaishou::ad::debug::PLT_ITEM);
      context->ad_log->item(i).SerializeToString(debug_info->mutable_serialized_bytes());
      copy_req->add_is_faked_item(context->ad_log->IsFakedItem(i));
    }
  }

  std::string peer, host, port;
  auto stub = copy_req_client_->SelectOne(&peer);
  if (!GetHostAndPort(peer, &host, &port)) {
    LOG_EVERY_N(WARNING, 10000) << "Get host and port from peer failed, peer: " << peer;
    copy_promise->set_value(false);
    return;
  }
  if (host.empty()) {
    LOG(INFO) << "Host is empty for service: " << copy_req_kess_name_;
    copy_promise->set_value(false);
    return;
  }
  auto copy_req_start = Clock::now();
  auto async_cb = [this, copy_promise, copy_req, copy_response, copy_req_start](
                      const ::grpc::Status& status,
                      UniversePredictResponse* response) mutable {
    falcon::Stat("feature_server.copy_req_total_time", GetMicroseconds(copy_req_start));
    if (!status.ok()) {
      LOG_EVERY_N(WARNING, 100)
          << "copy service predict failed, error_code " << status.error_code()
          << " error_msg " << status.error_message();
      copy_promise->set_value(false);
    } else if (response->status() != kuaishou::ad::algorithm::STATUS_OK) {
      LOG_EVERY_N(WARNING, 100) << "copy service predict failed, status "
                                << PredictStatus_Name(response->status());
      copy_promise->set_value(false);
    } else {
      LOG_EVERY_N(INFO, 10000) << "copy service predict successfully";
      copy_promise->set_value(true);
    }
  };

  stub->AsyncPredict(ks::ad_base::OptionsFromMilli(copy_req_timeout_), *copy_req,
                     copy_response.get(), copy_req_client_->GetEventLoop())
      .Submit(async_cb);
  return;
}

void FeatureServer::SetBSItemOptConfig() {
  size_t bs_sample_attr_id_size = GetBSSampleAttrIdSize();
  auto bs_item_init_cb =
      [this, bs_sample_attr_id_size](std::shared_ptr<BSItemWrapper>* wrapper) -> bool {
    if (wrapper == nullptr && !(*wrapper)) {
      LOG_EVERY_N(WARNING, 1000000) << "wrapper is a nullptr";
      return false;
    }
    return (*wrapper)->Init(bs_sample_attr_id_size, bs_sample_attr_id2idx_,
                            prune_bs_attr_);
  };
  if (use_component_item_) {
    for (auto& item_component : item_component_server_map_) {
      if (item_component.second.item_store != nullptr) {
        item_component.second.item_store
            ->SetElementInitCallback<std::shared_ptr<BSItemWrapper>*>(bs_item_init_cb);
      } else {
        LOG(FATAL) << item_component.first << " must has store but nullptr.";
      }
    }
  } else {
    auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
    if (KS_UNLIKELY(info_ptr == nullptr)) {
      LOG(FATAL) << "get component config failed.";
      return;
    }
    info_ptr->item_store->SetElementInitCallback<std::shared_ptr<BSItemWrapper>*>(
        bs_item_init_cb);
  }
}

int FeatureServer::InferServiceTimeout() {
  if (infer_service_timeout_update_times_-- > 0) {
    return infer_service_timeout_;
  }

  // check every 10000
  infer_service_timeout_update_times_ = 10000;

  const auto& model_cmd = GetKcsModelCmd();
  int64_t timeout = -1;
  auto kconf_model_config =
      ks::ad_nn::PredictServiceKconfUtil::GetModelConfig(model_cmd, false);
  if (kconf_model_config != nullptr &&
      kconf_model_config->GetInt("infer_service_timeout", &timeout)) {
    infer_service_timeout_ = timeout;
    return infer_service_timeout_;
  }

  const char* extra_env = std::getenv("EXTRA_ENV");
  if (extra_env != nullptr && std::strcmp(extra_env, "AD_UNIVERSE") == 0) {
    timeout = ks::ad_nn::PredictServiceKconfUtil::adPredictUniverseInferServiceTimeout();
  } else {
    timeout = ks::ad_nn::PredictServiceKconfUtil::adPredictInferServiceTimeout();
  }
  infer_service_timeout_ = timeout > 0 ? timeout : FLAGS_infer_service_timeout;
  return infer_service_timeout_;
}

void FeatureServer::InferResponseCb(
    std::shared_ptr<FeatureServerContext>& req_context,  // NOLINT
    UniversePredictResponse* response) {
  auto request = req_context->request;
  auto& ad_log = req_context->ad_log;
  if (req_context->response != response) {
    LOG(ERROR) << "callback response " << response << " does not equal context res "
               << req_context->response;
  }

  if ((use_bs_fast_feature_ || use_feature_extract_handler_) &&
      response->predict_result_size() == 0) {
    LOG(ERROR) << "Empty predict result in response for llsid: " << request->llsid()
               << ", item size in request: " << request->item_id_size();
    falcon::Inc("feature_server.empty_infer_result", 1);
    req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
    return;
  }

  if (req_context->offline_mode) {
    // offline 请求跳过后续逻辑
    req_context->finish(kuaishou::ad::algorithm::STATUS_OK);
    return;
  }

  // 新特征流不下发 fake item，需要对 predict_result 进行填充
  if ((use_feature_extract_handler_ || use_bs_fast_feature_) && !fake_bs_miss_item_) {
    const auto& faked_flatten_item_indices = req_context->faked_flatten_item_indices;
    if (response->predict_result_size() + faked_flatten_item_indices.size() !=
        request->item_id_size()) {
      LOG(ERROR) << "Unexpecting error: size not equal"
                 << " response predict result size: " << response->predict_result_size()
                 << " faked_flatten_item_indices: " << faked_flatten_item_indices.size()
                 << " request item id size: " << request->item_id_size();
      req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
      falcon::Inc("feature_server.invalid_infer_result_size", 1);
      return;
    }
    auto old_predict_result = response->mutable_predict_result();
    size_t expecting_predict_value_count = predict_value_num_;
    if (old_predict_result->size() > 0) {
      expecting_predict_value_count = old_predict_result->Get(0).value_size();
    }
    FillPredictResultForFakedItem(request, faked_flatten_item_indices,
                                  expecting_predict_value_count, response);
  }

  // 共享特征服务不相等
  if (!shared_feature_server_ &&
      response->predict_result_size() != request->item_id_size()) {
    LOG(ERROR) << "res predict size " << response->predict_result_size()
               << " does not match item size " << request->item_id_size();
    falcon::Inc("feature_server.invalid_infer_result", 1);
    req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
    return;
  }
  if (shared_feature_server_ &&
      response->predict_result_size() != req_context->item_id_idxs->size()) {
    LOG(ERROR) << "shared feature res predict size " << response->predict_result_size()
               << " does not match item size" << req_context->item_id_idxs->size();
    falcon::Inc("feature_server.invalid_infer_result", 1);
    req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
    return;
  }

  // fake item 置零逻辑
  auto json = PredictServiceKconfUtil::adPredictFakedItemFillZero()->data;
  bool fill_faked_item_result_by_zero = false;
  if (json != nullptr) {
    fill_faked_item_result_by_zero = json->GetBoolean(actual_service_name_, false);
  }
  fill_faked_item_result_by_zero = model_config_->GetBoolean(
      "fill_faked_item_result_by_zero", fill_faked_item_result_by_zero);
  // 是否填充 miss 后 fake 的 item 的 item_id
  bool keep_faked_item_id = model_config_->GetBoolean("keep_faked_item_id", false);

  uint32_t fill_zero_count = 0;
  uint32_t log_step = 10;

  std::unordered_set<uint64_t> fake_item_ids;
  bool dump_current_log = false;
  AdJointLabeledLog* adlog = nullptr;
  DynamicBatchedSamples* sample = nullptr;
  if (dump_log_) {
    auto cur_count = counter_.fetch_add(1);
    if (cur_count % dump_log_freq_ == 0) {
      dump_current_log = true;
      adlog = &req_context->ad_log->Get();
      sample = req_context->batched_samples.get();
    }
  }

  if (request->fill_item_meta_info()) {
    FillCopyResInfos(req_context, response);
  }
  // 定义统计结果的方法
  auto stat_result = [&](uint32_t request_item_idx, uint32_t response_item_idx) {
    bool fake = false;
    if (use_feature_extract_handler_ || use_bs_fast_feature_) {
      const auto& faked_flatten_item_indices = req_context->faked_flatten_item_indices;
      fake = faked_flatten_item_indices.find(request_item_idx) !=
             faked_flatten_item_indices.end();
    } else {
      fake = ad_log->IsFakedItem(request_item_idx);
    }
    if (!fake) {
      auto& res = response->predict_result(response_item_idx);
      auto val = res.value(0);
      if (res.item_id() != request->item_id(request_item_idx)) {
        falcon::Inc("feature_server.predict_result_invalid_item_count", 1);
        LOG_EVERY_N(WARNING, 100)
            << "req llsid " << request->llsid() << " user_id " << request->user_id()
            << " req item_id " << request->item_id(request_item_idx) << " res item_id "
            << res.item_id() << " result " << val;
      }
      if (response_item_idx % log_step == 0 && res.value_size() > 0) {
        auto stat_cmd = request->cmd(req_context->cmd_index);
        if (stat_cmd.size() > 20) {
          stat_cmd.erase(0, stat_cmd.size() - 20);
        }
        if (shared_feature_server_) {
          falcon::Stat(("shared_feature_server.result_" + stat_cmd).c_str(),
                       static_cast<int64_t>(res.value(0) * 1000000));
          ks::infra::PerfUtil::IntervalLogStash(
              static_cast<int64_t>(res.value(0) * 1000000), "shared_feature_server",
              "predict_result", request->cmd(req_context->cmd_index));
        }
        // 增加一个多 output 的预估值打点
        for (int output_index = 0; output_index < output_op_names_.size();
             output_index++) {
          auto& output_name = output_op_names_[output_index];
          auto val = res.value(output_index);
          if (val < 1e-10) {
            falcon::Inc(("feature_server.predict_result_zero_" + output_name).c_str(), 1);
            LOG_EVERY_N(WARNING, 1000)
                << "req llsid " << request->llsid() << " user_id " << request->user_id()
                << " item_id " << request->item_id(request_item_idx) << " output name "
                << output_name << " result " << val;
          }
          falcon::Stat(("feature_server.result_" + output_name).c_str(),
                       static_cast<int64_t>(val * 1000000));
        }
        falcon::Stat("feature_server.predict_result",
                     static_cast<int64_t>(res.value(0) * 1000000));
        // 增加预估值的 perf 打点以支持环比监控
        ks::infra::PerfUtil::IntervalLogStash(
            static_cast<int64_t>(res.value(0) * 1000000), "ad.predict",
            "feature_server.predict_result", std::string(std::getenv("MY_POD_NAME")));
        LOG_EVERY_N(INFO, FLAGS_dump_result_freq)
            << "req llsid " << request->llsid() << " user_id " << request->user_id()
            << " item_id " << request->item_id(request_item_idx) << " result " << val;
      }
    } else {
      // cmd result stat
      fake_item_ids.insert(request->item_id(request_item_idx));
      if (fill_faked_item_result_by_zero) {
        LOG_EVERY_N(INFO, 100000)
            << "faked item " << request->item_id(request_item_idx) << " filled with zero";
        auto predict_values =
            response->mutable_predict_result(response_item_idx)->mutable_value();
        std::fill(predict_values->begin(), predict_values->end(), 0.0);
        ++fill_zero_count;
      }
      if (keep_faked_item_id) {
        LOG_EVERY_N(INFO, 1000000)
            << "keep faked item id: " << request->item_id(request_item_idx)
            << " in predict_result";
        response->mutable_predict_result(response_item_idx)
            ->set_item_id(request->item_id(request_item_idx));
      }
    }
  };

  // 压测请求不打印预估值
  if (!::ks::ad_nn::IsOfflineTraffic()) {
    // 共享特征服务请求和返回的 item id 不一致；特征服务一致
    if (!shared_feature_server_) {
      for (uint32_t item_idx = 0; item_idx < request->item_id_size(); ++item_idx) {
        stat_result(item_idx, item_idx);
      }
    } else {
      auto& item_id_idxs = *req_context->item_id_idxs;
      for (uint32_t item_idx = 0; item_idx < response->predict_result_size();
           ++item_idx) {
        // 判断是否 fake
        auto& request_item_idx = item_id_idxs[item_idx];
        stat_result(request_item_idx, item_idx);
      }
    }
  }

  if (dump_current_log) {
    if (adlog != nullptr) {
      if (use_bs_fast_feature_) {
        for (int i = 0; i < req_context->real_items.size(); ++i) {
          auto item = adlog->add_item();
          if (req_context->real_items[i] != nullptr) {
            req_context->real_items[i]->CopyTo(item);
            if (item->id() == 0) {
              LOG(INFO) << "[Debug]llsid " << request->llsid()
                        << " item id is zero: " << item->ShortDebugString();
            }
          }
        }
      } else {
        for (int i = 0; i < req_context->ad_log->item_size(); ++i) {
          const auto& item = ad_log->item(i);
          auto added_item = adlog->add_item();
          item.CopyTo(added_item);
          if (item.id() == 0) {
            LOG(INFO) << "[Debug]llsid " << request->llsid()
                      << " item id is zero: " << item.ShortDebugString();
          }
        }
      }
      std::string adlog_str;
      adlog->SerializeToString(&adlog_str);
      std::fstream adlog_fs("adlog_" + std::to_string(request->llsid()),
                            std::ios::binary | std::ios::trunc | std::ios::out);
      std::string encoded_str;
      base::Base64Encode(adlog_str, &encoded_str);
      adlog_fs << encoded_str;
      adlog_fs.close();
      LOG(INFO) << "saved adlog data info for llsid " << request->llsid();
    } else {
      LOG(INFO) << "AdLog is a nullptr";
    }

    if (sample != nullptr) {
      std::string encoded_bs_str;
      std::fstream bs_fs("bs_" + std::to_string(request->llsid()),
                         std::ios::binary | std::ios::trunc | std::ios::out);
      base::Base64Encode(sample->ToString(), &encoded_bs_str);
      bs_fs << encoded_bs_str;
      bs_fs.close();
      LOG(INFO) << "saved bs data info for llsid " << request->llsid();
    } else {
      LOG(INFO) << "BS Sample is a nullptr";
    }
  }

  // cmd result stat
  if (fake_item_ids.size() > 0) {
    if (response->cmd_result_stats_size() == 0) {
      response->add_cmd_result_stats();
    }
    auto miss_item = response->mutable_cmd_result_stats(0)->add_miss_items();
    miss_item->set_type(::kuaishou::ad::algorithm::MissType::ITEM_MISS);
    miss_item->mutable_item_ids()->Reserve(fake_item_ids.size());
    for (auto id : fake_item_ids) {
      miss_item->add_item_ids(id);
    }
  }

  falcon::Inc("feature_server.fill_zero_item_count", fill_zero_count);

  if (request->is_debug_req()) {
    debug::ResponseWrapper<UniversePredictResponse> debug_res(response);
    if (!use_feature_extract_handler_ && !use_bs_fast_feature_) {
      auto debug_ad_joint_labeled_log = ad_log->Get();
      debug_ad_joint_labeled_log.clear_item();

      for (int i = 0; i < ad_log->item_size(); ++i) {
        const auto& item = ad_log->item(i);
        auto added_item = debug_ad_joint_labeled_log.add_item();
        item.CopyTo(added_item);
      }
      debug_res.UpdatePayLoad(kuaishou::ad::debug::PayLoadType::PLT_AD_JOINT_LABELED_LOG,
                              debug_ad_joint_labeled_log);
    } else {
      if (request->debug_raw_data_size() == 0) {
        if (req_context->batched_samples != nullptr) {
          std::vector<uint64_t> real_item_ids;
          for (int i = 0; i < request->item_id_size(); i++) {
            if (req_context->faked_flatten_item_indices.count(i) == 0) {
              real_item_ids.push_back(request->item_id(i));
            }
          }
          if (real_item_ids.size() > 0) {
            const std::string item_id_mapping_key = "17869";
            flatten::FlatValueWrapper<ks::cofea_fbs::FeatureSet> item_id_wrapper;
            item_id_wrapper.Build(real_item_ids);
            (void)req_context->batched_samples->AddSamplesAttrByColumn(
                item_id_mapping_key, item_id_wrapper.GetObject());
          }
          debug_res.UpdatePayLoad(kuaishou::ad::debug::PayLoadType::PLT_BATCHED_SAMPLES,
                                  req_context->batched_samples->ToString());
        }
      }
    }
    debug_res.UpdateNamedPayLoad("infer_feature_list",
                                 kuaishou::ad::debug::PayLoadType::PLT_INFER_FEATURE_LIST,
                                 *req_context->features_str);
    kuaishou::ad::algorithm::DebugUserInfo debug_user_info;
    debug_user_info.set_user_id(req_context->request->user_id());
    std::string debug_user_info_str;
    debug_user_info.SerializeToString(&debug_user_info_str);
    debug_res.UpdateNamedPayLoad("debug_user_info",
                                 kuaishou::ad::debug::PayLoadType::PLT_DEBUG_USER_INFO,
                                 debug_user_info_str);
    if (req_context->feature_req != nullptr) {
      std::string feature_req_str;
      req_context->feature_req->SerializeToString(&feature_req_str);
      debug_res.UpdateNamedPayLoad("feature_request",
                                   kuaishou::ad::debug::PayLoadType::PLT_FEATURE_REQ,
                                   feature_req_str);
    }
  }
  req_context->finish(response->status());

  if (KS_UNLIKELY(req_context->fake_debug)) {
    bool has_larger_predict_value = false;
    for (int i = 0; i < response->predict_result_size(); ++i) {
      for (int j = 0; j < response->predict_result(i).value_size(); ++j) {
        if (response->predict_result(i).value(j) >= FLAGS_dump_value_threshold) {
          has_larger_predict_value = true;
          break;
        }
      }
      if (has_larger_predict_value) {
        break;
      }
    }
    if (has_larger_predict_value) {
      for (int idx = 0; idx < response->predict_result_size(); ++idx) {
        LOG(INFO) << "debug req llsid " << request->llsid() << " user_id "
                  << request->user_id() << " item_id " << request->item_id(idx)
                  << " result " << response->predict_result(idx).value(0);
      }
      std::string dump_file = std::to_string(request->llsid()) + "_" +
                              std::to_string(ks::ad_base::AdRandom::GetInt(0, 100));
      std::fstream rfs(FLAGS_dump_request_path + "/req/" + dump_file,
                       std::ios::trunc | std::ios::out);
      std::string serialized_req;
      request->SerializeToString(&serialized_req);
      if (FLAGS_dump_base64_result) {
        std::string base64_str;
        base::Base64Encode(serialized_req, &base64_str);
        rfs << base64_str;
      } else {
        rfs << serialized_req;
      }
      rfs.close();

      std::fstream fs(FLAGS_dump_request_path + "/res/" + dump_file,
                      std::ios::trunc | std::ios::out);
      std::string serialized_res;
      response->SerializeToString(&serialized_res);
      if (FLAGS_dump_base64_result) {
        std::string base64_str;
        base::Base64Encode(serialized_res, &base64_str);
        fs << base64_str;
      } else {
        fs << serialized_res;
      }
      fs.close();

      std::ofstream infer_req_fs(FLAGS_dump_request_path + "/infer_req/" + dump_file,
                                 std::ios::out | std::ios::binary);
      if (FLAGS_dump_base64_result && req_context->debug_feature_req != nullptr) {
        std::string base64_str;
        base::Base64Encode(*req_context->debug_feature_req, &base64_str);
        infer_req_fs << base64_str;
      } else {
        infer_req_fs << *req_context->debug_feature_req;
      }
      infer_req_fs.close();

      LOG(INFO) << "saved debug info for llsid " << request->llsid();
    }
    response->clear_debug_info();
  }
  // dump adlog
  if (DebugSample::GetInstance() != nullptr &&
      DebugSample::GetInstance()->ProduceType() == DebugProduceType::AdLog &&
      DebugSample::GetInstance()->IsNeedDebugSample()) {
    if (!use_feature_extract_handler_) {
      DebugSample::GetInstance()->Produce(*ad_log, *response);
    } else {
      SampleBs(*request, *response, req_context.get());
    }
    LOG_EVERY_N(INFO, 5000) << "Debug Sample are working .....";
  }
  return;
}

void FeatureServer::SampleBs(const UniversePredictRequest& request,
                             const UniversePredictResponse& response,
                             FeatureServerContext* req_context) {
  auto bs_to_str_start = Clock::now();
  std::vector<uint64_t> real_item_ids;
  std::vector<std::vector<float>> probs;
  for (int i = 0; i < request.item_id_size(); i++) {
    if (req_context->faked_flatten_item_indices.count(i) == 0) {
      real_item_ids.push_back(request.item_id(i));
      const auto& predict_result = response.predict_result(i);
      probs.emplace_back();
      probs.back().reserve(predict_result.value_size());
      for (int j = 0; j < predict_result.value_size(); ++j) {
        probs.back().emplace_back(predict_result.value(j));
      }
    }
  }

  auto item_id_wrapper =
      std::make_shared<flatten::FlatValueWrapper<ks::cofea_fbs::FeatureSet>>();
  item_id_wrapper->Build(real_item_ids);

  auto probs_wrapper =
      std::make_shared<flatten::FlatValueWrapper<ks::cofea_fbs::FeatureSet>>();
  probs_wrapper->Build(probs);

  // ensure the lifetime
  auto bs = req_context->batched_samples;
  std::shared_ptr<::google::protobuf::Map<std::string, std::string>>
      flatten_attr_real_time_action;
  if (request.flatten_attr_real_time_action_size() > 0) {
    flatten_attr_real_time_action.reset(
        new ::google::protobuf::Map<std::string, std::string>(
            request.flatten_attr_real_time_action()));
    for (auto iter = flatten_attr_real_time_action->begin();
         iter != flatten_attr_real_time_action->end(); ++iter) {
      const ks::cofea_fbs::FeatureSets* temp =
          ks::ad_nn::flatten::StringToTable<ks::cofea_fbs::FeatureSets>(
              iter->second.data(), iter->second.size());
      if (temp == nullptr) {
        LOG_EVERY_N(WARNING, 1000)
            << "[SampleBs Fail] this attr real time featuresets is nullptr, "
            << "data key: " << iter->first << "data size: " << iter->second.size();
        continue;
      } else if (!bs->AddFeaturesets(*temp, ks::ad_nn::flatten::AttrType::kContext)) {
        LOG_EVERY_N(ERROR, 1000) << "replace sub featuresets fail, skip send sample.";
      }
    }
  }
  auto builders = std::make_shared<
      std::unordered_map<std::string, std::shared_ptr<flatbuffers::FlatBufferBuilder>>>(
      std::move(req_context->builders));
  auto user_info_strings = std::make_shared<
      std::unordered_map<std::string, std::shared_ptr<const std::string>>>();

  if (request.has_flatten_user_info()) {
    (*user_info_strings)["daily_user"].reset(const_cast<UniversePredictRequest*>(&request)
                                                 ->mutable_flatten_user_info()
                                                 ->release_daily_user_info());
    (*user_info_strings)["minute_user"].reset(
        const_cast<UniversePredictRequest*>(&request)
            ->mutable_flatten_user_info()
            ->release_minute_user_info());
    (*user_info_strings)["reco_user"].reset(const_cast<UniversePredictRequest*>(&request)
                                                ->mutable_flatten_user_info()
                                                ->release_reco_user_info());
    (*user_info_strings)["realtime_action"] =
        (req_context->user_info_strings["realtime_action"]);
    (*user_info_strings)["eb_user"] = (req_context->user_info_strings["eb_user"]);
  } else {
    user_info_strings = std::make_shared<
        std::unordered_map<std::string, std::shared_ptr<const std::string>>>(
        std::move(req_context->user_info_strings));
  }
  auto item_info_strs = std::make_shared<std::vector<std::shared_ptr<std::string>>>(
      std::move(req_context->item_info_strs));
  // 海外 FeatureServer 此部分不为空, userinfo 上移到 router 后可以删除该 map
  auto attr_real_time_map = req_context->attr_real_time_map;
  auto to_str = [builders, user_info_strings, item_info_strs, item_id_wrapper,
                 attr_real_time_map, probs_wrapper, bs, flatten_attr_real_time_action]() {
    const std::string item_id_mapping_key = "17869";
    (void)bs->AddSamplesAttrByColumn(item_id_mapping_key, item_id_wrapper->GetObject());
    (void)bs->AddSamplesAttrByColumn("predict_result", probs_wrapper->GetObject());
    return bs->ToString();
  };
  falcon::Stat("feature_server.bs_to_str_cost", GetMicroseconds(bs_to_str_start));
  if (DebugSample::GetInstance() != nullptr) {
    DebugSample::GetInstance()->AsyncProduce(to_str);
  }
  return;
}

bool FeatureServer::InitBSComponentItemConfig(
    std::shared_ptr<base::Json>& kconf_model_config,  // NOLINT
    StoreOption* store_option, ReaderOption* reader_option, FeatureReader** reader) {
  auto init_config = [this](ItemInfoType type, const ComponentItemConfig& config,
                            ReaderOption* reader_option, StoreOption* store_option,
                            FeatureReader** component_reader) -> bool {
    if (!config.has_btq_config()) {
      LOG(INFO) << "[component] type " << ItemInfoTypeStr(type) << " has no btq config";
      return false;
    }
    const auto& btq_config = config.btq_config();
    if (btq_config.is_main_type()) {
      main_type_ = type;
    }
    reader_option->incr_source_name = btq_config.btq_incr_source_name();
    store_option->capacity = btq_config.local_store_capacity();
    store_option->capacity <<= 30;
    if (reader_option->incr_source_name.empty()) {
      LOG(FATAL) << "component has no incr_source_name for type: "
                 << ItemInfoTypeStr(type);
      return false;
    }
    std::string err_msg;
    if (!VerifyItemOptions(*reader_option, *store_option, &err_msg)) {
      LOG(FATAL) << "Item Options Invalid, ERR_MSG: " << err_msg;
    }
    *component_reader =
        feature_manager_.RegisterReader(ItemInfoTypeStr(type), *reader_option);
    LOG(INFO) << "[component] register reader type " << ItemInfoTypeStr(type);
    (*component_reader)->SkipParseItem(true);
    auto store = feature_manager_.RegisterFeatureStore<RawFeatureStore>(
        ItemInfoTypeStr(type), *store_option);
    if (store == nullptr) {
      LOG(FATAL) << "failed to create store: " << ItemInfoTypeStr(type);
      return false;
    }
    store->Subscribe(*component_reader);
    item_component_server_map_[type].item_store = store;
    LOG(INFO) << ItemInfoTypeStr(type)
              << " reader config: " << reader_option->DebugString();
    LOG(INFO) << ItemInfoTypeStr(type)
              << " store config: " << store_option->DebugString();
    return true;
  };
  for (const auto& type_conf : component_item_config_) {
    init_config(type_conf.first, type_conf.second, reader_option, store_option, reader);
  }
  return true;
}

bool FeatureServer::InitInfoAndConnectItemServer(const ComponentItemConfig& conf,
                                                 ItemInfoType type) {
  if (!conf.has_item_server_config()) {
    return false;
  }
  const auto item_server_config = conf.item_server_config();
  auto* info_ptr = GetItemComponentInfo(type);
  if (info_ptr == nullptr) return false;
  info_ptr->item_server_cmd = item_server_config.item_server_cmd();
  info_ptr->item_server_shard_num = item_server_config.item_server_shard_num();
  info_ptr->item_service_timeout = item_server_config.item_service_timeout();
  if (item_server_config.item_service_req_batch_num() == 0) {
    int32_t default_count = opt_bs_item_ ? 10 : 20;
    info_ptr->req_item_service_batch_num = default_count;
  } else {
    info_ptr->req_item_service_batch_num =
        item_server_config.item_service_req_batch_num();
  }
  int event_loop_num = item_server_config.item_service_event_loop_num();
  info_ptr->item_server_event_loop.reset(
      new ks::kess::rpc::grpc::EventLoopGroup(event_loop_num));
  if (info_ptr->item_server_cmd.empty()) {
    LOG(FATAL) << ItemInfoTypeStr(type) << " has no item server cmd";
    return false;
  }
  if (use_brpc_) {
    WaitServiceReady<IndexService>(
        &info_ptr->item_server_cmd, &info_ptr->item_service_timeout,
        &info_ptr->item_server_shard_num, &info_ptr->item_server_shard_config,
        &info_ptr->item_server_clients, &info_ptr->brpc_item_server_clients,
        ks::ad_nn::GetItemServerKessName);
  } else {
    WaitServiceReady<IndexService>(
        &info_ptr->item_server_cmd, &info_ptr->item_service_timeout,
        &info_ptr->item_server_shard_num, &info_ptr->item_server_shard_config,
        &info_ptr->item_server_clients, ks::ad_nn::GetItemServerKessName);
  }
  if (info_ptr->item_store != nullptr) info_ptr->item_store->SetLoadOnlyOnce(true);
  return true;
}

bool FeatureServer::InitItemServerConfig(
    std::shared_ptr<base::Json>& kconf_model_config) {  // NOLINT
  if (use_component_item_) {
    for (const auto& type_conf : component_item_config_) {
      InitInfoAndConnectItemServer(type_conf.second, type_conf.first);
    }
    return true;
  } else {
    if (!InitInfoAndConnectItemServer(component_item_config_[ItemInfoType::FULL],
                                      ItemInfoType::FULL)) {
      LOG(ERROR) << "init creative item server config fail.";
      return false;
    }
    main_type_ = ItemInfoType::FULL;
    auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
    if (info_ptr == nullptr) return false;
    if (cmd_name_ == "") cmd_name_ = GetKcsModelCmd();
    if (cmd_name_ == "") {
      LOG(ERROR) << "error get cmd name, return false.";
      return false;
    }
    ItemConfigManager::GetInstance()->Init(cmd_name_, info_ptr->item_server_cmd);
    use_fake_item_cache_ = kconf_model_config->GetBoolean("use_fake_item_cache", true);
    if (use_fake_item_cache_) {
      int fake_cache_count = model_config_->GetInt("fake_item_cache_capacity", 5000000);
      fake_id_cache_.reset(
          new ks::ad_nn::TTLIdCache<uint64_t>("fake_item_cache", fake_cache_count));
    }
  }
  if (main_type_ == ItemInfoType::UNKNOWN_TYPE) {
    LOG(FATAL) << "no main type set: " << main_type_;
    return false;
  }
  return true;
}

bool FeatureServer::ConstructAdlog(const UniversePredictRequest& request,
                                   int req_item_server_ratio,
                                   ks::ad_algorithm::AdLogAdaptor* adlog,
                                   UniversePredictResponse* response,
                                   const std::vector<std::string>& shared_aa_alias) {
  req_time_ = base::GetTimestamp() / 1000000;
  item_expire_time_ =
      ad_nn::PredictServiceKconfUtil::adPredictServerCommonCacheExpireThreshold();
  adlog->Get().set_time(base::GetTimestamp() / 1000);
  adlog->Get().set_llsid(request.llsid());
  if (request.tab_type() != kuaishou::ad::algorithm::UNKNOWN_TAB) {
    adlog->Get().set_tab(request.tab_type());
  }
  // 获取外部 adx item info / user info
  std::map<uint64_t, ItemAdaptorBase*> fetch_item_map;
  auto rt_item_start = Clock::now();
  adlog->ResizeItemStoreCache(request.item_id_size());

  AdDataResponse* data_response = adlog->CreateMessage<AdDataResponse>();
  uint64_t data_cost = 0;
  if (need_load_user_data_ &&
      0 != predict_service_->LoadUserData(request, data_response, &data_cost, response)) {
    LOG_EVERY_N(WARNING, 10000) << "Load user data failed";
    falcon::Inc("feature_server.load_user_fail", 1);
    return false;
  }

  auto adlog_start = Clock::now();
  auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  if (use_dist_item_service_) {
    item_ids_.clear();
    item_ids_.resize(info.item_server_shard_num);
    req_idxes_.clear();
    req_idxes_.resize(info.item_server_shard_num);
  }
  bool has_reco_feature =
      (feature_extractor_ != nullptr ? feature_extractor_->HasRecoFeature()
                                     : feature_file_info_.has_reco_feature);
  bool parse_reco_user = has_reco_feature || need_parse_reco_user_info_;
  if (0 != predict_service_->PrepareAdLog(data_response, &request, fetch_item_map, adlog,
                                          nullptr, false, parse_reco_user)) {
    LOG(WARNING) << "prepare adlog failed";
    falcon::Inc("feature_server.prepare_adlog_fail", 1);
    return false;
  }
  if (request.is_debug_req()) {
    adlog->set_is_train(true);
  }

  if (!NeedDumpAdLogForCompare()) {
    bool skip_req_item = false;
    if (req_item_server_ratio < 100) {
      auto ratio = ad_base::AdRandom::GetInt(1, 100);
      if (ratio > req_item_server_ratio) {
        LOG_EVERY_N(INFO, 1000) << "req item server skipped, ratio: " << ratio
                                << ", expected: " << req_item_server_ratio;
        skip_req_item = true;
      }
    }
    if (!skip_req_item) {
      int req_remote_item_cnt = 0;
      for (int shard_id = 0; shard_id < item_ids_.size(); ++shard_id) {
        req_remote_item_cnt += item_ids_[shard_id].size();
      }
      falcon::Inc("feature_server.req_item_cnt_from_remote", req_remote_item_cnt);
      if (req_remote_item_cnt > 0) {
        auto get_item_start = Clock::now();
        GetRemoteItem(request, adlog);
        falcon::Stat("feature_server.get_remote_item_cost",
                     GetMicroseconds(get_item_start));
      } else {
        falcon::Stat("feature_server.fake_remote_item_rate", 0);
      }
    } else {
      int fake_count = 0;
      for (int i = 0; i < req_idxes_.size(); ++i) {
        fake_count = fake_count + req_idxes_[i].size();
        for (int j = 0; j < req_idxes_[i].size(); ++j) {
          adlog->AddFakedItemIndex(req_idxes_[i][j]);
        }
      }

      falcon::Inc("feature_server.fake_remote_item_count", fake_count);
      double fake_rate = 1e6 * fake_count / request.item_id_size();
      falcon::Stat("feature_server.fake_remote_item_rate", fake_rate);
    }
  }
  // pb_cache 标记 item 热点
  /*if (serialized_item_store_) {
    auto mark_spot_func = [this](uint64_t *data, uint32_t n) {
      for (int idx = 0; idx < n; idx++) {
        serialized_item_store_->GetStoreData<std::shared_ptr<std::string>>(data[idx]);
      }
    };
    predict_service_.MarkHotspot(&request, mark_spot_func);
  }*/

  if (!shared_feature_server_) {
    predict_service_->SetRankCmd(request.cmd(0), aa_alias_, adlog);
  } else {
    for (int i = 0; i < shared_aa_alias.size(); ++i) {
      auto& aa_alias = shared_aa_alias[i];
      if (!aa_alias.empty()) {
        predict_service_->SetRankCmd(request.cmd(i), aa_alias,
                                     adlog->Get().mutable_context());
      }
    }
  }
  falcon::Stat("feature_server.prepare_adlog_cost", GetMicroseconds(adlog_start));
  return true;
}

void FeatureServer::GetItem(uint64_t item_id, int32_t req_idx,
                            ks::ad_algorithm::AdLogItemStoreCache* store_cache) {
  auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
  if (KS_UNLIKELY(info_ptr == nullptr)) return;
  auto& info = *info_ptr;
  auto item = info.item_store->GetStoreData<std::shared_ptr<ItemAdaptorV1>>(item_id);
  if (!serialized_item_store_ && !use_dist_item_service_) {
    store_cache->item = item.data;
    return;
  }

  // 两级缓存时检查第一级缓存数据是否失效
  if (item.update_time > 0) {
    store_cache->item = item.data;
    LOG_EVERY_N(INFO, 6000000) << "item:" << item_id
                               << " update_time:" << item.update_time
                               << " build_time:" << item.data->build_time() / 1000
                               << " elapse:" << req_time_ - item.update_time;
    return;
  }
  // 需从 item service 获得 item
  if (use_dist_item_service_) {
    uint64_t shard_id = GetShardId(info, item_id);
    item_ids_[shard_id].push_back(item_id);
    req_idxes_[shard_id].push_back(req_idx);
    // 先填一个非空的 item， 避免在 SampleProvider::PrepareItemV1 触发
    // ad_log->AddFakedItem 等逻辑
    store_cache->item = std::make_shared<ItemAdaptorV1>();
    store_cache->item->set_id(item_id);
    return;
  }

  falcon::Inc("dnn_predict_server.pb_cache_get", 1);
  auto start = base::GetTimestamp();
  auto raw_item =
      serialized_item_store_->GetStoreData<std::shared_ptr<std::string>>(item_id);
  if (raw_item.update_time < 0) {
    falcon::Inc("dnn_predict_server.pb_cache_miss", 1);
    store_cache->item = item.data;
    return;
  }
  if (FLAGS_enable_item_pb_parse_opt) {
    store_cache->item = std::make_shared<ItemAdaptorV1>();
    store_cache->item->set_id(item_id);
    store_cache->serialized_item = raw_item.data;
    return;
  }

  auto item_ptr = ParseItemV1(item_id, *raw_item.data);
  if (!item_ptr) {
    falcon::Inc("dnn_predict_server.pb_cache_except", 1);
    return;
  }

  info.item_store->TryAddStoreData<std::shared_ptr<ItemAdaptorV1>>(
      static_cast<int64_t>(item_id), {static_cast<int64_t>(req_time_), item_ptr});
  falcon::Inc("dnn_predict_server.pb_cache_success", 1);
  store_cache->item = item_ptr;
}

bool FeatureServer::GetRemoteItem(const UniversePredictRequest& ps_request,
                                  ks::ad_algorithm::AdLogAdaptor* adlog) {
  std::vector<std::vector<IndexRequest>> requests;
  std::vector<std::vector<IndexResponse>> responses;
  auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  PrepareReqItemServer(info, item_ids_, &requests, &responses);

  std::atomic<int> fake_item_count(0);
  ks::kess::rpc::BatchWaiter batch_waiter;
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(info.item_service_timeout));
  std::vector<std::vector<std::vector<int>>> faked_item_indices(requests.size());
  for (int i = 0; i < faked_item_indices.size(); ++i) {
    faked_item_indices[i].resize(requests[i].size());
  }
  for (int i = 0; i < requests.size(); ++i) {
    std::string peer;
    auto stub = info.item_server_clients[i]->SelectOne(&peer);
    auto req_idx_begin = req_idxes_[i].cbegin();
    for (int j = 0; j < requests[i].size(); ++j) {
      auto& request = requests[i][j];
      std::vector<int>* faked_item_indices_ptr = &faked_item_indices[i][j];
      auto callback = [&, this, adlog, req_idx_begin, faked_item_indices_ptr](
                          const grpc::Status& status, IndexResponse* response) {
        if (!CheckItemServerRespValid(status, request, response)) {
          fake_item_count.fetch_add(request.creative_id_size());
          return;
        }
        int local_fake_item_count = 0;
        Semaphore item_semaphore;
        auto item_size = response->creative_info_size();
        for (int k = 0; k < item_size; ++k) {
          int32_t index = *(req_idx_begin + k);
          uint64_t item_id = request.creative_id(k);
          auto& serialized_data = response->creative_info(k).value();
          if (serialized_data.empty()) {
            ++local_fake_item_count;
            faked_item_indices_ptr->push_back(index);
            LOG_EVERY_N(INFO, FLAGS_fake_log_freq)
                << "item id not found, will fake it: " << item_id << " llsid "
                << ps_request.llsid() << " page_id " << ps_request.context().page_id()
                << " sub_page_id " << ps_request.context().sub_page_id() << " pos_id "
                << ps_request.context().pos_id();
            item_semaphore.Release();
            continue;
          }
          if (FLAGS_enable_item_pb_parse_opt) {
            adlog->SetItemStoreCacheRawData(
                index, std::make_shared<std::string>(serialized_data));
            item_semaphore.Release();
            continue;
          }

          ItemParseTask item_task;
          item_task.semaphore = &item_semaphore;
          item_task.item_id = item_id;
          item_task.serialized_data = &serialized_data;
          item_task.info = info_ptr;
          item_task.index = index;
          item_task.adlog = adlog;
          while (!item_tasks_.push(item_task)) {
            LOG_EVERY_N(WARNING, 100000)
                << "item parse task queue is full, wait some time";
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            falcon::Inc("feature_server.item_parse_task_queue_full", 1);
          }
        }
        item_semaphore.Acquire(item_size);
        fake_item_count.fetch_add(local_fake_item_count);
      };
      req_idx_begin += request.creative_id_size();
      auto fut = stub->AsyncGetCreative(options, request, &responses[i][j],
                                        info.item_server_event_loop->SelectOne());
      batch_waiter.Add(fut,
                       [callback](const grpc::Status& status, IndexResponse* response) {
                         callback(status, response);
                       });
    }
  }
  batch_waiter.Wait();

  for (int i = 0; i < faked_item_indices.size(); ++i) {
    for (int j = 0; j < faked_item_indices[i].size(); ++j) {
      for (int index : faked_item_indices[i][j]) {
        adlog->AddFakedItemIndex(index);
      }
    }
  }

  falcon::Inc("feature_server.fake_remote_item_count", fake_item_count.load());
  double fake_rate = 1e6 * fake_item_count.load() / ps_request.item_id_size();
  falcon::Stat("feature_server.fake_remote_item_rate", fake_rate);
  return true;
}

void FeatureServer::PrepareReqItemServer(
    const ItemServerInfo& info, const std::vector<std::vector<uint64_t>>& item_ids,
    std::vector<std::vector<IndexRequest>>* requests,
    std::vector<std::vector<IndexResponse>>* responses) {
  requests->resize(info.item_server_shard_num);
  responses->resize(info.item_server_shard_num);
  for (int shard_id = 0; shard_id < item_ids.size(); ++shard_id) {
    auto begin = item_ids[shard_id].cbegin(), end = item_ids[shard_id].cend();
    int sub_req_cnt = item_ids[shard_id].size() / info.req_item_service_batch_num + 1;
    requests->at(shard_id).reserve(sub_req_cnt);
    responses->at(shard_id).reserve(sub_req_cnt);
    while (begin != end) {
      int step = GetBatchNum(info, end - begin);
      IndexRequest request;
      IndexResponse response;
      if (use_opt_item_server_) {
        *request.mutable_req_cmd() = cmd_name_;
        LOG_EVERY_N(INFO, 100000) << "item request add cmd: " << cmd_name_;
      }
      request.mutable_creative_id()->Add(begin, begin + step);
      requests->at(shard_id).push_back(std::move(request));
      responses->at(shard_id).push_back(std::move(response));
      begin += step;
    }
  }
}

bool FeatureServer::CheckItemServerRespValid(const grpc::Status& status,
                                             const IndexRequest& request,
                                             IndexResponse* response) {
  falcon::Inc("feature_server.get_remote_item_freq", 1);
  if (!status.ok()) {
    falcon::Inc("feature_server.get_remote_item_fail", 1);
    LOG_EVERY_N(WARNING, 10000) << "ReqItemServer failed, grpc return status is not ok";
    return false;
  }
  if (response->creative_info_size() != request.creative_id_size()) {
    LOG_EVERY_N(WARNING, 100000)
        << "response and request not match,"
           " request size = "
        << request.creative_id_size()
        << ", response size = " << response->creative_info_size();
    falcon::Inc("feature_server.get_remote_item_fail", 1);
    return false;
  }
  return true;
}

bool FeatureServer::GetFlattenItem(uint64_t item_id, uint64_t req_time, ItemInfoType type,
                                   std::shared_ptr<std::string>* item_data) {
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  auto item = info.item_store->GetStoreData<std::shared_ptr<std::string>>(item_id);
  // 两级缓存时检查第一级缓存数据是否失效
  if (item.update_time > 0 && item.data != nullptr) {
    *item_data = item.data;
    LOG_EVERY_N(INFO, 6000000) << "item:" << item_id
                               << " update_time:" << item.update_time
                               << " elapse:" << req_time - item.update_time
                               << " type: " << type;
    return true;
  } else {
    LOG_EVERY_N(WARNING, 100000)
        << "get item from cache fail:" << item_id << " update_time:" << item.update_time
        << " elapse:" << req_time - item.update_time << " type: " << type;
  }

  if (use_dist_item_service_ && serialized_item_store_) {
    LOG_EVERY_N(WARNING, 10000) << "No need use the dist item service and "
                                << "serilized_item_store_ at the same time";
    return true;
  }

  if (serialized_item_store_) {
    falcon::Inc("dnn_predict_server.pb_cache_get", 1);
    falcon::Inc("dnn_predict_server.flatten_cache_get", 1);
    auto start = base::GetTimestamp();
    auto raw_item =
        serialized_item_store_->GetStoreData<std::shared_ptr<std::string>>(item_id);
    if (raw_item.update_time < 0) {
      falcon::Inc("dnn_predict_server.pb_cache_miss", 1);
      falcon::Inc("dnn_predict_server.flatten_cache_miss", 1);
      *item_data = item.data;
      return true;
    }

    auto decompressed_str = std::make_shared<std::string>();
    if (!DecompressStr(item_id, *raw_item.data, decompressed_str.get())) {
      LOG_EVERY_N(ERROR, 1000) << "decompressed str failed";
      falcon::Inc("dnn_predict_server.pb_cache_except", 1);
      falcon::Inc("dnn_predict_server.flatten_cache_except", 1);
      return false;
    }

    *item_data = decompressed_str;
    info.item_store->TryAddStoreData<std::shared_ptr<std::string>>(
        static_cast<int64_t>(item_id),
        {static_cast<int64_t>(req_time), decompressed_str});
    falcon::Inc("dnn_predict_server.pb_cache_success", 1);
    falcon::Inc("dnn_predict_server.flatten_cache_success", 1);
  }
  return true;
}

bool FeatureServer::GetFlattenItemOpt(uint64_t item_id, uint64_t req_time,
                                      ItemInfoType type,
                                      std::shared_ptr<BSItemWrapper>* item_data) {
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto item = info_ptr->item_store->GetStoreData<std::shared_ptr<BSItemWrapper>>(item_id);
  // 两级缓存时检查第一级缓存数据是否失效
  if (item.update_time > 0 && item.data != nullptr) {
    *item_data = item.data;
    LOG_EVERY_N(INFO, 6000000) << "item:" << item_id
                               << " update_time:" << item.update_time
                               << " elapse:" << req_time - item.update_time;
    return true;
  }

  return false;
}

bool FeatureServer::GetRemoteFlattenItemsOpt(
    ItemInfoType type, const std::vector<int>& indices,
    std::function<uint64_t(int i)> find_item_id, FeatureServerContext* fs_context,
    std::vector<std::shared_ptr<BSItemWrapper>>* samples,
    std::vector<int>* succ_indices) {
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  auto start = Clock::now();
  const auto& ps_request = *fs_context->request;
  int data_size = indices.size();
  std::vector<std::vector<uint64_t>> shard_item_ids(info.item_server_shard_num);
  std::vector<std::vector<int>> indices_vals(info.item_server_shard_num);
  thread_local std::vector<uint64_t> remote_not_found_ids;
  remote_not_found_ids.clear();
  for (int i = 0; i < data_size; i++) {
    uint64_t item_id = find_item_id(indices[i]);
    uint64_t shard_id = GetShardId(info, item_id);
    shard_item_ids[shard_id].push_back(item_id);
    indices_vals[shard_id].push_back(indices[i]);
  }

  std::vector<std::vector<IndexRequest>> requests;
  std::vector<std::vector<IndexResponse>> responses;

  PrepareReqItemServer(info, shard_item_ids, &requests, &responses);

  samples->reserve(data_size);
  succ_indices->reserve(data_size);
  std::vector<std::vector<std::shared_ptr<BSItemWrapper>>> local_samples(
      info.item_server_shard_num);
  std::vector<std::vector<int>> local_succ_indices(info.item_server_shard_num);
  for (int i = 0; i < info.item_server_shard_num; ++i) {
    local_samples[i].resize(indices_vals[i].size(), nullptr);
    local_succ_indices[i].resize(indices_vals[i].size(), -1);
  }
  falcon::Stat("feature_server.req_remote_item_count", data_size);

  std::atomic<int> fake_item_count(0);
  ks::kess::rpc::BatchWaiter batch_waiter;
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(info.item_service_timeout));
  for (int i = 0; i < requests.size(); ++i) {
    std::string peer;
    auto stub = info.item_server_clients[i]->SelectOne(&peer);
    auto index_begin = indices_vals[i].cbegin();
    auto local_samples_begin = local_samples[i].begin();
    auto local_succ_indices_begin = local_succ_indices[i].begin();
    for (int j = 0; j < requests[i].size(); ++j) {
      auto& request = requests[i][j];
      auto callback = [&, this, local_samples_begin, local_succ_indices_begin, fs_context,
                       index_begin](const grpc::Status& status, IndexResponse* response) {
        if (!CheckItemServerRespValid(status, request, response)) {
          fake_item_count.fetch_add(request.creative_id_size());
          return;
        }
        int local_fake_item_count = 0;
        auto callback_start = Clock::now();
        for (int k = 0; k < response->creative_info_size(); ++k) {
          int32_t index = *(index_begin + k);
          uint64_t item_id = request.creative_id(k);
          const std::string* serialized_data = &response->creative_info(k).value();
          if (serialized_data->empty()) {
            ++local_fake_item_count;
            LOG_EVERY_N(INFO, FLAGS_fake_log_freq)
                << "item id not found, will fake it: " << item_id << " llsid "
                << ps_request.llsid() << " page_id " << ps_request.context().page_id()
                << " sub_page_id " << ps_request.context().sub_page_id() << " pos_id "
                << ps_request.context().pos_id() << " type: " << type;
            if (fake_id_cache_ != nullptr && fs_context->fake_item_expire_time_us > 0) {
              remote_not_found_ids.push_back(item_id);
            }
            if (FLAGS_fake_photo_id_for_empty && type == ItemInfoType::PHOTO &&
                item_id == 28442895407) {
              static std::string fake_photo_str = [this] {
                flatbuffers::FlatBufferBuilder fbb;
                std::vector<flatbuffers::Offset<ks::ad_nn::simplified_raw_fea::Field>>
                    offsets;
                offsets.emplace_back();
                ks::ad_nn::GenerateField(28442895407, "22372", &fbb, &offsets.back());
                offsets.emplace_back();
                ks::ad_nn::GenerateField(static_cast<int64_t>(1), "73955", &fbb,
                                         &offsets.back());
                auto simplified_raw_fea_offset =
                    ks::ad_nn::simplified_raw_fea::CreateSimplifiedRawFeatureDirect(
                        fbb, &offsets);
                fbb.Finish(simplified_raw_fea_offset);
                return std::string(reinterpret_cast<const char*>(fbb.GetBufferPointer()),
                                   fbb.GetSize());
              }();
              LOG_FIRST_N(INFO, 1000)
                  << "use fake_photo_id_for_empty, fake photoid 28442895407.";
              serialized_data = &fake_photo_str;
            } else {
              continue;
            }
          }

          auto bs_item_wrapper = std::make_shared<BSItemWrapper>();
          bool flag = false;
          do {
            if (use_opt_item_server_) {
              bs_item_wrapper->flat_str = *serialized_data;
              bs_item_wrapper->need_convert_str2simple = false;
            } else {
              if (!DecompressStr(item_id, *serialized_data, &bs_item_wrapper->flat_str)) {
                LOG_EVERY_N(ERROR, 1000)
                    << "decompressed str failed for item: " << item_id;
                break;
              }
            }
            if (!flatten::Verify<SimplifiedRawFeature>(
                    bs_item_wrapper->flat_str.c_str(),
                    bs_item_wrapper->flat_str.size())) {
              LOG_EVERY_N(ERROR, 1000) << "verify str failed for item: " << item_id;
              break;
            }
            flag = true;
          } while (0);
          if (!flag) {
            ++local_fake_item_count;
            falcon::Inc("dnn_predict_server.pb_cache_except", 1);
            falcon::Inc("dnn_predict_server.flatten_cache_except", 1);
            continue;
          }

          StoreData<std::shared_ptr<BSItemWrapper>> store(
              static_cast<int64_t>(fs_context->req_time), bs_item_wrapper);
          bs_item_wrapper->data_source = DataSource::DS_RPC;
          if (!info.item_store->TryAddMutableStoreData<std::shared_ptr<BSItemWrapper>>(
                  static_cast<int64_t>(item_id), &store)) {
            LOG_EVERY_N(WARNING, 1000)
                << "TryAddMutableStoreData failed for item:" << item_id;
            continue;
          }
          *(local_samples_begin + k) = bs_item_wrapper;
          *(local_succ_indices_begin + k) = index;
        }
        fake_item_count.fetch_add(local_fake_item_count);
      };
      index_begin += request.creative_id_size();
      local_samples_begin += request.creative_id_size();
      local_succ_indices_begin += request.creative_id_size();
      auto fut = stub->AsyncGetCreative(options, request, &responses[i][j],
                                        info.item_server_event_loop->SelectOne());
      batch_waiter.Add(fut,
                       [callback](const grpc::Status& status, IndexResponse* response) {
                         callback(status, response);
                       });
    }
  }
  batch_waiter.Wait();

  for (int i = 0; i < local_samples.size(); ++i) {
    for (int j = 0; j < local_samples[i].size(); ++j) {
      if (local_samples[i][j] != nullptr) {
        int index = local_succ_indices[i][j];
        samples->emplace_back(local_samples[i][j]);
        succ_indices->emplace_back(index);
      }
    }
  }
  if (fake_id_cache_ != nullptr && fs_context->fake_item_expire_time_us > 0 &&
      remote_not_found_ids.size() > 0) {
    fake_id_cache_->UpdateCacheDirectly(remote_not_found_ids);
  }

  falcon::Inc("feature_server.fake_remote_item_count", fake_item_count.load());
  falcon::Inc("feature_server.fake_remote_flatten_item_count", fake_item_count.load());
  double fake_rate = 1e6 * fake_item_count.load() / ps_request.item_id_size();
  falcon::Stat("feature_server.fake_remote_item_rate", fake_rate);
  falcon::Stat(
      base::StringPrintf("feature_server.component_item_%d_fake_remote_item_rate", type)
          .c_str(),
      fake_rate);
  falcon::Stat("feature_server.fake_remote_flatten_item_rate", fake_rate);
  return true;
}

bool FeatureServer::GetColossusDBFlattenItemsOpt(
    ItemInfoType type, const std::vector<int>& indices,
    std::function<uint64_t(int i)> find_item_id, FeatureServerContext* fs_context,
    std::vector<std::shared_ptr<BSItemWrapper>>* samples,
    std::vector<int>* succ_indices) {
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) {
    return false;
  }
  auto& info = *info_ptr;
  auto start = Clock::now();
  const auto& ps_request = *fs_context->request;
  int data_size = indices.size();

  thread_local std::vector<uint64_t> remote_not_found_ids;
  remote_not_found_ids.clear();

  folly::F14FastMap<uint64_t, std::vector<int>> id_index_map;
  std::vector<uint64_t> item_ids;
  item_ids.reserve(data_size);
  for (int i = 0; i < data_size; i++) {
    uint64_t item_id = find_item_id(indices[i]);
    item_ids.emplace_back(item_id);
    auto iter = id_index_map.find(item_id);
    if (iter == id_index_map.end()) {
      id_index_map.insert(std::make_pair(item_id, std::vector<int>()));
      iter = id_index_map.find(item_id);
    }
    iter->second.emplace_back(indices[i]);
  }

  samples->reserve(data_size);
  succ_indices->reserve(data_size);

  falcon::Stat("feature_server.req_remote_item_count", data_size);

  std::atomic<int> fake_item_count(0);

  if (data_size > 0) {
    auto request_handler =
        [this](colossusdb::ps::proto::store::v1::BatchGetDocumentRequest& request) {
          if (use_opt_item_server_) {
            request.set_schema(cmd_name_);
            LOG_EVERY_N(INFO, 100000) << "item request add cmd: " << cmd_name_;
          }
        };

    auto resp_sub_cb =
        [&, this](colossusdb::ps::proto::store::v1::BatchGetDocumentRequest* request,
                  colossusdb::ps::proto::store::v1::BatchGetDocumentResponse* response,
                  const brpc::Controller& cntl) {
          falcon::Inc("feature_server.get_remote_item_freq", 1);
          if (cntl.Failed()) {
            fake_item_count.fetch_add(request->ids_size());
            falcon::Inc("feature_server.get_remote_item_fail", 1);
            LOG_EVERY_N(WARNING, 10000)
                << "BatchGetDocumentRequest failed, grpc return status is not ok";
          } else if (response->founds_size() != request->ids_size()) {
            fake_item_count.fetch_add(request->ids_size());
            LOG_EVERY_N(WARNING, 100000)
                << "response and request not match,"
                   " request size = "
                << request->ids_size() << ", response size = " << response->founds_size();
            falcon::Inc("feature_server.get_remote_item_fail", 1);
          }
        };

    auto future =
        clsdb_client_->GetClient()
            ->AsyncBatchGetResponse(item_ids, request_handler, nullptr, nullptr,
                                    resp_sub_cb)
            .then([&,
                   this ](std::vector<std::shared_ptr<
                              colossusdb::ps::proto::store::v1::BatchGetDocumentResponse>>
                              results) noexcept {
              // auto callback_start = Clock::now();

              for (auto& response : results) {
                for (auto& found : response->founds()) {
                  uint64_t item_id = found.id();
                  auto bs_item_wrapper = std::make_shared<BSItemWrapper>();

                  auto& serialized_data = found.value();
                  if (serialized_data.empty()) {
                    fake_item_count.fetch_add(1);
                    LOG_EVERY_N(INFO, FLAGS_fake_log_freq)
                        << "item id not found, will fake it: " << item_id << " llsid "
                        << ps_request.llsid() << " page_id "
                        << ps_request.context().page_id() << " sub_page_id "
                        << ps_request.context().sub_page_id() << " pos_id "
                        << ps_request.context().pos_id() << " type: " << type;
                    if (fake_id_cache_ != nullptr &&
                        fs_context->fake_item_expire_time_us > 0) {
                      remote_not_found_ids.push_back(item_id);
                    }
                    continue;
                  }

                  bool flag = false;
                  do {
                    if (use_opt_item_server_) {
                      bs_item_wrapper->flat_str = serialized_data;
                      bs_item_wrapper->need_convert_str2simple = false;
                    } else {
                      if (!DecompressStr(item_id, serialized_data,
                                         &bs_item_wrapper->flat_str)) {
                        LOG_EVERY_N(ERROR, 1000)
                            << "decompressed str failed for item: " << item_id;
                        break;
                      }
                    }
                    if (!flatten::Verify<SimplifiedRawFeature>(
                            bs_item_wrapper->flat_str.c_str(),
                            bs_item_wrapper->flat_str.size())) {
                      LOG_EVERY_N(ERROR, 1000)
                          << "verify str failed for item: " << item_id;
                      break;
                    }
                    flag = true;
                  } while (0);
                  if (!flag) {
                    fake_item_count.fetch_add(1);
                    falcon::Inc("dnn_predict_server.pb_cache_except", 1);
                    falcon::Inc("dnn_predict_server.flatten_cache_except", 1);
                    continue;
                  }

                  StoreData<std::shared_ptr<BSItemWrapper>> store(
                      static_cast<int64_t>(fs_context->req_time), bs_item_wrapper);
                  bs_item_wrapper->data_source = DataSource::DS_RPC;
                  if (!info.item_store
                           ->TryAddMutableStoreData<std::shared_ptr<BSItemWrapper>>(
                               static_cast<int64_t>(item_id), &store)) {
                    LOG_EVERY_N(WARNING, 1000)
                        << "TryAddMutableStoreData failed for item:" << item_id;
                    continue;
                  }

                  auto iter = id_index_map.find(item_id);
                  if (iter == id_index_map.end() || iter->second.empty()) {
                    fake_item_count.fetch_add(1);
                    falcon::Inc("dnn_predict_server.pb_cache_except", 1);
                    falcon::Inc("dnn_predict_server.flatten_cache_except", 1);
                    LOG_EVERY_N(ERROR, 1000) << "no index for item: " << item_id;
                    continue;
                  }
                  int index = iter->second.back();
                  iter->second.pop_back();
                  samples->emplace_back(bs_item_wrapper);
                  succ_indices->emplace_back(index);
                }
              }
            });
    future.wait();

    if (fake_id_cache_ != nullptr && fs_context->fake_item_expire_time_us > 0 &&
        remote_not_found_ids.size() > 0) {
      fake_id_cache_->UpdateCacheDirectly(remote_not_found_ids);
    }
  }

  falcon::Inc("feature_server.fake_remote_item_count", fake_item_count.load());
  falcon::Inc("feature_server.fake_remote_flatten_item_count", fake_item_count.load());
  double fake_rate = 1e6 * fake_item_count.load() / ps_request.item_id_size();
  falcon::Stat("feature_server.fake_remote_item_rate", fake_rate);
  falcon::Stat(
      base::StringPrintf("feature_server.component_item_%d_fake_remote_item_rate", type)
          .c_str(),
      fake_rate);
  falcon::Stat("feature_server.fake_remote_flatten_item_rate", fake_rate);
  return true;
}

bool FeatureServer::GetRemoteFlattenItems(ItemInfoType type,
                                          const std::vector<int>& indices,
                                          std::function<uint64_t(int i)> find_item_id,
                                          FeatureServerContext* fs_context,
                                          std::vector<const FeatureSets*>* samples,
                                          std::vector<int>* succ_indices) {
  const auto& ps_request = *fs_context->request;
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  int data_size = indices.size();
  std::vector<std::vector<uint64_t>> shared_item_ids(info.item_server_shard_num);
  std::vector<std::vector<int>> indices_vals(info.item_server_shard_num);
  for (int i = 0; i < data_size; i++) {
    uint64_t item_id = 0;
    item_id = find_item_id(indices[i]);
    uint64_t shard_id = GetShardId(info, item_id);
    shared_item_ids[shard_id].push_back(item_id);
    indices_vals[shard_id].push_back(indices[i]);
  }

  std::vector<std::vector<IndexRequest>> requests;
  std::vector<std::vector<IndexResponse>> responses;

  PrepareReqItemServer(info, shared_item_ids, &requests, &responses);
  samples->reserve(data_size);
  succ_indices->reserve(data_size);
  std::vector<std::vector<const FeatureSets*>> local_samples(info.item_server_shard_num);
  std::vector<std::vector<int>> local_succ_indices(info.item_server_shard_num);
  for (int i = 0; i < info.item_server_shard_num; ++i) {
    local_samples[i].resize(indices_vals[i].size(), nullptr);
    local_succ_indices[i].resize(indices_vals[i].size(), -1);
  }
  falcon::Stat("feature_server.req_remote_item_count", data_size);

  std::atomic<int> fake_item_count(0);
  ks::kess::rpc::BatchWaiter batch_waiter;
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(info.item_service_timeout));
  for (int i = 0; i < requests.size(); ++i) {
    std::string peer;
    auto stub = info.item_server_clients[i]->SelectOne(&peer);
    auto index_begin = indices_vals[i].cbegin();
    auto local_samples_begin = local_samples[i].begin();
    auto local_succ_indices_begin = local_succ_indices[i].begin();
    for (int j = 0; j < requests[i].size(); ++j) {
      auto& request = requests[i][j];
      auto callback = [&, this, local_samples_begin, local_succ_indices_begin, fs_context,
                       index_begin](const grpc::Status& status, IndexResponse* response) {
        if (!CheckItemServerRespValid(status, request, response)) {
          fake_item_count.fetch_add(request.creative_id_size());
          return;
        }
        int local_fake_item_count = 0;
        for (int k = 0; k < response->creative_info_size(); ++k) {
          int32_t index = *(index_begin + k);
          uint64_t item_id = request.creative_id(k);
          auto& serialized_data = response->creative_info(k).value();
          if (serialized_data.empty()) {
            ++local_fake_item_count;
            LOG_EVERY_N(INFO, FLAGS_fake_log_freq)
                << "item id not found, will fake it: " << item_id << " llsid "
                << ps_request.llsid() << " page_id " << ps_request.context().page_id()
                << " sub_page_id " << ps_request.context().sub_page_id() << " pos_id "
                << ps_request.context().pos_id() << " type: " << type;
            continue;
          }

          auto decompressed_str = std::make_shared<std::string>();
          if (!DecompressStr(item_id, serialized_data, decompressed_str.get())) {
            ++local_fake_item_count;
            LOG_EVERY_N(ERROR, 1000) << "decompressed str failed";
            falcon::Inc("dnn_predict_server.pb_cache_except", 1);
            falcon::Inc("dnn_predict_server.flatten_cache_except", 1);
            continue;
          }
          auto item_batched_samples =
              flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*decompressed_str);
          if (item_batched_samples == nullptr ||
              item_batched_samples->samples() == nullptr) {
            ++local_fake_item_count;
            LOG_EVERY_N(WARNING, 10000) << "StringToTable failed for batched samples";
            continue;
          }

          *(local_samples_begin + k) = item_batched_samples->samples();
          *(local_succ_indices_begin + k) = index;
          if (use_component_item_) {
            fs_context->bs_component_item_holders[type][find_item_id(index)] =
                decompressed_str;
          } else {
            fs_context->item_info_strs[index] = decompressed_str;
          }
          info.item_store->TryAddStoreData<std::shared_ptr<std::string>>(
              static_cast<int64_t>(item_id),
              {static_cast<int64_t>(fs_context->req_time), decompressed_str});
        }
        fake_item_count.fetch_add(local_fake_item_count);
      };
      index_begin += request.creative_id_size();
      local_samples_begin += request.creative_id_size();
      local_succ_indices_begin += request.creative_id_size();
      auto fut = stub->AsyncGetCreative(options, request, &responses[i][j],
                                        info.item_server_event_loop->SelectOne());
      batch_waiter.Add(fut,
                       [callback](const grpc::Status& status, IndexResponse* response) {
                         callback(status, response);
                       });
    }
  }
  batch_waiter.Wait();

  for (int i = 0; i < local_samples.size(); ++i) {
    for (int j = 0; j < local_samples[i].size(); ++j) {
      if (local_samples[i][j] != nullptr) {
        int index = local_succ_indices[i][j];
        samples->emplace_back(local_samples[i][j]);
        succ_indices->emplace_back(index);
      }
    }
  }

  falcon::Inc("feature_server.fake_remote_item_count", fake_item_count.load());
  falcon::Inc("feature_server.fake_remote_flatten_item_count", fake_item_count.load());
  double fake_rate = 1e6 * fake_item_count.load() / ps_request.item_id_size();
  falcon::Stat("feature_server.fake_remote_item_rate", fake_rate);
  falcon::Stat(
      base::StringPrintf("feature_server.component_item_%d_fake_remote_item_rate", type)
          .c_str(),
      fake_rate);
  falcon::Stat("feature_server.fake_remote_flatten_item_rate", fake_rate);
  return true;
}

bool FeatureServer::ConstructBatchedSamples_I18N(FeatureServerContext* req_context) {
  auto req = req_context->request;
  auto samples_vec = std::make_unique<std::vector<const FeatureSets*>>();
  req_context->item_info_strs.resize(req->item_id_size());
  auto start = Clock::now();
  for (int i = 0; i < req->item_id_size(); i++) {
    auto item_id = req->item_id(i);
    auto& item_info_str = req_context->item_info_strs[i];
    if (!GetFlattenItem(item_id, req_context->req_time, ItemInfoType::FULL,
                        &item_info_str) ||
        item_info_str == nullptr || item_info_str->empty()) {
      falcon::Inc("feature_server.load_flatten_item_data_fail", 1);
      req_context->faked_flatten_item_indices.insert(i);
      if (fake_bs_miss_item_) {
        samples_vec->emplace_back(nullptr);
      }
      LOG_EVERY_N(WARNING, 100000)
          << "fake item: " << item_id
          << ", caused by nullptr: " << (item_info_str == nullptr);
      continue;
    }

    auto item_batched_samples =
        flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*item_info_str, true);
    if (item_batched_samples == nullptr || item_batched_samples->samples() == nullptr) {
      req_context->faked_flatten_item_indices.insert(i);
      if (fake_bs_miss_item_) {
        samples_vec->emplace_back(nullptr);
      }
      continue;
    }

    samples_vec->emplace_back(item_batched_samples->samples());
  }
  falcon::Stat("feature_server.add_bs_item_cost", GetMicroseconds(start));

  auto construct_user_start = Clock::now();
  std::vector<size_t> sample_lens = {samples_vec->size()};
  auto& batched_samples = req_context->batched_samples;
  const AttrNameCollection* attr_name_ptr =
      (use_bs_fast_feature_ ? nullptr : &dag_bs_extractor_->GetAttrNames());
  batched_samples.reset(new DynamicBatchedSamples(attr_name_ptr, nullptr, nullptr,
                                                  std::move(samples_vec), sample_lens));

  if (!i18n_user_manager_.FillUserInfo(
          *req, &req_context->user_info_strings, &req_context->attr_real_time_map,
          batched_samples.get(), req->enable_fake_user_info())) {
    falcon::Inc("feature_server.fill_user_info_fail", 1);
    return false;
  }
  falcon::Stat("feature_server.add_bs_user_cost", GetMicroseconds(construct_user_start));
  auto init_start = Clock::now();
  if (!batched_samples->Initialize()) {
    falcon::Inc("feature_server.init_batched_samples_fail", 1);
    LOG(ERROR) << "batched samples init failed";
    return false;
  }
  falcon::Stat("feature_server.init_bs_cost", GetMicroseconds(init_start));

  return true;
}

bool FeatureServer::DecompressBsUserInfo(
    const std::string& source, std::shared_ptr<const std::string>* target) const {
  std::unique_ptr<std::string> decompressed_target(new std::string());
  if (source.size() > 0) {
    decompressed_target->clear();
    int decompress_ret = zstd_decompress(reinterpret_cast<const void*>(source.data()),
                                         source.size(), *decompressed_target);
    if (decompress_ret <= 0) {
      decompressed_target->clear();
      target->reset(decompressed_target.release());
      return false;
    }
  }
  target->reset(decompressed_target.release());
  return true;
}

bool FeatureServer::AddBSAdUserInfo(FeatureServerContext* req_context) {
  // auto start = Clock::now();
  const FeatureSets* ad_user_info_sub_context = nullptr;
  DynamicBatchedSamples* batched_samples = nullptr;
  batched_samples = req_context->batched_samples.get();

  if (ConvertAdUserInfoToFeatureSets(*req_context->request, req_context,
                                     &ad_user_info_sub_context)) {
    if (!batched_samples->AddSubContextAttrs(added_bs_attr_keys::kBsAdUserProfile,
                                             ad_user_info_sub_context)) {
      LOG_EVERY_N(WARNING, 100000) << "AddSubContextAttrs from ad_user_info failed";
      return false;
    }
  } else {
    falcon::Inc("feature_server.convert_flatten_ad_user_info_fail", 1);
    LOG_EVERY_N(WARNING, 100000) << "ConvertAdUserInfoToFeatureSets failed";
    return false;
  }
  // falcon::Stat("feature_server.construct_bs_add_bs_ad_user_info_cost",
  // GetMicroseconds(start));
  return true;
}

bool FeatureServer::AddBSUserRealtimeAction(FeatureServerContext* req_context) {
  const FeatureSets* user_real_time_action = nullptr;
  DynamicBatchedSamples* batched_samples = req_context->batched_samples.get();
  // auto add_bs_user_realtime_action_start = Clock::now();
  if (ConvertUserRealtimeAction(req_context, &user_real_time_action)) {
    if (!batched_samples->AddSubContextAttrs(added_bs_attr_keys::kBsAdUserRealTimeAction,
                                             user_real_time_action)) {
      LOG_EVERY_N(WARNING, 100000)
          << "AddSubContextAttrs from " << added_bs_attr_keys::kBsAdUserRealTimeAction
          << " failed";
      return false;
    }
  } else {
    falcon::Inc("feature_server.convert_flatten_user_real_time_fail", 1);
    LOG_EVERY_N(WARNING, 100000) << "ConvertUserRealtimeAction failed";
    return false;
  }
  // falcon::Stat("feature_server.construct_bs_add_bs_user_realtime_action_cost",
  //              GetMicroseconds(add_bs_user_realtime_action_start));
  return true;
}

bool FeatureServer::AddBSUserByTransfer(FeatureServerContext* req_context) {
  req_context->req_time = base::GetTimestamp() / 1000000;
  auto req = req_context->request;
  auto get_user_info = [](const std::string& log_str,
                          const std::string& info_str) -> const BatchedSamples* {
    if (info_str.empty()) {
      LOG_EVERY_N(INFO, 100000) << "Empty str for " << log_str;
      return nullptr;
    }
    auto ret = flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(info_str);
    if (ret == nullptr) {
      LOG_EVERY_N(WARNING, 100000) << "Convert string to batched_samples failed for "
                                   << log_str << " size: " << info_str.size();
    }
    return ret;
  };

  auto daily_batched_samples =
      get_user_info("daily_user_info", *req_context->transfer_bs_user_info);
  auto minute_batched_samples = daily_batched_samples;

  if (daily_batched_samples == nullptr && minute_batched_samples == nullptr) {
    LOG_EVERY_N(ERROR, 100000)
        << "Both daily and minute user info batched samples is a nullptr";
    return false;
  }

  auto get_sub_context = [](const std::string& log_str,
                            const BatchedSamples* bs) -> const FeatureSets* {
    if (bs != nullptr) {
      auto sub_context_vec = bs->sub_context();
      if (!sub_context_vec) {
        LOG(ERROR) << "sub context vector is nullptr for " << log_str;
        return nullptr;
      }

      if (sub_context_vec->size() != 1) {
        LOG(ERROR) << "sub context vector size invalid, size: " << sub_context_vec->size()
                   << " for " << log_str;
        return nullptr;
      }
      return sub_context_vec->Get(0);
    }
    return nullptr;
  };
  const FeatureSets* minute_sub_context =
      get_sub_context("minute_user_info", minute_batched_samples);
  const FeatureSets* context = nullptr;
  if (daily_batched_samples != nullptr) {
    context = daily_batched_samples->context();
    if (context == nullptr) {
      LOG(ERROR) << "context is nullptr";
      return false;
    }
  }
  req_context->batched_samples->SetContext(context);
  req_context->batched_samples->SetSubContext(minute_sub_context);

  return true;
}

bool FeatureServer::AddBSRecoUserInfo(FeatureServerContext* req_context) {
  if (req_flatten_reco_user_info_) {
    auto req = req_context->request;
    auto is_unlogin_user = predict_service_->IsUnloginUser(*req);
    const auto& user_id = req->user_id();
    const auto& device_id = req->ad_user_info().device_id();
    std::string key;

    if (is_unlogin_user) {
      key = device_id;
    } else {
      key = std::to_string(user_id);
    }
    std::unique_ptr<std::string> reco_cofea_res(new std::string());
    bool reco_ret = predict_service_->GetFlattenRecoUserInfo(is_unlogin_user, key,
                                                             reco_cofea_res.get());
    if (!reco_ret) {
      falcon::Inc("feature_server.load_flatten_reco_user_data_fail", 1);
      return false;
    }

    auto& reco_cofea_res_str = req_context->user_info_strings["reco_user"];
    reco_cofea_res_str.reset(reco_cofea_res.release());

    if (!AddBSRecoUserInfoInner(*reco_cofea_res_str, req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "AddBSRecoUserInfoInner failed";
      return false;
    }
  }

  return true;
}

bool FeatureServer::AddBSRecoUserInfoInner(const std::string& reco_cofea_res_str,
                                           FeatureServerContext* req_context) {
  if (reco_cofea_res_str.empty()) {
    LOG_EVERY_N(WARNING, 100000) << "Flatten reco user info is empty";
    return false;
  }

  const ks::cofea_fbs::CofeaResponse* reco_user_info_res = nullptr;
  reco_user_info_res =
      flatten::StringToTable<ks::cofea_fbs::CofeaResponse>(reco_cofea_res_str);
  if (reco_user_info_res == nullptr) {
    LOG(ERROR) << "Transfer reco user info str to CofeaResponse object failed";
    return false;
  }

  DynamicBatchedSamples* batched_samples = req_context->batched_samples.get();

  for (const auto& raw_feature : *reco_user_info_res->features()) {
    if (raw_feature == nullptr) {
      LOG(ERROR) << "Raw feature is a nullptr";
      return false;
    }
    auto flatten_name = raw_feature->name();
    if (flatten_name == nullptr) {
      LOG(ERROR) << "name is a nullptr in raw feature";
      return false;
    }
    const auto& mapping = AttrNameMapping::Instance().Name2IdStrMapping();
    std::string prefixed_name = base::StringPrintf(
        "%s.%s", "adlog.serialized_reco_user_info", flatten_name->str().c_str());

    auto iter = mapping.find(prefixed_name);
    if (iter != mapping.end()) {
      if (!batched_samples->SetSubContextAttrByColumn(iter->second, raw_feature)) {
        LOG_EVERY_N(WARNING, 100000) << "Add reco user info attr failed";
        continue;
      }
    } else {
      LOG_EVERY_N(INFO, 10000000)
          << "The reco user info attr is not found in mapping"
          << ", name: " << flatten_name->str() << ", prefixed name: " << prefixed_name;
      continue;
    }
  }
  return true;
}

bool FeatureServer::AddBSReqSubContext(FeatureServerContext* req_context) {
  auto start = Clock::now();
  DynamicBatchedSamples* batched_samples = nullptr;
  batched_samples = req_context->batched_samples.get();
  const FeatureSets* req_sub_context = nullptr;
  if (GetRequestSubContext(*req_context->request, req_context, &req_sub_context)) {
    if (!batched_samples->AddSubContextAttrs(added_bs_attr_keys::kBsReqSubContext,
                                             req_sub_context)) {
      falcon::Inc("feature_server.set_request_sub_context_fail", 1);
      LOG_EVERY_N(WARNING, 100000) << "Set request sub context failed";
      return false;
    }
  } else {
    LOG_EVERY_N(WARNING, 100000) << "get flatten request sub context failed";
    falcon::Inc("feature_server.get_request_sub_context_fail", 1);
    return false;
  }
  falcon::Stat("feature_server.construct_bs_add_bs_request_sub_context_cost",
               GetMicroseconds(start));
  return true;
}

bool FeatureServer::AddPB2BSAttrRealTimeActionMapFromReq(
    FeatureServerContext* req_context) {
  auto req = req_context->request;
  if (req->attr_real_time_action_size() <= 0) {
    LOG_EVERY_N(WARNING, 100) << "need transform pb maplist but it's empty. size: "
                              << req->attr_real_time_action_size();
    return true;
  }
  auto batched_samples = req_context->batched_samples;
  auto builder_iter = req_context->builders.find("pb_bs_maplist");
  if (builder_iter == req_context->builders.end()) {
    LOG_EVERY_N(ERROR, 1000) << "no realtime builder";
    return false;
  }
  auto& builder = *(builder_iter->second);
  falcon::Inc("feature_server.has_attr_real_time_action", 1);
  LOG_EVERY_N(INFO, 100000) << "request use pb attr realtime action, need merge, "
                            << "pb attr realtime action size: "
                            << req->attr_real_time_action_size();
  auto& attr_real_time_action = req->attr_real_time_action();

  std::vector<flatbuffers::Offset<ks::cofea_fbs::FeatureSet>> offset_vec;
  int64_t old_action_size = 0;
  const auto& mapping = DynamicBatchedSamples::StaticMapping();
  for (auto iter = attr_real_time_action.begin(); iter != attr_real_time_action.end();
       ++iter) {
    auto maplist_ptr = std::make_shared<xlib::picasso::pb::kv::MapList>();
    bool ret = maplist_ptr->ParseFromString(iter->second);
    if (!ret) {
      falcon::Inc("attr_real_time_action_maplist_parse_fail", 1);
      LOG_EVERY_N(ERROR, 1000) << "maplist parse fail, value map size: "
                               << attr_real_time_action.size() << ", ret: " << ret;
      continue;
    }
    auto int_list_map = maplist_ptr->mutable_map_list();
    for (auto int_list_ptr = int_list_map->begin(); int_list_ptr != int_list_map->end();
         int_list_ptr++) {
      const uint32_t attr_id = int_list_ptr->first;
      if (mapping.count(attr_id) > 0) {
        ++old_action_size;
      }
      if (prune_bs_attr_ &&
          bs_pruned_user_attr_ids_.find(attr_id) == bs_pruned_user_attr_ids_.end())
        continue;
      ::xlib::picasso::pb::kv::IntList& list = int_list_ptr->second;
      offset_vec.emplace_back();
      bool ret = ks::ad_nn::flatten::PrimitiveListToFeatureSet(
          base::Uint64ToString(attr_id), list.mutable_vals()->mutable_data(),
          list.vals_size(), &builder, &offset_vec.back());
      if (!ret) {
        LOG(ERROR) << "failed to convert to feature set. ";
        return false;
      }
    }
  }
  // add adlog.user_info.user_real_time_action.real_time_dsp_action_detail.size
  if (remap_bs_field_enum_) {
    offset_vec.emplace_back();
    std::string attr_id = "52614";
    bool ret = ks::ad_nn::flatten::PrimitiveToFeatureSet(attr_id, old_action_size,
                                                         &builder, &offset_vec.back());
    if (!ret) {
      LOG(ERROR) << "failed to convert to feature set. "
                 << ", attr_id=" << attr_id << ", value=" << old_action_size;
    }
  }
  flatbuffers::Offset<ks::cofea_fbs::FeatureSets> fss_offset =
      ks::cofea_fbs::CreateFeatureSetsDirect(builder, &offset_vec);
  builder.Finish(fss_offset);
  const ks::cofea_fbs::FeatureSets* temp = nullptr;
  char* buf = reinterpret_cast<char*>(builder.GetBufferPointer());
  temp = ks::ad_nn::flatten::StringToTable<FeatureSets>(buf, builder.GetSize());
  if (temp == nullptr) {
    falcon::Inc("feature_server.pb_attr_real_time_action_to_table_fail", 1);
    LOG_EVERY_N(WARNING, 10000) << "this pb attr real time featuresets is nullptr, "
                                << "data size: " << builder.GetSize();
  } else {
    if (!batched_samples->AddSubContextAttrs(
            added_bs_attr_keys::kBsAdUserRealTimeActionAttrMap, temp)) {
      // if (!batched_samples->AddFeaturesets(*temp,
      // ks::ad_nn::flatten::AttrType::kContext)) {
      falcon::Inc("feature_server.add_pb_attr_real_time_featuresets_fail", 1);
      LOG_EVERY_N(WARNING, 100000) << "add pb attr real time failed.";
    } else {
      falcon::Inc("feature_server.add_pb_attr_real_time_featuresets_success", 1);
    }
  }
  // falcon::Stat("feature_server.construct_bs_add_pb2bs_attr_realtime_action_map_cost",
  //              GetMicroseconds(add_pb2bs_attr_realtime_action_map_start));
  return true;
}

bool FeatureServer::AddBSAttrRealTimeActionMapFromReq(FeatureServerContext* req_context) {
  auto req = req_context->request;
  auto batched_samples = req_context->batched_samples;
  if (req->flatten_attr_real_time_action_size() > 0) {
    falcon::Inc("feature_server.has_flatten_attr_real_time_action", 1);
    LOG_EVERY_N(INFO, 100000) << "request has bs attr realtime action, need merge, "
                              << "bs attr realtime action size: "
                              << req->flatten_attr_real_time_action_size();
    auto& flatten_attr_real_time_action = req->flatten_attr_real_time_action();
    for (auto iter = flatten_attr_real_time_action.begin();
         iter != flatten_attr_real_time_action.end(); ++iter) {
      const ks::cofea_fbs::FeatureSets* temp = nullptr;
      falcon::Inc("feature_server.flatten_attr_real_time_action_to_table", 1);
      temp = ks::ad_nn::flatten::StringToTable<ks::cofea_fbs::FeatureSets>(
          iter->second.data(), iter->second.size());
      if (temp == nullptr) {
        falcon::Inc("feature_server.flatten_attr_real_time_action_to_table_fail", 1);
        LOG_EVERY_N(WARNING, 10000)
            << "this attr real time featuresets is nullptr, "
            << "data key: " << iter->first << "data size: " << iter->second.size();
        continue;
      } else {
        if (!batched_samples->AddFeaturesets(*temp,
                                             ks::ad_nn::flatten::AttrType::kContext)) {
          falcon::Inc("feature_server.add_attr_real_time_featuresets_fail", 1);
          LOG_EVERY_N(WARNING, 100000) << "add attr real time failed" << iter->first;
        }
      }
    }
  }
  return true;
}

bool FeatureServer::AddBSAttrRealTimeActionMap(FeatureServerContext* req_context) {
  if (bs_need_pb_maplist_) {
    return AddPB2BSAttrRealTimeActionMapFromReq(req_context);
  } else if (req_context->request->flatten_attr_real_time_action_size() > 0) {
    return AddBSAttrRealTimeActionMapFromReq(req_context);
  }

  DynamicBatchedSamples* batched_samples = nullptr;
  batched_samples = req_context->batched_samples.get();

  auto& attr_real_time_map = req_context->attr_real_time_map;
  int attr_real_time_ret =
      predict_service_->GetFlattenAttrRealTime(*req_context->request, attr_real_time_map);
  if (attr_real_time_ret == 0) {
    for (auto iter = attr_real_time_map->begin(); iter != attr_real_time_map->end();
         iter++) {
      auto temp = flatten::StringToTable<ks::cofea_fbs::FeatureSets>(iter->second);
      // 用户行为长序列按天聚合， 目前放到 context 字段
      if (!batched_samples->AddFeaturesets(*temp,
                                           ks::ad_nn::flatten::AttrType::kContext)) {
        falcon::Inc("feature_server.add_attr_real_time_featuresets", 1);
        LOG_EVERY_N(WARNING, 10000) << "Set attr real_time sub context failed";
        return false;
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 10000) << "Get attr real time failed, skip add feturesets.";
    return false;
  }
  return true;
}

bool FeatureServer::AddBSItemByTransfer(FeatureServerContext* req_context) {
  auto samples_vec = std::make_unique<std::vector<const FeatureSets*>>();
  for (int i = 0; i < req_context->ad_log->item_size(); ++i) {
    bool fake = req_context->ad_log->IsFakedItem(i);
    if (fake) {
      if (fake_bs_miss_item_) {
        samples_vec->emplace_back(nullptr);
      }
      req_context->faked_flatten_item_indices.insert(i);
    } else {
      auto cur_item =
          reinterpret_cast<const ItemAdaptorV1*>(&req_context->ad_log->item(i));
      if (cur_item == nullptr) {
        req_context->faked_flatten_item_indices.insert(i);
        LOG_EVERY_N(WARNING, 10000) << "cur item is a nullptr, index: " << i;
        continue;
      }
      auto transfered_str = cur_item->GetTransferedStr();
      if (transfered_str == nullptr) {
        req_context->faked_flatten_item_indices.insert(i);
        if (fake_bs_miss_item_) {
          samples_vec->emplace_back(nullptr);
        }
        LOG_EVERY_N(WARNING, 10000)
            << "cur item's transfered str is a nullptr, index: " << i;
        continue;
      }
      auto item_batched_samples =
          flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*transfered_str);
      if (item_batched_samples == nullptr || item_batched_samples->samples() == nullptr) {
        if (fake_bs_miss_item_) {
          samples_vec->emplace_back(nullptr);
        }
        LOG_EVERY_N(WARNING, 100000)
            << "convert item batched samples failed, index: " << i;
        req_context->faked_flatten_item_indices.insert(i);
        continue;
      }
      req_context->real_items.emplace_back(&req_context->ad_log->item(i));
      samples_vec->emplace_back(item_batched_samples->samples());
    }
  }

  // LOG(INFO) << "[Debug] request item size: " << req_context->request->item_id_size()
  //           << " adlog item size: " << req_context->ad_log->item_size()
  //           << " bs size: " << samples_vec->size()
  //           << " faked bs item indices: " <<
  //           req_context->faked_flatten_item_indices.size();

  std::vector<size_t> sample_lens = {samples_vec->size()};
  auto& batched_samples = req_context->batched_samples;
  batched_samples->SetSamples(std::move(samples_vec));
  batched_samples->SetSampleLens(sample_lens);
  return true;
}

const BatchedSamples* FeatureServer::GetBSUserInfo(const std::string& log_str,
                                                   const std::string& info_str) {
  if (info_str.empty()) {
    LOG_EVERY_N(INFO, 100000) << "Empty str for " << log_str;
    return nullptr;
  }
  auto ret = flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(info_str);
  if (ret == nullptr) {
    LOG_EVERY_N(WARNING, 100000) << "Convert string to batched_samples failed for "
                                 << log_str << " size: " << info_str.size();
  }
  return ret;
}

const FeatureSets* FeatureServer::GetSubContext(const std::string& log_str,
                                                const BatchedSamples* bs) {
  if (bs != nullptr) {
    auto sub_context_vec = bs->sub_context();
    if (!sub_context_vec) {
      LOG(ERROR) << "sub context vector is nullptr for " << log_str;
      return nullptr;
    }

    if (sub_context_vec->size() != 1) {
      LOG(ERROR) << "sub context vector size invalid, size: " << sub_context_vec->size()
                 << " for " << log_str;
      return nullptr;
    }
    return sub_context_vec->Get(0);
  }
  return nullptr;
}

bool FeatureServer::AddBSUserDirect(FeatureServerContext* req_context) {
  auto construct_bs_user_direct_start = Clock::now();
  bool ret = true;
  if (use_full_bs_user_info_) {
    ret = AddFullBSUserDirect(req_context);
  } else {
    ret = AddMultiPartBSUserDirect(req_context);
  }
  falcon::Stat("feature_server.construct_bs_user_direct_cost",
               GetMicroseconds(construct_bs_user_direct_start));
  return ret;
}

bool FeatureServer::DealOptUserInfo(FeatureServerContext* req_context) {
  auto str = req_context->user_info_strings["bs_opt_userinfo"];
  auto simple_req_sub_context =
      ks::ad_nn::flatten::StringToTable<SimplifiedRawFeature>(str->data(), str->size());
  if (simple_req_sub_context == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to convert bs_opt_userinfo";
    return false;
  }
  auto fields = simple_req_sub_context->fields();
  if (fields == nullptr) {
    LOG_EVERY_N(ERROR, 100000) << "simple fields get failed.";
    return false;
  }
  std::shared_ptr<BSUserWrapper> bs_user_wrapper = std::make_shared<BSUserWrapper>();
  bs_user_wrapper->Init(bs_user_attr_id2idx_, true);
  bs_user_wrapper->BuildRawFeasSimple(*fields);
  auto single_ad_user_info_iter =
      req_context->user_info_strings.find("single_ad_user_info");
  if (single_ad_user_info_iter != req_context->user_info_strings.end() &&
      single_ad_user_info_iter->second) {
    auto single_ad_user_info = ks::ad_nn::flatten::StringToTable<SimplifiedRawFeature>(
        single_ad_user_info_iter->second->data(),
        single_ad_user_info_iter->second->size());
    if (single_ad_user_info == nullptr) {
      LOG_EVERY_N(WARNING, 10000) << "Failed to convert single_ad_user_info";
      return false;
    }
    auto ad_user_info_fields = single_ad_user_info->fields();
    if (ad_user_info_fields == nullptr) {
      LOG_EVERY_N(ERROR, 100000) << "simple ad_user_info_fields get failed.";
      return false;
    }
    falcon::Inc("bs_user_info.merge_single_ad_user_info", 1);
    bs_user_wrapper->BuildRawFeasSimple(*ad_user_info_fields);
  }
  auto& batched_samples = req_context->batched_samples;
  batched_samples->SetUserWrapper(bs_user_wrapper);
  return true;
}

bool FeatureServer::AddFullBSUserDirect(FeatureServerContext* req_context) {
  if (req_context->user_info_strings["bs_opt_userinfo"]) {
    falcon::Inc("feature_server.bs_opt_userinfo_router", 1);
    return DealOptUserInfo(req_context);
  } else {
    falcon::Inc("feature_server.no_bs_opt_router", 1);
  }
  auto req = req_context->request;
  // load user info
  // auto construct_full_bs_start = Clock::now();
  std::shared_ptr<const std::string> full_user_info_str(nullptr);
  bool req_has_flatten_user_info = req->has_flatten_user_info();
  size_t req_full_user_info_size =
      (req_has_flatten_user_info ? req->flatten_user_info().full_user_info().size() : 0);
  if (copy_req_client_ != nullptr && !use_opt_user_info_) {
    if (!ConvertUserInfo2BS(req_context)) {
      LOG_EVERY_N(WARNING, 100) << "ConvertUserInfo2Bs failed";
    }
    full_user_info_str.reset(req_context->transfer_bs_user_info.get(),
                             [](const std::string*) {});
  } else {
    // load user info
    // auto construct_full_bs_start = Clock::now();
    full_user_info_str = req_context->user_info_strings["full_user"];
    if (req_full_user_info_size > 0) {
      falcon::Inc("feature_server.full_user_info_from_router", 1);
      if (!DecompressBsUserInfo(req->flatten_user_info().full_user_info(),
                                &full_user_info_str)) {
        falcon::Inc("feature_server.decompress_full_user_info_failed", 1);
        LOG_EVERY_N(WARNING, 10000) << "Decompress full user info failed.";
        return false;
      }
    }
  }

  if (!full_user_info_str || full_user_info_str->empty()) {
    LOG_EVERY_N(WARNING, 100000)
        << "full user info is empty"
        << " full size: " << (full_user_info_str ? full_user_info_str->size() : 0)
        << " is nullptr: " << (full_user_info_str == nullptr)
        << ", user id: " << req->user_id() << ", is unlogin user: "
        << (req->has_ad_user_info() && req->ad_user_info().is_unlogin_user())
        << ", device id: " << req->ad_user_info().device_id()
        << ", app id: " << req->app_id()
        << " req has flatten_user_info: " << req_has_flatten_user_info
        << " req full user info size: " << req_full_user_info_size
        << " req->peer_ip: " << req_context->peer_ip;
    falcon::Inc("feature_server.empty_flatten_user_info", 1);
    return false;
  }
  auto full_batched_samples = GetBSUserInfo("full_bs_user", *full_user_info_str);

  if (full_batched_samples == nullptr) {
    LOG_EVERY_N(ERROR, 100000) << "full user info batched samples is a nullptr";
    return false;
  }

  // get user id in bs
  if (!GetAttrValueInBS(full_batched_samples, "full_bs", "0", flatten::AttrType::kContext,
                        &req_context->user_id_in_bs)) {
    LOG_EVERY_N(WARNING, 10000) << "Get user id in bs user info failed";
  }

  auto minute_sub_context = GetSubContext("full_bs_minute", full_batched_samples);
  const FeatureSets* context = full_batched_samples->context();
  if (context == nullptr) {
    LOG(ERROR) << "context is nullptr";
    return false;
  }

  DynamicBatchedSamples* batched_samples = nullptr;
  batched_samples = req_context->batched_samples.get();

  if (copy_req_client_) {
    (void)GetAttrValueInBS(full_batched_samples, "full_bs", "13562",
                           flatten::AttrType::kContext,
                           &req_context->copy_req_context.bs_user_build_time);
  }
  batched_samples->SetContext(context);
  batched_samples->SetSubContext(minute_sub_context);
  // falcon::Stat("feature_server.construct_bs_full_user_info_cost",
  // GetMicroseconds(construct_full_bs_start));
  return true;
}

bool FeatureServer::DiffI18nUserInfoRawFea(const UniversePredictRequest* req,
                                           const std::string* simple_userinfo_router,
                                           const std::string* simple_userinfo_fea) {
  if (simple_userinfo_router == nullptr || simple_userinfo_fea == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to convert bs_opt_userinfo";
    return false;
  }
  auto* exp_fields = ks::ad_nn::GetFields(*simple_userinfo_router, false);
  auto* base_fields = ks::ad_nn::GetFields(*simple_userinfo_fea, false);
  if (base_fields == nullptr || exp_fields == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to get fields";
    return false;
  }
  std::map<std::string, std::string> base_map;
  std::map<std::string, std::string> exp_map;
  for (int i = 0; i < base_fields->size(); ++i) {
    auto field = base_fields->Get(i);
    if (field == nullptr) {
      LOG_EVERY_N(INFO, 10000000) << "field is a nullptr, index: " << i;
      continue;
    }
    std::string fea_name{field->name()->c_str(), field->name()->size()};
    auto value = ks::ad_nn::DebugString(field);
    base_map[fea_name] = value;
  }
  for (int i = 0; i < exp_fields->size(); ++i) {
    auto field = exp_fields->Get(i);
    if (field == nullptr) {
      LOG_EVERY_N(INFO, 10000000) << "field is a nullptr, index: " << i;
      continue;
    }
    std::string fea_name{field->name()->c_str(), field->name()->size()};
    auto value = ks::ad_nn::DebugString(field);
    exp_map[fea_name] = value;
  }
  bool flag = true;
  int exp_not_found_count = 0;
  int base_not_found_count = 0;
  for (auto& iter : base_map) {
    if (exp_map.find(iter.first) == exp_map.end()) {
      flag = false;
      exp_not_found_count++;
      LOG(ERROR) << "Exp map Not found key:" << iter.first
                 << " base value: " << iter.second << " user id: " << req->user_id();
    } else {
      if (base_map[iter.first] != exp_map[iter.first]) {
        flag = false;
        LOG(ERROR) << "Exp map Diff key:" << iter.first
                   << " base value: " << base_map[iter.first]
                   << " exp value:" << exp_map[iter.first] << std::endl
                   << " user id: " << req->user_id();
      }
    }
  }
  for (auto& iter : exp_map) {
    if (base_map.find(iter.first) == base_map.end()) {
      flag = false;
      base_not_found_count++;
      LOG(ERROR) << "Base map Not found key:" << iter.first
                 << " exp value: " << iter.second << " user id: " << req->user_id();
    } else {
      if (base_map[iter.first] != exp_map[iter.first]) {
        flag = false;
        LOG(ERROR) << "Base map Diff key:" << iter.first
                   << " base value: " << base_map[iter.first]
                   << " exp value:" << exp_map[iter.first] << std::endl
                   << " user id: " << req->user_id();
      }
    }
  }
  if (flag) {
    LOG(ERROR) << "I18n user info raw fea equal" << std::endl
               << " user id: " << req->user_id() << " base map size: " << base_map.size()
               << " exp map size: " << exp_map.size();
  } else {
    LOG(ERROR) << "I18n user info raw fea not equal: " << req->llsid()
               << " uid: " << req->user_id()
               << " exp not found count: " << exp_not_found_count
               << " base not found count: " << base_not_found_count
               << " exp map size: " << exp_map.size()
               << " base map size: " << base_map.size();
  }
  return flag;
}

bool FeatureServer::AddMultiPartBSUserDirect(FeatureServerContext* req_context) {
  auto req = req_context->request;
  std::shared_ptr<BSUserWrapper> bs_user_wrapper = std::make_shared<BSUserWrapper>();
  static const folly::F14FastSet<int32_t> user_attr_set(bs_pruned_user_attr_ids_.begin(),
                                                        bs_pruned_user_attr_ids_.end());
  auto& merge_user_info_str = req_context->user_info_strings["bs_opt_userinfo"];
  falcon::Inc("feature_server.i18n_merge_userinfo", 1);
  // use feature user info if router user info is empty
  if (merge_user_info_str == nullptr ||
      (merge_user_info_str && merge_user_info_str->empty())) {
    std::unique_ptr<std::string> merge_user_info(new std::string());
    if (!i18n_user_manager_.FillUserInfoOpt(
            *req, user_attr_set, req->enable_fake_user_info(), merge_user_info.get())) {
      falcon::Inc("feature_server.fill_user_info_fail", 1);
      return false;
    }
    merge_user_info_str.reset(merge_user_info.release());
  } else if (user_info_diff_) {
    std::unique_ptr<std::string> merge_user_info(new std::string());
    i18n_user_manager_.FillUserInfoOpt(*req, user_attr_set, req->enable_fake_user_info(),
                                       merge_user_info.get());
    if (!DiffI18nUserInfoRawFea(req, merge_user_info_str.get(), merge_user_info.get())) {
      LOG_EVERY_N(WARNING, 10000) << "Failed to diff i18n user info raw fea";
      ks::infra::PerfUtil::IntervalLogStash(1, "ad.predict", "feature_server",
                                            "i18n_user_info_raw_fea_diff_count");
    } else {
      ks::infra::PerfUtil::IntervalLogStash(0, "ad.predict", "feature_server",
                                            "i18n_user_info_raw_fea_diff_count");
    }
  }
  auto simple_userinfo = ks::ad_nn::flatten::StringToTable<SimplifiedRawFeature>(
      merge_user_info_str->data(), merge_user_info_str->size());
  if (simple_userinfo == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to convert bs_opt_userinfo";
    return false;
  }
  auto fields = simple_userinfo->fields();
  if (fields == nullptr) {
    LOG_EVERY_N(ERROR, 100000) << "simple fields get failed.";
    return false;
  }
  bs_user_wrapper->Init(bs_user_attr_id2idx_, true);
  bs_user_wrapper->BuildRawFeasSimple(*fields);
  auto& batched_samples = req_context->batched_samples;
  batched_samples->SetUserWrapper(bs_user_wrapper);
  return true;
}

bool FeatureServer::AddPbRecoUserInfo(FeatureServerContext* req_context) {
  // auto add_pb_reco_user_info_start = Clock::now();
  bool has_reco_feature =
      (feature_extractor_ != nullptr ? feature_extractor_->HasRecoFeature()
                                     : feature_file_info_.has_reco_feature);
  bool parse_reco_user = has_reco_feature || need_parse_reco_user_info_;
  if (!use_bs_fast_feature_) {
    auto ad_log = req_context->ad_log.get();
    if (!predict_service_->AddRecoUserInfo(parse_reco_user, req_context->request,
                                           ad_log)) {
      LOG_EVERY_N(WARNING, 10000) << "Add pb reco user info failed";
      return false;
    }
  } else {
    auto bs_log = &req_context->bs_log;
    if (!predict_service_->AddRecoUserInfoForBS(parse_reco_user, req_context->request,
                                                bs_log)) {
      LOG_EVERY_N(WARNING, 10000) << "Add pb reco user info for bs failed";
      return false;
    }
  }
  // falcon::Stat("feature_server.construct_bs_add_reco_user_info_cost",
  //              GetMicroseconds(add_pb_reco_user_info_start));
  return true;
}

bool FeatureServer::AddFantopActionDirect(FeatureServerContext* req_context) {
  auto req = req_context->request;
  auto batched_samples = req_context->batched_samples.get();
  auto& fantop_user_info_str = req_context->user_info_strings["fantop_action"];
  bool req_has_flatten_user_info = req->has_flatten_user_info();
  size_t req_fantop_user_info_size =
      (req_has_flatten_user_info
           ? req->flatten_user_info().fanstop_action_list_with_tag().size()
           : 0);
  if (req_fantop_user_info_size > 0) {
    falcon::Inc("feature_server.fantop_action_from_router", 1);
    if (!DecompressBsUserInfo(req->flatten_user_info().fanstop_action_list_with_tag(),
                              &fantop_user_info_str)) {
      falcon::Inc("feature_server.decompress_fantop_user_info_failed", 1);
      LOG_EVERY_N(WARNING, 10000) << "Decompress fantop user info failed.";
      return false;
    }
  }

  if (!fantop_user_info_str || fantop_user_info_str->empty()) {
    LOG_EVERY_N(WARNING, 100000)
        << "fantop user info is empty"
        << " fantop size: " << (fantop_user_info_str ? fantop_user_info_str->size() : 0)
        << " is nullptr: " << (fantop_user_info_str == nullptr)
        << ", user id: " << req->user_id() << ", is unlogin user: "
        << (req->has_ad_user_info() && req->ad_user_info().is_unlogin_user())
        << ", device id: " << req->ad_user_info().device_id()
        << ", app id: " << req->app_id()
        << " req has flatten_user_info: " << req_has_flatten_user_info
        << " req fantop user info size: " << req_fantop_user_info_size
        << " req->peer_ip: " << req_context->peer_ip;
    falcon::Inc("feature_server.empty_fantop_action_flatten_user_info", 1);
    return false;
  }
  auto fantop_batched_samples = GetBSUserInfo("fantop_bs_user", *fantop_user_info_str);
  if (fantop_batched_samples == nullptr) {
    LOG_EVERY_N(ERROR, 100000) << "fantop user info batched samples is a nullptr";
    return false;
  }
  const FeatureSets* fantop_context = fantop_batched_samples->context();
  if (fantop_context == nullptr) {
    LOG(ERROR) << "context is nullptr";
    return false;
  }

  if (!batched_samples->AddSubContextAttrs(added_bs_attr_keys::kBsAdFantopAction,
                                           fantop_context)) {
    falcon::Inc("feature_server.set_request_sub_context_fail", 1);
    LOG_EVERY_N(WARNING, 100000) << "Set request sub context failed";
    return false;
  }
  return true;
}

bool FeatureServer::AddBSItemDirectWithRangeOpt(int32_t start_idx, int32_t end_idx,
                                                FeatureServerContext* req_context) {
  if (end_idx < start_idx) {
    LOG_EVERY_N(WARNING, 10000)
        << "End_index < start_index: "
        << " end_index: " << end_idx << " start_index: " << start_idx;
    return false;
  }
  auto add_bs_item_start = Clock::now();
  auto req = req_context->request;

  auto cur_size = end_idx - start_idx + 1;
  std::vector<int> faked_item_indices;
  thread_local std::vector<uint64_t> fake_id_cache_ids;
  thread_local std::vector<int> fake_id_cache_indexs;
  thread_local std::vector<bool> fake_id_cache_rets;
  fake_id_cache_indexs.clear();
  fake_id_cache_rets.clear();
  fake_id_cache_ids.clear();
  for (int i = 0; i < req->item_id_size() && i < cur_size; i++) {
    int32_t index = i + start_idx;
    auto item_id = req->item_id(index);
    std::shared_ptr<BSItemWrapper> item_info = nullptr;
    if (!GetFlattenItemOpt(item_id, req_context->req_time, ItemInfoType::FULL,
                           &item_info) ||
        item_info == nullptr) {
      falcon::Inc("feature_server.load_flatten_item_data_fail", 1);
      if (fake_id_cache_ && req_context->fake_item_expire_time_us > 0) {
        fake_id_cache_indexs.emplace_back(index);
        fake_id_cache_ids.emplace_back(item_id);
      } else {
        faked_item_indices.emplace_back(index);
      }
      LOG_EVERY_N(WARNING, 100000) << "fake item: " << item_id
                                   << ", caused by nullptr: " << (item_info == nullptr);
      continue;
    }
    req_context->bs_item_wrappers[index] = item_info;
  }
  if (fake_id_cache_ && req_context->fake_item_expire_time_us > 0 &&
      fake_id_cache_ids.size() > 0) {
    fake_id_cache_rets.resize(fake_id_cache_ids.size(), false);
    if (fake_id_cache_->GetFromCacheDirectly(fake_id_cache_ids,
                                             req_context->fake_item_expire_time_us,
                                             &fake_id_cache_rets) == 0) {
      for (int i = 0; i < fake_id_cache_rets.size(); i++) {
        if (!fake_id_cache_rets[i]) {
          faked_item_indices.emplace_back(fake_id_cache_indexs[i]);
        }
      }
    } else {
      LOG(ERROR) << "get fake item cache ret fail.";
      return false;
    }
  }

  req_context->AddCostStat("prepare_feature.add_bs_item_local", add_bs_item_start);

  std::vector<std::shared_ptr<BSItemWrapper>> temp_remote_samples;
  std::vector<int> succ_remote_indices;
  auto add_remote_bs_item_start = Clock::now();
  std::function<uint64_t(int i)> find_item_ids = [req](int i) -> uint64_t {
    if (req->item_id_size() > i) {
      return req->item_id(i);
    } else {
      LOG_EVERY_N(ERROR, 1000) << "index beyond the req item size: " << i;
      return 0;
    }
  };

  if (use_dist_item_service_) {
    bool skip_req_item = false;
    if (req_context->req_item_server_ratio < 100) {
      auto ratio = ad_base::AdRandom::GetInt(1, 100);
      if (ratio > req_context->req_item_server_ratio) {
        LOG_EVERY_N(INFO, 1000) << "req item server skipped, ratio: " << ratio
                                << ", expected: " << req_context->req_item_server_ratio;
        skip_req_item = true;
      }
    }
    if (!skip_req_item) {
      auto get_item_start = Clock::now();
      if (!access_clsdb_ad_item_) {
        if (!GetRemoteFlattenItemsOpt(ItemInfoType::FULL, faked_item_indices,
                                      find_item_ids, req_context, &temp_remote_samples,
                                      &succ_remote_indices)) {
          LOG_EVERY_N(WARNING, 100000) << "GetRemoteFlattenItemsOpt failed";
        }
      } else {
        if (!GetColossusDBFlattenItemsOpt(ItemInfoType::FULL, faked_item_indices,
                                          find_item_ids, req_context,
                                          &temp_remote_samples, &succ_remote_indices)) {
          LOG_EVERY_N(WARNING, 100000) << "GetColossusDBFlattenItemsOpt failed";
        }
      }
      falcon::Stat("feature_server.get_remote_item_cost",
                   GetMicroseconds(get_item_start));
    }
  }
  int64_t add_cost = GetMicroseconds(add_remote_bs_item_start);
  req_context->AddCostStat("prepare_feature.add_bs_item_remote", add_cost);
  // falcon::Stat("prepare_feature.add_bs_item_remote", add_cost);
  req_context->AddCostStat("prepare_feature.add_bs_item_remote.avg",
                           add_cost / (end_idx - start_idx + 1));
  // falcon::Stat("prepare_feature.add_bs_item_remote_avg", add_cost / (end_idx -
  // start_idx + 1));

  if (succ_remote_indices.size() == temp_remote_samples.size()) {
    for (int i = 0; i < temp_remote_samples.size(); ++i) {
      if (temp_remote_samples[i] != nullptr) {
        req_context->bs_item_wrappers[succ_remote_indices[i]] =
            std::move(temp_remote_samples[i]);
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 10000)
        << "succ_remote indices.size != temp_remote_samples.size"
        << " succ_remote_indices size: " << succ_remote_indices.size()
        << " temp_remote_samples size: " << temp_remote_samples.size();
  }
  falcon::Stat("feature_server.construct_bs_add_bs_item_async_cost",
               GetMicroseconds(add_bs_item_start));
  return true;
}

bool FeatureServer::ConvertItemContextWithRangeOpt(int32_t start_idx, int32_t end_idx,
                                                   FeatureServerContext* req_context) {
  if (KS_UNLIKELY(end_idx < start_idx || start_idx < 0)) {
    LOG_EVERY_N(ERROR, 1000) << "Invalid item_context_range index: "
                             << " end_index: " << end_idx
                             << " start_index: " << start_idx;
    return false;
  }
  if (KS_UNLIKELY(req_context == nullptr)) {
    LOG_EVERY_N(ERROR, 1000) << "req_context is nullptr";
    return false;
  }
  auto req = req_context->request;
  auto convert_item_common_attr_start = Clock::now();
  for (int context_idx = start_idx; context_idx <= end_idx; context_idx++) {
    if (KS_UNLIKELY(context_idx >= req_context->item_context_flatbuffer_builders.size() ||
                    context_idx >= req->item_context_size())) {
      LOG_EVERY_N(ERROR, 1000) << "bs_item_wrapper is invalid, context_idx: "
                               << context_idx
                               << ", item_context_size: " << req->item_context_size()
                               << ", item_context_flatbuffer_builders size: "
                               << req_context->item_context_flatbuffer_builders.size();
      continue;
    }
    auto& cur_item_context = req->item_context(context_idx);
    if (KS_UNLIKELY(cur_item_context.item_id() != req->item_id(context_idx))) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.predict", "feature_server",
                                         "item_common_attr_item_id_mismatch_count");
      LOG_EVERY_N(ERROR, 1000) << "item id of " << context_idx
                               << "-th item_context in request: "
                               << cur_item_context.item_id()
                               << " is not equal to item_id in request: "
                               << req->item_id(context_idx);
      continue;
    }
    std::vector<flatbuffers::Offset<ks::ad_nn::simplified_raw_fea::Field>> offsets;
    auto& cur_flatbuffer_builder =
        req_context->item_context_flatbuffer_builders[context_idx];
    cur_flatbuffer_builder = std::make_shared<flatbuffers::FlatBufferBuilder>();
    LOG_FIRST_N(INFO, 20) << "debug item_context: " << cur_item_context.DebugString();
    if (!ConvertSingleItemContext(cur_item_context, cur_flatbuffer_builder, &offsets)) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.predict", "feature_server",
                                         "convert_item_common_attr_failed");
      LOG_EVERY_N(ERROR, 1000) << "ConvertSingleItemContext failed";
      cur_flatbuffer_builder = nullptr;
      continue;
    }
    if (offsets.size() == 0) {
      ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                         "item_common_attr_empty");
      LOG_EVERY_N(WARNING, 1000) << "item_common_attr is empty";
      cur_flatbuffer_builder = nullptr;
      return false;
    }
    if (build_photo_item_id_from_request_) {
      offsets.emplace_back();
      // adlog.item.id
      ks::ad_nn::GenerateField((long int)cur_item_context.item_id(), "17869",  // NOLINT
                               cur_flatbuffer_builder.get(), &offsets.back());
      if (context_idx < req->item_id_info_size()) {
        offsets.emplace_back();
        // adlog.item.ad_dsp_info.photo_info.id
        ks::ad_nn::GenerateField(
            (long int)req->item_id_info(context_idx).photo_id(),  // NOLINT
            "22372", cur_flatbuffer_builder.get(), &offsets.back());
      } else {
        LOG_EVERY_N(ERROR, 100)
            << context_idx
            << " -th item_id_info is not found in request, item_id_info size: "
            << req->item_id_info_size();
      }
    }
    auto simplified_raw_fea_offset =
        ks::ad_nn::simplified_raw_fea::CreateSimplifiedRawFeatureDirect(
            *cur_flatbuffer_builder.get(), &offsets);
    cur_flatbuffer_builder->Finish(simplified_raw_fea_offset);
  }
  ks::infra::PerfUtil::IntervalLogStash(GetMicroseconds(convert_item_common_attr_start),
                                        "ad.predict", cmd_name_,
                                        "convert_item_common_attr_async_cost");
  return true;
}

bool FeatureServer::ConvertSingleItemContext(
    const kuaishou::ad::algorithm::ItemContext& cur_item_context,
    std::shared_ptr<flatbuffers::FlatBufferBuilder>& cur_flatbuffer_builder,
    std::vector<flatbuffers::Offset<ks::ad_nn::simplified_raw_fea::Field>>* offsets) {
  // https://kconf.corp.kuaishou.com/#/ad/algorithm_feature/featureConfigMap
  const auto& feature_config_map =
      ks::ad_algorithm::BSInfoStatic::Instance().GetFeatureMaps();
  // featureConfigMap 查询前缀
  static std::string feature_map_prefix = "adlog.item.item_common_attr.key:";
  if (!cur_item_context.item_common_attr().empty()) {
    for (int attr_idx = 0; attr_idx < cur_item_context.item_common_attr_size();
         attr_idx++) {
      auto& cur_item_attr = cur_item_context.item_common_attr(attr_idx);
      if (KS_UNLIKELY(!cur_item_attr.has_name_value())) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                           "common_attr_name_value_missed");
        LOG_EVERY_N(ERROR, 1000)
            << attr_idx << "-th item_common_attr's name_value in item_context is missing";
        continue;
      }
      auto cur_feature_map_key = base::StringPrintf("%s%d", feature_map_prefix.data(),
                                                    cur_item_attr.name_value());
      auto feature_map_iter = feature_config_map.find(cur_feature_map_key);
      if (KS_UNLIKELY(feature_map_iter == feature_config_map.end())) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                           "not_in_feature_config_map");
        LOG_EVERY_N(ERROR, 1000) << "feature map key: " << cur_feature_map_key
                                 << " in featureConfigMap is missing";
        continue;
      }
      if (KS_UNLIKELY(bs_pruned_sample_attr_ids_.find(feature_map_iter->second) ==
                      bs_pruned_sample_attr_ids_.end())) {
        ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                           "not_in_bs_pruned_sample_attr_ids");
        LOG_EVERY_N(WARNING, 100000)
            << "feature map key: " << cur_feature_map_key
            << " is in featureConfigMap but not in bs_pruned_sample_attr_ids_";
        continue;
      }
      switch (cur_item_attr.type()) {
      case CommonTypeEnum::INT_ATTR:
        if (KS_UNLIKELY(!cur_item_attr.has_int_value())) {
          LOG(ERROR) << attr_idx
                     << "-th item_common_attr's int_value in item_context is missing";
          ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                             "item_common_attr_data_empty");
          break;
        }
        offsets->emplace_back();
        ks::ad_nn::GenerateField(cur_item_attr.int_value(),
                                 std::to_string(feature_map_iter->second),
                                 cur_flatbuffer_builder.get(), &offsets->back());
        break;
      case CommonTypeEnum::FLOAT_ATTR:
        if (KS_UNLIKELY(!cur_item_attr.has_float_value())) {
          LOG(ERROR) << attr_idx
                     << "-th item_common_attr's float_value in item_context is missing";
          ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                             "item_common_attr_data_empty");
          break;
        }
        offsets->emplace_back();
        ks::ad_nn::GenerateField(cur_item_attr.float_value(),
                                 std::to_string(feature_map_iter->second),
                                 cur_flatbuffer_builder.get(), &offsets->back());
        break;
      case CommonTypeEnum::STRING_ATTR:
        if (KS_UNLIKELY(!cur_item_attr.has_string_value())) {
          LOG(ERROR) << attr_idx
                     << "-th item_common_attr's string_value in item_context is missing";
          ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                             "item_common_attr_data_empty");
          break;
        }
        offsets->emplace_back();
        ks::ad_nn::GenerateField(&cur_item_attr.string_value(), 1,
                                 std::to_string(feature_map_iter->second),
                                 cur_flatbuffer_builder.get(), &offsets->back());
        break;
      case CommonTypeEnum::INT_LIST_ATTR:
        if (KS_UNLIKELY(cur_item_attr.int_list_value().empty())) {
          LOG(ERROR) << attr_idx
                     << "-th item_common_attr's int_list_value in item_context is empty";
          ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                             "item_common_attr_data_empty");
          break;
        }
        offsets->emplace_back();
        ks::ad_nn::GenerateField(cur_item_attr.int_list_value().data(),
                                 cur_item_attr.int_list_value_size(),
                                 std::to_string(feature_map_iter->second),
                                 cur_flatbuffer_builder.get(), &offsets->back());
        break;
      case CommonTypeEnum::FLOAT_LIST_ATTR:
        if (KS_UNLIKELY(cur_item_attr.float_list_value().empty())) {
          LOG(ERROR)
              << attr_idx
              << "-th item_common_attr's float_list_value in item_context is empty";
          ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                             "item_common_attr_data_empty");
          break;
        }
        offsets->emplace_back();
        ks::ad_nn::GenerateField(cur_item_attr.float_list_value().data(),
                                 cur_item_attr.float_list_value_size(),
                                 std::to_string(feature_map_iter->second),
                                 cur_flatbuffer_builder.get(), &offsets->back());
        break;
      default:
        LOG(ERROR) << attr_idx
                   << "-th item_common_attr's data type: " << cur_item_attr.type()
                   << " is not surpported yet";
        ks::infra::PerfUtil::CountLogStash(1, "ad.predict", cmd_name_,
                                           "item_common_attr_data_type_not_surpported");
        break;
      }
    }
  }
  return true;
}

bool FeatureServer::AddBSItemDirectWithRange(int32_t start_idx, int32_t end_idx,
                                             FeatureServerContext* req_context) {
  if (end_idx < start_idx) {
    LOG_EVERY_N(WARNING, 10000)
        << "End_index < start_index: "
        << " end_index: " << end_idx << " start_index: " << start_idx;
    return false;
  }
  auto add_bs_item_start = Clock::now();
  auto req = req_context->request;
  auto cur_size = end_idx - start_idx + 1;
  std::vector<const FeatureSets*> temp_samples(req->item_id_size(), nullptr);
  std::vector<int> faked_item_indices;
  for (int i = 0; i < req->item_id_size() && i < cur_size; i++) {
    int32_t index = i + start_idx;
    auto item_id = req->item_id(index);
    auto& item_info_str = req_context->item_info_strs[index];
    if (!GetFlattenItem(item_id, req_context->req_time, ItemInfoType::FULL,
                        &item_info_str) ||
        item_info_str == nullptr || item_info_str->empty()) {
      falcon::Inc("feature_server.load_flatten_item_data_fail", 1);
      faked_item_indices.emplace_back(index);
      LOG_EVERY_N(WARNING, 100000)
          << "fake item: " << item_id
          << ", caused by nullptr: " << (item_info_str == nullptr);
      continue;
    }
    auto item_batched_samples =
        flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*item_info_str, true);
    if (item_batched_samples == nullptr || item_batched_samples->samples() == nullptr) {
      faked_item_indices.emplace_back(index);
      LOG_EVERY_N(WARNING, 100000) << "convert item batched samples failed, index: " << i;
      continue;
    }

    temp_samples[index] = item_batched_samples->samples();
  }

  std::vector<const FeatureSets*> temp_remote_samples;
  std::vector<int> succ_remote_indices;
  std::function<uint64_t(int i)> find_item_ids = [req](int i) -> uint64_t {
    if (req->item_id_size() > i) {
      return req->item_id(i);
    } else {
      LOG_EVERY_N(ERROR, 1000) << "index beyond the req item size: " << i;
      return 0;
    }
  };
  if (use_dist_item_service_) {
    if (!GetRemoteFlattenItems(ItemInfoType::FULL, faked_item_indices, find_item_ids,
                               req_context, &temp_remote_samples, &succ_remote_indices)) {
      LOG_EVERY_N(WARNING, 100000) << "GetRemoteFlattenItems failed";
    }
  }

  if (succ_remote_indices.size() == temp_remote_samples.size()) {
    for (int i = 0; i < temp_remote_samples.size(); ++i) {
      if (temp_remote_samples[i] != nullptr) {
        temp_samples[succ_remote_indices[i]] = temp_remote_samples[i];
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 10000)
        << "succ_remote indices.size != temp_remote_samples.size"
        << " succ_remote_indices size: " << succ_remote_indices.size()
        << " temp_remote_samples size: " << temp_remote_samples.size();
  }
  for (int i = 0; i < temp_samples.size(); ++i) {
    if (temp_samples[i] != nullptr) {
      (*req_context->bs_items)[i] = temp_samples[i];
    }
  }
  falcon::Stat("feature_server.construct_bs_add_bs_item_async_cost",
               GetMicroseconds(add_bs_item_start));
  return true;
}

bool FeatureServer::AddBSComponentItemDirect(
    ItemInfoType type, FeatureServerContext* req_context,
    folly::F14FastMap<uint64_t, BSItemInfo>* item_map) {
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  if (item_map->size() == 0) {
    LOG_EVERY_N(INFO, 1000000) << "type " << type << " req itemid count is 0.";
    return true;
  }
  auto samples = std::make_unique<std::vector<const FeatureSets*>>();
  std::vector<uint64_t> faked_item_ids;

  for (auto& map_iter : *item_map) {
    auto item_id = map_iter.first;
    auto& item_info_str = req_context->bs_component_item_holders[type][item_id];
    if (!GetFlattenItem(item_id, req_context->req_time, type, &item_info_str) ||
        item_info_str == nullptr || item_info_str->empty()) {
      falcon::Inc("feature_server.load_flatten_item_data_fail", 1);
      faked_item_ids.emplace_back(map_iter.first);
      LOG_EVERY_N(WARNING, 100000)
          << "fake item: " << item_id << ", type: " << type
          << ", caused by nullptr: " << (item_info_str == nullptr);
      continue;
    } else {
      LOG_EVERY_N(INFO, 100000) << "success get item: " << item_id << ", type: " << type
                                << ", caused by nullptr: " << (item_info_str == nullptr);
    }
    auto item_batched_samples =
        flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*item_info_str, false);
    if (item_batched_samples == nullptr || item_batched_samples->samples() == nullptr) {
      faked_item_ids.emplace_back(map_iter.first);
      LOG_EVERY_N(WARNING, 100000)
          << "convert item batched samples failed, item: " << map_iter.first
          << ", type: " << type;
      falcon::Inc("feature_server.flatten_item_data_fail", 1);
      continue;
    } else {
      falcon::Inc("feature_server.flatten_item_data_success", 1);
      LOG_EVERY_N(INFO, 100000)
          << "convert item batched samples success, item: " << map_iter.first
          << ", type: " << type;
    }
    map_iter.second.item_info = item_batched_samples->samples();
  }
  std::vector<const FeatureSets*> temp_remote_samples;
  std::vector<int> succ_remote_indices;
  std::function<uint64_t(int i)> find_item_ids = [&faked_item_ids](int i) -> uint64_t {
    if (faked_item_ids.size() > i) {
      return faked_item_ids[i];
    } else {
      LOG_EVERY_N(ERROR, 1000) << "index beyond the req item size: " << i;
      return 0;
    }
  };
  std::vector<int> indices(faked_item_ids.size());
  std::generate(indices.begin(), indices.end(), [n = 0]() mutable { return n++; });
  if (!GetRemoteFlattenItems(type, indices, find_item_ids, req_context,
                             &temp_remote_samples, &succ_remote_indices)) {
    LOG_EVERY_N(WARNING, 100000) << "GetRemoteFlattenItems failed";
  }

  if (succ_remote_indices.size() == temp_remote_samples.size()) {
    for (int i = 0; i < temp_remote_samples.size(); ++i) {
      if (temp_remote_samples[i] != nullptr) {
        auto map_iter = item_map->find(faked_item_ids[succ_remote_indices[i]]);
        if (map_iter == item_map->end()) {
          LOG(ERROR) << "uncorrect item id: " << type
                     << ", aim id: " << faked_item_ids[succ_remote_indices[i]];
        } else {
          map_iter->second.is_remote = true;
          map_iter->second.item_info = temp_remote_samples[i];
        }
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 10000)
        << "succ_remote indices.size != temp_remote_samples.size"
        << " succ_remote_indices size: " << succ_remote_indices.size()
        << " temp_remote_samples size: " << temp_remote_samples.size();
  }
  return true;
}

bool FeatureServer::AddBSComponentItemDirectOpt(
    ItemInfoType type, FeatureServerContext* req_context,
    folly::F14FastMap<uint64_t, BSItemInfo>* item_map) {
  auto* info_ptr = GetItemComponentInfo(type);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  std::vector<uint64_t> faked_item_ids;
  if (item_map->size() == 0) {
    LOG_EVERY_N(INFO, 1000000) << "type " << type << " req item id count is 0.";
    return true;
  }
  for (auto& map_iter : *item_map) {
    auto item_id = map_iter.first;
    auto& item_info_wrapper = map_iter.second.item_info_opt;
    if (!GetFlattenItemOpt(item_id, req_context->req_time, type, &item_info_wrapper) ||
        item_info_wrapper == nullptr || !item_info_wrapper->IsValid()) {
      item_info_wrapper.reset();
      faked_item_ids.emplace_back(item_id);
      LOG_EVERY_N(WARNING, 100000)
          << "fake item: " << item_id << ", type: " << type
          << ", caused by nullptr: " << (item_info_wrapper == nullptr);
      continue;
    }
    LOG_EVERY_N(INFO, 1000000) << "success get item: " << item_id << ", type: " << type;
  }
  falcon::Stat(base::StringPrintf("feature_server.component_item_%d_size", type).c_str(),
               faked_item_ids.size());
  std::vector<std::shared_ptr<BSItemWrapper>> temp_remote_samples;
  std::vector<int> succ_remote_indices;
  std::function<uint64_t(int i)> find_item_ids = [&faked_item_ids](int i) -> uint64_t {
    if (faked_item_ids.size() > i) {
      return faked_item_ids[i];
    } else {
      LOG_EVERY_N(ERROR, 1000) << "index beyond the req item size: " << i;
      return 0;
    }
  };
  std::vector<int> indices(faked_item_ids.size());
  std::generate(indices.begin(), indices.end(), [n = 0]() mutable { return n++; });
  auto get_item_start = Clock::now();
  if (!access_clsdb_ad_item_) {
    if (!GetRemoteFlattenItemsOpt(type, indices, find_item_ids, req_context,
                                  &temp_remote_samples, &succ_remote_indices)) {
      LOG_EVERY_N(WARNING, 100000) << "GetRemoteFlattenItemsOpt failed";
    }
  } else {
    if (!GetColossusDBFlattenItemsOpt(type, indices, find_item_ids, req_context,
                                      &temp_remote_samples, &succ_remote_indices)) {
      LOG_EVERY_N(WARNING, 100000) << "GetColossusDBFlattenItemsOpt failed";
    }
  }
  falcon::Stat(base::StringPrintf("feature_server.get_remote_item_%d_cost", type).c_str(),
               GetMicroseconds(get_item_start));
  if (succ_remote_indices.size() == temp_remote_samples.size()) {
    for (int i = 0; i < temp_remote_samples.size(); ++i) {
      if (temp_remote_samples[i] != nullptr) {
        auto map_iter = item_map->find(faked_item_ids[succ_remote_indices[i]]);
        if (map_iter == item_map->end()) {
          LOG(ERROR) << "uncorrect item id: " << type
                     << ", aim id: " << faked_item_ids[succ_remote_indices[i]];
        } else {
          map_iter->second.is_remote = true;
          map_iter->second.item_info_opt = temp_remote_samples[i];
        }
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 10000)
        << "succ_remote indices.size != temp_remote_samples.size"
        << " succ_remote_indices size: " << succ_remote_indices.size()
        << " temp_remote_samples size: " << temp_remote_samples.size();
  }
  return true;
}

bool FeatureServer::PrepareBSComponentItem(FeatureServerContext* req_context) {
  auto req = req_context->request;
  int req_item_count = req->item_id_info_size();
  if (req->item_id_size() != req_item_count) {
    LOG_EVERY_N(ERROR, 1000) << "invaild item count, item_id:" << req->item_id_size()
                             << ", item_id_info: " << req_item_count;
    falcon::Inc("feature_server.item_id_info_error", 1);
    return false;
  }
  for (auto& map_iter : item_component_server_map_) {
    req_context->component_bs_items[map_iter.first].resize(req_item_count);
    if (!opt_bs_item_) {
      req_context->bs_component_item_holders[map_iter.first];
    }
  }
  auto& item_id_info = req->item_id_info();
  folly::F14FastMap<ItemInfoType, folly::F14FastMap<uint64_t, BSItemInfo>> ids_info;
  GenComponentInfo(req_context->request->item_id_info(), &ids_info,
                   use_strict_item_info_);
  for (auto& iter : ids_info) {
    falcon::Stat(
        base::StringPrintf("feature_server.component_item_%d_size", iter.first).c_str(),
        iter.second.size());
    AddBSComponentItemDirect(iter.first, req_context, &iter.second);
    CollectBSItemInfo(iter.first, iter.second, req_context);
  }
  std::vector<size_t> sample_lens;
  if (opt_bs_item_) {
    if (!TranslateItemOpt(req_context, &sample_lens)) {
      LOG_EVERY_N(ERROR, 1000) << "TranslateItemOpt failed";
      return false;
    }
  } else {
    if (!TranslateItem(req_context, &sample_lens)) {
      LOG_EVERY_N(ERROR, 1000) << "TranslateItem failed";
      return false;
    }
  }
  return true;
}

bool FeatureServer::AddBSItemDirect(FeatureServerContext* req_context) {
  auto samples = std::make_unique<std::vector<const FeatureSets*>>();
  auto req = req_context->request;
  std::vector<const FeatureSets*> temp_samples(req->item_id_size(), nullptr);
  req_context->item_info_strs.resize(req->item_id_size());

  std::vector<int> faked_item_indices;
  for (int i = 0; i < req->item_id_size(); i++) {
    auto item_id = req->item_id(i);
    auto& item_info_str = req_context->item_info_strs[i];
    if (!GetFlattenItem(item_id, req_context->req_time, ItemInfoType::FULL,
                        &item_info_str) ||
        item_info_str == nullptr || item_info_str->empty()) {
      falcon::Inc("feature_server.load_flatten_item_data_fail", 1);
      faked_item_indices.emplace_back(i);
      LOG_EVERY_N(WARNING, 100000)
          << "fake item: " << item_id
          << ", caused by nullptr: " << (item_info_str == nullptr);
      continue;
    }
    auto item_batched_samples =
        flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*item_info_str, true);
    if (item_batched_samples == nullptr || item_batched_samples->samples() == nullptr) {
      faked_item_indices.emplace_back(i);
      LOG_EVERY_N(WARNING, 100000) << "convert item batched samples failed, index: " << i;
      continue;
    }

    temp_samples[i] = item_batched_samples->samples();
  }

  std::vector<const FeatureSets*> temp_remote_samples;
  std::vector<int> succ_remote_indices;
  std::function<uint64_t(int i)> find_item_ids = [req](int i) -> uint64_t {
    if (req->item_id_size() > i) {
      return req->item_id(i);
    } else {
      LOG_EVERY_N(ERROR, 1000) << "index beyond the req item size: " << i;
      return 0;
    }
  };
  if (use_dist_item_service_) {
    if (!GetRemoteFlattenItems(ItemInfoType::FULL, faked_item_indices, find_item_ids,
                               req_context, &temp_remote_samples, &succ_remote_indices)) {
      LOG_EVERY_N(WARNING, 100000) << "GetRemoteFlattenItems failed";
    }
  }

  if (succ_remote_indices.size() == temp_remote_samples.size()) {
    for (int i = 0; i < temp_remote_samples.size(); ++i) {
      if (temp_remote_samples[i] != nullptr) {
        temp_samples[succ_remote_indices[i]] = temp_remote_samples[i];
      }
    }
  } else {
    LOG_EVERY_N(WARNING, 10000)
        << "succ_remote indices.size != temp_remote_samples.size"
        << " succ_remote_indices size: " << succ_remote_indices.size()
        << " temp_remote_samples size: " << temp_remote_samples.size();
  }

  samples->reserve(temp_samples.size());
  for (int i = 0; i < temp_samples.size(); ++i) {
    if (temp_samples[i] != nullptr) {
      samples->emplace_back(temp_samples[i]);
    } else {
      req_context->faked_flatten_item_indices.insert(i);
    }
  }

  std::vector<size_t> sample_lens = {samples->size()};
  auto& batched_samples = req_context->batched_samples;
  batched_samples->SetSamples(std::move(samples));
  batched_samples->SetSampleLens(sample_lens);
  return true;
}

// 所有 user 会用到的数据都需要在此处预先分配
void FeatureServer::GenerateUserHolderMap(FeatureServerContext* req_context) {
  req_context->user_info_strings["reco_user"];
  if (use_opt_user_info_) {
    auto& flatten_data_map = req_context->request->flatten_data_map();
    auto user_info_opt_iter = flatten_data_map.find("bs_opt_userinfo");
    if (user_info_opt_iter != flatten_data_map.end()) {
      req_context->user_info_strings["bs_opt_userinfo"].reset(
          &user_info_opt_iter->second, [](const std::string* p) {});
      req_context->builders.insert(
          {"custom_feature", std::make_shared<flatbuffers::FlatBufferBuilder>()});
      if (fill_ad_user_info_) {
        auto single_ad_user_info_iter = flatten_data_map.find("single_ad_user_info");
        if (single_ad_user_info_iter != flatten_data_map.end()) {
          req_context->user_info_strings["single_ad_user_info"].reset(
              &single_ad_user_info_iter->second, [](const std::string* p) {});
        }
      }
      return;
    } else {
      if (skip_router_req_) {
        auto convert_start = Clock::now();
        LOG_FIRST_N(INFO, 100) << "get skip router req.";
        std::string* str = new std::string();
        auto req = req_context->request;
        if (req->has_context()) {
          ks::ad_nn::data_converter::DataConverter::GetInstance()->Convert(
              req->context(), "adlog.context", str, true);
        }
        // 此处如果需要支持更多组件, 需要 merge 到 bs_opt_userinfo, 尽量别分散
        req_context->user_info_strings["bs_opt_userinfo"].reset(str);
        req_context->builders.insert(
            {"custom_feature", std::make_shared<flatbuffers::FlatBufferBuilder>()});
        falcon::Stat("feature_server.data_converter_time_cost",
                     GetMicroseconds(convert_start));
        return;
      }
      if (use_i18n_opt_) req_context->user_info_strings["bs_opt_userinfo"];
    }
  }
  req_context->user_info_strings["full_user"];
  req_context->builders.insert(
      {"req_sub_context", std::make_shared<flatbuffers::FlatBufferBuilder>()});
  req_context->builders.insert(
      {"pb_user_real_time_action", std::make_shared<flatbuffers::FlatBufferBuilder>()});
  req_context->builders.insert(
      {"pb_bs_maplist", std::make_shared<flatbuffers::FlatBufferBuilder>()});
  if (!req_context->request->flatten_user_info().fanstop_action_list_with_tag().empty()) {
    req_context->user_info_strings["fantop_action"];
  }
  if (use_data_converter_) {
    req_context->builders.insert(
        {"custom_feature", std::make_shared<flatbuffers::FlatBufferBuilder>()});
    req_context->user_info_strings["convert_realtime_action"];
    req_context->user_info_strings["convert_context"];
    req_context->user_info_strings["convert_ad_user_info"];
  }
}

bool FeatureServer::ConvertItemContext(FeatureServerContext* req_context) {
  // 对 item_context 中存放的实时 item 数据（item_common_attr）转化为 BS 数据格式
  auto req = req_context->request;
  if (KS_UNLIKELY(req->item_context_size() != req->item_id_size())) {
    LOG_EVERY_N(ERROR, 1000) << "llsid: " << req->llsid()
                             << " number of item_context in request: "
                             << req->item_context_size()
                             << " is not equal to the number of item_id in request: "
                             << req->item_id_size();
    return false;
  }
  if (!req->item_context().empty()) {
    // item_context 数量必须与 item 数量一致
    size_t req_item_count = req->item_id_size();
    req_context->item_context_flatbuffer_builders.resize(req_item_count, nullptr);
    size_t step = req_item_count;
    if (bs_build_item_concurrency_ > 0) {
      // item 数量太小或并发太高时进行修复
      if (bs_build_item_concurrency_ >= req_item_count) {
        step = req_item_count;
      } else {
        step = std::ceil(static_cast<float>(req_item_count) / bs_build_item_concurrency_);
      }
    }
    for (int start_idx = 0; start_idx < req_item_count; start_idx += step) {
      int end_idx = std::min(start_idx + step - 1, req_item_count - 1);
      req_context->convert_item_context_future.push_back(
          FeatureThreadResourceManager::GetIOThreadPool()->addFuture(
              [start_idx, end_idx, req_context, this]() {
                if (!ConvertItemContextWithRangeOpt(start_idx, end_idx, req_context)) {
                  LOG(ERROR) << "ConvertItemContextWithRangeOpt failed";
                }
              }));
    }
  }
  return true;
}

bool FeatureServer::AsyncConstructBSUser(FeatureServerContext* req_context) {
  LOG_EVERY_N(INFO, 100000) << "AsyncConstructBSUser";
  GenerateUserHolderMap(req_context);
  if (!FLAGS_use_bs_reco_userinfo) {
    falcon::Inc("feature_server.bs_cmd_use_pb_reco_userinfo", 1);
    req_context->bs_build_result_future.push_back(
        FeatureThreadResourceManager::GetIOThreadPool()->addFuture(
            [req_context, this]() { (void)AddPbRecoUserInfo(req_context); }));
    req_context->bs_build_task_count += 1;
  } else {
    LOG_FIRST_N(INFO, 100) << "use bs reco userinfo, skip use pb reco userinfo.";
  }
  req_context->bs_build_result_future.push_back(
      FeatureThreadResourceManager::GetIOThreadPool()->addFuture([req_context, this]() {
        req_context->async_bs_build_ret &= AddBSUserDirect(req_context);
      }));

  req_context->bs_build_task_count += 1;

  if (req_context->user_info_strings["bs_opt_userinfo"]) return true;

  req_context->bs_build_result_future.push_back(
      FeatureThreadResourceManager::GetIOThreadPool()->addFuture([req_context, this]() {
        if (!req_context->request->realtime_action().empty() &&
            convert_pb_real_time_action_) {
          (void)AddBSUserRealtimeAction(req_context);
        }
        (void)AddBSAttrRealTimeActionMap(req_context);
        (void)AddBSReqSubContext(req_context);
      }));

  req_context->bs_build_task_count += 1;
  if (!req_context->request->flatten_user_info().fanstop_action_list_with_tag().empty()) {
    req_context->bs_build_result_future.push_back(
        FeatureThreadResourceManager::GetIOThreadPool()->addFuture(
            [req_context, this]() { (void)AddFantopActionDirect(req_context); }));

    req_context->bs_build_task_count += 1;
  }
  return true;
}

bool FeatureServer::AddBSItemByTransferOpt(FeatureServerContext* req_context) {
  for (int i = 0; i < req_context->ad_log->item_size(); ++i) {
    bool fake = req_context->ad_log->IsFakedItem(i);
    if (fake) {
      req_context->faked_flatten_item_indices.insert(i);
    } else {
      auto cur_item =
          reinterpret_cast<const ItemAdaptorV1*>(&req_context->ad_log->item(i));
      if (cur_item == nullptr) {
        req_context->faked_flatten_item_indices.insert(i);
        LOG_EVERY_N(WARNING, 10000) << "cur item is a nullptr, index: " << i;
        continue;
      }
      auto transfered_str = cur_item->GetTransferedStr();
      if (transfered_str == nullptr) {
        req_context->faked_flatten_item_indices.insert(i);
        LOG_EVERY_N(WARNING, 10000)
            << "cur item's transfered str is a nullptr, index: " << i;
        continue;
      }
      auto item_batched_samples =
          flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(*transfered_str);
      if (item_batched_samples == nullptr || item_batched_samples->samples() == nullptr) {
        LOG_EVERY_N(WARNING, 100000)
            << "convert item batched samples failed, index: " << i;
        req_context->faked_flatten_item_indices.insert(i);
        continue;
      }
      req_context->real_items.emplace_back(&req_context->ad_log->item(i));
      auto bs_item_wrapper = std::make_shared<BSItemWrapper>();
      bs_item_wrapper->data_source = DataSource::DS_RPC;
      bs_item_wrapper->flat_str = *transfered_str;
      size_t bs_sample_attr_id_size = GetBSSampleAttrIdSize();
      if (!bs_item_wrapper->Init(bs_sample_attr_id_size, bs_sample_attr_id2idx_,
                                 prune_bs_attr_)) {
        LOG(ERROR) << "Init bs wrapper failed for item: " << i;
        continue;
      }
      req_context->bs_item_wrappers[i] = std::move(bs_item_wrapper);
    }
  }
  return true;
}

bool FeatureServer::AsyncConstructBSItem(FeatureServerContext* req_context) {
  auto req = req_context->request;
  size_t step = req->item_id_size();
  size_t req_item_count = req->item_id_size();

  if (use_component_item_ || convert_real_time_item_context_) {
    thread_local std::vector<std::shared_ptr<BSItemWrapper>> on_bs_item_wrappers;
    if (on_bs_item_wrappers.size() < req_item_count) {
      size_t bs_sample_attr_id_size = GetBSSampleAttrIdSize();
      for (int i = on_bs_item_wrappers.size(); i < req_item_count; i++) {
        on_bs_item_wrappers.emplace_back(std::make_shared<BSItemWrapper>());
        on_bs_item_wrappers[i]->SimpleInit(bs_sample_attr_id_size,
                                           bs_sample_attr_id2idx_);
      }
    }
    req_context->inner_bs_item_wrappers = &on_bs_item_wrappers;
    for (int i = 0; i < req_item_count; i++)
      (*req_context->inner_bs_item_wrappers)[i]->ClearWrapper();
  }

  if (use_component_item_) {
    if (req->item_id_info_size() != req_item_count) {
      LOG_EVERY_N(ERROR, 1000) << "invaild item count, item_id:" << req_item_count
                               << ", item_id_info: " << req->item_id_info_size();
      falcon::Inc("feature_server.item_id_info_error", 1);
      return false;
    }
    for (auto& map_iter : item_component_server_map_) {
      req_context->component_bs_items[map_iter.first].resize(req_item_count);
      if (!opt_bs_item_) {
        req_context->bs_component_item_holders[map_iter.first];
      }
    }
    req_context->pv_label.resize(req_item_count);
    if (skip_req_item_server_) return true;

    return AsyncPrepareBSComponentItem(req_context);
  }

  if (opt_bs_item_) {
    req_context->bs_item_wrappers.resize(req_item_count, nullptr);
  } else {
    req_context->item_info_strs.resize(req_item_count);
    req_context->bs_items = std::move(
        std::make_unique<std::vector<const FeatureSets*>>(req_item_count, nullptr));
  }
  if (skip_req_item_server_) return true;

  if (copy_req_cmd_ != "") {
    return AddBSItemByTransferOpt(req_context);
  }

  if (bs_build_item_concurrency_ > 0) {
    // the req->item_id_size is too small or the bs_build_item_concurrency_ is too big,
    // no concurrency
    if (bs_build_item_concurrency_ >= req_item_count) {
      step = req_item_count;
    } else {
      step = std::ceil(static_cast<float>(req_item_count) / bs_build_item_concurrency_);
    }
  }
  for (int start_idx = 0; start_idx < req_item_count; start_idx += step) {
    int end_idx = std::min(start_idx + step - 1, req_item_count - 1);
    req_context->bs_build_result_future.push_back(
        FeatureThreadResourceManager::GetIOThreadPool()->addFuture(
            [start_idx, end_idx, req_context, this]() {
              if (opt_bs_item_) {
                if (!AddBSItemDirectWithRangeOpt(start_idx, end_idx, req_context)) {
                  LOG_EVERY_N(WARNING, 100000) << "AddBSItemDirectWithRangeOpt failed";
                }
              } else {
                if (!AddBSItemDirectWithRange(start_idx, end_idx, req_context)) {
                  LOG_EVERY_N(WARNING, 100000) << "AddBSItemDirectWithRange failed";
                }
              }
            }));
    req_context->bs_build_task_count += 1;
  }
  return true;
}

bool FeatureServer::AsyncPrepareBSComponentItem(FeatureServerContext* req_context) {
  // 分割 各任务
  auto start = Clock::now();
  GenComponentInfo(req_context->request->item_id_info(), &(req_context->ids_info),
                   use_strict_item_info_);
  std::vector<folly::Future<folly::Unit>> prepare_result_future;
  prepare_result_future.reserve(req_context->ids_info.size());
  auto req = req_context->request;
  std::mutex pv_mutex;
  auto& pv_label = req_context->pv_label;
  for (auto& iter : req_context->ids_info) {
    auto component_type = iter.first;
    auto type_ids_info = &iter.second;
    prepare_result_future.push_back(
        FeatureThreadResourceManager::GetIOThreadPool()->addFuture(
            [req_context, &pv_label, component_type, type_ids_info, &pv_mutex, this]() {
              if (opt_bs_item_) {
                if (!AddBSComponentItemDirectOpt(component_type, req_context,
                                                 type_ids_info)) {
                  LOG_EVERY_N(WARNING, 100000) << "AddBSComponentItemDirectOpt failed"
                                               << ", type: " << component_type;
                }
              } else {
                if (!AddBSComponentItemDirect(component_type, req_context,
                                              type_ids_info)) {
                  LOG_EVERY_N(WARNING, 100000) << "AddBSComponentItemDirect failed"
                                               << ", type: " << component_type;
                }
              }
              CollectBSItemInfo(component_type, *type_ids_info, req_context, &pv_label,
                                &pv_mutex);
            }));
  }
  folly::collectAll(prepare_result_future).wait();
  falcon::Stat("feature_server.component_item_cost", GetMicroseconds(start));
  return true;
}

bool FeatureServer::CollectBSItemInfo(
    ItemInfoType type, const folly::F14FastMap<uint64_t, BSItemInfo>& item_infos,
    FeatureServerContext* req_context, std::vector<BSItemInfo>* label,
    std::mutex* pv_mutex) {
  auto& item_id_infos = req_context->request->item_id_info();
  for (int i = 0; i < item_id_infos.size(); i++) {
    if (use_strict_item_info_ &&
        !ks::ad_nn::NeedItemComponent(type, item_id_infos[i].item_type()))
      continue;
    uint64_t item_id = ks::ad_nn::GetComponentID(item_id_infos[i], type);
    if (item_id == 0) continue;  // 这里 miss 很正常, 因为是按配置组件全集取
    auto info_ptr = item_infos.find(item_id);
    if (info_ptr == item_infos.end()) {
      LOG_EVERY_N(ERROR, 100) << "invaild situation, not found item_id: " << item_id
                              << ", type: " << type
                              << " item_type: " << item_id_infos[i].item_type()
                              << " req detail: "
                              << req_context->request->item_id_info(i).DebugString();
    } else {
      LOG_EVERY_N(INFO, 1000000) << "found item_id: " << item_id << ", type: " << type
                                 << " item_type: " << item_id_infos[i].item_type();
      if (info_ptr->second.item_info_opt) {
        // 不考虑多线程加锁
        (*req_context->inner_bs_item_wrappers)[i]->MergeField(
            info_ptr->second.item_info_opt);
        if (info_ptr->second.is_remote) {
          std::lock_guard<std::mutex> lock(*pv_mutex);
          (*label)[i].is_remote = true;
        }
      } else {
        // 开启 use_strict_vaild_info 只要有组件 miss 就失败
        if (use_strict_vaild_info_) {
          std::lock_guard<std::mutex> lock(*pv_mutex);
          (*label)[i].is_miss = true;
        } else if (type == main_type_) {
          (*label)[i].is_miss = true;
        }
      }
    }
  }
  return true;
}

// 废弃
bool FeatureServer::CollectBSItemInfo(
    ItemInfoType type, const folly::F14FastMap<uint64_t, BSItemInfo>& item_infos,
    FeatureServerContext* req_context) {
  auto& item_id_infos = req_context->request->item_id_info();
  auto map_iter = req_context->component_bs_items.find(type);  // 存放数据
  if (map_iter == req_context->component_bs_items.end()) {
    LOG_EVERY_N(ERROR, 10000) << "check fail, need collect type: " << type
                              << ", but has no config for this type.";
    falcon::Inc("component_config_miss_type", 1);
    return false;
  }
  if (map_iter->second.size() != item_id_infos.size()) {
    LOG(ERROR) << "check fail, type: " << type
               << ", bs_items size: " << map_iter->second.size()
               << ", item_id_info size: " << item_id_infos.size();
    return false;
  }
  auto& vec = map_iter->second;
  for (int i = 0; i < item_id_infos.size(); i++) {
    if (use_strict_item_info_ &&
        !ks::ad_nn::NeedItemComponent(type, item_id_infos[i].item_type()))
      continue;
    uint64_t item_id = ks::ad_nn::GetComponentID(item_id_infos[i], type);
    auto info_ptr = item_infos.find(item_id);
    if (info_ptr == item_infos.end()) {
      // 此处只应该出现 id 为 0 的 miss 情况
      LOG_EVERY_N(WARNING, 1000000)
          << "not found item_id: " << item_id << ", type: " << type
          << " item_type: " << item_id_infos[i].item_type()
          << " req detail: " << req_context->request->item_id_info(i).DebugString();
    } else {
      LOG_EVERY_N(INFO, 1000000) << "found item_id: " << item_id << ", type: " << type
                                 << " item_type: " << item_id_infos[i].item_type();
      vec[i] = info_ptr->second;
    }
  }
  return true;
}

bool FeatureServer::AsyncInitBSSample(FeatureServerContext* req_context) {
  auto& batched_samples = req_context->batched_samples;
  auto req = req_context->request;
  size_t step = req->item_id_size();
  if (bs_init_item_concurrency_ > 0) {
    step = std::ceil(static_cast<float>(batched_samples->total_sample_len() /
                                        bs_init_item_concurrency_));
    if (step == 0) {
      step = batched_samples->total_sample_len();
    }
  }

  for (int start_idx = 0; start_idx < batched_samples->total_sample_len();
       start_idx += step) {
    int end_idx = std::min(start_idx + step - 1, batched_samples->total_sample_len() - 1);
    req_context->bs_init_result_future.push_back(
        FeatureThreadResourceManager::GetIOThreadPool()->addFuture([start_idx, end_idx,
                                                                    req_context, this]() {
          req_context->async_bs_init_ret &= InitBSSample(start_idx, end_idx, req_context);
        }));
    req_context->bs_init_task_count += 1;
  }

  return true;
}

bool FeatureServer::InitBSSample(int32_t start, int32_t end,
                                 FeatureServerContext* context) {
  auto init_bs_sample_start = Clock::now();
  auto& batched_samples = context->batched_samples;
  auto ret = batched_samples->InitSamples(start, end);
  falcon::Stat("feature_server.init_bs_sample_cost",
               GetMicroseconds(init_bs_sample_start));
  return ret;
}

bool FeatureServer::ConstructBSUser(FeatureServerContext* req_context) {
  GenerateUserHolderMap(req_context);
  if (copy_req_cmd_ != "") {
    LOG_EVERY_N(INFO, 100000) << "ConstructBSUser ByTransfer";
    if (!ConvertUserInfo2BS(req_context)) {
      LOG_EVERY_N(WARNING, 100) << "ConvertUserInfo2Bs failed";
      return false;
    }
    if (!AddBSUserByTransfer(req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "AddBSUserByTransfer failed";
      return false;
    }
  } else {
    auto construct_bs_user_start = Clock::now();
    if (!AddBSUserDirect(req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "AddBSUserDirect failed";
      return false;
    }
    falcon::Stat("feature_server.construct_bs_user_part_cost",
                 GetMicroseconds(construct_bs_user_start));
  }

  if (!AddPbRecoUserInfo(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "AddPbRecoUserinfo failed";
  }

  // convert pb real time action
  if (!req_context->request->realtime_action().empty() && convert_pb_real_time_action_ &&
      !AddBSUserRealtimeAction(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "AddBSRealTimeAction failed";
  }

  if (!AddBSAttrRealTimeActionMap(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "AddBSAttrRealTimeMap failed";
  }

  if (!AddBSAdUserInfo(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "AddBSAdUserInfo failed";
  }
  PrepareBsAdCallbackEvents(req_context);
  if (!AddBSReqSubContext(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "AddBSReqSubContext failed";
  }
  if (use_data_converter_ && !AddCustomFeature(req_context)) {
    LOG(ERROR) << "AddCustomFeature failed.";
  }
  return true;
}

bool FeatureServer::ConstructBSItem(FeatureServerContext* req_context) {
  auto start = Clock::now();
  if (copy_req_cmd_ != "") {
    LOG_EVERY_N(INFO, 100000) << "ConstructBSItem ByTransfer";
    if (!AddBSItemByTransfer(req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "AddBSItemByTransfer failed";
      return false;
    }
  } else {
    LOG_EVERY_N(INFO, 100000) << "ConstructBSItem Direct";
    if (use_component_item_) {
      if (!PrepareBSComponentItem(req_context)) {
        LOG_EVERY_N(WARNING, 10000) << "ConstructBSItem failed";
        return false;
      }
    } else if (!AddBSItemDirect(req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "AddBSItemDirect failed";
      return false;
    }
  }
  falcon::Stat("feature_server.construct_bs_item_part_cost", GetMicroseconds(start));
  return true;
}

bool FeatureServer::TranslateItem(FeatureServerContext* req_context,
                                  std::vector<size_t>* sample_lens) {
  int req_item_count = 0;
  auto req = req_context->request;
  auto& batched_samples = req_context->batched_samples;
  if (use_component_item_) {
    batched_samples->SetComponentItem(true);
    req_item_count = req->item_id_info_size();
    auto& item_info_map = req_context->component_bs_items;
    auto samples = std::make_unique<std::vector<std::vector<const FeatureSets*>>>(
        item_component_server_map_.size());
    for (auto& sample : *samples) {
      sample.reserve(req_item_count);
    }
    if (item_info_map.size() == 0) {
      LOG(ERROR) << "no bs item info."
                 << ", req item count: " << req_item_count
                 << " bs_items size: " << item_info_map.size();
      return false;
    }
    auto creative_ptr = item_info_map.find(ItemInfoType::CREATIVE);
    auto photo_ptr = item_info_map.find(ItemInfoType::PHOTO);
    auto live_ptr = item_info_map.find(ItemInfoType::LIVE);
    if (creative_ptr == item_info_map.end() || photo_ptr == item_info_map.end() ||
        live_ptr == item_info_map.end()) {
      LOG_EVERY_N(ERROR, 1000) << "Not have enough component, "
                               << ", has creative: "
                               << (creative_ptr == item_info_map.end())
                               << ", has photo: " << (photo_ptr == item_info_map.end())
                               << ", has live: " << (live_ptr == item_info_map.end());
      return false;
    }
    auto& creative_items = creative_ptr->second;
    auto& photo_items = photo_ptr->second;
    auto& live_items = live_ptr->second;
    bool has_creative = true, has_photo = true, has_live = true, is_vaild = false;
    for (int i = 0; i < req_item_count; ++i) {
      auto item_type = req->item_id_info(i).item_type();
      is_vaild = false;
      has_creative = creative_items[i].item_info == nullptr ? false : true;
      has_photo = photo_items[i].item_info == nullptr ? false : true;
      has_live = live_items[i].item_info == nullptr ? false : true;
      if (!use_strict_vaild_info_ && has_creative) {
        is_vaild = true;
      } else {
        switch (item_type) {
        case kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO:
          if (has_creative && has_photo) {
            is_vaild = true;
          }
          break;
        case kuaishou::ad::AdEnum_ItemType_ITEM_LIVE:
          if (has_creative && has_live) {
            is_vaild = true;
          }
          break;
        case kuaishou::ad::AdEnum_ItemType_ITEM_PHOTO_TO_LIVE:
          if (has_creative && has_photo && has_live) {
            is_vaild = true;
          }
          break;
        default:
          LOG(ERROR) << "unsupport item type: " << item_type;
        }
      }
      if (!is_vaild) {
        req_context->faked_flatten_item_indices.insert(i);
        LOG_EVERY_N(ERROR, 10000)
            << "a item is fake: " << req->item_id_info(i).cretive_id() << " "
            << req->item_id_info(i).photo_id() << " " << req->item_id_info(i).live_id();
      }
      if (is_vaild || fake_bs_miss_item_) {
        (*samples)[0].emplace_back(creative_items[i].item_info);
        (*samples)[1].emplace_back(photo_items[i].item_info);
        (*samples)[2].emplace_back(live_items[i].item_info);
      }
    }
    if ((*samples)[0].size() == 0) {
      LOG_EVERY_N(WARNING, 10000) << "No valid bs item";
      return false;
    }
    sample_lens->push_back((*samples)[0].size());
    batched_samples->SetSamples(std::move(samples));
  } else {
    auto samples = std::make_unique<std::vector<const FeatureSets*>>();
    samples->reserve(req_context->bs_items->size());
    for (int i = 0; i < req_context->bs_items->size(); ++i) {
      auto bs_item = (*req_context->bs_items)[i];
      if (bs_item != nullptr) {
        samples->emplace_back(bs_item);
      } else {
        if (fake_bs_miss_item_) {
          samples->emplace_back(nullptr);
        }
        req_context->faked_flatten_item_indices.insert(i);
      }
    }
    if (samples->size() == 0) {
      LOG_EVERY_N(WARNING, 10000) << "No valid bs item";
      return false;
    }
    sample_lens->emplace_back(samples->size());
    batched_samples->SetSamples(std::move(samples));
  }
  batched_samples->SetSampleLens(*sample_lens);
  return true;
}

bool FeatureServer::TranslateItemOpt(FeatureServerContext* req_context,
                                     std::vector<size_t>* sample_lens) {
  auto req = req_context->request;
  auto& batched_samples = req_context->batched_samples;
  std::vector<std::shared_ptr<ks::ad_nn::BSItemWrapper>> bs_item_wrappers;
  if (use_component_item_) {
    int req_item_count = req->item_id_info_size();
    auto& pv_label = req_context->pv_label;
    bs_item_wrappers.reserve(req_item_count);
    for (int i = 0; i < req_item_count; i++) {
      if (pv_label[i].is_remote && item_feature_cache_) {
        item_feature_cache_->Erase(
            ks::ad_nn::GetComponentID(req->item_id_info(i), main_type_));
      }
      bool is_miss = pv_label[i].is_miss;
      if (convert_real_time_item_context_) {
        if (req_context->item_context_flatbuffer_builders[i] == nullptr) {
          LOG_EVERY_N(WARNING, 100000) << i << "-th item context is nullptr";
          if (miss_when_item_context_nullptr_) is_miss = true;
        } else {
          if (req_context->inner_bs_item_wrappers != nullptr &&
              i < req_context->inner_bs_item_wrappers->size() &&
              (*req_context->inner_bs_item_wrappers)[i] != nullptr) {
            auto item_context_fields =
                ks::ad_nn::simplified_raw_fea::GetSimplifiedRawFeature(
                    req_context->item_context_flatbuffer_builders[i]->GetBufferPointer());
            if (item_context_fields == nullptr ||
                item_context_fields->fields() == nullptr) {
              LOG_EVERY_N(WARNING, 10000) << "item_context_fields is nullptr";
              if (miss_when_item_context_nullptr_) is_miss = true;
            } else {
              (*req_context->inner_bs_item_wrappers)[i]->BuildRawFeasSimple(
                  *(item_context_fields->fields()));
            }
          } else {
            if (miss_when_item_context_nullptr_) is_miss = true;
            LOG(ERROR) << "inner_bs_item_wrappers is nullptr";
          }
        }
      }
      if (is_miss) {
        req_context->faked_flatten_item_indices.insert(i);
        if (fake_bs_miss_item_) bs_item_wrappers.emplace_back(nullptr);
      } else {
        bs_item_wrappers.emplace_back((*req_context->inner_bs_item_wrappers)[i]);
      }
    }
  } else {
    bs_item_wrappers.reserve(req_context->bs_item_wrappers.size());
    for (int i = 0; i < req_context->bs_item_wrappers.size(); ++i) {
      auto& bs_item_wrapper = req_context->bs_item_wrappers[i];
      bool is_miss = (bs_item_wrapper == nullptr);
      if (convert_real_time_item_context_) {
        if (req_context->item_context_flatbuffer_builders[i] == nullptr) {
          LOG_EVERY_N(WARNING, 100000) << i << "-th item context is nullptr";
          if (miss_when_item_context_nullptr_) {
            is_miss = true;
          }
        } else {
          auto item_context_fields =
              ks::ad_nn::simplified_raw_fea::GetSimplifiedRawFeature(
                  req_context->item_context_flatbuffer_builders[i]->GetBufferPointer());
          if (item_context_fields == nullptr ||
              item_context_fields->fields() == nullptr ||
              req_context->inner_bs_item_wrappers == nullptr ||
              i >= req_context->inner_bs_item_wrappers->size()) {
            LOG(ERROR) << "item_context_fields is nullptr";
            if (miss_when_item_context_nullptr_) {
              is_miss = true;
            }
          } else {
            (*req_context->inner_bs_item_wrappers)[i]->BuildRawFeasSimple(
                *(item_context_fields->fields()));
            // 只能将通用 bs_item_wrapper build 到
            // item_context_bs_item_wrapper，不能反过来，防止污染 cache 中的 BsItemWrapper
            if (!(*req_context->inner_bs_item_wrappers)[i] ||
                !(*req_context->inner_bs_item_wrappers)[i]->MergeField(bs_item_wrapper)) {
              LOG_EVERY_N(ERROR, 10000) << "merge real time item context failed.";
              ks::infra::PerfUtil::CountLogStash(1, "ad.predict", "feature_server",
                                                 "build_with_item_context_failed");
            }
            is_miss = false;
          }
        }
      }
      if (!is_miss) {
        if (convert_real_time_item_context_)
          bs_item_wrappers.emplace_back((*req_context->inner_bs_item_wrappers)[i]);
        else
          bs_item_wrappers.emplace_back(std::move(bs_item_wrapper));
      } else {
        if (fake_bs_miss_item_) {
          bs_item_wrappers.emplace_back(nullptr);
        }
        req_context->faked_flatten_item_indices.insert(i);
      }
    }
  }
  if (bs_item_wrappers.size() == 0) {
    LOG_EVERY_N(WARNING, 10000) << "No valid bs item";
    return false;
  }
  sample_lens->emplace_back(bs_item_wrappers.size());
  batched_samples->SetSamples(std::move(bs_item_wrappers));
  batched_samples->SetSampleLens(*sample_lens);
  return true;
}

bool FeatureServer::ConstructBSUserFromOpt(FeatureServerContext* req_context) {
  GenerateUserHolderMap(req_context);
  (void)AddPbRecoUserInfo(req_context);
  if (!AddBSUserDirect(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "AddBSUserDirect failed!";
    return false;
  }
  return true;
}

bool FeatureServer::ConstructBSItemFromOpt(FeatureServerContext* req_context) {
  auto req = req_context->request;
  size_t req_item_count = req->item_id_size();
  req_context->bs_item_wrappers.resize(req_item_count, nullptr);

  if (copy_req_cmd_ != "") {
    return AddBSItemByTransferOpt(req_context);
  }
  if (!AddBSItemDirectWithRangeOpt(0, req_item_count - 1, req_context)) {
    LOG_EVERY_N(WARNING, 100000) << "AddBSItemDirectWithRangeOpt failed";
    return false;
  }
  return true;
}

bool FeatureServer::ConstructBSFromOpt(FeatureServerContext* req_context) {
  auto& batched_samples = req_context->batched_samples;
  auto req = req_context->request;
  batched_samples.reset(new DynamicBatchedSamples(0));

  uint64 adlog_time = base::GetTimestamp() / 1000;
  // debug 时使用了该字段传递 adlog time
  if (debug_diff_ && req->real_time_item_id_size() > 0) {
    adlog_time = req->real_time_item_id(0);
  }
  batched_samples->SetTimestamp(adlog_time);
  batched_samples->SetPrimaryKey(req->llsid());
  if (remap_bs_field_enum_) {
    batched_samples->SetAttrAlias();
  }

  auto build_start = Clock::now();
  bool build_success = true;
  do {
    // item_context 转 bs
    if (opt_bs_item_ && convert_real_time_item_context_ &&
        !ConvertItemContext(req_context)) {
      LOG_EVERY_N(WARNING, 1000) << "ConvertItemContext failed";
      build_success = false;
      break;
    }
    if (async_build_bs_) {
      if (!AsyncConstructBSUser(req_context)) {
        LOG_EVERY_N(WARNING, 1000) << "AsyncConstructBSUser failed";
        build_success = false;
        break;
      }
      if (!AsyncConstructBSItem(req_context)) {
        LOG_EVERY_N(WARNING, 1000) << "AsyncConstructBSItem failed";
        build_success = false;
        break;
      }
    } else {
      if (!ConstructBSUserFromOpt(req_context)) {
        LOG_EVERY_N(WARNING, 1000) << "ConstructBSUserFromOpt failed";
        build_success = false;
        break;
      }

      if (!ConstructBSItemFromOpt(req_context)) {
        LOG_EVERY_N(WARNING, 1000) << "AsyncConstructBSItem failed";
        build_success = false;
        break;
      }
    }
  } while (0);

  if (opt_bs_item_ && convert_real_time_item_context_) {
    folly::collectAll(req_context->convert_item_context_future).wait();
  }
  if (async_build_bs_) {
    folly::collectAll(req_context->bs_build_result_future).wait();
  }
  if (!build_success || !req_context->async_bs_build_ret) {
    LOG_EVERY_N(WARNING, 10000) << "Construct asynchronously failed, task count: "
                                << req_context->bs_build_task_count;
    return false;
  }
  req_context->AddCostStat("construct_bs.construct_opt", build_start);

  // ad_user_info has dependency in daily user info,
  // thus, put it after async build finish
  // opt user 不需要 merge ad_user_info、context、realtime
  if (!req_context->user_info_strings["bs_opt_userinfo"]) {
    auto add_bs_ad_user_info_start = Clock::now();
    if (!AddBSAdUserInfo(req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "AddBSAdUserInfo failed";
    }
    req_context->AddCostStat("async_construct_bs.add_bs_ad_user_info",
                             add_bs_ad_user_info_start);
  }
  if (use_data_converter_ && !AddCustomFeature(req_context)) {
    LOG(ERROR) << "AddCustomFeature failed.";
  }
  auto init_start = Clock::now();
  std::vector<size_t> sample_lens;
  if (opt_bs_item_) {
    if (!TranslateItemOpt(req_context, &sample_lens)) {
      LOG_EVERY_N(ERROR, 1000) << "TranslateItemOpt failed";
      return false;
    }
  } else {
    if (!TranslateItem(req_context, &sample_lens)) {
      LOG_EVERY_N(ERROR, 1000) << "TranslateItem failed";
      return false;
    }
  }
  PrepareBsAdCallbackEvents(req_context);
  falcon::Stat("feature_server.construct_bs_merge_cost", GetMicroseconds(build_start));
  if (prune_bs_attr_) {
    if (!req_context->user_info_strings["bs_opt_userinfo"])
      batched_samples->SetUserWrapper(bs_user_attr_id2idx_, true);
    batched_samples->SetAllPruneAttrId2Idx(bs_all_prune_attr_id2idx_);
  }
  if (opt_bs_item_ || !async_build_bs_) {
    auto bs_init_start = Clock::now();
    if (!batched_samples->Initialize()) {
      falcon::Inc("feature_server.init_batched_samples_fail", 1);
      LOG(ERROR) << "batched samples init failed";
      return false;
    }
    falcon::Stat("feature_server.construct_bs_init_cost", GetMicroseconds(bs_init_start));
  } else {
    auto bs_init_async_start = Clock::now();
    if (!AsyncInitBSSample(req_context)) {
      LOG_EVERY_N(WARNING, 10000) << "Async init bs sample failed";
      return false;
    }
    if (!batched_samples->InitUser()) {
      LOG_EVERY_N(WARNING, 10000) << "Async init bs user failed";
      return false;
    }
    folly::collectAll(req_context->bs_init_result_future).wait();
    if (!req_context->async_bs_init_ret) {
      LOG_EVERY_N(WARNING, 10000) << "Init BS asynchronously failed, task count: "
                                  << req_context->bs_init_task_count;
      return false;
    }
    batched_samples->FinishInit();
    falcon::Stat("feature_server.construct_bs_init_async_cost",
                 GetMicroseconds(bs_init_async_start));
  }
  req_context->AddCostStat("construct_bs.init_bs_opt_cost", init_start);

  return true;
}

bool FeatureServer::ConstructBS(FeatureServerContext* req_context) {
  if (is_i18n_) return ConstructBatchedSamples_I18N(req_context);
  auto& batched_samples = req_context->batched_samples;
  auto req = req_context->request;
  batched_samples.reset(new DynamicBatchedSamples());

  batched_samples->SetTimestamp(base::GetTimestamp() / 1000);
  batched_samples->SetPrimaryKey(req->llsid());
  if (remap_bs_field_enum_) {
    batched_samples->SetAttrAlias();
  }

  if (!ConstructBSUser(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "ConstructBSUser failed";
    return false;
  }
  if (!ConstructBSItem(req_context)) {
    LOG_EVERY_N(WARNING, 10000) << "ConstructBSItem failed";
    return false;
  }
  auto bs_init_start = Clock::now();
  if (!batched_samples->Initialize()) {
    falcon::Inc("feature_server.init_batched_samples_fail", 1);
    LOG(ERROR) << "batched samples init failed";
    return false;
  }
  falcon::Stat("feature_server.construct_bs_init_cost", GetMicroseconds(bs_init_start));

  if (dump_log_ && transfer_type_ == TransferType::NO_TRANSFER) {
    if (!ConstructAdlog(*req, req_context->ad_log.get(), req_context->response,
                        req_context->shared_aa_alias)) {
      LOG(WARNING) << "ConstructAdlog failed";
    }
    if (!AddAdLogItemForCompare(req_context)) {
      LOG_EVERY_N(WARNING, 1000) << "Add AdLog item for compare failed";
    }
  }

  return true;
}

bool FeatureServer::AddAdLogItemForCompare(FeatureServerContext* req_context) {
  const auto& ps_request = *req_context->request;
  auto* info_ptr = GetItemComponentInfo(ItemInfoType::FULL);
  if (KS_UNLIKELY(info_ptr == nullptr)) return false;
  auto& info = *info_ptr;
  std::vector<std::vector<uint64_t>> item_ids(info.item_server_shard_num);
  std::vector<std::vector<int>> indices_vals(info.item_server_shard_num);
  int item_id_size = 0;
  for (int i = 0; i < ps_request.item_id_size(); ++i) {
    if (req_context->faked_flatten_item_indices.count(i) == 0) {
      uint64_t item_id = ps_request.item_id(i);
      uint64_t shard_id = GetShardId(info, item_id);
      item_ids[shard_id].push_back(item_id);
      indices_vals[shard_id].push_back(item_id_size++);
    }
  }
  req_context->compare_items.resize(item_id_size, nullptr);
  req_context->real_items.resize(item_id_size, nullptr);
  LOG_EVERY_N(INFO, 10000) << "item id size: " << item_id_size;
  falcon::Stat("feature_server.compare_item_count", item_id_size);

  std::vector<std::vector<IndexRequest>> requests;
  std::vector<std::vector<IndexResponse>> responses;
  PrepareReqItemServer(info, item_ids, &requests, &responses);

  std::atomic<int> fake_item_count(0);
  ks::kess::rpc::BatchWaiter batch_waiter;
  ks::kess::rpc::grpc::Options options;
  options.SetTimeout(std::chrono::milliseconds(info.item_service_timeout));
  for (int i = 0; i < requests.size(); ++i) {
    std::string peer;
    auto stub = info.item_server_clients[i]->SelectOne(&peer);
    auto index_begin = indices_vals[i].cbegin();
    for (int j = 0; j < requests[i].size(); ++j) {
      auto& request = requests[i][j];
      auto callback = [&, this, index_begin, req_context](const grpc::Status& status,
                                                          IndexResponse* response) {
        if (!CheckItemServerRespValid(status, request, response)) {
          fake_item_count.fetch_add(request.creative_id_size());
          return;
        }
        int local_fake_item_count = 0;
        for (int k = 0; k < response->creative_info_size(); ++k) {
          int32_t index = *(index_begin + k);
          uint64_t item_id = request.creative_id(k);
          auto& serialized_data = response->creative_info(k).value();
          if (serialized_data.empty()) {
            ++local_fake_item_count;
            LOG_EVERY_N(INFO, FLAGS_fake_log_freq)
                << "item id not found, will fake it: " << item_id << " llsid "
                << ps_request.llsid() << " page_id " << ps_request.context().page_id()
                << " sub_page_id " << ps_request.context().sub_page_id() << " pos_id "
                << ps_request.context().pos_id();
            continue;
          }

          std::shared_ptr<ItemAdaptorV1> item_ptr = ParseItemV1(item_id, serialized_data);
          if (!item_ptr) {
            falcon::Inc("dnn_predict_server.pb_cache_except", 1);
            continue;
          }
          req_context->compare_items[index] = item_ptr;
          req_context->real_items[index] = item_ptr.get();
        }
        fake_item_count.fetch_add(local_fake_item_count);
      };
      index_begin += request.creative_id_size();
      auto fut = stub->AsyncGetCreative(options, request, &responses[i][j],
                                        info.item_server_event_loop->SelectOne());
      batch_waiter.Add(fut,
                       [callback](const grpc::Status& status, IndexResponse* response) {
                         callback(status, response);
                       });
    }
  }
  batch_waiter.Wait();
  falcon::Inc("feature_server.fake_remote_item_count", fake_item_count.load());
  double fake_rate = 1e6 * fake_item_count.load() / ps_request.item_id_size();
  falcon::Stat("feature_server.fake_remote_item_rate", fake_rate);
  return true;
}

bool FeatureServer::AddRequestSubContextByTransfer(FeatureServerContext* req_context) {
  auto bs_str = std::make_shared<std::string>();
  AdJointLabeledLog temp_ad_log;
  temp_ad_log.mutable_context()->CopyFrom(req_context->request->context());
  std::string temp_str;
  temp_ad_log.SerializeToString(&temp_str);
  if (!TransferBS(temp_str, "", &bs_str)) {
    LOG_EVERY_N(WARNING, 1000) << "TransferBS for context failed";
    return false;
  }
  const FeatureSets* req_sub_context = nullptr;
  req_context->transfered_strs["context"] = bs_str;
  auto req_sub_ret =
      ks::ad_nn::flatten::StringToMu2Table<FeatureSets>(bs_str->data(), bs_str->size());
  if (req_sub_ret == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to convert req sub context";
    return false;
  }
  if (!aa_alias_.empty() && req_context->request->cmd_size() > 0 &&
      !shared_feature_server_) {
    flatten::SetRankCmd(req_context->request->cmd(0), aa_alias_, req_sub_ret);
  }
  if (shared_feature_server_) {
    for (int i = 0; i < req_context->shared_aa_alias.size(); ++i) {
      auto& aa_alias = req_context->shared_aa_alias[i];
      if (!aa_alias.empty()) {
        flatten::SetRankCmd(req_context->request->cmd(i), aa_alias, req_sub_ret);
      }
    }
  }
  req_sub_context = req_sub_ret;
  LOG_EVERY_N(INFO, 10000) << "Transfer context successfully";
  if (!req_context->batched_samples->AddSubContextAttrs(
          added_bs_attr_keys::kBsReqSubContext, req_sub_context)) {
    falcon::Inc("feature_server.set_request_sub_context_fail", 1);
    LOG_EVERY_N(WARNING, 100000) << "Set request sub context failed";
  } else {
    LOG_EVERY_N(WARNING, 100000) << "get flatten request sub context failed";
    falcon::Inc("feature_server.get_request_sub_context_fail", 1);
    return false;
  }
  return true;
}

bool FeatureServer::GetRequestSubContext(const UniversePredictRequest& req,
                                         FeatureServerContext* req_context,
                                         const FeatureSets** req_sub_context) {
  if (use_data_converter_ && !convert_in_local_ && req.has_context()) {
    if (!req.flatten_user_info().req_context().empty()) {
      LOG_EVERY_N(INFO, 1000000) << "use router bs convert context";
      req_context->user_info_strings["convert_context"].reset(
          &req.flatten_user_info().req_context(), [](const std::string* p) {});
      auto* bs = GetBSUserInfo("req_context",
                               *req_context->user_info_strings["convert_context"]);
      if (bs == nullptr || bs->sub_context()->Get(0) == nullptr) {
        LOG(ERROR) << "Convert req_context on local failed.";
        return false;
      }
      *req_sub_context = bs->sub_context()->Get(0);
      return true;
    }
    LOG_EVERY_N(WARNING, 1000) << "use router converter bs req_context, but empty.";
    return false;
  }
  if (convert_in_local_ && req.has_context()) {
    std::string* str = new std::string();
    ks::ad_nn::data_converter::DataConverter::GetInstance()->Convert(
        req.context(), "adlog.context", str);
    req_context->user_info_strings["convert_context"].reset(str);
    auto* bs = GetBSUserInfo("convert_context",
                             *req_context->user_info_strings["convert_context"]);
    if (bs == nullptr || bs->sub_context()->Get(0) == nullptr) {
      LOG(ERROR) << "Convert convert_context on local failed.";
      return false;
    } else {
      LOG_EVERY_N(INFO, 1000000) << "Success convert_context.";
    }
    *req_sub_context = bs->sub_context()->Get(0);
    return true;
  }
  auto builder_iter = req_context->builders.find("req_sub_context");
  if (builder_iter == req_context->builders.end()) {
    LOG_EVERY_N(ERROR, 1000) << "no realtime builder";
    return false;
  }
  auto builder = builder_iter->second;
  if (!builder) {
    LOG(ERROR) << "Builder is a nullptr for req_sub_context";
    return false;
  }
  std::vector<flatbuffers::Offset<ks::cofea_fbs::FeatureSet>> context_offsets;
  // adlog.context.existis
  bool has_req_context = req.has_context();
  if (!flatten::AddFeatureSet("73924", static_cast<int64_t>(has_req_context),
                              builder.get(), &context_offsets)) {
    LOG_EVERY_N(WARNING, 100000) << "Add adlog.context.exists failed";
  }
  // adlog.context.detail_context.exists
  if (!flatten::AddFeatureSet(
          "73925",
          static_cast<int64_t>(has_req_context && req.context().has_detail_context()),
          builder.get(), &context_offsets)) {
    LOG_EVERY_N(WARNING, 100000) << "Add adlog.context.detail_context.exists failed";
  }

  // adlog.tab
  if (req.tab_type() != kuaishou::ad::algorithm::UNKNOWN_TAB) {
    if (!flatten::AddFeatureSet("51936", static_cast<int64_t>(req.tab_type()),
                                builder.get(), &context_offsets)) {
      LOG_EVERY_N(WARNING, 100000) << "Add adlog.tab failed";
    }
  }

  if (has_req_context) {
    flatten::ConvertContext(req.context(),
                            AttrNameMapping::Instance().Name2IdStrMapping(),
                            builder.get(), &context_offsets);
  }
  // adlog.context.info_common_attr.key:RANK_MODEL_NAME
  // 共享特征服务该字段会发生错误
  if (!aa_alias_.empty()) {
    flatten::AddPbSingular("90308", aa_alias_, builder.get(), &context_offsets);
  } else if (req.cmd_size() > 0) {
    flatten::AddPbSingular("90308", req.cmd(req_context->cmd_index), builder.get(),
                           &context_offsets);
  }

  auto feature_sets_offset =
      ks::cofea_fbs::CreateFeatureSetsDirect(*builder, &context_offsets);

  builder->Finish(feature_sets_offset);
  char* buf = reinterpret_cast<char*>(builder->GetBufferPointer());
  auto req_sub_ret =
      ks::ad_nn::flatten::StringToMu2Table<FeatureSets>(buf, builder->GetSize());
  if (req_sub_ret == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to convert req sub context";
    return false;
  }
  if (!aa_alias_.empty() && req.cmd_size() > 0 && !shared_feature_server_) {
    flatten::SetRankCmd(req.cmd(0), aa_alias_, req_sub_ret);
  }
  if (shared_feature_server_) {
    for (int i = 0; i < req_context->shared_aa_alias.size(); ++i) {
      auto& aa_alias = req_context->shared_aa_alias[i];
      if (!aa_alias.empty()) {
        flatten::SetRankCmd(req.cmd(i), aa_alias, req_sub_ret);
      }
    }
  }
  *req_sub_context = req_sub_ret;

  return true;
}

bool FeatureServer::ConvertAdUserInfoToFeatureSets(const UniversePredictRequest& req,
                                                   FeatureServerContext* req_context,
                                                   const FeatureSets** ad_user_info) {
  if (predict_service_->DisableRealtimeAdUserInfo()) {
    LOG_EVERY_N(INFO, 100000) << "Disable realtime ad user info";
    return true;
  }

  if (req.ad_user_info().id() != req_context->user_id_in_bs) {
    LOG_EVERY_N(INFO, 100000) << "user id not equal, in req.ad_user_info:"
                              << req.ad_user_info().id() << " user_id in bs "
                              << req_context->user_id_in_bs
                              << " just skip ad user info convert";
    return true;
  }
  LOG_EVERY_N(INFO, 100000) << req.ad_user_info().id()
                            << ", BSAdUserInfo replaced from request";
  // req_context->bs_log.SetAdUserInfo(&req.ad_user_info());

  if (use_data_converter_ && !convert_in_local_) {
    if (!req_context->request->flatten_user_info().ad_user_info().empty()) {
      LOG_EVERY_N(INFO, 1000000) << "use router bs convert ad_user_info";
      req_context->user_info_strings["convert_ad_user_info"].reset(
          &req_context->request->flatten_user_info().ad_user_info(),
          [](const std::string* p) {});
      auto* bs = GetBSUserInfo("ad_user_info",
                               *req_context->user_info_strings["convert_ad_user_info"]);
      if (bs == nullptr || bs->sub_context()->Get(0) == nullptr) {
        LOG(ERROR) << "Convert ad_user_info on local failed.";
        return false;
      }
      *ad_user_info = bs->sub_context()->Get(0);
      return true;
    }
    LOG_EVERY_N(WARNING, 1000) << "use router converter bs ad_user_info, but empty.";
    return false;
  }

  if (convert_in_local_) {
    std::string* str = new std::string();
    ks::ad_nn::data_converter::DataConverter::GetInstance()->Convert(
        req.ad_user_info(), "adlog.user_info.ad_user_info", str);
    req_context->user_info_strings["convert_ad_user_info"].reset(str);
    auto* bs = GetBSUserInfo("convert_ad_user_info",
                             *req_context->user_info_strings["convert_ad_user_info"]);
    if (bs == nullptr || bs->sub_context()->Get(0) == nullptr) {
      LOG(ERROR) << "Convert ad user info on local failed.";
      return false;
    } else {
      LOG_EVERY_N(INFO, 1000000) << "Success ad user info.";
    }
    *ad_user_info = bs->sub_context()->Get(0);
    return true;
  }

  flatten::FeatureSetOffsetVec ad_user_info_offset_vec;
  auto builder_ret = req_context->builders.insert(
      {"ad_user_info", std::make_shared<flatbuffers::FlatBufferBuilder>()});
  auto builder = builder_ret.first->second;
  if (!builder) {
    LOG(ERROR) << "Builder for ad_user_info is a nullptr";
    return false;
  }
  // adlog.user_info.ad_user_info.exists
  flatten::AddFeatureSet("73915", 1LL,
                         // static_cast<int64_t>(req.has_ad_user_info()),
                         builder.get(), &ad_user_info_offset_vec);
  flatten::ConvertAdUserInfo(true, /* keep empty */
                             req.ad_user_info(), builder.get(), &ad_user_info_offset_vec);
  auto feature_sets_offset =
      ks::cofea_fbs::CreateFeatureSetsDirect(*builder, &ad_user_info_offset_vec);
  builder->Finish(feature_sets_offset);
  char* buf = reinterpret_cast<char*>(builder->GetBufferPointer());
  *ad_user_info = ks::ad_nn::flatten::StringToTable<FeatureSets>(buf, builder->GetSize());
  if (*ad_user_info == nullptr) {
    LOG_EVERY_N(WARNING, 10000) << "Failed to convert flatten ad user info";
    return false;
  }
  return true;
}

int FeatureServer::PrepareBsAdCallbackEvents(FeatureServerContext* req_context) {
  auto request = req_context->request;
  auto& bs_log = req_context->bs_log;
  if (request == nullptr) {
    LOG_EVERY_N(WARNING, 100) << "request is nullptr";
    return -1;
  }

  int callback_event_size = request->callback_event_size();
  if (callback_event_size != request->item_id_size()) {
    LOG_EVERY_N(WARNING, 100000) << "callback_event_size: " << callback_event_size
                                 << ", item size: " << request->item_id_size();
    return -1;
  }
  bs_log.reserve_ad_callback_event(callback_event_size);
  for (int i = 0; i < request->callback_event_size(); i++) {
    if (fake_bs_miss_item_ || req_context->faked_flatten_item_indices.find(i) ==
                                  req_context->faked_flatten_item_indices.end())
      bs_log.add_ad_callback_event(
          static_cast<::bs::kuaishou::ad::AdCallbackLog::EventType>(
              request->callback_event(i)));
  }
  return 0;
}

bool FeatureServer::AddCustomFeature(FeatureServerContext* req_context) {
  auto req = req_context->request;
  auto builder_iter = req_context->builders.find("custom_feature");
  if (builder_iter == req_context->builders.end()) {
    LOG_EVERY_N(ERROR, 1000) << "no custom_feature builder";
    return false;
  }
  auto builder = builder_iter->second;
  if (!builder) {
    LOG(ERROR) << "Builder is a nullptr for custom_feature";
    return false;
  }
  // aa_alias
  std::vector<flatbuffers::Offset<ks::cofea_fbs::FeatureSet>> custom_feature_offsets;
  if (need_fake_user_) {
    if (req_context->user_info_strings["bs_opt_userinfo"]) {
      auto str = req_context->user_info_strings["bs_opt_userinfo"];
      bool need_insert_id = true;
      auto* fields = ks::ad_nn::GetFields(*str, true);
      if (fields == nullptr) {
        LOG_EVERY_N(ERROR, 100000) << "simple fields get failed.";
      } else {
        for (int i = 0; i < fields->size(); ++i) {
          auto field = fields->Get(i);
          if (field == nullptr || field->name() == nullptr) {
            LOG_EVERY_N(INFO, 10000000) << "field is invaild, index: " << i;
            continue;
          }
          if (absl::string_view(field->name()->c_str()) == "0") {
            need_insert_id = false;
            break;
          }
        }
      }
      if (need_insert_id) {
        // adlog.user_info.id
        if (!flatten::AddFeatureSet("0", static_cast<int64_t>(0), builder.get(),
                                    &custom_feature_offsets)) {
          LOG_EVERY_N(WARNING, 100000) << "Add user id failed: " << req->user_id();
        }
        // adlog.user_info.exists
        if (!flatten::AddFeatureSet("73884", 1LL, builder.get(),
                                    &custom_feature_offsets)) {
          LOG_EVERY_N(WARNING, 100000) << "Add user id failed: " << req->user_id();
        }
      }
    }
  }
  if (!aa_alias_.empty()) {
    flatten::AddPbSingular("90308", aa_alias_, builder.get(), &custom_feature_offsets);
  } else if (req->cmd_size() > 0) {
    flatten::AddPbSingular("90308", req->cmd(req_context->cmd_index), builder.get(),
                           &custom_feature_offsets);
  }
  // 共享特征服务设置 rank name
  for (auto& attr : req->context().info_common_attr()) {
    if (attr.name_value() ==
        ::kuaishou::ad::ContextInfoCommonAttr_Name_INNER_RANK_ITEM_MODEL_NAME) {
      const auto& rank_cmd_map = attr.map_int64_string_value();
      flatten::AddPbMap("90306", "90307", rank_cmd_map, builder.get(),
                        &custom_feature_offsets);
      break;
    }
  }
  // req.tab
  if (req->tab_type() != kuaishou::ad::algorithm::UNKNOWN_TAB) {
    if (!flatten::AddFeatureSet("51936", static_cast<int64_t>(req->tab_type()),
                                builder.get(), &custom_feature_offsets)) {
      LOG_EVERY_N(WARNING, 100000) << "Add adlog.tab failed";
    }
  }
  auto feature_sets_offset =
      ks::cofea_fbs::CreateFeatureSetsDirect(*builder, &custom_feature_offsets);
  builder->Finish(feature_sets_offset);
  char* buf = reinterpret_cast<char*>(builder->GetBufferPointer());
  const FeatureSets* custom_feature_sets =
      ks::ad_nn::flatten::StringToTable<FeatureSets>(buf, builder->GetSize());
  DynamicBatchedSamples* batched_samples = req_context->batched_samples.get();
  if (!batched_samples->AddSubContextAttrs(added_bs_attr_keys::kBsCustomFeature,
                                           custom_feature_sets)) {
    falcon::Inc("feature_server.set_custom_feature_fail", 1);
    LOG_EVERY_N(WARNING, 100000) << "Set request sub context failed";
    return false;
  }

  falcon::Inc("feature_server.set_custom_feature_success", 1);
  // rand_cmd
  if (!aa_alias_.empty() && req->cmd_size() > 0) {
    if (req_context->user_info_strings["bs_opt_userinfo"]) {
      auto str = req_context->user_info_strings["bs_opt_userinfo"];
      auto req_sub_ret = ks::ad_nn::flatten::StringToMu2Table<SimplifiedRawFeature>(
          str->data(), str->size());
      if (req_sub_ret == nullptr) {
        LOG_EVERY_N(WARNING, 10000) << "Failed to convert bs_opt_userinfo";
        return false;
      }
      if (!shared_feature_server_) {
        flatten::SetRankCmd(req->cmd(0), aa_alias_, req_sub_ret);
      } else {
        for (int i = 0; i < req_context->shared_aa_alias.size(); ++i) {
          auto& aa_alias = req_context->shared_aa_alias[i];
          if (!aa_alias.empty()) {
            flatten::SetRankCmd(req->cmd(i), aa_alias, req_sub_ret);
          }
        }
      }
    } else {
      if (!req_context->user_info_strings["convert_context"]) {
        LOG(ERROR) << "convert_context str is empty.";
        return false;
      }
      auto str = req_context->user_info_strings["convert_context"];
      auto req_sub_ret =
          ks::ad_nn::flatten::StringToMu2Table<FeatureSets>(str->data(), str->size());
      if (req_sub_ret == nullptr) {
        LOG_EVERY_N(WARNING, 10000) << "Failed to convert convert_context";
        return false;
      }
      if (!shared_feature_server_) {
        flatten::SetRankCmd(req->cmd(0), aa_alias_, req_sub_ret);
      } else {
        for (int i = 0; i < req_context->shared_aa_alias.size(); ++i) {
          auto& aa_alias = req_context->shared_aa_alias[i];
          if (!aa_alias.empty()) {
            flatten::SetRankCmd(req->cmd(i), aa_alias, req_sub_ret);
          }
        }
      }
    }
  }
  return true;
}

bool FeatureServer::ConvertUserRealtimeAction(FeatureServerContext* req_context,
                                              const FeatureSets** user_real_time_action) {
  if (use_data_converter_ && !convert_in_local_) {
    if (!req_context->request->flatten_user_info().realtime_action().empty()) {
      LOG_EVERY_N(INFO, 1000000) << "use router bs convert realtime";
      req_context->user_info_strings["convert_realtime_action"].reset(
          &req_context->request->flatten_user_info().realtime_action(),
          [](const std::string* p) {});
      auto* bs = GetBSUserInfo(
          "realtime", *req_context->user_info_strings["convert_realtime_action"]);
      if (bs == nullptr || bs->sub_context()->Get(0) == nullptr) {
        LOG(ERROR) << "Convert realtime on local failed.";
        return false;
      }
      *user_real_time_action = bs->sub_context()->Get(0);
      return true;
    }
    LOG_EVERY_N(WARNING, 1000) << "use router converter bs realtime, but empty.";
    return false;
  }
  bool has_user_real_time_action = (!req_context->request->realtime_action().empty());
  if (has_user_real_time_action) {
    auto ret = req_context->data_response.mutable_user_info()
                   ->mutable_user_real_time_action()
                   ->ParseFromString(req_context->request->realtime_action());
    if (!ret) {
      LOG_EVERY_N(ERROR, 10000) << "realtime action parse from string failed";
      return false;
    }
    if (convert_in_local_) {
      std::string* str = new std::string();
      ks::ad_nn::data_converter::DataConverter::GetInstance()->Convert(
          req_context->data_response.user_info().user_real_time_action(),
          "adlog.user_info.user_real_time_action", str);
      req_context->user_info_strings["convert_realtime_action"].reset(str);
      auto* bs = GetBSUserInfo(
          "realtime", *req_context->user_info_strings["convert_realtime_action"]);
      if (bs == nullptr || bs->sub_context()->Get(0) == nullptr) {
        LOG(ERROR) << "Convert realtime on local failed.";
        return false;
      } else {
        LOG_EVERY_N(INFO, 1000000) << "Success relatime.";
      }
      *user_real_time_action = bs->sub_context()->Get(0);
      return true;
    }
    flatten::FeatureSetOffsetVec user_real_time_action_offset_vec;
    auto builder_iter = req_context->builders.find("pb_user_real_time_action");
    if (builder_iter == req_context->builders.end()) {
      LOG_EVERY_N(ERROR, 1000) << "no realtime builder";
      return false;
    }
    auto builder = builder_iter->second;
    if (!builder) {
      LOG(ERROR) << "Builder for user_real_time_action is a nullptr";
      return false;
    }
    flatten::ConvertUserRealtimeAction(
        req_context->data_response.user_info().user_real_time_action(), builder.get(),
        &user_real_time_action_offset_vec);
    // adlog.user_info.user_real_time_action.exists
    flatten::AddFeatureSet("73911", static_cast<int64_t>(has_user_real_time_action),
                           builder.get(), &user_real_time_action_offset_vec);
    auto feature_sets_offset = ks::cofea_fbs::CreateFeatureSetsDirect(
        *builder, &user_real_time_action_offset_vec);
    builder->Finish(feature_sets_offset);
    char* buf = reinterpret_cast<char*>(builder->GetBufferPointer());
    *user_real_time_action =
        ks::ad_nn::flatten::StringToTable<FeatureSets>(buf, builder->GetSize());
    if (*user_real_time_action == nullptr) {
      LOG_EVERY_N(WARNING, 10000) << "Failed to convert flatten user real time action";
      return false;
    }
  }
  return true;
}

bool FeatureServer::ConvertRawBuffer2FeatureList(const std::vector<size_t>& item_ids,
                                                 const DagBSFeatures& raw_buffer,
                                                 InferFeatureList* feature_list) {
  auto item_size = item_ids.size();

  using SparseFeatures =
      std::unordered_map<std::string, std::vector<std::vector<uint64_t>>>;
  using DenseFeatures = std::unordered_map<std::string, std::vector<float>>;

  auto user_offset = feature_list->mutable_user_feature_offset();
  auto user_feature = feature_list->mutable_user_features();

  // convert user
  auto add_sparse_user = [&](const SparseFeatures& features) {
    for (const auto& feature : features) {
      const auto& name = feature.first;
      feature_list->add_user_feature_names(name);
      const auto& data = feature.second;
      for (auto item : data[0]) {
        user_feature->add_sign(item);
      }
      user_offset->Add(user_feature->sign_size());
    }
  };

  auto add_dense_user = [&](const DenseFeatures& features) {
    for (const auto& feature : features) {
      const auto& name = feature.first;
      feature_list->add_user_feature_names(name);
      auto& data = feature.second;
      for (int i = 0; i < data.size(); i++) {
        user_feature->add_value(data[i]);
      }
      user_offset->Add(user_feature->value_size());
    }
  };

  // convert saprse aggregate user
  add_sparse_user(raw_buffer.sparse_aggregate_user);

  // convert sparse user
  add_sparse_user(raw_buffer.sparse_user);

  // convert dense aggregate user
  add_dense_user(raw_buffer.dense_aggregate_user);

  // convert dense user
  add_dense_user(raw_buffer.dense_user);

  // convert item
  auto item_features = feature_list->mutable_item_features();
  for (int i = 0; i < item_size; i++) {
    auto item_feature = item_features->Add();
    item_feature->set_item_id(item_ids[i]);
  }

  auto add_sparse_item = [&](const SparseFeatures& features) {
    for (const auto& feature : features) {
      const auto& name = feature.first;
      feature_list->add_item_feature_names(name);

      const auto& data = feature.second;
      for (int i = 0; i < item_size; i++) {
        auto item_feature = item_features->Mutable(i);
        auto feature_value = item_feature->mutable_features();
        for (auto item : data[i]) {
          feature_value->add_sign(item);
        }
        item_feature->add_feature_offset(feature_value->sign_size());
      }
    }
  };

  auto add_dense_item = [&](const DenseFeatures& features) {
    for (const auto& feature : features) {
      const auto& name = feature.first;
      feature_list->add_item_feature_names(name);

      auto& data = feature.second;
      auto len = data.size() / item_size;
      size_t offset = 0;
      for (int i = 0; i < item_size; i++) {
        auto item_feature = item_features->Mutable(i);
        auto feature_value = item_feature->mutable_features();
        for (int j = 0; j < len; j++) {
          feature_value->add_value(data[offset++]);
        }
        item_feature->add_feature_offset(feature_value->value_size());
      }
    }
  };

  // convert sparse item
  add_sparse_item(raw_buffer.sparse_item);

  // convert sparse combine
  add_sparse_item(raw_buffer.sparse_combine);

  // convert dense item
  add_dense_item(raw_buffer.dense_item);

  // convert dense combine
  add_dense_item(raw_buffer.dense_combine);
  return true;
}

void FeatureServer::Prepare(
    grpc::ServerContext* grpc_context, const PreparePredictRequest* request,
    ks::kess::rpc::grpc::ResponseWriter<PreparePredictResponse> res_writer,
    ks::kess::rpc::grpc::EventLoop* event_loop) {
  LOG_EVERY_N(WARNING, 10) << "unsupported ack method";
  res_writer.FinishWithError(
      grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "PREPARE_NOT_SUPPORTED"),
      [](bool ok) {});
  return;
}

void FeatureServer::Prepare(
    ::google::protobuf::RpcController* controller,
    const ::kuaishou::ad::algorithm::PreparePredictRequest* request,
    ::kuaishou::ad::algorithm::PreparePredictResponse* response,
    ::google::protobuf::Closure* done) {
  brpc::ClosureGuard done_guard(done);
  LOG_EVERY_N(WARNING, 10) << "unsupported prepare method";
  brpc::Controller* cntl = static_cast<brpc::Controller*>(controller);
  if (cntl == nullptr) {
    return;
  }
  auto code = grpc::StatusCode::UNIMPLEMENTED;
  cntl->SetFailed(code, "PREPARE_NOT_SUPPORTED");
  return;
}

void FeatureServer::Predict(
    ::google::protobuf::RpcController* controller,
    const ::kuaishou::ad::algorithm::UniversePredictRequest* request,
    ::kuaishou::ad::algorithm::UniversePredictResponse* response,
    ::google::protobuf::Closure* done) {
  LOG_EVERY_N(INFO, 1000000) << "predict with brpc";
  brpc::ClosureGuard done_guard(done);
  brpc::Controller* cntl = static_cast<brpc::Controller*>(controller);
  if (cntl == nullptr) {
    response->set_status(kuaishou::ad::algorithm::INTERNAL_ERROR);
    return;
  }
  auto queue_time = static_cast<int64_t>(cntl->latency_us());
  if (cmd_name_ != "")
    ks::infra::PerfUtil::IntervalLogStash(queue_time, (cmd_name_ + "_feature_server"),
                                          "predict_queue_latency");
  ks::kess::rpc::grpc::ResponseWriter<UniversePredictResponse> res_writer(nullptr);
  auto req_context =
      std::make_shared<FeatureServerContext>(request, res_writer, response);
  req_context->enable_cost_stat = FLAGS_enable_cost_stat;
  req_context->peer_ip = butil::endpoint2str(cntl->remote_side()).c_str();
  req_context->brpc_cntl = cntl;
  req_context->use_brpc = true;
  req_context->done = done;
  req_context->done_guard = &done_guard;
  PredictWithContext(req_context);
  return;
}

void FeatureServer::Predict(
    grpc::ServerContext* grpc_context, const UniversePredictRequest* request,
    ks::kess::rpc::grpc::ResponseWriter<UniversePredictResponse> res_writer,
    ks::kess::rpc::grpc::EventLoop* event_loop) {
  auto req_context = std::make_shared<FeatureServerContext>(request, res_writer);
  req_context->enable_cost_stat = FLAGS_enable_cost_stat;
  req_context->peer_ip = grpc_context->peer();
  req_context->grpc_context = grpc_context;
  req_context->use_brpc = use_brpc_;
  return PredictWithContext(req_context);
}

void FeatureServer::CompareUserInfo(FeatureServerContext* req_context) {
  const auto& request = *req_context->request;
  if (request.has_user_info_bin() && request.has_flatten_user_info()) {
    auto& data_response = req_context->data_response;
    UniversePredictResponse response;
    uint64_t data_cost = 0;
    bool pb_succ = true;
    if (predict_service_->LoadUserData(request, &data_response, &data_cost, &response) !=
        0) {
      LOG(INFO) << "[Debug] Load user data failed";
      pb_succ = false;
    }
    uint64_t pb_bt = data_response.user_info().build_time();      // bs 13562
    uint64_t pb_sub_bt = data_response.user_info().build_time();  // bs 52186
    uint64_t pb_realtime_bt =
        data_response.user_info().user_real_time_action().build_time();  // bs 53271
    std::string daily_user_info_str(request.flatten_user_info().daily_user_info());
    std::string minute_user_info_str(request.flatten_user_info().minute_user_info());
    auto get_user_info = [](const std::string& log_str,
                            const std::string& info_str) -> const BatchedSamples* {
      if (info_str.empty()) {
        LOG(INFO) << "[Debug] Empty str for " << log_str;
        return nullptr;
      }
      auto ret = flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(info_str);
      if (ret == nullptr) {
        LOG(INFO) << "[Debug] Convert string to batched_samples failed for " << log_str
                  << " size: " << info_str.size();
      }
      return ret;
    };
    auto daily_batched_samples = get_user_info("daily_user_info", daily_user_info_str);
    auto minute_batched_samples = get_user_info("minute_user_info", minute_user_info_str);

    // get daily build time
    uint64_t bs_bt = 0;
    bool bs_succ = GetAttrValueInBS(daily_batched_samples, "daily_bs", "13562",
                                    flatten::AttrType::kContext, &bs_bt);
    // get minute build time
    uint64_t bs_sub_bt = 0;
    bool bs_sub_succ = GetAttrValueInBS(minute_batched_samples, "minute_bs", "52186",
                                        flatten::AttrType::kSubContext, &bs_sub_bt);
    auto diff = static_cast<int64_t>(pb_bt - bs_bt);
    auto sub_diff = static_cast<int64_t>(pb_sub_bt - bs_sub_bt);
    auto stat_diff = [pb_succ](bool is_succ, int64_t diff, const std::string& prefix) {
      if (pb_succ) {
        if (is_succ) {
          auto abs_diff = std::abs(diff);
          falcon::Stat((prefix + "_diff_abs").c_str(), abs_diff / 1000);
          falcon::Inc((prefix + "_total_count").c_str(), 1);
          if (diff > 0) {
            falcon::Stat((prefix + "_diff_postive").c_str(), diff / 1000);
          } else if (diff < 0) {
            falcon::Stat((prefix + "_diff_negative").c_str(), diff / 1000);
          }
          if (diff != 0) {
            falcon::Inc((prefix + "_diff_count").c_str(), 1);
          } else {
            falcon::Inc((prefix + "_same_count").c_str(), 1);
          }
        }
      }
    };
    stat_diff(bs_succ, diff, "feature_server.daily_build_time");
    stat_diff(bs_sub_succ, sub_diff, "feature_server.minute_build_time");
    LOG(INFO) << "[Debug] pb build_time: " << pb_bt
              << ", pb sub_build_time: " << pb_sub_bt << ". bs build_time: " << bs_bt
              << ", bs sub_build_time: " << bs_sub_bt
              << ". build time diff(pb - bs): " << diff / 1000
              << ", sub_diff: " << sub_diff / 1000 << " pb_succ: " << pb_succ
              << " bs_succ: " << bs_succ << " bs_sub_succ: " << bs_sub_succ
              << " pb_user_info_size: " << data_response.user_info().ByteSizeLong()
              << " bs_daily_size: " << daily_user_info_str.size()
              << " bs_minute_size: " << minute_user_info_str.size()
              << " user_id: " << data_response.user_info().id() << " is_unlogin: "
              << data_response.user_info().ad_user_info().is_unlogin_user()
              << " device_id: " << data_response.user_info().ad_user_info().device_id()
              << " peer ip: " << req_context->peer_ip;
  }
}

void FeatureServer::PredictWithContext(
    std::shared_ptr<FeatureServerContext>& req_context) {  // NOLINT
  auto request = req_context->request;
  item_cnt_ = request->item_id_size();
  // CompareUserInfo(req_context.get());
  // for embedding cache
  static thread_local std::vector<std::shared_ptr<std::vector<float>>> embedding_map;
  embedding_map.clear();
  req_context->embedding_map = &embedding_map;
  falcon::Inc("feature_server.predict", 1);
  auto legal_ret = CheckLegal(request);
  if (legal_ret != kuaishou::ad::algorithm::STATUS_OK) {
    falcon::Inc("feature_server.illegal_req_count", 1);
    req_context->finish(legal_ret);
    return;
  }
  static thread_local reco_arch::utils::ReusedPbArena arena(FLAGS_feature_req_arena_size);
  arena.ResetArena();
  auto feature_list = Arena::CreateMessage<InferFeatureList>(arena.GetArena());

  if (KS_UNLIKELY(FLAGS_dump_request_freq > 0)) {
    static thread_local uint64_t fake_debug_count = 1;
    if (fake_debug_count++ % FLAGS_dump_request_freq == 0) {
      const_cast<UniversePredictRequest*>(request)->set_is_debug_req(true);
      req_context->fake_debug = true;
    }
  }

  if (use_feature_extract_handler_) {
    if (!PredictWithExtractHandler(req_context.get(), feature_list)) {
      LOG_EVERY_N(WARNING, 100000) << "PredictWithExtractHandlerFailed";
      return;
    }
  } else {
    auto start = Clock::now();
    if (!PredictWithFastFeature(req_context.get(), feature_list)) {
      LOG_EVERY_N(WARNING, 100000) << "PredictWithFastFeature";
      return;
    }
    req_context->AddCostStat("prepare_feature", start);
  }
  // monitor fake item rate
  double fake_rate = 0;
  int fake_item_count = 0;
  if (use_bs_fast_feature_) {
    fake_item_count = req_context->faked_flatten_item_indices.size();
  } else {
    if (req_context->ad_log.get() != nullptr) {
      fake_item_count = req_context->ad_log->GetFakedItemCount();
    }
  }
  if (request->item_id_size() > 0) {
    fake_rate = fake_item_count * 1e6 / request->item_id_size();
  }
  std::string cmdkey = "empty_cmdkey";
  if (request->cmdkey_size() > 0) {
    cmdkey = request->cmdkey(0);
  }
  static const std::string tag = "feature_server.fake_remote_item_rate";
  // perf every 500, value scale is 1
  std::vector<std::string> extra_tags = {monitor_name_, cmdkey, cmd_name_};
  ks::ad_nn::monitor::Monitor::Instance()->IntervalLogStash(
      "ad.predict", tag, extra_tags, monitor_handler_, static_cast<int64_t>(fake_rate),
      500, 1);
  bool async_debug_sample = false;
  if (DebugSample::GetInstance() != nullptr &&
      DebugSample::GetInstance()->IsNeedProduce(request->llsid()) &&
      feature_list != nullptr) {
    switch (DebugSample::GetInstance()->ProduceType()) {
    case DebugProduceType::AdLogWithFeatures: {
      static const uint32 dense_user_start_idx =
          feature_file_info_.user_slots.size() +
          feature_file_info_.reco_user_slots.size();
      static const uint32 item_dense_start_idx =
          feature_file_info_.photo_slots.size() +
          feature_file_info_.reco_photo_slots.size();
      static const uint32 combine_sparse_start_idx =
          item_dense_start_idx + feature_file_info_.dense_photo_slots.size();
      static const uint32 combine_dense_start_idx =
          combine_sparse_start_idx + feature_file_info_.combine_slots.size();
      if (use_bs_fast_feature_) {
        const ks::reco::UserInfo* reco_user_info = req_context->bs_log.reco_user_info();
        const std::shared_ptr<const std::string> bs_opt_userinfo =
            req_context->user_info_strings["bs_opt_userinfo"];
        const std::vector<std::shared_ptr<BSItemWrapper>>& bs_item_wrappers =
            req_context->bs_item_wrappers;
        uint64_t req_time = req_context->req_time;
        uint64_t llsid = request->llsid();
        uint64_t user_id = request->user_id();
        if (debug_feature_value_v2_) {
          async_debug_sample = true;
          req_context->raw_features.CopyFrom(*feature_list);
          auto to_str = [req_context]() -> std::string {
            std::string output_str;
            uint64_t req_time = req_context->req_time;
            uint64_t llsid = req_context->request->llsid();
            uint64_t user_id = req_context->request->user_id();
            const ks::reco::UserInfo* reco_user_info =
                req_context->bs_log.reco_user_info();
            kuaishou::ad::algorithm::AdJointLabeledLogWithFeatures adlog_with_features;
            DebugSample::GetInstance()->ConvertToAdLogWithFeatures(
                req_context->raw_features, req_time, llsid, user_id, reco_user_info,
                &adlog_with_features);
            adlog_with_features.SerializeToString(&output_str);
            return output_str;
          };
          DebugSample::GetInstance()->AsyncProduce(to_str);
        } else {
          DebugSample::GetInstance()->Produce(
              reco_user_info, bs_opt_userinfo, bs_item_wrappers, *feature_list,
              dense_user_start_idx, item_dense_start_idx, combine_sparse_start_idx,
              combine_dense_start_idx, req_time, llsid, user_id);
        }
      } else {
        DebugSample::GetInstance()->Produce(
            *req_context->ad_log, *feature_list, dense_user_start_idx,
            item_dense_start_idx, combine_sparse_start_idx, combine_dense_start_idx);
      }
      break;
    }
    case DebugProduceType::SampleJoinApiRequest: {
      async_debug_sample = true;
      req_context->raw_features.CopyFrom(*feature_list);
      auto to_str = [req_context]() -> std::string {
        std::string output_str;
        uint64_t req_time = req_context->req_time;
        uint64_t llsid = req_context->request->llsid();
        uint64_t user_id = req_context->request->user_id();
        ks::reco::SampleJoinApiRequest sample;
        DebugSample::GetInstance()->ConvertToSampleJoinApiRequest(
            req_context->raw_features, req_time, llsid, user_id, &sample);
        sample.SerializeToString(&output_str);
        return output_str;
      };
      DebugSample::GetInstance()->AsyncProduce(to_str);
      break;
    }
    default:
      break;
    }
  }
  falcon::Stat("dnn_predict_server.request_item_id_count", item_cnt_);
  // 对 sign 值差分存储
  if (enable_sign_diff_) {
    auto value_delta = [&](InferFeatureList* features) {
      for (size_t i = 0; i < features->item_features_size(); ++i) {
        auto item_feature = features->mutable_item_features(i)->mutable_features();
        auto sign_size = item_feature->sign_size();
        auto sign_data = item_feature->mutable_sign();
        for (size_t j = sign_size - 1; j > 0; --j) {
          (*sign_data)[j] -= (*sign_data)[j - 1];
        }
      }
    };
    value_delta(feature_list);
  }
  // 异步 dump 特征样本时已经拷贝了 feature_list
  if (!async_debug_sample &&
      ((copy_req_client_ && compare_feature_info_) || request->fill_feature_info())) {
    req_context->raw_features.CopyFrom(*feature_list);
  }
  if (!GetInferResult(*feature_list, arena.GetArena(), req_context)) {
    falcon::Inc("feature_server.get_infer_result_fail", 1);
    LOG_EVERY_N(ERROR, 100) << "get infer result failed";
    req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
    return;
  }
  if (copy_req_client_) {
    TriggerCopyCompare(req_context.get());
  }
  return;
}

void FeatureServer::ComparePredictResult(const FeatureServerContext& context,
                                         const UniversePredictResponse& copy_response) {
  auto self_res = context.response;
  if (self_res->predict_result_size() != copy_response.predict_result_size() ||
      (compare_item_meta_info_ &&
       self_res->predict_result_size() != copy_response.item_meta_infos_size())) {
    LOG(WARNING) << "[Compare] size not equal"
                 << " need compare item meta info: " << compare_item_meta_info_
                 << " llsid: " << context.request->llsid()
                 << " user_id: " << context.request->user_id()
                 << " device_id: " << context.request->ad_user_info().device_id()
                 << " is_unlogin: " << context.request->ad_user_info().is_unlogin_user()
                 << " self response's predict result size: "
                 << self_res->predict_result_size()
                 << " copy response's predict result size: "
                 << copy_response.predict_result_size()
                 << " copy response's item_meta_infos' size: "
                 << copy_response.item_meta_infos_size();
    return;
  }

  static const float epsilon = 0.000001;
  auto bs = context.bs_log.GetBS();
  if (bs == nullptr) {
    LOG(ERROR) << "[Compare] BS is a null ptr";
    return;
  }
  std::unordered_map<uint64_t, uint64_t> item_id2build_time;
  if (compare_item_meta_info_) {
    for (int i = 0; i < context.bs_log.item_size(); ++i) {
      uint64_t item_id = BSFieldHelper::GetSingular<uint64_t>(*bs, 17869, i);
      uint64_t bt = BSFieldHelper::GetSingular<uint64_t>(*bs, 17888, i);
      item_id2build_time[item_id] = bt;
    }
  }
  InferFeatureList copy_features;
  PlainFeatures raw_plain_features;
  PlainFeatures copy_plain_features;

  if (compare_feature_info_) {
    if (copy_response.debug_info_size() > 0) {
      const auto& str_values = copy_response.debug_info(0).debug_data().str_to_value();
      auto raw_fea_iter = str_values.find("raw_features");
      if (raw_fea_iter == str_values.end() ||
          raw_fea_iter->second.string_val_size() == 0) {
        LOG(WARNING) << "[Compare] raw features is not found in debug_info 0";
      } else {
        if (!copy_features.ParseFromString(raw_fea_iter->second.string_val(0))) {
          LOG(INFO) << "[Compare] copy feature parse from string failed";
        } else {
          LOG(INFO) << "[Compare] copy features: " << copy_features.ByteSizeLong()
                    << " raw features: " << context.raw_features.ByteSizeLong();
        }
        BuildPlainFeatures(context.raw_features, &raw_plain_features);
        BuildPlainFeatures(copy_features, &copy_plain_features);
      }
    } else {
      LOG(INFO) << "[Compare] empty debug info in response";
    }
  }

  bool has_predict_result_diff = false;
  for (int i = 0; i < self_res->predict_result_size(); ++i) {
    auto& cur_self = self_res->predict_result(i);
    auto& cur_copy = copy_response.predict_result(i);
    uint64_t item_id_in_meta = 0;
    if (compare_item_meta_info_) {
      item_id_in_meta = copy_response.item_meta_infos(i).id();
    }
    if (cur_self.item_id() != cur_copy.item_id() ||
        (compare_item_meta_info_ && cur_self.item_id() != item_id_in_meta)) {
      LOG(WARNING) << "[Compare] item id not equal"
                   << " compare_item_meta_info: " << compare_item_meta_info_
                   << " llsid: " << context.request->llsid()
                   << " user_id: " << context.request->user_id()
                   << " device_id: " << context.request->ad_user_info().device_id()
                   << " is_unlogin: " << context.request->ad_user_info().is_unlogin_user()
                   << " self item id: " << cur_self.item_id()
                   << " copy item id: " << cur_copy.item_id()
                   << " item id in meta: " << item_id_in_meta;
      continue;
    }
    if (cur_self.value_size() != cur_copy.value_size()) {
      LOG(WARNING) << "[Compare] item predict value's size not equal"
                   << " llsid: " << context.request->llsid()
                   << " user_id: " << context.request->user_id()
                   << " device_id: " << context.request->ad_user_info().device_id()
                   << " is_unlogin: " << context.request->ad_user_info().is_unlogin_user()
                   << " item id: " << cur_self.item_id()
                   << " self size: " << cur_self.value_size()
                   << " copy size: " << cur_copy.value_size();
      continue;
    }
    int64_t bs_item_build_time = -1;
    auto iter = item_id2build_time.find(cur_self.item_id());
    if (iter != item_id2build_time.end()) {
      bs_item_build_time = iter->second;
    }
    bool is_bs_fake_item = (context.faked_flatten_item_indices.count(i) > 0);
    int64_t copy_item_build_time = 0;
    if (compare_item_meta_info_) {
      copy_item_build_time = copy_response.item_meta_infos(i).build_time();
    }
    auto build_time_diff = bs_item_build_time - copy_item_build_time;
    std::string diff_str;
    bool has_diff = false;
    for (int j = 0; j < cur_self.value_size(); ++j) {
      auto diff = cur_self.value(j) - cur_copy.value(j);
      bool has_fake_item = (cur_self.value(j) < (epsilon / 1000) ||
                            cur_copy.value(j) < (epsilon / 1000) || is_bs_fake_item);
      auto abs_diff = std::abs(diff);
      float enlarge_abs_diff = 0.0f;
      if (abs_diff > epsilon) {
        base::StringAppendF(&diff_str, "j: %d, bs_v: %f pb_v: %f, v_diff:%f ", j,
                            cur_self.value(j), cur_copy.value(j), diff);
        has_diff = true;
        enlarge_abs_diff = 1000000 * abs_diff;
      }
      if (has_diff) {
        if (diff > 0) {
          if (!has_fake_item) {
            falcon::Stat("feature_server.predict_result_positive_exclude_fake_diff",
                         enlarge_abs_diff);
            falcon::Inc("feature_server.predict_result_positive_exclude_fake_cnt", 1);
          }
          falcon::Stat("feature_server.predict_result_positive_include_fake_diff",
                       enlarge_abs_diff);
          falcon::Inc("feature_server.predict_result_positive_include_fake_cnt", 1);
        } else if (diff < 0) {
          if (!has_fake_item) {
            falcon::Stat("feature_server.predict_result_negative_exclude_fake_diff",
                         enlarge_abs_diff);
            falcon::Inc("feature_server.predict_result_negative_exclude_fake_cnt", 1);
          }
          falcon::Stat("feature_server.predict_result_negative_include_fake_diff",
                       enlarge_abs_diff);
          falcon::Inc("feature_server.predict_result_negative_include_fake_cnt", 1);
        }

        if (!has_fake_item) {
          falcon::Stat("feature_server.predict_result_merge_exclude_fake_diff",
                       enlarge_abs_diff);
          falcon::Inc("feature_server.predict_result_merge_exclude_fake_cnt", 1);
        }
        falcon::Stat("feature_server.predict_result_merge_include_fake_diff",
                     enlarge_abs_diff);
        falcon::Inc("feature_server.predict_result_merge_include_fake_cnt", 1);
        if (build_time_diff + 1 > 0) {
          falcon::Inc("feature_server.item_build_time_include_fake_cnt_positive", 1);
          falcon::Stat("feature_server.item_build_time_include_fake_diff_positive",
                       build_time_diff + 1);
          if (!has_fake_item) {
            falcon::Inc("feature_server.item_build_time_exclude_fake_cnt_positive", 1);
            falcon::Stat("feature_server.item_build_time_exclude_fake_diff_positive",
                         build_time_diff + 1);
          }
        } else if (build_time_diff + 1 < 0) {
          falcon::Inc("feature_server.item_build_time_include_fake_cnt_negative", 1);
          falcon::Stat("feature_server.item_build_time_include_fake_diff_negative",
                       build_time_diff + 1);
          if (!has_fake_item) {
            falcon::Inc("feature_server.item_build_time_exclude_fake_cnt_negative", 1);
            falcon::Stat("feature_server.item_build_time_exclude_fake_diff_negative",
                         build_time_diff + 1);
          }
        }
        if (build_time_diff + 1 != 0) {
          falcon::Inc("feature_server.item_build_time_include_fake_cnt_all", 1);
          falcon::Stat("feature_server.item_build_time_include_fake_diff_all",
                       build_time_diff + 1);
          if (!has_fake_item) {
            falcon::Inc("feature_server.item_build_time_exclude_fake_cnt_all", 1);
            falcon::Stat("feature_server.item_build_time_exclude_fake_diff_all",
                         build_time_diff + 1);
          }
        }
      }
    }
    if (has_diff) {
      has_predict_result_diff = true;
      LOG(INFO) << "[Compare] predict result diff"
                << " llsid: " << context.request->llsid()
                << " user_id: " << context.request->user_id()
                << " device_id: " << context.request->ad_user_info().device_id()
                << " is_unlogin: " << context.request->ad_user_info().is_unlogin_user()
                << " item id: " << cur_self.item_id() << " item index: " << i
                << " bs item build time: " << bs_item_build_time
                << " copy item build time: " << copy_item_build_time
                << " item build time diff: " << build_time_diff + 1
                << " bs user build time: " << context.copy_req_context.bs_user_build_time
                << " pb user build time: " << context.copy_req_context.pb_user_build_time
                << " user build time diff: "
                << (static_cast<int64_t>(context.copy_req_context.bs_user_build_time) -
                    static_cast<int64_t>(context.copy_req_context.pb_user_build_time))
                << " " << diff_str;
      if (compare_feature_info_) {
        std::string item_fea_diff_log_str;
        base::StringAppendF(&item_fea_diff_log_str,
                            "llsid: %lu, user_id: %lu, devic_id: %s, item id: %lu",
                            context.request->llsid(), context.request->user_id(),
                            context.request->ad_user_info().device_id().c_str(),
                            cur_self.item_id());
        CompareItemFeatureDiff(item_fea_diff_log_str, cur_self.item_id(),
                               raw_plain_features, copy_plain_features);
      }
    } else {
      LOG(INFO) << "[Compare] predict result no diff"
                << " llsid: " << context.request->llsid()
                << " user_id: " << context.request->user_id()
                << " device_id: " << context.request->ad_user_info().device_id()
                << " is_unlogin: " << context.request->ad_user_info().is_unlogin_user()
                << " item id: " << cur_self.item_id() << " item index: " << i
                << " bs item build time: " << bs_item_build_time
                << " copy item build time: " << copy_item_build_time
                << " item build time diff: " << build_time_diff + 1
                << " bs user build time: " << context.copy_req_context.bs_user_build_time
                << " pb user build time: " << context.copy_req_context.pb_user_build_time
                << " user build time diff: "
                << (static_cast<int64_t>(context.copy_req_context.bs_user_build_time) -
                    static_cast<int64_t>(context.copy_req_context.pb_user_build_time));
    }
  }
  if (compare_feature_info_ && has_predict_result_diff) {
    std::string user_fea_diff_log_str;
    base::StringAppendF(&user_fea_diff_log_str, "llsid: %lu, user_id: %lu, device_id: %s",
                        context.request->llsid(), context.request->user_id(),
                        context.request->ad_user_info().device_id().c_str());
    CompareUserFeatureDiff(user_fea_diff_log_str, raw_plain_features,
                           copy_plain_features);
  }
  return;
}

bool FeatureServer::PrepareBS(bool* is_debug_req_with_raw_data,
                              FeatureServerContext* req_context) {
  auto construct_bs_start = Clock::now();
  auto request = req_context->request;
  req_context->req_time = base::GetTimestamp() / 1000000;
  const BatchedSamples* debug_bs = nullptr;
  if (request->is_debug_req() && request->debug_raw_data_size() == 1 &&
      request->debug_raw_data(0).pay_load_type() ==
          kuaishou::ad::debug::PayLoadType::PLT_BATCHED_SAMPLES) {
    LOG(INFO) << "A debug request";
    *is_debug_req_with_raw_data = true;
    debug_bs = flatten::StringToTable<ks::cofea_fbs::BatchedSamples>(
        request->debug_raw_data(0).serialized_bytes());
    if (debug_bs == nullptr) {
      LOG(ERROR) << "debug request parse batched samples failed from the payload";
      return false;
    }

    if (prune_bs_attr_) {
      req_context->simplified_debug_sample = std::make_shared<SimplifiedSample>(
          debug_bs, bs_sample_attr_id2idx_, bs_user_attr_id2idx_,
          bs_all_prune_attr_id2idx_, true /* is_train */);
      if (!req_context->simplified_debug_sample->Initialize()) {
        LOG(ERROR) << "Debug simplified sample initialized failed";
        return false;
      }
    } else {
      req_context->static_debug_samples.reset(new StaticBatchedSamples(debug_bs));
      if (!req_context->static_debug_samples->Initialize()) {
        LOG(ERROR) << "debug static batched samples initialize failed";
        return false;
      }
    }
    return true;
  }
  if (copy_req_cmd_ != "") {
    if (!ConstructAdlog(*request, req_context->ad_log.get(), req_context->response,
                        req_context->shared_aa_alias)) {
      LOG_EVERY_N(WARNING, 100) << "ConstructAdlog failed";
      return false;
    }
    if (copy_req_client_ && compare_feature_info_) {
      TriggerCopyRequest(req_context);
    }
  }

  if (use_component_item_debug_) {
    for (int i = 0; i < request->item_id_info_size(); i++) {
      auto& item_info = request->item_id_info(i);
      ks::ad_nn::data_online_checker::DataOnlineChecker::GetInstance()->Check(
          item_info.cretive_id(), item_info.photo_id(), item_info.live_id(),
          item_info.item_type(), request->context().sub_page_id());
    }
  }
  if (async_build_bs_ || (use_opt_user_info_ && use_opt_item_server_)) {
    if (!ConstructBSFromOpt(req_context)) {
      LOG_EVERY_N(WARNING, 100000) << "construct batched samples asynchrounously failed";
      falcon::Inc("feature_server.batched_samples_async_construct_fail", 1);
      return false;
    }
  } else {
    if (!(*is_debug_req_with_raw_data) && !ConstructBS(req_context)) {
      // !ConstructBatchedSamples(req_context)) {
      LOG_EVERY_N(WARNING, 100000) << "construct batched samples failed";
      falcon::Inc("feature_server.batched_samples_construct_fail", 1);
      return false;
    }
  }
  req_context->AddCostStat("prepare_bs.construct_bs", construct_bs_start);

  falcon::Stat("feature_server.batched_samples_construct_cost",
               GetMicroseconds(construct_bs_start));
  return true;
}

bool FeatureServer::PredictWithExtractHandler(FeatureServerContext* req_context,
                                              InferFeatureList* feature_list) {
  auto request = req_context->request;
  std::vector<size_t> item_ids;
  for (int i = 0; i < request->item_id_size(); i++) {
    item_ids.push_back(request->item_id(i));
  }

  bool is_debug_req_with_raw_data = false;
  if (!PrepareBS(&is_debug_req_with_raw_data, req_context)) {
    req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
    LOG_EVERY_N(ERROR, 100000) << "PrepareBS failed";
    return false;
  }
  size_t real_item_size =
      request->item_id_size() - req_context->faked_flatten_item_indices.size();
  item_cnt_ = real_item_size;
  if (!ignore_fake_item_monitor_)
    FakeItemCounter::Instance()->UpdateFakeItemCount(
        req_context->faked_flatten_item_indices.size(), request->item_id_size());
  bool is_all_faked =
      (req_context->faked_flatten_item_indices.size() == request->item_id_size());
  if (is_all_faked && (!fake_bs_miss_item_)) {
    LOG_EVERY_N(INFO, 1000) << "All items are faked"
                            << ", llsid: " << request->llsid()
                            << ", item size: " << request->item_id_size();
  } else {
    std::vector<size_t> sample_lens{real_item_size};
    if (is_debug_req_with_raw_data) {
      sample_lens = req_context->static_debug_samples->sample_lens();
    }
    const SampleInterface* bs_holder = req_context->batched_samples.get();
    if (is_debug_req_with_raw_data) {
      bs_holder = req_context->static_debug_samples.get();
    }

    auto bs_feature_extract_start = Clock::now();
    if (!dag_bs_extractor_->Extract(bs_holder, &sample_lens, {},
                                    &req_context->dag_features)) {
      falcon::Inc("feature_server.feature_extract_handle_fail", 1);
      req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
      return false;
    }
    auto extract_cost = GetMicroseconds(bs_feature_extract_start);
    falcon::Stat("feature_server.batched_samples_feature_extract_cost", extract_cost);
    falcon::Stat("feature_server.extract_total_time", extract_cost);

    std::vector<size_t> real_item_ids;
    for (int i = 0; i < item_ids.size(); i++) {
      if (req_context->faked_flatten_item_indices.count(i) == 0) {
        real_item_ids.push_back(item_ids[i]);
      }
    }

    auto bs_feature_convert_start = Clock::now();
    if (!ConvertRawBuffer2FeatureList(real_item_ids, req_context->dag_features,
                                      feature_list)) {
      LOG(ERROR) << "convert raw buffer 2 feature list failed";
      falcon::Inc("feature_server.convert_raw_buf_fail", 1);
      req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
      return false;
    }
    falcon::Stat("feature_server.batched_samples_feature_convert_cost",
                 GetMicroseconds(bs_feature_convert_start));
    FeaValueStat(req_context, feature_list);
  }
  return true;
}

bool FeatureServer::PredictWithFastFeature(FeatureServerContext* req_context,
                                           InferFeatureList* feature_list) {
  auto start = Clock::now();
  // 判断是否需要抽 dragon 特征
  bool need_dragon = enable_dragon_pipeline_;
  if (shared_feature_server_ && req_context->dragon_result == nullptr) {
    need_dragon = false;
  }
  int pid_size;
  bool adlog_ready = false;
  auto request = req_context->request;
  ItemConfigManager::GetInstance()->GetFakeItemCacheTTLTime(
      &req_context->fake_item_expire_time_us);
  ItemConfigManager::GetInstance()->GetItemReqRatio(&req_context->req_item_server_ratio);
  if (use_bs_fast_feature_) {
    bool is_debug_req_with_raw_data = false;
    if (!PrepareBS(&is_debug_req_with_raw_data, req_context)) {
      req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
      LOG_EVERY_N(ERROR, 100) << "Prepare BS for bs fast feature failed";
      return false;
    }
    bool is_all_faked =
        (req_context->faked_flatten_item_indices.size() == request->item_id_size());
    if (is_all_faked) {
      LOG_EVERY_N(INFO, 100000) << "All items are faked"
                                << ", llsid: " << request->llsid()
                                << ", item size: " << request->item_id_size();
    }
    if (is_debug_req_with_raw_data) {
      if (prune_bs_attr_) {
        req_context->ad_log->SetBS(req_context->simplified_debug_sample.get());
        req_context->bs_log.SetBS(req_context->simplified_debug_sample.get());
      } else {
        req_context->ad_log->SetBS(req_context->static_debug_samples.get());
        req_context->bs_log.SetBS(req_context->static_debug_samples.get());
      }
    } else {
      req_context->ad_log->SetBS(req_context->batched_samples.get());
      req_context->bs_log.SetBS(req_context->batched_samples.get());
    }
    req_context->ad_log_base = &req_context->bs_log;
    // bslog debug 请求设置 is train
    if (request->is_debug_req()) {
      req_context->bs_log.set_is_train(true);
    }
    req_context->bs_log.set_item_main_type(main_type_);
  } else {
    req_context->ad_log_base = req_context->ad_log.get();
  }
  req_context->AddCostStat("prepare_feature.prepare_bs", start);

  auto construct_ad_log_start = Clock::now();
  if (!use_bs_fast_feature_) {
    if (!adlog_ready && !ConstructAdlog(*request, req_context->req_item_server_ratio,
                                        req_context->ad_log.get(), req_context->response,
                                        req_context->shared_aa_alias)) {
      req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
      LOG_EVERY_N(ERROR, 100) << "ConstructAdlog failed";
      return false;
    }
  }
  req_context->AddCostStat("prepare_feature.construct_adlog", construct_ad_log_start);
  if (request->fill_item_info_from_req()) {
    if (request->debug_raw_data_size() != request->item_id_size()) {
      LOG(WARNING) << "debug raw data size != item_id_size"
                   << " debug raw size: " << request->debug_raw_data_size()
                   << " item_id_size: " << request->item_id_size();
    } else {
      LOG_EVERY_N(INFO, 1000) << "Fill item from req";
      if (use_bs_fast_feature_) {
        std::vector<std::shared_ptr<BSItemWrapper>> bs_item_wrappers;
        std::vector<size_t> sample_lens;
        auto sample_interface = const_cast<SampleInterface*>(req_context->bs_log.GetBS());
        auto raw_batched_samples = dynamic_cast<DynamicBatchedSamples*>(sample_interface);
        if (raw_batched_samples) {
          raw_batched_samples->GetSamples(&bs_item_wrappers);
          bs_item_wrappers.clear();
          bs_item_wrappers.reserve(request->debug_raw_data_size());
          req_context->faked_flatten_item_indices.clear();
          for (int i = 0; i < request->debug_raw_data_size(); ++i) {
            auto& item_str = request->debug_raw_data(i).serialized_bytes();
            if (!item_str.empty()) {
              auto bs_item_wrapper = std::make_shared<BSItemWrapper>();
              bs_item_wrapper->data_source = DataSource::DS_IDT;
              bs_item_wrapper->flat_str = item_str;
              size_t bs_sample_attr_id_size = GetBSSampleAttrIdSize();
              if (!bs_item_wrapper->Init(bs_sample_attr_id_size, bs_sample_attr_id2idx_,
                                         prune_bs_attr_)) {
                LOG(ERROR) << "Init bs wrapper failed for item: " << i;
                continue;
              }
              bs_item_wrappers.emplace_back(std::move(bs_item_wrapper));
            } else {
              if (fake_bs_miss_item_) {
                bs_item_wrappers.emplace_back(nullptr);
              }
              req_context->faked_flatten_item_indices.insert(i);
            }
          }
          sample_lens.emplace_back(bs_item_wrappers.size());
          raw_batched_samples->SetSampleLens(sample_lens);
          raw_batched_samples->SetSamples(std::move(bs_item_wrappers));
        } else {
          LOG(ERROR) << "bs log is nullptr";
        }
      } else {
        for (int i = 0; i < request->debug_raw_data_size(); ++i) {
          req_context->items_from_req.emplace_back(new ItemAdaptorV1());
          if (!req_context->items_from_req.back()->ParseFromString(
                  request->debug_raw_data(i).serialized_bytes())) {
            LOG(ERROR) << "Item parse from req debug raw data failed, index: " << i;
            continue;
          }
          req_context->ad_log->SetItem(req_context->items_from_req.back().get(), i);
          if (request->is_faked_item(i)) {
            req_context->ad_log->AddFakedItemIndex(i);
          }
          LOG_EVERY_N(INFO, 1000) << "Fill item from req index: " << i
                                  << " is faked: " << request->is_faked_item(i);
        }
      }
    }
  }
  // 进行行业 5.0 的预映射, 避免后续特征抽取时进行重复映射
  uint64_t unique_key = ks::ad_algorithm::GenerateUniqueKey();

  if (LabelCentreMapping::Instance().Inited() && req_context->ad_log_base != nullptr) {
    auto pre_mapping_start = Clock::now();
    req_context->ad_log_base->set_unique_key(unique_key);
    if (!LabelCentreMapping::Instance().PreMapping(*req_context->ad_log_base)) {
      LOG_EVERY_N(WARNING, 1000) << "LabelCentreMapping failed";
    }
    falcon::Stat("feature_server.label_centre_pre_mapping_cost",
                 GetMicroseconds(pre_mapping_start));
  }

  if (IdIndustryMapping::Instance().Inited() && req_context->ad_log_base != nullptr) {
    auto pre_mapping_start = Clock::now();
    req_context->ad_log_base->set_unique_key(unique_key);
    if (!IdIndustryMapping::Instance().PreMapping(*req_context->ad_log_base)) {
      LOG_EVERY_N(WARNING, 1000) << "Industry id premapping failed";
    }
    falcon::Stat("feature_server.industry_v5_id_pre_mapping_cost",
                 GetMicroseconds(pre_mapping_start));
    req_context->AddCostStat("prepare_feature.pre_mapping_industry_v5_id_cost",
                             pre_mapping_start);
  }

  if (GameProductNameMapping::Instance().Inited() &&
      req_context->ad_log_base != nullptr) {
    auto pre_mapping_start = Clock::now();
    req_context->ad_log_base->set_unique_key(unique_key);
    if (!GameProductNameMapping::Instance().PreMapping(*req_context->ad_log_base)) {
      LOG_EVERY_N(WARNING, 1000) << "Industry product name premapping failed";
    }
    falcon::Stat("feature_server.industry_v5_product_name_pre_mapping_cost",
                 GetMicroseconds(pre_mapping_start));
    req_context->AddCostStat("prepare_feature.pre_mapping_industry_v5_product_name_cost",
                             pre_mapping_start);
  }

#ifdef BUILD_WITH_DRAGON
  // 先跑 dragon 提取特征，后续在 Extract 方法中进行填充
  static thread_local DragonOutputResult dragon_result;
  std::vector<folly::Future<folly::Unit>> dragon_result_future;
  dragon_result.clear();
  dragon_result.resize(req_context->ad_log->item_size());
  DragonTask dragon_task;
  auto dragon_task_start = Clock::now();
  if (need_dragon) {
    dragon_task.request = request;
    req_context->device_id = request->ad_user_info().device_id();
    dragon_task.device_id = &req_context->device_id;
    dragon_task.user_id = request->user_id();
    dragon_task.adlog = req_context->ad_log.get();
    if (use_bs_fast_feature_) {
      // adlog has been created for bs
      dragon_task.bslog = &req_context->bs_log;
    }
    if (shared_feature_server_) {
      dragon_task.template_keys = req_context->template_keys;
      dragon_task.template_semaphores = req_context->template_semaphores;
      dragon_task.dragon_result = req_context->dragon_result;
      dragon_task.pipeline2item_id_idxs = req_context->pipeline2item_id_idxs;
    } else {
      dragon_task.dragon_result = &dragon_result;
    }
    feature_dragon_->RunDragonTask(dragon_task, &dragon_result_future);
  }
#endif

  auto feature_process_start = Clock::now();
  if (feature_processor_) {
    auto context = std::make_shared<ProcessorContext>();
    context->start = Clock::now();
    context->request = req_context->request;
    context->ad_log = req_context->ad_log;
    if (use_bs_fast_feature_) {
      context->bs_log.reset(&req_context->bs_log,
                            [](const ks::ad_algorithm::BSLog* p) {});
      context->is_bs = true;
    }
    if (opt_bs_item_ && need_bs_proccessor_opt_) {
      std::vector<std::vector<FeaProcessFunc>> funcs;
      feature_processor_->GetProcessFuncs(&context, &funcs);
      if (funcs.size() > 0) {
        std::vector<std::vector<folly::Future<folly::Unit>>> processer_result_future;
        processer_result_future.resize(funcs.size());
        for (int i = 0; i < funcs.size(); ++i) {
          for (int j = 0; j < funcs[i].size(); ++j) {
            processer_result_future[i].push_back(
                FeatureThreadResourceManager::GetIOThreadPool()->addFuture(
                    [& func = funcs[i][j], this]() { func(); }));
          }
          if (funcs[i].size() > 0) {
            folly::collectAll(processer_result_future[i]).wait();
          }
        }
      }
    } else {
      // 共享特征服务按需调用 preprocessor
      if (shared_feature_server_) {
        if (req_context->preprocessor_set) {
          feature_processor_->AsyncProcessWithNames(context,
                                                    *req_context->preprocessor_set);
        }
      } else {
        feature_processor_->Process(context);
      }
    }
  }
  req_context->AddCostStat("prepare_feature.feature_processor", feature_process_start);
  falcon::Stat("feature_processor.process_time_total",
               GetMicroseconds(feature_process_start));
  /**
   * embedding_map 缓存 item 中 dense 特征和 embedding 后的 sparse 特征；
   */
  bool extract_success;
  // 共享特征服务不支持 embedding 缓存
  auto extract_start = Clock::now();
  if (FLAGS_enable_embedding_feature_cache) {
    req_context->embedding_map->resize(req_context->ad_log_base->item_size());
    extract_success = feature_extractor_->Extract(*req_context->ad_log_base, feature_list,
                                                  req_context->embedding_map);
    req_context->combine_feature_start = feature_extractor_->GetCombineFeatureStart();
  } else if (shared_feature_server_) {
    extract_success = shared_feature_extractor_->Extract(
        *req_context->ad_log_base, *req_context->user_feature_configs,
        *req_context->item_feature_configs, *req_context->item_feature_table,
        req_context->user_extract_results, req_context->item_extract_results);
  } else {
    extract_success =
        feature_extractor_->Extract(*req_context->ad_log_base, feature_list);
  }

  if (!extract_success) {
    falcon::Inc("feature_server.feature_extract_fail", 1);
    LOG_EVERY_N(ERROR, 100) << "extract feature failed";
    req_context->finish(kuaishou::ad::algorithm::INTERNAL_ERROR);
#ifdef BUILD_WITH_DRAGON
    if (need_dragon && !shared_feature_server_) {
      auto dragon_task_collect_start = Clock::now();
      folly::collectAll(dragon_result_future).wait();
      falcon::Stat("feature_server.dragon_task_wait_time_cost",
                   GetMicroseconds(dragon_task_start));
      falcon::Stat("feature_server.dragon_task_collect_time_cost",
                   GetMicroseconds(dragon_task_collect_start));
    }
#endif
    return false;
  }
  req_context->AddCostStat("prepare_feature.extract_fea", extract_start);

#ifdef BUILD_WITH_DRAGON
  if (need_dragon && !shared_feature_server_) {
    // 等待 dragon pipeline 完成
    auto dragon_task_collect_start = Clock::now();
    folly::collectAll(dragon_result_future).wait();
    falcon::Stat("feature_server.dragon_task_wait_time_cost",
                 GetMicroseconds(dragon_task_start));
    falcon::Stat("feature_server.dragon_task_collect_time_cost",
                 GetMicroseconds(dragon_task_collect_start));
    // 共享特征服务拆分时进行填充
    if (!shared_feature_server_) {
      feature_dragon_->ExtractDragonFeature(*req_context->ad_log, feature_list,
                                            dragon_result);
    }
  }
#endif
  // 共享特征服务在拆分之后统计特征值
  if (!shared_feature_server_) {
    FeaValueStat(req_context, feature_list);
  }

  return true;
}

void FeatureServer::FeaValueStat(const FeatureServerContext* req_context,
                                 const InferFeatureList* feature_list) {
  auto& stat_data_info = feature_stat_.GetCustomDataInfo();
  auto request = req_context->request;
  switch (stat_data_info.type) {
  case CustomDataType::kNoCustomData:
  case CustomDataType::kCmdKey:
    if (request->cmdkey_size() > 0) {
      feature_stat_.DoFeaValueStat(*feature_list, request->cmdkey(0));
    } else {
      feature_stat_.DoFeaValueStat(*feature_list, request->cmd(0));
    }
    break;
  case CustomDataType::kUserEnumId: {
    if (!use_bs_fast_feature_) {
      LOG_EVERY_N(ERROR, 1000) << "feature stat with UserEnumId only support bs model.";
      break;
    }
    std::string data_str = "";
    if (stat_data_info.data_type == "uint64_t") {
      uint64_t stat_value = BSFieldHelper::GetSingular<uint64_t>(
          *(req_context->batched_samples), stat_data_info.enum_id, 0);
      if (stat_data_info.int_stat_attr_set.find(stat_value) ==
          stat_data_info.int_stat_attr_set.end())
        break;
      data_str = stat_data_info.short_key + "_" + std::to_string(stat_value);
    } else if (stat_data_info.data_type == "string") {
      std::string stat_value = std::string(BSFieldHelper::GetSingular<absl::string_view>(
          *(req_context->batched_samples), stat_data_info.enum_id, 0));
      if (stat_data_info.string_stat_attr_set.find(stat_value) ==
          stat_data_info.string_stat_attr_set.end())
        break;
      data_str = stat_data_info.short_key + "_" + stat_value;
    }
    feature_stat_.DoFeaValueStat(*feature_list, data_str);
    break;
  }
  default:
    LOG_EVERY_N(ERROR, 1000) << "Invaild stat_data_info type: " << stat_data_info.type;
  }
  return;
}

bool FeatureServer::TransferBSItem(const std::string& raw_str,
                                   std::shared_ptr<ItemAdaptorV1>* item_ptr) {
  auto bs_str = std::make_shared<std::string>();
  if (!TransferBS(raw_str, "item", &bs_str)) {
    LOG_EVERY_N(WARNING, 1000) << "TransferBS failed";
    return false;
  }
  (*item_ptr)->SetTransferedStr(std::move(bs_str));
  return true;
}

bool FeatureServer::TransferBS(const std::string& raw_str, const std::string& type,
                               std::shared_ptr<std::string>* bs_str) {
  if (transfer_client_) {
    auto transfer_start = Clock::now();
    std::string peer, host, port, endpoint;
    auto stub = transfer_client_->SelectOne(&peer);
    if (!GetHostAndPort(peer, &host, &port)) {
      LOG_EVERY_N(WARNING, 10000) << "Get host and port from peer failed, peer: " << peer;
      return false;
    }
    if (host.empty()) {
      LOG(ERROR) << "Transfer host is empty";
      return false;
    } else {
      endpoint = absl::Substitute("$0:$1", host, port);
    }

    CommonRpcRequest transfer_req;
    auto transfer_res = std::make_shared<CommonRpcResponse>();
    transfer_req.set_data(raw_str);
    (*transfer_req.mutable_params())["type"] = type;
    auto status = stub->TransForm(ks::ad_base::OptionsFromMilli(transfer_bs_timeout_),
                                  transfer_req, transfer_res.get());
    auto transfer_cost = GetMicroseconds(transfer_start);
    falcon::Stat("feature_server.transfer_bs_cost", transfer_cost);
    if (!status.ok()) {
      LOG_EVERY_N(WARNING, 100)
          << "transfer service failed, error_code " << status.error_code()
          << " error_msg " << status.error_message();
      return false;
    }
    bs_str->reset(transfer_res->release_data());
  }
  return true;
}

bool FeatureServer::ConvertUserInfo2BS(FeatureServerContext* req_context) {
  auto build_start = Clock::now();
  auto adlog = req_context->ad_log.get();
  AdJointLabeledLog* ad_joint_labeled_log = &(adlog->Get());
  std::string serialized_str;
  ad_joint_labeled_log->user_info().SerializeToString(&serialized_str);
  req_context->user_id_in_bs = ad_joint_labeled_log->user_info().id();
  auto build_cost = GetMicroseconds(build_start);
  falcon::Stat("feature_server.construct_user_info_str_cost", build_cost);

  if (!TransferBS(serialized_str, "user", &req_context->transfer_bs_user_info)) {
    LOG_EVERY_N(WARNING, 1000) << "transfer bs for user info failed";
    return false;
  }

  return true;
}

void FeatureServer::AckUsed(
    grpc::ServerContext* context, const UsedItem* used_item,
    ks::kess::rpc::grpc::ResponseWriter<::kuaishou::ad::algorithm::AckStatus> res_writer,
    ks::kess::rpc::grpc::EventLoop* event_loop) {
  falcon::Inc("predict_server.illegal_rpc_ack_request", 1);
  LOG_EVERY_N(WARNING, 10) << "unsupported ack method";
  res_writer.FinishWithError(
      grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "ACK_NOT_SUPPORTED"), [](bool ok) {});
}

void FeatureServer::AckUsed(::google::protobuf::RpcController* controller,
                            const ::kuaishou::ad::algorithm::UsedItem* request,
                            ::kuaishou::ad::algorithm::AckStatus* response,
                            ::google::protobuf::Closure* done) {
  falcon::Inc("predict_server.illegal_rpc_ack_request", 1);
  brpc::ClosureGuard done_guard(done);
  LOG_EVERY_N(WARNING, 10) << "unsupported ack method";
  brpc::Controller* cntl = static_cast<brpc::Controller*>(controller);
  if (cntl == nullptr) {
    response->set_status(kuaishou::ad::algorithm::INTERNAL_ERROR);
    return;
  }
  auto code = grpc::StatusCode::UNIMPLEMENTED;
  cntl->SetFailed(code, "AckUsed_NOT_SUPPORTED");
  return;
}

kuaishou::ad::algorithm::ItemType FeatureServer::ParseItemType(
    const std::string& item_type_string) {
  kuaishou::ad::algorithm::ItemType type{kuaishou::ad::algorithm::UNKNOWN};
  if (!kuaishou::ad::algorithm::ItemType_Parse(item_type_string, &type)) {
    LOG(FATAL) << "unknown item type: " << item_type_string;
    return kuaishou::ad::algorithm::UNKNOWN;
  }
  return type;
}

void FeatureServer::ParseCopyReqParas(const base::Json* kconf_model_config) {
  copy_req_cmd_ = kconf_model_config->GetString("copy_req_cmd", "");
  if (!copy_req_cmd_.empty()) {
    copy_req_kess_name_ = kconf_model_config->GetString("copy_req_kess_name", "");
    if (copy_req_kess_name_.empty()) {
      copy_req_kess_name_ =
          base::StringReplace(copy_req_cmd_, "/", "_", true /* replace all */);
      copy_req_kess_name_ =
          base::StringReplace(copy_req_kess_name_, ":", "_", true /* replace all */);
      copy_req_kess_name_ = "grpc" + copy_req_kess_name_ + "_predict_server";
    }
    copy_req_timeout_ = kconf_model_config->GetInt("copy_req_timeout", 500);
    compare_item_meta_info_ =
        kconf_model_config->GetBoolean("compare_item_meta_info", true);
    compare_feature_info_ = kconf_model_config->GetBoolean("compare_feature_info", true);
    fill_item_info_in_req_ =
        kconf_model_config->GetBoolean("fill_item_info_in_req", true);
    LOG(INFO) << "[Debug] compare feature info: " << compare_feature_info_
              << " fill item info in req: " << fill_item_info_in_req_;
    WaitForCopyReqServerReady();
  }
  return;
}

void FeatureServer::FillCopyResInfos(
    std::shared_ptr<FeatureServerContext>& req_context,  // NOLINT
    UniversePredictResponse* response) {
  std::unordered_map<uint64_t, uint64_t> item_id2bs_build_time;
  if (use_bs_fast_feature_) {
    auto bs = req_context->bs_log.GetBS();
    if (bs == nullptr) {
      LOG(ERROR) << "[Debug] BS is a null ptr";
      return;
    }
    for (int i = 0; i < req_context->bs_log.item_size(); ++i) {
      // 17869 is the adlog.item_info.build_time
      uint64_t item_id = BSFieldHelper::GetSingular<uint64_t>(*bs, 17869, i);
      uint64_t bt = BSFieldHelper::GetSingular<uint64_t>(*bs, 17888, i);
      item_id2bs_build_time[item_id] = bt;
    }
  }

  if (req_context->request->fill_feature_info()) {
    auto debug_info = response->add_debug_info();
    kuaishou::ad::debug::ValueProto fea_values;
    fea_values.set_data_type(kuaishou::ad::debug::DT_STRING);
    auto temp_str = fea_values.add_string_val();
    req_context->raw_features.SerializeToString(temp_str);
    (*debug_info->mutable_debug_data()->mutable_str_to_value())["raw_features"] =
        fea_values;
    LOG_EVERY_N(INFO, 10000) << "[Debug] feature size: " << temp_str->size();
  }
  auto request = req_context->request;
  for (uint32_t item_idx = 0; item_idx < request->item_id_size(); ++item_idx) {
    bool fake = false;
    if (use_feature_extract_handler_ || use_bs_fast_feature_) {
      const auto& faked_flatten_item_indices = req_context->faked_flatten_item_indices;
      fake =
          faked_flatten_item_indices.find(item_idx) != faked_flatten_item_indices.end();
    } else {
      fake = req_context->ad_log->IsFakedItem(item_idx);
    }
    auto p_res = response->mutable_predict_result(item_idx);
    auto item_meta = response->add_item_meta_infos();
    item_meta->set_id(request->item_id(item_idx));
    if (fake) {
      item_meta->set_build_time(0);
      LOG_EVERY_N(INFO, 100000)
          << "[Debug] Add build time for fake: 0"
          << " llsid: " << req_context->request->llsid()
          << " item id: " << req_context->request->item_id(item_idx);
    } else {
      if (use_bs_fast_feature_) {
        auto iter = item_id2bs_build_time.find(request->item_id(item_idx));
        if (iter != item_id2bs_build_time.end()) {
          item_meta->set_build_time(iter->second);
        } else {
          item_meta->set_build_time(0);
          LOG_EVERY_N(WARNING, 100000)
              << "[Debug] not fake, but failed to find the build time, "
              << "set it to 0, "
              << " llsid: " << req_context->request->llsid()
              << " item id: " << req_context->request->item_id(item_idx);
        }
      } else {
        item_meta->set_build_time(req_context->ad_log->item(item_idx).build_time());
      }
      LOG_EVERY_N(INFO, 100000)
          << "[Debug] Add build time: " << item_meta->build_time()
          << " llsid: " << req_context->request->llsid()
          << " item id: " << req_context->request->item_id(item_idx);
    }
  }
  return;
}

bool FeatureServer::GetAttrValueInBS(const BatchedSamples* bs, const std::string& source,
                                     const std::string& attr_id,
                                     flatten::AttrType attr_type, uint64_t* value) {
  if (bs == nullptr) {
    LOG_EVERY_N(WARNING, 100000) << "bs is a nullptr, source: " << source;
    return false;
  }
  std::vector<const RawFeature*> target_raw_feas;
  if (!flatten::GetAttrsByName(*bs, attr_id, attr_type, &target_raw_feas) ||
      target_raw_feas.size() <= 0) {
    LOG_EVERY_N(WARNING, 100000)
        << "GetAttsByName failed "
        << " raw feas.size: " << target_raw_feas.size() << " source: " << source;
    return false;
  } else {
    if (target_raw_feas[0] == nullptr) {
      LOG_EVERY_N(INFO, 100000) << "raw fea is a nullptr, source: " << source;
      return false;
    }
    auto kind = target_raw_feas[0]->kind();
    if (kind == nullptr) {
      LOG_EVERY_N(WARNING, 100000) << "[Debug] kind is a nullptr, source: " << source;
      return false;
    }
    if (!flatten::GetValue<int64_t>(*kind, reinterpret_cast<int64_t*>(value))) {
      LOG_EVERY_N(WARNING, 100000) << "[Debug] GetValue failed, source: " << value;
      return false;
    }
  }
  return true;
}

void FeatureServer::TriggerCopyRequest(FeatureServerContext* req_context) {
  auto request = req_context->request;
  auto& copy_req_context = req_context->copy_req_context;
  copy_req_context.copy_request = std::make_shared<UniversePredictRequest>();
  copy_req_context.copy_response = std::make_shared<UniversePredictResponse>();
  copy_req_context.copy_promise = std::make_shared<std::promise<bool>>();
  copy_req_context.source_promise = std::make_shared<std::promise<bool>>();
  copy_req_context.copy_future = copy_req_context.copy_promise->get_future().share();
  copy_req_context.source_future = copy_req_context.source_promise->get_future().share();
  auto* client = ks::ad_nn::CachedUserInfoClient::getInstance();
  kuaishou::ad::algorithm::UserInfo user_info;
  int ret = client->ParseUserInfo(&user_info, request->user_info_bin(), true);
  if (ret != 0) {
    LOG(INFO) << "[Compare] parse pb user info failed, llsid: " << request->llsid()
              << " user_id: " << request->user_id()
              << " device_id: " << request->ad_user_info().device_id()
              << " is_unlogin: " << request->ad_user_info().is_unlogin_user();
  } else {
    copy_req_context.pb_user_build_time = user_info.build_time();
  }
  AsyncCopyPredict(req_context, copy_req_context.copy_request,
                   copy_req_context.copy_response, copy_req_context.copy_promise);
  return;
}

void FeatureServer::TriggerCopyCompare(FeatureServerContext* req_context) {
  auto& copy_req_context = req_context->copy_req_context;
  copy_req_context.copy_future.wait();
  if (copy_req_context.copy_response->status() != kuaishou::ad::algorithm::STATUS_OK) {
    LOG_EVERY_N(WARNING, 1000) << "copy predict failed";
  } else {
    LOG_EVERY_N(INFO, 1000) << "copy predict successfully";
    copy_req_context.source_future.wait();
    ComparePredictResult(*req_context, *copy_req_context.copy_response);
  }
}

FeatureServer::FeatureServer(bool shared_feature)
    : item_type_(ParseItemType(FLAGS_item_type)),
      item_tasks_(kTaskQueueCapacity),
      is_i18n_(FLAGS_is_i18n),
      counter_(0) {
  shared_feature_server_ = true;
  // 1. 读取模型配置
  std::string model_cmd = GetKcsModelCmd();
  auto kconf_model_config = ks::ad_nn::PredictServiceKconfUtil::GetModelConfig(model_cmd);
  if (!kconf_model_config) {
    std::ostringstream oss;
    oss << "no model config found for model " << model_cmd;
    ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
    LOG(FATAL) << "no model config found for model " << model_cmd;
  }
  model_config_ = kconf_model_config;
  int loop_num = kconf_model_config->GetInt("shared_event_loop", 100);
  event_loops_.reset(new ks::kess::rpc::grpc::EventLoopGroup(loop_num));
  shared_use_brpc_ = kconf_model_config->GetBoolean("shared_use_brpc", false);
  use_brpc_ = kconf_model_config->GetBoolean("use_brpc", false);
  LOG(INFO) << "feature_server use_brpc set: " << use_brpc_;
  // 2. init base variable
  InitBaseVariable(model_cmd);
  shared_total_timeout_ = kconf_model_config->GetInt("shared_total_timeout", 100);
  // TODO(xiaoxiaojun) 支持 DAG 分支
  use_feature_extract_handler_ = false;
  FLAGS_enable_item_pb_parse_opt = false;
  // 3. init dragon
  InitDragon();
  // 4. init bs
  InitBsFastFeature();
  // 5. init predict service
  InitPredictService();
  // 6. init picasso
  InitPicasso();
  // 7. 初始化共享特征提取模块
  if (0 != FeatureFileInfo::LoadSharedFeatureFile("", use_bs_fast_feature_,
                                                  &shared_feature_file_infos_)) {
    std::ostringstream oss;
    oss << "load feature file failed";
    ks::ad_nn::ServiceStatus::Get().GetModule(MODULE_DEFAULT)->LogFail(oss.str());
    LOG(FATAL) << "load feature file failed";
  }
  shared_feature_extractor_.reset(new SharedFeatureExtractor());
  // 8. 初始化 debugsample 配置
  DebugSample::GetInstance();
  // 9. init infer service timeout
  InferServiceTimeout();
  // 12. init preprocessor
  auto preprocessor_config = kconf_model_config->Get("feature_process_config");
  if (preprocessor_config != nullptr) {
    feature_processor_ = std::make_unique<FeatureProcessor>();
    feature_processor_->Init(preprocessor_config, shared_feature_server_);
    LOG(INFO) << "feature_process_config init success";
  } else {
    LOG(INFO) << "feature_process_config not found";
  }
  InitAttrId2Idx();
  // 10. 初始化物料相关逻辑
  InitItem();
  // 11. init dump request dir
  if (FLAGS_dump_request_freq > 0) {
    base::file_util::CreateDirectory(FLAGS_dump_request_path + "/req");
    base::file_util::CreateDirectory(FLAGS_dump_request_path + "/res");
    // 存储请求 infer server 的 FeatureRequest
    base::file_util::CreateDirectory(FLAGS_dump_request_path + "/infer_req");
  }
  // 13. init item parse
  InitItemParse();
  LOG(INFO) << "feature server init succeeds with actual service name "
            << actual_service_name_;
}

void FeatureServer::BuildPlainFeatures(const InferFeatureList& feature_list,
                                       PlainFeatures* plain_features) {
  auto build_fea_value =
      [this, plain_features](
          bool is_user, uint64_t item_id,
          const kuaishou::ad::algorithm::FeatureValueList& fea_values,
          const ::google::protobuf::RepeatedPtrField<std::string>& fea_names,
          const ::google::protobuf::RepeatedField<uint32_t>& fea_offsets) {
        uint32_t last_offset = 0;
        uint32_t cur_offset = 0;
        uint32_t last_sparse_offset = 0;
        uint32_t last_dense_offset = 0;
        for (int i = 0; i < fea_names.size(); ++i) {
          const auto& fea_name = fea_names.Get(i);
          cur_offset = fea_offsets.Get(i);
          bool is_dense = feature_stat_.IsDenseFeature(fea_name);
          // is_dense
          if (is_dense) {
            last_offset = last_dense_offset;
            double total_value = 0;
            std::vector<float>* dst = nullptr;
            if (is_user) {
              dst = &(*plain_features).user_dense_features[fea_name];
            } else {
              dst = &(*plain_features).item_dense_features[item_id][fea_name];
            }
            for (int j = last_offset; j < cur_offset && j < fea_values.value().size();
                 ++j) {
              dst->emplace_back(fea_values.value(j));
            }
            last_dense_offset = cur_offset;
          } else {
            last_offset = last_sparse_offset;
            std::vector<uint64_t>* dst = nullptr;
            if (is_user) {
              dst = &(*plain_features).user_sparse_features[fea_name];
            } else {
              dst = &(*plain_features).item_sparse_features[item_id][fea_name];
            }
            for (int j = last_offset; j < cur_offset && j < fea_values.sign().size();
                 ++j) {
              dst->emplace_back(fea_values.sign(j));
            }
            last_sparse_offset = cur_offset;
          }
          uint32_t fea_count = cur_offset - last_offset;
          LOG(INFO) << "[Debug] fea name: " << fea_name << " is dense: " << is_dense
                    << " is user: " << is_user << " last offset: " << last_offset
                    << " cur offset: " << cur_offset
                    << " last sparse offset: " << last_sparse_offset
                    << " last dense offset: " << last_dense_offset
                    << " fea_count: " << fea_count << " index : " << i
                    << " fea_names.size(): " << fea_names.size();
        }
      };

  const auto& user_feature_names = feature_list.user_feature_names();
  const auto& user_features = feature_list.user_features();
  const auto& user_feature_offset = feature_list.user_feature_offset();
  if (user_feature_names.size() == user_feature_offset.size()) {
    build_fea_value(true, 0, user_features, user_feature_names, user_feature_offset);
  } else {
    LOG_EVERY_N(WARNING, 1) << "Size not match"
                            << ", user_feature_names.size(): "
                            << user_feature_names.size()
                            << ", user_feature_offset.size(): "
                            << user_feature_offset.size();
  }

  const auto& item_feature_names = feature_list.item_feature_names();
  const auto& item_features = feature_list.item_features();
  for (int i = 0; i < item_features.size(); ++i) {
    const auto& entry = item_features.Get(i);
    const auto& features = entry.features();
    const auto& feature_offset = entry.feature_offset();
    if (feature_offset.size() == item_feature_names.size()) {
      build_fea_value(false, entry.item_id(), features, item_feature_names,
                      feature_offset);
    } else {
      LOG_EVERY_N(WARNING, 1) << "Size not match"
                              << ", item_feature_names.size(): "
                              << item_feature_names.size()
                              << ", item_feature_offset.size(): " << feature_offset.size()
                              << ", item id: " << entry.item_id();
      continue;
    }
  }
  return;
}

template <typename T>
bool CompareFeatureDiff(
    bool is_sparse, const std::string& log_str,
    const std::unordered_map<std::string, std::vector<T>>& raw_features,
    const std::unordered_map<std::string, std::vector<T>>& copy_features) {
  if (raw_features.size() != copy_features.size()) {
    LOG(WARNING) << "[Compare] " << log_str << " type: features' size are not equal"
                 << " raw features' size: " << raw_features.size()
                 << " copy features' size: " << copy_features.size();
    ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                       "invalid_size");
    return false;
  }
  bool ok = true;
  for (const auto& entry : raw_features) {
    auto copy_iter = copy_features.find(entry.first);
    if (copy_iter == copy_features.end()) {
      LOG(WARNING) << "[Compare] " << log_str
                   << " type: fea name missing: " << entry.first;
      ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                         "miss_feature", entry.first);
      ok = false;
      continue;
    }
    if (is_sparse) {
      std::unordered_map<T, int> raw_feature_sets;
      std::string raw_feas_str;
      for (const auto value : entry.second) {
        raw_feature_sets[value]++;
        base::StringAppendF(&raw_feas_str, "%lu ", static_cast<uint64_t>(value));
      }
      std::string copy_feas_str;
      std::unordered_map<T, int> copy_feature_sets;
      for (const auto value : copy_iter->second) {
        copy_feature_sets[value]++;
        base::StringAppendF(&copy_feas_str, "%lu ", static_cast<uint64_t>(value));
      }
      bool has_diff = false;
      for (const auto& fea_entry : raw_feature_sets) {
        auto copy_fea_iter = copy_feature_sets.find(fea_entry.first);
        if (copy_fea_iter == copy_feature_sets.end()) {
          LOG(INFO) << "[Compare] " << log_str << " fea name: " << entry.first
                    << " is sparse: 1"
                    << " has diff: 1"
                    << " type: sparse_value_miss: " << fea_entry.first
                    << " raw_feas: " << raw_feas_str << " copy_feas: " << copy_feas_str;
          ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                             "sparse_value_diff", entry.first);
          ok = false;
          has_diff = true;
          break;
        } else {
          if (fea_entry.second != copy_fea_iter->second) {
            LOG(INFO) << "[Compare] " << log_str << " fea name: " << entry.first
                      << " is sparse: 1"
                      << " has diff: 1"
                      << " type: sparse_value_diff"
                      << " raw_feas: " << raw_feas_str << " copy_feas: " << copy_feas_str;
            ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                               "sparse_value_diff", entry.first);
            ok = false;
            has_diff = true;
            break;
          }
        }
      }
      if (!has_diff) {
        ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                           "sparse_diff_succ", entry.first);
      }
    } else {
      std::string raw_feas_str;
      for (const auto value : entry.second) {
        base::StringAppendF(&raw_feas_str, "%f ", static_cast<float>(value));
      }
      std::string copy_feas_str;
      for (const auto value : copy_iter->second) {
        base::StringAppendF(&copy_feas_str, "%f ", static_cast<float>(value));
      }
      if (entry.second.size() != copy_iter->second.size()) {
        LOG(INFO) << "[Compare] " << log_str << " fea name: " << entry.first
                  << " is sparse: 0"
                  << " has diff: 1"
                  << " type: size not equal"
                  << " raw feas' size: " << entry.second.size()
                  << " copy feas' size: " << copy_iter->second.size()
                  << " raw feas: " << raw_feas_str << " copy feas: " << copy_feas_str;
        ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                           "dense_value_diff", entry.first);
        continue;
      }
      bool has_diff = false;
      for (int i = 0; i < entry.second.size(); ++i) {
        if (std::abs(static_cast<float>(entry.second[i] - copy_iter->second[i])) >
            0.000001) {
          LOG(INFO) << "[Compare] " << log_str << " fea name: " << entry.first
                    << " is sparse: 0"
                    << " has diff: 1"
                    << " type: value diff"
                    << " index: " << i << " raw feas: " << raw_feas_str
                    << " copy feas: " << copy_feas_str;
          ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                             "dense_value_diff", entry.first);
          ok = false;
          has_diff = true;
          break;
        }
      }
      if (!has_diff) {
        ks::infra::PerfUtil::CountLogStash(1, "shared_feature_server", "compare_diff",
                                           "dense_diff_succ", entry.first);
      }
    }
  }
  return ok;
}

bool FeatureServer::CompareUserFeatureDiff(const std::string& log_str,
                                           const PlainFeatures& raw_features,
                                           const PlainFeatures& copy_features) {
  LOG_EVERY_N(INFO, 1000)
      << "[Compare] compare user feature diff"
      << " raw sparse feature size: " << raw_features.user_sparse_features.size()
      << " copy sparse feature size: " << copy_features.user_sparse_features.size()
      << " raw dense feature size: " << raw_features.user_dense_features.size()
      << " copy dense feature size: " << copy_features.user_dense_features.size();
  auto sparse_ok = CompareFeatureDiff(true, log_str, raw_features.user_sparse_features,
                                      copy_features.user_sparse_features);
  auto dense_ok = CompareFeatureDiff(false, log_str, raw_features.user_dense_features,
                                     copy_features.user_dense_features);
  return sparse_ok && dense_ok;
}

bool FeatureServer::CompareItemFeatureDiff(const std::string& log_str, uint64_t item_id,
                                           const PlainFeatures& raw_features,
                                           const PlainFeatures& copy_features) {
  bool dense_ok = true;
  bool sparse_ok = true;
  auto raw_sparse_fea_iter = raw_features.item_sparse_features.find(item_id);
  auto copy_sparse_fea_iter = copy_features.item_sparse_features.find(item_id);
  auto raw_dense_fea_iter = raw_features.item_dense_features.find(item_id);
  auto copy_dense_fea_iter = copy_features.item_dense_features.find(item_id);
  auto found_raw_sparse =
      (raw_sparse_fea_iter != raw_features.item_sparse_features.end());
  auto found_copy_sparse =
      (copy_sparse_fea_iter != copy_features.item_sparse_features.end());
  auto found_raw_dense = (raw_dense_fea_iter != raw_features.item_dense_features.end());
  auto found_copy_dense =
      (copy_dense_fea_iter != copy_features.item_dense_features.end());
  if ((found_raw_sparse && found_copy_sparse) || (found_raw_dense && found_copy_dense)) {
    if (found_raw_sparse && found_copy_sparse) {
      LOG_EVERY_N(INFO, 1000) << "[Compare] compare item feature diff"
                              << " raw sparse feature size: "
                              << raw_sparse_fea_iter->second.size()
                              << " copy sparse feature size: "
                              << copy_sparse_fea_iter->second.size();
      sparse_ok = CompareFeatureDiff(true, log_str, raw_sparse_fea_iter->second,
                                     copy_sparse_fea_iter->second);
    }
    if (found_raw_dense && found_copy_dense) {
      LOG_EVERY_N(INFO, 1000) << "[Compare] compare item feature diff"
                              << " raw dense feature size: "
                              << raw_dense_fea_iter->second.size()
                              << " copy dense feature size: "
                              << copy_dense_fea_iter->second.size();
      dense_ok = CompareFeatureDiff(false, log_str, raw_dense_fea_iter->second,
                                    copy_dense_fea_iter->second);
    }
  } else {
    LOG(INFO) << "[Compare] " << log_str << " some fea missing "
              << " found_raw_sparse: " << found_raw_sparse
              << " found_copy_sparse: " << found_copy_sparse
              << " found_raw_dense: " << found_raw_dense
              << " found_copy_dense: " << found_copy_dense;
  }
  return sparse_ok && dense_ok;
}

void FeatureServer::InitItemParse() {
  auto item_parse_thread_count = model_config_->GetInt("item_parse_thread_count", 5);
  item_thread_pool_ = std::make_unique<::thread::ThreadPool>(item_parse_thread_count);
  for (size_t idx = 0; idx < item_parse_thread_count; ++idx) {
    item_thread_pool_->AddTask(::NewCallback(this, &FeatureServer::ItemParseTaskLoop));
  }
  LOG(INFO) << "init item parse, thread count: " << item_parse_thread_count;
}

void FeatureServer::ItemParseTaskLoop() {
  ItemParseTask task;
  while (true) {
    if (!item_tasks_.pop(task)) {
      LOG_EVERY_N(INFO, 200000) << "no item parse task, wait for wake up";
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
      continue;
    }
    auto start = Clock::now();
    auto& item_id = task.item_id;
    auto& serialized_data = *task.serialized_data;
    std::shared_ptr<ItemAdaptorV1> item_ptr = nullptr;
    if (copy_req_cmd_ != "") {
      item_ptr = std::make_shared<ItemAdaptorV1>();
      static thread_local std::string pb_data;
      if (!DecompressStr(task.item_id, serialized_data, &pb_data)) {
        LOG_EVERY_N(WARNING, 100) << "decompress str failed, id: " << task.item_id;
        continue;
      }
      if (!item_ptr->ParseFromString(pb_data)) {
        LOG_EVERY_N(WARNING, 100) << "failed to ParseFromString, id: " << task.item_id;
        continue;
      }
      if (!TransferBSItem(pb_data, &item_ptr)) {
        LOG_EVERY_N(WARNING, 100) << "transfer bs item failed, id: " << task.item_id;
        continue;
      }
      task.adlog->SetItemStoreCacheParsedItem(task.index, item_ptr);
    } else {
      auto store_cache = task.adlog->GetItemStoreCache(task.index);
      if (store_cache == nullptr) {
        continue;
      }
      if (!ParseItemV1(item_id, serialized_data, store_cache->item.get())) {
        falcon::Inc("dnn_predict_server.pb_cache_except", 1);
        continue;
      }
      item_ptr = store_cache->item;
    }
    if (!item_ptr) {
      falcon::Inc("dnn_predict_server.pb_cache_except", 1);
      continue;
    }
    auto req_time = base::GetTimestamp() / 1000000;
    task.info->item_store->TryAddStoreData<std::shared_ptr<ItemAdaptorV1>>(
        static_cast<int64_t>(item_id), {static_cast<int64_t>(req_time), item_ptr});
    falcon::Inc("dnn_predict_server.pb_cache_success", 1);
    falcon::Stat("feature_server.item_parse_once", GetMicroseconds(start));
    task.semaphore->Release();
  }
}

}  //  namespace ad_nn
}  //  namespace ks
