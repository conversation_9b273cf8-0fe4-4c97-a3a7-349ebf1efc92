#include "teams/ad/ad_user_profile/handler/universe_user_profile_handler_v2.h"
#include "teams/ad/ad_user_profile/handler/universe_user_profile_handler.h"
#include <algorithm>
#include <cstdint>
#include <memory>
#include <string>
#include <set>
#include <unordered_set>
#include <vector>
#include <thread>
#include <future>
#include "base/common/logging.h"
#include "base/hash_function/md5.h"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/strings/string_util.h"
#include "base/time/timestamp.h"
#include "dynamic_kafka_client/dynamic_kafka_client.h"
#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_base/src/hash/hash.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/front_server/util/common/common.h"
#include "teams/ad/ad_user_profile/common/picasso_user_profile.h"
#include "teams/ad/ad_user_profile/common/id_helper.h"
#include "teams/ad/ad_user_profile/utils/ad_user_info/ad_user_info_builder.h"
#include "teams/ad/ad_user_profile/utils/constant.h"
#include "teams/ad/ad_user_profile/utils/kconf/kconf.h"
#include "teams/ad/ad_user_profile/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_user_profile/utils/utils.h"
#include "teams/ad/ad_user_profile/utils/universe_device_data/universe_device_mode_data.h"
#include "teams/ad/ad_user_profile/utils/universe_device_data/universe_device_mode_data_v2.h"
#include "teams/ad/picasso/sdk/picasso_client/picasso_client.h"
#include "third_party/abseil/absl/strings/numbers.h"
#include "teams/ad/ad_user_profile/utils/scoped_perf_timer.h"
#include "falcon/counter.h"
#include "teams/ad/ad_user_profile/utils/reco_user_info_collect/reco_user_info_collection.h"
#include "teams/ad/ad_base/src/bthread/bthread_task.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_profile/ad_profile_realtime_tag.pb.h"

#define AD_USER_DEFAULT_AGE 20

#include "ks/base/abtest/abtest_instance.h"
#include "ks/base/abtest/abtest_globals.h"

namespace ks {
namespace ad_user_profile {

using kuaishou::frigate::fetch::location::sdk::kess::FrigateFetchLocationService;
using kuaishou::frigate::fetch::location::sdk::FrigateLatestLocationRequest;
using ::ks::infra::PerfUtil;
using kuaishou::ad::AdUserInfo;
using ::kuaishou::ad::KeyType;
using ::kuaishou::ad::RequestType;
static const int64 universe_fake_user_id_base = 400000000000000UL;
static const int64 universe_fake_user_id_interval = 100000000000000UL;

static const int64 fake_user_id_base = 900000000000000UL;
static const int64 fake_user_id_interval = 100000000000000UL;

#define UP_PERF_SET(val, tag, status) \
ks::infra::PerfUtil::SetLogStash(val, "ad.userprofile.set", tag, status)

#define FILL_DEVICE_FROM_ATHENA(feature) \
if (!athena_ad_request.device_info().feature().empty()) { \
  device_info->set_##feature(athena_ad_request.device_info().feature()); \
}
void UniverseUserProfileHandlerV2::Initialize() {
  // 设置一个默认值
  session_data_->user_profile->set_age(20);
  session_data_->user_profile->set_gender("U");
  session_data_->user_profile->set_age_segment("U");
  enable_frigate_location_info = AdKconfUtil::enableFrigateLocationConf();  // 该服务 hn 机房不可用，hn 先不开   // NOLINT
}

void UniverseUserProfileHandlerV2::IdMapping() {
  auto& user_id_response = session_data_->user_id_response;
  auto ab_user_info =
      ks::AbtestUserInfo::Builder(0, session_data_->device_id, session_data_->llsid).Build();
  bool enable_update_idmapping_redis_name = ks::abtest::AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP,
      "enable_update_idmapping_redis_name", ab_user_info, false);
  BuildKeyForUserRecognition(*session_data_->request, &session_data_->user_recognition_keys);
  IdHelper::GetUserIdsFromUserRecognition(session_data_->user_recognition_keys, &user_id_response,
    &session_data_->fake_uid_create_time_ms,
    ::kuaishou::ad::universe::DeviceInfo_OsType_Name(
        session_data_->request->athena_ad_request().device_info().os_type()),
    session_data_->request->athena_ad_request().app_info().app_id(),
    &session_data_->final_key_type_name, enable_update_idmapping_redis_name);

  if (user_id_response.user_id()) {
    session_data_->uid = user_id_response.user_id();
    session_data_->dot_perf->Count(1, "idmapping_result", "kwai_user_id");
  } else if (user_id_response.fake_user_id()) {
    session_data_->fake_uid = user_id_response.fake_user_id();
    session_data_->dot_perf->Count(1, "idmapping_result", "fake_user_id");
  } else {
    // mapping fails, so generate a fakeuid here.
    session_data_->fake_uid = ParseFakeUidNew();
    session_data_->fake_uid_create_time_ms = time(nullptr) * 1000;
    session_data_->dot_perf->Count(1, "idmapping_result", "mock_user_id");
  }
}

void UniverseUserProfileHandlerV2::BuildKey(const ::kuaishou::ad::AdUserProfileRequest *request,
                              std::string* key, KeyType* type) {
  if (KS_UNLIKELY(!request->has_athena_ad_request())) {
    falcon::Inc("user_profile_no_athena_ad_request");
    LOG(ERROR) << "no athena ad request!";
    return;
  }
  const kuaishou::ad::universe::AthenaAdRequest &athena_ad_request = request->athena_ad_request();
  const auto& device_info = athena_ad_request.device_info();
  if (device_info.os_type() == ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_ANDROID) {
    if (Util::IsValidKey(device_info.imei())) {
      *key = device_info.imei();
      *type = ::kuaishou::ad::KeyType::IMEI;
    } else if (Util::IsValidKey(device_info.imei_md5())) {
      *key = device_info.imei_md5();
      *type = ::kuaishou::ad::KeyType::IMEIMD5;
    } else if (Util::IsValidKey(device_info.oaid())) {
      *key = device_info.oaid();
      *type = ::kuaishou::ad::KeyType::OA_ID;
    } else if (Util::IsValidKey(device_info.android_id())) {
      *key = device_info.android_id();
      *type = ::kuaishou::ad::KeyType::ANDROID_ID;
    } else if (Util::IsValidKey(device_info.android_id_md5())) {
      *key = device_info.android_id_md5();
      *type = ::kuaishou::ad::KeyType::ANDROID_ID_MD5;
    } else if (Util::IsValidKey(device_info.oaid_md5())) {
      *key = device_info.oaid_md5();
      *type = ::kuaishou::ad::KeyType::OA_ID_MD5;
    }
  } else if (device_info.os_type() == ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_IOS) {
    if (Util::IsValidKey(device_info.idfa())) {
      *key = device_info.idfa();
      *type = ::kuaishou::ad::KeyType::IDFA;
    }
  } else if (device_info.os_type() == ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_HARMONY) { // NOLINT
    if (Util::IsValidKey(device_info.oaid())) {
      *key = device_info.oaid();
      *type = ::kuaishou::ad::KeyType::OA_ID;
    }
  }
}

void UniverseUserProfileHandlerV2::AsyncGetProfile() {
  auto* clotho_client = ::reco::clotho::sdk::ClothoClientBase::GetInstance<
                                          ::reco::clotho::sdk::ClothoClientInternal>();
  if (clotho_client == nullptr) {
    LOG_EVERY_N(ERROR, AdKconfUtil::logFrequency()) << "clotho client get error, clotho_client is nullptr.";
    return;
  }
  std::string key;
  if (session_data_->uid > 0) {
    key = ConstantAttr::kUniversePicassoKeyPrefixV2 + std::to_string(session_data_->uid);
  } else if (session_data_->fake_uid > 0) {
    std::string device_key;
    KeyType type = ::kuaishou::ad::KeyType::UNKOWN;
    BuildKey(session_data_->request, &device_key, &type);
    if (Util::RequestKeyIsValid(device_key) ||
        (session_data_->request->athena_ad_request().device_info().os_type() ==
            ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_IOS)) {
      key = ConstantAttr::kUniversePicassoKeyPrefixV2 + std::to_string(session_data_->fake_uid);
    } else {
      session_data_->dot_perf->Count(1, "fake_user_key_invalid", "new");
      return;
    }
  } else {
    session_data_->dot_perf->Count(1, "fake_user_key_invalid", "new");
    return;
  }
  ::reco::clotho::gateway::TableReadRequest req;
  req.set_table(ConstantAttr::kPicassoUniverseTableNameV3);
  req.add_keys(key);
  req.set_get_pb_result(true);
  auto req_column = req.add_column_read_reqs();
  req_column->set_column("profile_data");

  session_data_->profile_pms = std::make_shared<std::promise<::reco::clotho::sdk::Status>>();
  session_data_->profile_future = session_data_->profile_pms->get_future();
  auto callback = [&](::reco::clotho::sdk::Status status,
                                const ::reco::clotho::gateway::TableReadResponse &value) {
    session_data_->profile_resp.Swap(const_cast<::reco::clotho::gateway::TableReadResponse*>(&value));
    session_data_->profile_pms->set_value(status);
  };
  auto start_ts = base::GetTimestamp();
  auto status_async = clotho_client->AsyncTableRead(req, callback, 10);
  if (status_async != ::reco::clotho::sdk::Status::STATUS_OK) {
    // 这里主要是对 client 创建和参数是否正常进行的检查
    LOG(INFO) << "read not ok, status=" << StatusToString(status_async);
    return;
  }
}

void UniverseUserProfileHandlerV2::AsyncGetAudienceTagInfo() {
  auto* clotho_client = ::reco::clotho::sdk::ClothoClientBase::GetInstance<
                                          ::reco::clotho::sdk::ClothoClientInternal>();
  if (clotho_client == nullptr) {
    LOG_EVERY_N(ERROR, AdKconfUtil::logFrequency()) << "clotho client get error, clotho_client is nullptr.";
    return;
  }
  std::string key;
  if (session_data_->uid > 0) {
    key = ConstantAttr::kPicassoTagIfoKeyPrefix + std::to_string(session_data_->uid);
  } else if (session_data_->fake_uid > 0) {
    key = ConstantAttr::kPicassoTagIfoKeyPrefix + std::to_string(session_data_->fake_uid);
  } else {
    return;
  }
  ::reco::clotho::gateway::TableReadRequest req;
  req.set_table("ad_user_profile_tags");
  req.add_keys(key);
  req.set_get_pb_result(true);
  auto req_column = req.add_column_read_reqs();
  req_column->set_column("profile_tags_col_1");

  session_data_->audience_tag_pms = std::make_shared<std::promise<::reco::clotho::sdk::Status>>();
  session_data_->audience_tag_future = session_data_->audience_tag_pms->get_future();
  auto callback = [&](::reco::clotho::sdk::Status status,
                                const ::reco::clotho::gateway::TableReadResponse &value) {
    session_data_->audience_tag_resp.Swap(const_cast<::reco::clotho::gateway::TableReadResponse*>(&value));
    session_data_->audience_tag_pms->set_value(status);
  };
  auto status_async = clotho_client->AsyncTableRead(req, callback, 10);
  if (status_async != ::reco::clotho::sdk::Status::STATUS_OK) {
    // 这里主要是对 client 创建和参数是否正常进行的检查
    LOG(INFO) << "read not ok, status=" << status_async;
    return;
  }
}

void UniverseUserProfileHandlerV2::AsyncRpcFrigateLocation() {
  if (enable_frigate_location_info && session_data_->uid > 0 &&
      session_data_->user_profile && session_data_->user_profile->device_info_size() > 0) {
    FrigateLatestLocationRequest frigate_location_req;
    frigate_location_req.set_device_id(session_data_->user_profile->device_info(0).device_id());
    frigate_location_req.set_user_id(session_data_->uid);
    frigate_location_req.set_product("kuaishou");
    frigate_location_req.set_frigate_project("ad_user_location_info");
    int status = KESS_STATUS_OK;
    std::function<void(FrigateAllLocationResponse& res, int status)> callback =
        [&](FrigateAllLocationResponse& res, int status) {
            if (status != KESS_STATUS_OK) {
            return;
            }
            session_data_->frigate_location_resp.Swap(&res);
            session_data_->dot_perf->Count(1, "enable_get_region_id_v2", "frigate");
        };
    AD_HALF_ASYNC_KESS_CALL_WITH_TAG("fetch_location_rpc_service", FrigateFetchLocationService, GetAllLocation,   // NOLINT
                                    FrigateAllLocationResponse, frigate_location_req, callback, status,   // NOLINT
                                    "async_rpc_frigate_location_service_call_tag");
  }
}

void UniverseUserProfileHandlerV2::AsyncGetGidProfile() {
  if (session_data_->uid == 0 || session_data_->gid.empty()) {
    return;
  }
  auto* clotho_client = ::reco::clotho::sdk::ClothoClientBase::GetInstance<
                                          ::reco::clotho::sdk::ClothoClientInternal>();
  if (clotho_client == nullptr) {
    LOG_EVERY_N(ERROR, AdKconfUtil::logFrequency()) << "clotho client get error, clotho_client is nullptr.";
    return;
  }
  std::string key = "adp_gid_" + session_data_->gid;

  ::reco::clotho::gateway::TableReadRequest req;
  req.set_table("ad_user_profile_gid");
  req.add_keys(key);
  req.set_get_pb_result(true);
  auto req_column = req.add_column_read_reqs();
  req_column->set_column("profile_col_1");

  session_data_->gid_profile_pms = std::make_shared<std::promise<::reco::clotho::sdk::Status>>();
  session_data_->gid_profile_future = session_data_->gid_profile_pms->get_future();
  auto callback = [&](::reco::clotho::sdk::Status status,
                                const ::reco::clotho::gateway::TableReadResponse &value) {
    session_data_->gid_profile_resp.Swap(const_cast<::reco::clotho::gateway::TableReadResponse*>(&value));
    session_data_->gid_profile_pms->set_value(status);
  };
  auto start_ts = base::GetTimestamp();
  auto status_async = clotho_client->AsyncTableRead(req, callback, 10);
  if (status_async != ::reco::clotho::sdk::Status::STATUS_OK) {
    // 这里主要是对 client 创建和参数是否正常进行的检查
    LOG(INFO) << "GidProfile read not ok, status=" << StatusToString(status_async);
    return;
  }
}

void UniverseUserProfileHandlerV2::AsyncDataSourceInit() {
  // 算法挖掘人群标签数据获取
  AsyncGetAudienceTagInfo();
  // gid 画像获取
  AsyncGetGidProfile();
  // 实时人群包数据获取
  real_time_orientation_info_params_.Init(session_data_);
  async_redis_helper_.AsyncSGetMembers(&real_time_orientation_info_params_);
  // 实时删除人群包数据获取
  real_time_delete_orientation_params_.Init(session_data_);
  async_redis_helper_.AsyncSGetMembers(&real_time_delete_orientation_params_);
  // 快手用户查询 kiwi 获取实时人群包结构数据
  kwai_user_realtime_orientation_kiwi_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&kwai_user_realtime_orientation_kiwi_params_);
  // 通过 ipv4 查询 adcode
  universe_address_ip_to_adcode_params_.Init(session_data_);
  async_redis_helper_.AsyncGet(&universe_address_ip_to_adcode_params_);
}
void UniverseUserProfileHandlerV2::FillDmpProfile() {
  // 下面是 rpc 请求的结果
  if (!session_data_->profile_future.valid()) {
    return;
  }
  auto status = session_data_->profile_future.get();
  if (status != ::reco::clotho::sdk::Status::STATUS_OK) {
    if (session_data_->uid > 0) {
      WriteNoProfileInfoKafka(session_data_->uid, AdProfileCoreReads::USER, session_data_->device_id);
    }
    LOG(INFO) << "read not ok, status=" << StatusToString(status);
    return;
  }

  kuaishou::ad::profilecore::AdUserInfos user_profiles;
  for (int i = 0; i < session_data_->profile_resp.rows_size(); i++) {
    bool get_empty_row = session_data_->profile_resp.rows(i).get_empty_row();
    if (get_empty_row) {
      LOG(INFO) << "the whole row is miss, got empty value, key=" << session_data_->profile_resp.rows(i).key(); // NOLINT
      continue;
    }
    for (int j = 0; j < session_data_->profile_resp.rows(i).raw_data_size(); j++) {
      const auto &field = session_data_->profile_resp.rows(i).raw_data(j);
      if (user_profiles.ParseFromArray(field.str_value().c_str(), field.str_value().size())) {
        break;
      }
    }
  }
  auto it = user_profiles.ad_user_info().find("base");

  if (it == user_profiles.ad_user_info().end()) {
    if (session_data_->uid > 0) {
      WriteNoProfileInfoKafka(session_data_->uid, AdProfileCoreReads::USER, session_data_->device_id);
    }
    LOG(INFO) << "profile_clotho get empty data, user_id = "
              << session_data_->uid << " " << session_data_->fake_uid;
    return;
  } else {
    session_data_->user_profile->Swap(const_cast<kuaishou::ad::AdUserInfo*>(&it->second));
  }
  if (session_data_->uid > 0) {
    for (int i = 0; i < session_data_->user_profile->device_info_size(); i++) {
      auto device_info = session_data_->user_profile->mutable_device_info(i);
      device_info->clear_app_package_purchase();
    }
    it = user_profiles.ad_user_info().find("exp");
    if (it != user_profiles.ad_user_info().end()) {
      auto& exp_user_profile = it->second;
      // 采买数据
      for (int i = 0; i < session_data_->user_profile->device_info_size(); i++) {
        auto device_info = session_data_->user_profile->mutable_device_info(i);
        for (auto& exp_device_info : exp_user_profile.device_info()) {
          if (device_info->device_id() == exp_device_info.device_id()) {
            for (auto& app_package : exp_device_info.app_package_purchase()) {
              device_info->add_app_package_purchase(app_package);
            }
          }
        }
      }
      // 常住地实验
      if (SPDM_universe_fre_adcodes_exp(session_data_->get_spdm_ctx())) {
        session_data_->user_profile->clear_fre_adcodes();
        session_data_->user_profile->mutable_fre_adcodes()->Add(
            exp_user_profile.fre_adcodes().begin(), exp_user_profile.fre_adcodes().end());
      }
      // 居住地实验
      if (SPDM_universe_residence_adcodes_exp(session_data_->get_spdm_ctx())) {
        session_data_->user_profile->clear_residence_adcodes();
        session_data_->user_profile->mutable_residence_adcodes()->Add(
            exp_user_profile.residence_adcodes().begin(), exp_user_profile.residence_adcodes().end());
      }
      // 商圈常住地实验
      if (SPDM_universe_fre_business_district_exp(session_data_->get_spdm_ctx())) {
        session_data_->user_profile->clear_fre_business_district();
        session_data_->user_profile->mutable_fre_business_district()->Add(
            exp_user_profile.fre_business_district().begin(),
            exp_user_profile.fre_business_district().end());
      }
    }
  }
  if (session_data_->uid > 0) {
    switch (SPDM_universe_residence_map_id(session_data_->get_spdm_ctx())) {
      case UniverseUserInfoMapType::SPT:
        it = user_profiles.ad_user_info().find("spt");
        break;
      case UniverseUserInfoMapType::BASE:
        it = user_profiles.ad_user_info().find("base");
        break;
      case UniverseUserInfoMapType::EXP:
        it = user_profiles.ad_user_info().find("exp");
        break;
      default:
        it = user_profiles.ad_user_info().find("base");
        break;
    }
    if (it != user_profiles.ad_user_info().end()) {
    auto& user_profile = it->second;
    session_data_->user_profile->clear_fre_adcodes();
    session_data_->user_profile->mutable_fre_adcodes()->
    Add(user_profile.fre_adcodes().begin(), user_profile.fre_adcodes().end());
  }
  }
}

void UniverseUserProfileHandlerV2::FillAudienceTagInfo() {
  if (!session_data_->audience_tag_future.valid()) {
    LOG(INFO) << "audience_tag_future not valid";
    return;
  }
  auto status = session_data_->audience_tag_future.get();
  if (status != ::reco::clotho::sdk::Status::STATUS_OK) {
    LOG(INFO) << "read not ok, status=" << status;
    return;
  }

  AdAudienceTagInfo audience_tag_info;
  for (int i = 0; i < session_data_->audience_tag_resp.rows_size(); i++) {
    bool get_empty_row = session_data_->audience_tag_resp.rows(i).get_empty_row();
    if (get_empty_row) {
      LOG(INFO) << "the whole row is miss, got empty value, key=" << session_data_->audience_tag_resp.rows(i).key();  // NOLINT
      continue;
    }
    for (int j = 0; j < session_data_->audience_tag_resp.rows(i).raw_data_size(); j++) {
      const auto &field = session_data_->audience_tag_resp.rows(i).raw_data(j);
      if (audience_tag_info.ParseFromArray(field.str_value().c_str(), field.str_value().size())) {
        break;
      }
    }
  }
  if (audience_tag_info.tag_info_size() == 0) {
    LOG_EVERY_N(WARNING, AdKconfUtil::logFrequency())
        << "new clotho tag_info get empty data, user_id = "
        << session_data_->uid << " " << session_data_->fake_uid;
    return;
  }
  bool td_tag_flag = false;
  bool gt_tag_flag = false;
  for (int i = 0; i < audience_tag_info.tag_info_size(); ++i) {
    TagInfo src_tag_info = audience_tag_info.tag_info(i);
    auto *tag_info = session_data_->user_profile->add_universe_tag_info();
    tag_info->set_id(src_tag_info.id());
    if (src_tag_info.id() == 1) {
      td_tag_flag = true;
    }
    if (src_tag_info.id() == 2) {
      gt_tag_flag = true;
    }
    tag_info->set_level(src_tag_info.level());
    tag_info->set_type(src_tag_info.type());
    tag_info->set_val_long(src_tag_info.val_long());
    tag_info->set_val_double(src_tag_info.val_double());
    tag_info->set_val_str(src_tag_info.val_str());
    for (auto item : src_tag_info.arr_long()) {
      tag_info->add_arr_long(item);
    }
    for (auto item : src_tag_info.arr_double()) {
      tag_info->add_arr_double(item);
    }
    for (auto item : src_tag_info.arr_str()) {
      tag_info->add_arr_str(item);
    }
    tag_info->set_json(src_tag_info.json());
  }

  session_data_->dot_perf->Interval(session_data_->user_profile->universe_tag_info_size(), "universe_tag_info");   // NOLINT
}

void UniverseUserProfileHandlerV2::FillRegionInfo() {
  if (universe_address_ip_to_adcode_params_.IsAllow()) {
    std::string str_value;
    auto ret = async_redis_helper_.Wait(&universe_address_ip_to_adcode_params_, &str_value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
      Json json(StringToJson(str_value));
      if (json.IsObject()) {
        auto adcode = std::atoi(json.GetString("adcode", "").c_str());
        if (adcode > 0) {
          session_data_->user_profile->mutable_adcode()->set_origin_adcode(adcode);
        }
      }
    }
  }

  kuaishou::newsmodel::UserInfo reco_user_info;
  auto* location = reco_user_info.mutable_location();
  location->set_lat(session_data_->request->athena_ad_request().geo_info().latitude());
  location->set_lon(session_data_->request->athena_ad_request().geo_info().longitude());
  if (enable_frigate_location_info && session_data_->uid > 0) {
    WAIT_ASYNC_RESULT_WITH_TAG("async_rpc_frigate_location_service_call_tag");
    auto* request = session_data_->request;
    int64 gps_cache_ts = -1;
    double lat = 0.0f, lon = 0.0f;
    int location_info_ret = -1;   // -1 表示没有使用 location 服务的数据
    location_info_ret = AdUserInfoBuilder::GetLatAndLon(session_data_->frigate_location_resp, 100, &lat, &lon, &gps_cache_ts);   // NOLINT

    if (location_info_ret > 0 && (location->lat() == 0 || location->lon() == 0)) {
      location->set_lat(lat);
      location->set_lon(lon);
      session_data_->dot_perf->Count(1, "enable_get_region_id_v2", "success");
    }
  }
  // 如果 location 服务也没有填充上 gps ，则使用 geo_info_v2 的数据
  if (location->lat() == 0 || location->lon() == 0) {
    location->set_lat(session_data_->request->athena_ad_request().geo_info_v2().latitude());
    location->set_lon(session_data_->request->athena_ad_request().geo_info_v2().longitude());
    session_data_->dot_perf->Count(1, "enable_use_geo_info_v2", "success");
  }
  location->set_ip(session_data_->request->athena_ad_request().network_info().ip_v4());
  location->set_ipv6(session_data_->request->athena_ad_request().network_info().ipv6());
  AdUserInfoBuilder::FillRegionInfo(reco_user_info, session_data_->request->request_type(), session_data_->user_profile);  // NOLINT
  AdUserInfoBuilder::SetRegionLiterals(session_data_->user_profile);
}

void UniverseUserProfileHandlerV2::FillOrientationInfo() {
  auto* user_profile = session_data_->user_profile;
  std::unordered_set<int64_t> orientation_info;
  orientation_info.reserve(user_profile->orientation_size() * 2);
  // dmp 离线人群包
  orientation_info.insert(user_profile->orientation().begin(), user_profile->orientation().end());
  session_data_->dot_perf->Interval(orientation_info.size(), "orientation_info_size", "after_user_profile");

  // gid 离线画像人群包
  if (session_data_->uid > 0 && session_data_->gid_profile_future.valid() &&
      session_data_->gid_profile_future.get() == ::reco::clotho::sdk::Status::STATUS_OK) {
    kuaishou::ad::AdUserInfo ad_user_info;
    for (int i = 0; i < session_data_->gid_profile_resp.rows_size(); i++) {
      bool get_empty_row = session_data_->gid_profile_resp.rows(i).get_empty_row();
      if (get_empty_row) {
        LOG(INFO) << "the whole row is miss, got empty value, key=" << session_data_->gid_profile_resp.rows(i).key(); // NOLINT
        continue;
      }
      for (int j = 0; j < session_data_->gid_profile_resp.rows(i).raw_data_size(); j++) {
        const auto &field = session_data_->gid_profile_resp.rows(i).raw_data(j);
        if (ad_user_info.ParseFromArray(field.str_value().c_str(), field.str_value().size())) {
          break;
        }
      }
    }
    for (auto& id : ad_user_info.orientation()) {
      orientation_info.insert(id);
    }
    session_data_->dot_perf->Interval(orientation_info.size(), "orientation_info_size", "after_gid_profile");
  }

  // 实时新增人群包
  std::set<std::string> value;
  auto ret = async_redis_helper_.Wait(&real_time_orientation_info_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    for (const auto &val : value) {
      int64_t t = 0;
      if (absl::SimpleAtoi(val, &t)) {
        orientation_info.insert(t);
      }
    }
  }

  // 快手用户从 kiwi 获取实时人群包结构数据
  if (session_data_->uid > 0) {
    std::string str_value;
    ret = async_redis_helper_.Wait(&kwai_user_realtime_orientation_kiwi_params_, &str_value);
    if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
      kuaishou::ad::profilecore::UserRealTimeTag user_tag;
      if (user_tag.ParseFromString(str_value)) {
        int64 start_ms = base::GetTimestamp() / 1000;
        for (auto tag_info : user_tag.tag_list()) {
          if (tag_info.expire_time() < start_ms) continue;
          orientation_info.insert(tag_info.tag_id());
        }
      }
    }
  }

  // 实时删除人群包
  value.clear();
  ret = async_redis_helper_.Wait(&real_time_delete_orientation_params_, &value);
  if (ret == ks::infra::KS_INF_REDIS_NO_ERROR) {
    for (const auto &val : value) {
      int64_t t = 0;
      if (absl::SimpleAtoi(val, &t)) {
        orientation_info.erase(t);
      }
    }
  }

  user_profile->clear_orientation();
  std::copy(orientation_info.begin(), orientation_info.end(),
      google::protobuf::RepeatedFieldBackInserter(user_profile->mutable_orientation()));
  session_data_->dot_perf->Count(user_profile->orientation_size(), "orientation_size");
}

void UniverseUserProfileHandlerV2::FillDataFromReq() {
  auto* user_profile = session_data_->user_profile;
  if (KS_UNLIKELY(user_profile == nullptr)) {
    LOG(ERROR) << "ad user info null pointer!";
    return;
  }

  auto* request = session_data_->request;
  auto fake_uid = session_data_->fake_uid;
  const kuaishou::ad::universe::AthenaAdRequest &athena_ad_request = request->athena_ad_request();
  int64_t user_id = 0;
  std::string& device_id = session_data_->device_id;
  if (user_profile->id() == 0) {
    user_id = fake_uid;
  } else {
    user_id = user_profile->id();
  }
  user_profile->set_parent_control_enabled(athena_ad_request.user_info().parent_control_enabled());

  // 如果 ad user profile 没有 gender 则使用 athena_ad_request 中的
  if (!user_profile->has_gender()) {
    user_profile->set_gender(athena_ad_request.user_info().gender());
  }
  // 如果 ad user profile 没有 age 则使用 athena_ad_request 中的
  if (!user_profile->has_age()) {
    if (athena_ad_request.user_info().age() > 0) {
      user_profile->set_age(athena_ad_request.user_info().age());
    } else {
      user_profile->set_age(AD_USER_DEFAULT_AGE);
    }
  }

  if (!device_id.empty()) {
    user_profile->set_device_id(device_id);
  }
  bool enable_device_info_match_opt = SPDM_enable_device_info_match_opt_v2(session_data_->get_spdm_ctx());
  uint64_t is_oaid_match_flag = 0;
  bool is_hit_oaid = false;
  if (user_profile->device_info_size() > 0 && !device_id.empty()) {
    if (enable_device_info_match_opt) {
      for (auto iter = (user_profile->mutable_device_info())->begin();
                iter != (user_profile->mutable_device_info())->end();) {
        if (athena_ad_request.device_info().os_type() ==
            ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_ANDROID) {
          if (!athena_ad_request.user_info().gid().empty() &&
              iter->gid() == athena_ad_request.user_info().gid()) {
            ++iter;
          } else if (iter->device_id() == device_id) {
            ++iter;
          } else if (!Util::IsImeiEmpty(athena_ad_request.device_info().imei()) &&
                    iter->imei() == athena_ad_request.device_info().imei()) {
            ++iter;
          } else if (!athena_ad_request.device_info().oaid().empty() &&
                    iter->oaid() == athena_ad_request.device_info().oaid()) {
            ++iter;
            is_hit_oaid = true;
          } else if (!athena_ad_request.device_info().android_id().empty() &&
                    iter->android_id() == athena_ad_request.device_info().android_id()) {
            ++iter;
          } else {
            iter = (user_profile->mutable_device_info())->erase(iter);
          }
        } else if (athena_ad_request.device_info().os_type() ==
                  ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_IOS) {
          if (!athena_ad_request.user_info().gid().empty() &&
              iter->gid() == athena_ad_request.user_info().gid()) {
            ++iter;
          } else if (iter->device_id() == device_id) {
            ++iter;
          } else if (!Util::IsImeiEmpty(athena_ad_request.device_info().imei()) &&
                    iter->imei() == athena_ad_request.device_info().imei()) {
            ++iter;
          } else if (!athena_ad_request.device_info().idfa().empty() &&
                    iter->idfa() == athena_ad_request.device_info().idfa()) {
            ++iter;
          } else if (!athena_ad_request.device_info().current_caid().empty() &&
                    iter->current_caid() == athena_ad_request.device_info().current_caid()) {
            ++iter;
          } else if (!athena_ad_request.device_info().last_caid().empty() &&
                    iter->last_caid() == athena_ad_request.device_info().last_caid()) {
            ++iter;
          } else {
            iter = (user_profile->mutable_device_info())->erase(iter);
          }
        } else if (athena_ad_request.device_info().os_type() ==
                   ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_HARMONY &&
                   AdKconfUtil::enableHarmonyDeviceInfoMatchOpt()) {
          if (!athena_ad_request.user_info().gid().empty() &&
              iter->gid() == athena_ad_request.user_info().gid()) {
            ++iter;
          } else if (iter->device_id() == device_id) {
            ++iter;
          } else if (!athena_ad_request.device_info().oaid().empty() &&
                    iter->oaid() == athena_ad_request.device_info().oaid()) {
            ++iter;
            is_hit_oaid = true;
          } else {
            iter = (user_profile->mutable_device_info())->erase(iter);
          }
        } else {
          iter = (user_profile->mutable_device_info())->erase(iter);
        }
      }
    } else {
      for (auto iter = (user_profile->mutable_device_info())->begin();
              iter != (user_profile->mutable_device_info())->end();) {
        if (iter->device_id() == device_id) {
          ++iter;
        } else if (!Util::IsImeiEmpty(athena_ad_request.device_info().imei()) &&
                  iter->imei() == athena_ad_request.device_info().imei()) {
          ++iter;
        } else if (!athena_ad_request.device_info().oaid().empty() &&
                  iter->oaid() == athena_ad_request.device_info().oaid()) {
          ++iter;
          is_hit_oaid = true;
        } else {
          iter = (user_profile->mutable_device_info())->erase(iter);
        }
      }
    }
    PerfUtil::CountLogStash(1, "ad.profile", "device_info_match",
        user_profile->device_info_size() > 0 ? "match_success" : "match_fail",
        enable_device_info_match_opt ? "exp" : "base",
        ::kuaishou::ad::universe::DeviceInfo_OsType_Name(athena_ad_request.device_info().os_type()),
        fake_uid > 0 ? "fake_uid" : "uid");
  }
  ::kuaishou::ad::DeviceInfo *device_info = nullptr;
  if (user_profile->device_info().size() != 0) {
    device_info = user_profile->mutable_device_info(0);
  } else {
    device_info = user_profile->add_device_info();
  }
  if (!athena_ad_request.device_info().imei().empty()) {
    device_info->set_imei(athena_ad_request.device_info().imei());
  }
  if (!athena_ad_request.device_info().idfa().empty()) {
    device_info->set_idfa(athena_ad_request.device_info().idfa());
  }
  if (!athena_ad_request.device_info().android_id().empty()) {
    device_info->set_android_id(athena_ad_request.device_info().android_id());
  }
  if (!athena_ad_request.device_info().oaid().empty()) {
    device_info->set_oaid(athena_ad_request.device_info().oaid());
  }
  Json ext_json(StringToJson(athena_ad_request.ext()));
  auto* sub_json = ext_json.IsObject() ? ext_json.Get("modelInfo") : nullptr;
  if (ext_json.IsObject()) {
    int64_t ram = ext_json.GetInt("physical_memory_kbytes", 0);
    int64_t rom = ext_json.GetInt("hard_disk_size_kbytes", 0);
    if (ram > 0 || rom > 0) {
      device_info->set_rammb(ram / 1024);
      device_info->set_rommb(rom / 1024);
    } else {
      if (sub_json && sub_json->IsObject()) {
        ram = sub_json->GetInt("totalMemorySize", 0);
        rom = sub_json->GetInt("totalDiskSize", 0);
        if (ram > 0 || rom > 0) {
          device_info->set_rammb(ram / 1024 / 1024);
          device_info->set_rommb(rom / 1024 / 1024);
        }
      }
    }
  }

  if (SPDM_enableUniverseMergeKwaiRealtimeTag()) {
    auto timestamp_ms = base::GetTimestamp() / 1000;
    auto expire_time_minute = AdKconfUtil::kwaiRealtimeTagExpireMinute();
  #define CLEAR_IF_EXPIRED(FIELD) \
    if (timestamp_ms - (device_info->FIELD().collect_time()) >= expire_time_minute * 60 * 1000) {\
      device_info->clear_##FIELD();\
    }
  #define MONITOR_FIELD(FIELD, ...) \
    session_data_->dot_perf->Interval((!device_info->FIELD().collect_time())* 100, \
      "universe_"#FIELD"_empty_rate", ##__VA_ARGS__);
  #define EXPAND_MACRO_BY_REALTIME_TAG(MACRO, ...)\
    MACRO(current_memory_info, ##__VA_ARGS__)\
    MACRO(current_disk_info, ##__VA_ARGS__)\
    MACRO(current_volume_info, ##__VA_ARGS__)\
    MACRO(current_battery_info, ##__VA_ARGS__)

    EXPAND_MACRO_BY_REALTIME_TAG(MONITOR_FIELD, "dmp_before_filter")
    EXPAND_MACRO_BY_REALTIME_TAG(CLEAR_IF_EXPIRED)
    EXPAND_MACRO_BY_REALTIME_TAG(MONITOR_FIELD, "dmp_after_filter")
    if (ext_json.IsObject() && sub_json && sub_json->IsObject()) {
      int64_t total_memory_size = sub_json->GetInt("totalMemorySize", 0) / 1024 / 1024;
      if (total_memory_size > 0) {
        int64_t available_memory_size = sub_json->GetInt("availableMemorySize", 0)  / 1024 / 1024;
        device_info->mutable_current_memory_info()->set_total_memory_size(total_memory_size);
        device_info->mutable_current_memory_info()->set_available_memory_size(available_memory_size);
        device_info->mutable_current_memory_info()->set_available_memory_rate(
          1.0 * available_memory_size / total_memory_size);
        device_info->mutable_current_memory_info()->set_collect_time(timestamp_ms);
        device_info->mutable_current_memory_info()->set_collect_source(1);
      }
      int64_t total_disk_size = sub_json->GetInt("totalDiskSize", 0) / 1024 / 1024;
      if (total_disk_size > 0) {
        int64_t available_disk_size = sub_json->GetInt("availableDiskSize", 0) / 1024 / 1024;
        device_info->mutable_current_disk_info()->set_total_disk_size(total_disk_size);
        device_info->mutable_current_disk_info()->set_available_disk_size(available_disk_size);
        device_info->mutable_current_disk_info()->set_available_disk_rate(
          1.0 * available_disk_size / total_disk_size);
        device_info->mutable_current_disk_info()->set_collect_time(timestamp_ms);
        device_info->mutable_current_disk_info()->set_collect_source(1);
      }
      auto audio_stream_info  = sub_json->Get("audioStreamInfo");
      if (audio_stream_info && audio_stream_info->IsArray() && audio_stream_info->size() > 3) {
        auto audio_stream_music_info = audio_stream_info->array()[3];
        if (audio_stream_music_info && audio_stream_music_info->IsObject()) {
          auto max_volume = audio_stream_music_info->GetInt("maxVolume", 0);
          auto min_volume = audio_stream_music_info->GetInt("minVolume", 0);
          auto current_volume = audio_stream_music_info->GetInt("currentVolume", 0);
          if (min_volume != max_volume) {
            device_info->mutable_current_volume_info()->set_max_volume(max_volume);
            device_info->mutable_current_volume_info()->set_volume_rate(
              1.0 * (current_volume - min_volume) / (max_volume - min_volume));
            device_info->mutable_current_volume_info()->set_collect_time(timestamp_ms);
            device_info->mutable_current_volume_info()->set_collect_source(1);
          }
        }
      }
      auto battery_percent = sub_json->GetInt("batteryPercent", -1);
      if (battery_percent > 0) {
        device_info->mutable_current_battery_info()->set_battery_rate(1.0 * battery_percent / 100);
        device_info->mutable_current_battery_info()->set_collect_time(timestamp_ms);
        device_info->mutable_current_battery_info()->set_collect_source(1);
      }
    }
    EXPAND_MACRO_BY_REALTIME_TAG(MONITOR_FIELD, "after_merge")
#undef EXPAND_MACRO_BY_REALTIME_TAG
#undef MONITOR_FIELD
#undef CLEAR_IF_EXPIRED
  }

  if (SPDM_enable_device_id_match_opt(session_data_->get_spdm_ctx()) && !device_id.empty()) {
     device_info->set_device_id(device_id);
  }  // deviceid 覆盖，防止使用主站的 device_id

  FILL_DEVICE_FROM_ATHENA(oaid_md5)
  FILL_DEVICE_FROM_ATHENA(idfa_md5)
  FILL_DEVICE_FROM_ATHENA(imei_md5)
  FILL_DEVICE_FROM_ATHENA(android_id_md5)

  // could be warpped
  ::kuaishou::ad::DeviceInfo_OperatorType operator_type =
      ::kuaishou::ad::DeviceInfo_OperatorType::DeviceInfo_OperatorType_UNKNOWN_OPERATOR_TYPE;
  auto operator_type_universe = athena_ad_request.network_info().operator_type();
  if (operator_type_universe !=
      ::kuaishou::ad::universe::NetworkInfo_OperatorType::NetworkInfo_OperatorType_UNKNOWN_OPERATOR_TYPE) {
    operator_type = static_cast<::kuaishou::ad::DeviceInfo_OperatorType>(operator_type_universe);
  }
  device_info->set_operator_type(operator_type);

  // could be warpped
  ::kuaishou::ad::DeviceInfo_ConnectionType connection_type =
      ::kuaishou::ad::DeviceInfo_ConnectionType::DeviceInfo_ConnectionType_UNKNOWN_CONNECTION_TYPE;
  auto connection_type_universe = athena_ad_request.network_info().connection_type();
  if (connection_type_universe != ::kuaishou::ad::universe::NetworkInfo_ConnectionType::
                                      NetworkInfo_ConnectionType_UNKNOWN_CONNECTION_TYPE) {
    connection_type = static_cast<::kuaishou::ad::DeviceInfo_ConnectionType>(connection_type_universe);
  }
  device_info->set_connection_type(connection_type);

  if (!athena_ad_request.network_info().ip_v4().empty()) {
    device_info->set_ip_v4(athena_ad_request.network_info().ip_v4());
  }
  if (!athena_ad_request.network_info().ipv6().empty()) {
    device_info->set_ipv6(athena_ad_request.network_info().ipv6());
  }
  if (!athena_ad_request.device_info().os_version().empty()) {
    device_info->set_os_version(athena_ad_request.device_info().os_version());
  }
  if (!athena_ad_request.device_info().user_agent().empty()) {
    std::string user_agent = athena_ad_request.device_info().user_agent();
    const std::string& app_id = athena_ad_request.app_info().app_id();
    if (!app_id.empty()) {
      size_t pos = user_agent.find(app_id);
      if (pos != std::string::npos) {
        user_agent.replace(pos, app_id.length(), "");
      }
    }
    device_info->set_user_agent(user_agent);
  }
  if (!athena_ad_request.device_info().device_brand().empty()) {
    device_info->set_brand(athena_ad_request.device_info().device_brand());
  }
  if (!athena_ad_request.device_info().device_mode_new().empty()) {
    device_info->set_device_mod(athena_ad_request.device_info().device_mode_new());
  }
  if (!athena_ad_request.device_info().device_mode().empty()) {
    device_info->set_device_mode_from_athena(athena_ad_request.device_info().device_mode());
  }
  if (!athena_ad_request.device_info().browser_ua().empty()) {
    device_info->set_browser_ua(athena_ad_request.device_info().browser_ua());
  }
  if (!device_info->device_mod().empty()) {
    std::string brand;
    int price;
    const std::string key = absl::AsciiStrToLower(device_info->device_mod());
    DeviceModeDataV2 device_model_data;
    if (UniverseDeviceModeDataV2::GetInstance()->GetModelData(key, device_info->rammb(),
        device_info->rommb(), &device_model_data)) {
      if (SPDM_universe_device_info_opt_mode(session_data_->get_spdm_ctx())) {
        device_info->set_readable_mod(device_model_data.model_name);
        session_data_->dot_perf->Count(1, "universe_device_info_opt", "readable_mod");
      }
      if (SPDM_universe_device_info_opt_price(session_data_->get_spdm_ctx())) {
        device_info->set_price(device_model_data.publish_price);
        session_data_->dot_perf->Count(1, "universe_device_info_opt", "price");
      }
      if (SPDM_universe_device_info_opt_brand(session_data_->get_spdm_ctx())) {
        device_info->set_brand_id(device_model_data.brand_id);
        session_data_->dot_perf->Count(1, "universe_device_info_opt", "brand_id",
            "opt", absl::StrCat(device_info->brand_id()));
      }
    } else if (SPDM_universe_device_info_opt_brand(session_data_->get_spdm_ctx())) {
      session_data_->dot_perf->Count(1, "universe_device_info_opt", "brand_id",
          "origin", absl::StrCat(device_info->brand_id()));
    }
    if (!(SPDM_universe_device_info_opt_brand(session_data_->get_spdm_ctx()) ||
          SPDM_universe_device_info_opt_brand_v2(session_data_->get_spdm_ctx())) &&
        UniverseDeviceModeData::GetInstance()->GetBrand(key, &brand)) {
      device_info->set_brand(brand);
    }
    if (!SPDM_universe_device_info_opt_price(session_data_->get_spdm_ctx()) &&
        UniverseDeviceModeData::GetInstance()->GetPrice(key, &price)) {
      device_info->set_price(price);
    }
  } else {
    if (SPDM_universe_device_info_opt_brand(session_data_->get_spdm_ctx())) {
      session_data_->dot_perf->Count(1, "universe_device_info_opt", "brand_id",
          "origin", absl::StrCat(device_info->brand_id()));
    }
  }
  if (is_hit_oaid && device_info->has_price()) {   // 没有实时数据，且画像是由 oaid 匹配到
    is_oaid_match_flag |= ((uint64_t) 1 << 14);
  }

  if (athena_ad_request.device_info().app_package().size() > 0) {
    if (athena_ad_request.device_info().app_package().size() <
            SPDM_sdk_app_package_valid_num_v2(session_data_->get_spdm_ctx()) && fake_uid == 0) {
      PerfUtil::CountLogStash(1, "ad.profile", "app_package_merge");
      std::set<std::string> app_package_set(device_info->app_package().begin(),
          device_info->app_package().end());
      for (const auto &item : athena_ad_request.device_info().app_package()) {
        if (app_package_set.count(item.pkg_name()) == 0) {  // 去重
          device_info->add_app_package(item.pkg_name());
          device_info->add_app_first_install_time(item.first_install_time());
          device_info->add_app_last_update_time(item.last_update_time());
          device_info->add_is_system_app(item.system_app());
        }
      }
    } else {
      device_info->clear_app_package();
      device_info->clear_app_first_install_time();
      device_info->clear_app_last_update_time();
      device_info->clear_is_system_app();
      for (const auto &item : athena_ad_request.device_info().app_package()) {
        device_info->add_app_package(item.pkg_name());
        device_info->add_app_first_install_time(item.first_install_time());
        device_info->add_app_last_update_time(item.last_update_time());
        device_info->add_is_system_app(item.system_app());
      }
    }
  } else if (is_hit_oaid && device_info->app_package_size() > 0) {   // 没有实时数据，且画像是由 oaid 匹配到
    is_oaid_match_flag |= ((uint64_t) 1 << 1);
  }

  // 补充活跃数据实验
  if (device_info->app_package_alliance_active_size() > 0) {
    std::set<std::string> app_package_set(device_info->app_package().begin(),
        device_info->app_package().end());
    for (auto& app_package_active : device_info->app_package_alliance_active()) {
      if (app_package_set.count(app_package_active) == 0) {
        device_info->add_app_package(app_package_active);
      }
    }
  }

  if (athena_ad_request.device_info().os_type() ==
      ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_ANDROID) {
    user_profile->set_platform("android");
    device_info->set_os_type(kuaishou::ad::DeviceInfo_OsType::DeviceInfo_OsType_ANDROID);
  } else if (athena_ad_request.device_info().os_type() ==
             ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_IOS) {
    user_profile->set_platform("ios");
    device_info->set_os_type(kuaishou::ad::DeviceInfo_OsType::DeviceInfo_OsType_IOS);
  } else if (athena_ad_request.device_info().os_type() ==
             ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_HARMONY) {
    user_profile->set_platform("harmony");
    device_info->set_os_type(kuaishou::ad::DeviceInfo_OsType::DeviceInfo_OsType_HARMONY);
  }

  if (!athena_ad_request.user_info().gid().empty()) {
    device_info->set_gid(athena_ad_request.user_info().gid());
  }
  device_info->set_screen_direction(athena_ad_request.device_info().screen_direction());


  // caid relative
  if (!athena_ad_request.device_info().current_caid().empty()) {
    device_info->set_current_caid(athena_ad_request.device_info().current_caid());
  }
  if (!athena_ad_request.device_info().last_caid().empty()) {
    device_info->set_last_caid(athena_ad_request.device_info().last_caid());
  }
  if (!athena_ad_request.device_info().caid_version().empty()) {
    device_info->set_current_caid_version(athena_ad_request.device_info().caid_version());
  }
  if (!athena_ad_request.device_info().last_caid_version().empty()) {
    device_info->set_last_caid_version(athena_ad_request.device_info().last_caid_version());
  }

  // TODO(CBB) field not found corresponding os version

  if (!athena_ad_request.device_info().language().empty()) {
    user_profile->set_language(athena_ad_request.device_info().language());
  }

  user_profile->set_is_oaid_match_flag(is_oaid_match_flag);


  //  could be warpped
  std::string visit_net;
  if (connection_type_universe ==
      ::kuaishou::ad::universe::NetworkInfo_ConnectionType::NetworkInfo_ConnectionType_WIFI) {
    visit_net = "wifi";
  } else if ((connection_type_universe ==
              ::kuaishou::ad::universe::NetworkInfo_ConnectionType::NetworkInfo_ConnectionType_CELL_4G) ||
             (connection_type_universe ==
              ::kuaishou::ad::universe::NetworkInfo_ConnectionType::NetworkInfo_ConnectionType_LTE)) {
    visit_net = "4g";
  } else if (connection_type_universe ==
             ::kuaishou::ad::universe::NetworkInfo_ConnectionType::NetworkInfo_ConnectionType_CELL_5G) {
    visit_net = "5g";
  }
  user_profile->set_network(visit_net);
  if (!athena_ad_request.app_info().version().empty()) {
    user_profile->set_platform_version(athena_ad_request.app_info().version());
  }
  // TODO(CBB) fields not found corresponding app name & package name

  std::set<std::string> merge_interest_set;
  for (auto &item : athena_ad_request.user_info().interest()) {
    merge_interest_set.insert(item);
  }
  for (int i = 0; i < user_profile->interest_size(); i++) {
    merge_interest_set.insert(user_profile->interest(i));
  }
  user_profile->clear_interest();
  for (auto item : merge_interest_set) {
    *(user_profile->add_interest()) = item;
  }

  user_profile->set_lat(athena_ad_request.geo_info().latitude());
  user_profile->set_lng(athena_ad_request.geo_info().longitude());

  if (athena_ad_request.fake_type() != ::kuaishou::ad::UserFakeType::NORMAL_USER) {
    if (user_profile->fake_type() == ::kuaishou::ad::UserFakeType::NORMAL_USER) {
      user_profile->set_fake_type(athena_ad_request.fake_type());
    }
  }
}

void UniverseUserProfileHandlerV2::FillData() {
  // 填充 dmp 数据
  FillDmpProfile();
  // 异步调用位置服务获取 gps 信息 因为使用画像 device_id，在此调用
  AsyncRpcFrigateLocation();
  // 填充请求携带的数据
  FillDataFromReq();
  // 填充人群包信息
  FillOrientationInfo();
  // 填充地理信息
  FillRegionInfo();
  // 填充算法挖掘的人群标签信息
  FillAudienceTagInfo();
}

bool UniverseUserProfileHandlerV2::Process() {
  auto start_ts = base::GetTimestamp();
  Initialize();
  AdUserInfo *user_profile = session_data_->response->mutable_user_profile();

  // 1. IdMapping
  IdMapping();
  auto after_idmapping = base::GetTimestamp();
  session_data_->dot_perf->Interval(after_idmapping - start_ts, "handler_process_time_us", "idmapping");
  // 2. ab 初始化
  session_data_->AbInit();
  auto after_abinit = base::GetTimestamp();
  session_data_->dot_perf->Interval(after_abinit - after_idmapping, "handler_process_time_us", "abinit");
  // 3. 异步调用画像 clotho
  AsyncGetProfile();
  auto after_clotho = base::GetTimestamp();
  session_data_->dot_perf->Interval(after_clotho - after_abinit, "handler_process_time_us", "async_profile_clotho");  // NOLINT
  // 4. 异步调用其它数据源
  AsyncDataSourceInit();
  auto after_datasource = base::GetTimestamp();
  session_data_->dot_perf->Interval(after_datasource - after_clotho,
                                     "handler_process_time_us", "async_datasource");
  // 5. 获取数据后处理逻辑
  FillData();
  auto after_fill_data = base::GetTimestamp();
  session_data_->dot_perf->Interval(after_fill_data - after_datasource,
                                     "handler_process_time_us", "fill_data");

  session_data_->response->set_status(kuaishou::ad::ResponseStatus::OK_STATUS);
  if (session_data_->uid > 0) {
    RecoUserInfoCollection::ReqInfo req_info;
    req_info.device_id = session_data_->device_id;
    req_info.id = user_profile->id();
    RecoUserInfoCollection::GetInstance()->AddRequest(req_info);
  }

  if (session_data_->uid > 0) {
    user_profile->set_id(session_data_->uid);
    user_profile->set_is_universe_fake_user(false);
  } else {
    user_profile->set_id(session_data_->fake_uid);
    user_profile->set_is_universe_fake_user(true);
    user_profile->set_register_timestamp(session_data_->fake_uid_create_time_ms);
  }
  auto after_process = base::GetTimestamp();
  // 6. 监控画像关键标签字段覆盖率情况
  PostProc();
  session_data_->dot_perf->Interval(after_process - start_ts, "handler_process_time_us", "total");
  session_data_->dot_perf->Interval(after_process - after_fill_data, "handler_process_time_us", "post_proc");
  return true;
}

void UniverseUserProfileHandlerV2::PostProc() {
  auto& dot = session_data_->dot_perf;
  if (!dot) {
    return;
  }
  // 用户画像
  dot->Count(1, "user_profile", "gender", session_data_->user_profile->gender());
  dot->Count(1, "user_profile", "age", absl::StrCat(session_data_->user_profile->age()));
  dot->Count(1, "user_profile", "language", session_data_->user_profile->language());
  dot->Count(1, "user_profile", "network", session_data_->user_profile->network());
  dot->Count(1, "user_profile", "platform", session_data_->user_profile->platform());
  dot->Count(1, "user_profile", "adcode",
      session_data_->user_profile->adcode().origin_adcode() > 0 ? "ok" : "null");
  dot->Count(1, "user_profile", "fre_adcodes",
      session_data_->user_profile->fre_adcodes_size() > 0 ? "ok" : "null");
  // 设备画像
  if (session_data_->user_profile->device_info_size() <= 0) {
    dot->Count(1, "device_profile", "null");
    return;
  }
  auto& device_info = session_data_->user_profile->device_info(0);
  std::string os_type = session_data_->user_profile->platform();
  dot->Count(1, "device_profile", "brand", device_info.brand().empty() ? "null" : "ok", os_type);
  dot->Count(1, "device_profile", "price", device_info.price() <= 0 ? "null" : "ok", os_type);
  dot->Count(1, "device_profile", "device_mod", device_info.device_mod().empty() ? "null" : "ok", os_type);
  dot->Count(1, "device_profile", "app_package", device_info.app_package_size() < 0 ? "null" : "ok", os_type);
  dot->Count(1, "device_profile", "operator_type",
      device_info.operator_type() == ::kuaishou::ad::DeviceInfo_OperatorType_UNKNOWN_OPERATOR_TYPE ? "null" : "ok", os_type);   // NOLINT
}

void UniverseUserProfileHandlerV2::BuildKeyForUserRecognition(
    const ::kuaishou::ad::AdUserProfileRequest& request,
    RepeatedPtrField<MultiTypeKey>* user_recognition_keys) {
  if (KS_UNLIKELY(!request.has_athena_ad_request())) {
    falcon::Inc("user_profile_no_athena_ad_request");
    LOG(ERROR) << "no athena ad request!";
    return;
  }
  // gid > imei_md5(imei) > oaid > android_id_md5(android_id)
  const auto &athena_ad_request = request.athena_ad_request();
  const auto& device_info = athena_ad_request.device_info();
  if (Util::IsValidKey(athena_ad_request.user_info().gid())) {
    auto* type_key = user_recognition_keys->Add();
    type_key->set_key(athena_ad_request.user_info().gid());
    type_key->set_key_type(KeyType::GID);
  }
  if (device_info.os_type() == ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_ANDROID) {
    if (Util::IsValidKey(device_info.oaid())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.oaid());
      type_key->set_key_type(KeyType::OA_ID);
    }
    if (Util::IsValidKey(device_info.oaid_md5())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.oaid_md5());
      type_key->set_key_type(KeyType::OA_ID_MD5);
    }
    if (Util::IsValidKey(device_info.imei())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(MD5String(device_info.imei()));
      type_key->set_key_type(KeyType::IMEIMD5);
    } else if (Util::IsValidKey(device_info.imei_md5())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.imei_md5());
      type_key->set_key_type(KeyType::IMEIMD5);
    }
    if (Util::IsValidKey(device_info.android_id())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(MD5String(device_info.android_id()));
      type_key->set_key_type(KeyType::ANDROID_ID_MD5);
    } else if (Util::IsValidKey(device_info.android_id_md5())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.android_id_md5());
      type_key->set_key_type(KeyType::ANDROID_ID_MD5);
    }
  } else if (device_info.os_type() == ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_IOS) {
    if (!device_info.current_caid().empty()) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.current_caid());
      type_key->set_key_type(KeyType::CAID);
    }
    if (!device_info.last_caid().empty()) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.last_caid());
      type_key->set_key_type(KeyType::CAID);
    }
    if (Util::IsValidKey(device_info.idfa())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(MD5String(device_info.idfa()));
      type_key->set_key_type(KeyType::IDFA);
    } else if (Util::IsValidKey(device_info.idfa_md5())) {
      PerfUtil::CountLogStash(1, "ad.userprofile.count", "has_idfa_md5");
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.idfa_md5());
      type_key->set_key_type(KeyType::IDFA);
    }
  } else if (device_info.os_type() == ::kuaishou::ad::universe::DeviceInfo_OsType::DeviceInfo_OsType_HARMONY) {  // NOLINT
    if (Util::IsValidKey(device_info.oaid())) {
      auto* type_key = user_recognition_keys->Add();
      type_key->set_key(device_info.oaid());
      type_key->set_key_type(KeyType::OA_ID);
    }
  }
}

int64_t UniverseUserProfileHandlerV2::ParseFakeUidNew() {
  if (ks::ad_base::IsUniverseEmptyDid(session_data_->device_id, "")) {
    // 没有 device_id 则用 llsid hash 一个
    return ad_base::fnv_hashstr(std::to_string(session_data_->llsid).c_str())
        % fake_user_id_interval + fake_user_id_base;
  } else {
    return ad_base::fnv_hashstr(session_data_->device_id.c_str())
        % fake_user_id_interval + fake_user_id_base;
  }
}

void UniverseUserProfileHandlerV2::WriteNoProfileInfoKafka(uint64_t id,
                  AdProfileCoreReads_ReadType read_type, const std::string& device_id) {
  AdProfileCoreReads ad_profile_core_reads;
  auto* profile = ad_profile_core_reads.add_ad_profile_core_reads();

  ad_profile_core_reads.set_type(read_type);
  ad_profile_core_reads.set_client(AdProfileCoreReads::ALLIANCE);
  switch (read_type) {
    case AdProfileCoreReads::USER:
      profile->set_user_id(id);
      profile->set_device_id(device_id);
      break;
    default:
      // pass
      break;
  }
  LOG_EVERY_N(INFO, AdKconfUtil::logFrequency())
      << "ad_profile_core_reads: " << ad_profile_core_reads.ShortDebugString();
  static std::shared_ptr<::ks::infra::kfk::Producer> producer = nullptr;
  static std::once_flag flag;
  std::call_once(flag, [] {
    producer = ks::infra::kfk::DynamicKafkaClient::GetProducerByLogicalTopicId("knews_ad_profile_read_topic");
  });

  if (producer == nullptr) {
    LOG_EVERY_N(ERROR, AdKconfUtil::logFrequency())
        << "Encounter nullptr when try to get kafka producer, topic id: knews_ad_profile_read_topic";
    return;
  }
  std::string request_msg;
  ad_profile_core_reads.SerializeToString(&request_msg);
  RdKafka::ErrorCode err_code = RdKafka::ERR_UNKNOWN;
  {
    ScopedPerfTimer scoped_perf_timer("ad.userprofile.interval", "new_sys_kfk_write_cost");
    err_code = producer->Produce(request_msg, nullptr);
  }
  if (err_code != RdKafka::ERR_NO_ERROR) {
    LOG_EVERY_N(WARNING, AdKconfUtil::logFrequency())
        << "New system write kafka error. code:" << err_code;
  } else {
    LOG_EVERY_N(WARNING, AdKconfUtil::logFrequency())
        << "New system write kafka ok";
  }
}

}  // namespace ad_user_profile
}  // namespace ks
