#pragma once

#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"

namespace ks {
namespace ad_user_profile {
// SPDM 使用文档: https://docs.corp.kuaishou.com/k/home/<USER>/fcAAIDGfeGPH2sQAxiBwuiaJ4
// 声明 kconf 开关:  DECLARE_SPDM_KCONF_BOOL(key);  // [owner] 开关用途/作用
// 声明 abtest 开关: DECLARE_SPDM_ABTEST_BOOL(key);  // [owner] 开关用途/作用

// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.
// 所有 spdm 开关在本文件中声明, 在 spdm_switches.cc 中定义.

DECLARE_SPDM_KCONF_BOOL(disableAiwenIpData);
DECLARE_SPDM_KCONF_BOOL(enableUniverseMergeKwaiRealtimeTag);

DECLARE_SPDM_ABTEST_BOOL(enable_device_info_match_opt_v2);
DECLARE_SPDM_ABTEST_BOOL(enable_device_id_match_opt);
DECLARE_SPDM_ABTEST_BOOL(universe_device_info_opt_mode);
DECLARE_SPDM_ABTEST_BOOL(universe_device_info_opt_price);
DECLARE_SPDM_ABTEST_BOOL(universe_device_info_opt_brand);
DECLARE_SPDM_ABTEST_BOOL(universe_device_info_opt_brand_v2);
DECLARE_SPDM_ABTEST_BOOL(universe_fre_adcodes_exp);
DECLARE_SPDM_ABTEST_BOOL(universe_residence_adcodes_exp);
DECLARE_SPDM_ABTEST_BOOL(universe_fre_business_district_exp);

// DECLARE_SPDM_ABTEST_INT64(universe_ad_brand_id);
DECLARE_SPDM_ABTEST_INT64(sdk_app_package_valid_num_v2);
DECLARE_SPDM_ABTEST_INT64(universe_residence_map_id);
}  // namespace ad_user_profile
}  // namespace ks
