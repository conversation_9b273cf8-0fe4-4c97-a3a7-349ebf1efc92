#include "teams/ad/ad_user_profile/utils/spdm/spdm_switches.h"
#include "teams/ad/ad_base/src/spdm_lib/src/spdm_api.h"
namespace ks {
namespace ad_user_profile {

// 定义 kconf 开关:  SPDM_KCONF_BOOL(path, key);
// 定义 abtest 开关: SPDM_ABTEST_BOOL(key, ks::AbtestBiz);
SPDM_KCONF_BOOL(ad.userprofile, disableAiwenIpData);
SPDM_KCONF_BOOL(ad.userprofile, enableUniverseMergeKwaiRealtimeTag);

SPDM_ABTEST_BOOL(enable_device_info_match_opt_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(enable_device_id_match_opt, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_device_info_opt_mode, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_device_info_opt_price, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_device_info_opt_brand, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_device_info_opt_brand_v2, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_fre_adcodes_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_residence_adcodes_exp, ks::AbtestBiz::AD_DSP);
SPDM_ABTEST_BOOL(universe_fre_business_district_exp, ks::AbtestBiz::AD_DSP);

// SPDM_ABTEST_INT64(universe_ad_brand_id, ks::AbtestBiz::AD_DSP, 0);
SPDM_ABTEST_INT64(sdk_app_package_valid_num_v2, ks::AbtestBiz::AD_DSP, 8);
SPDM_ABTEST_INT64(universe_residence_map_id, ks::AbtestBiz::AD_DSP, 0);

}  // namespace ad_user_profile
}  // namespace ks
