#include "teams/ad/ad_target_search/engine/node/trigger_merge.h"

#include <cstdint>
#include <unordered_map>
#include <utility>
#include <vector>
#include <unordered_set>
#include <algorithm>
#include <string>

#include "absl/strings/substitute.h"
#include "base/common/logging.h"
#include "teams/ad/ad_base/src/ktrace/local_ktrace_span.h"
#include "teams/ad/ad_target_search/engine/context_data/context_data.h"
#include "teams/ad/ad_target_search/engine/node/utility/e2e_twin_tower.h"
#include "teams/ad/ad_target_search/engine/node/utility/scope_perf.h"
#include "teams/ad/ad_target_search/engine/node/utility/scope_time_recorder.h"
#include "teams/ad/ad_target_search/util/kconf/kconf.h"
#include "teams/ad/ad_target_search/util/spdm/spdm_switches.h"
#include "teams/ad/engine_base/search/cache_loader/live_stream_index.h"
#include "dragon/src/core/common_reco_util.h"
#include "base/encoding/base64.h"
#include "teams/ad/engine_base/search/util/table_extension/table_wrapper.h"
#include "teams/ad/ad_table/code_generator/ad_target_search/LiveStreamUserInfo.h"
#include "teams/ad/ad_table/code_generator/ad_target_search/Target.h"

namespace ks {
namespace ad_target_search {

TriggerMerge::TriggerMerge(AdContext &context) : AdSyncNode(context, "trigger_merge") {
  Clear();
}

TriggerMerge::~TriggerMerge() { Clear(); }

void TriggerMerge::Clear() {
  context_data_ = nullptr;
  perf_ = nullptr;

  query_photo_list_.reset();
  query_live_list_.reset();
  strong_card_photo_list_.reset();
  query_sku_photo_list_.reset();
  query_sku_live_list_.reset();

  strategy_miss_item_count_.clear();
  strategy_valid_item_count_.clear();

  photo_merge_strategy_.reset();
  live_merge_strategy_.reset();
  item_merge_strategy_.reset();
  strong_card_merge_strategy_.reset();
  author_trigger_trans_strategy_.reset();
}

bool TriggerMerge::Prepare() {
  context_data_ = context_->GetMutableContextData<ContextData>();
  if (!context_data_) {
    LOG(ERROR) << "context data is null!";
    return false;
  }

  perf_ = context_data_->dot_perf;
  if (!perf_) {
    LOG(ERROR) << "perf is null!";
    return false;
  }

  StrategyManager* p_strategy_manager = context_->GetContextSystem<StrategyManager>();
  photo_merge_strategy_ =
      p_strategy_manager->GetStrategy<PhotoMergeStrategy>(AdStrategyType::PhotoMergeStrategy);
  live_merge_strategy_ =
      p_strategy_manager->GetStrategy<LiveMergeStrategy>(AdStrategyType::LiveMergeStrategy);
  strong_card_merge_strategy_ =
      p_strategy_manager->GetStrategy<StrongCardMergeStrategy>(AdStrategyType::StrongCardMergeStrategy);
  author_trigger_trans_strategy_ =
      p_strategy_manager->GetStrategy<AuthorTriggerTransStrategy>(AdStrategyType::AuthorTriggerTransStrategy);
  item_merge_strategy_ =
      p_strategy_manager->GetStrategy<ItemMergeStrategy>(AdStrategyType::ItemMergeStrategy);
  photo_merge_strategy_->Prepare();
  live_merge_strategy_->Prepare();
  strong_card_merge_strategy_->Prepare();
  author_trigger_trans_strategy_->Prepare();
  item_merge_strategy_->Prepare();

  return true;
}

void TriggerMerge::GetPhotoTriggerResultFromTrans(
    const kuaishou::ad::SearchQueryTransportInfo& transport_info) {
  const auto& resp = transport_info.photo_trigger_res();
  PhotoTriggerResultPostproc(resp);
}

void TriggerMerge::PhotoTriggerResultPostproc(const ks::platform::CommonRecoResponse& resp) {
  auto& photo_trigger_result = context_data_->photo_trigger_result;
  // 提取 common attr
  for (const auto& attr : resp.common_attr()) {
    if (attr.name() == "photo_cnt_before_merge" && attr.type() == kuiba::CommonSampleEnum::INT_ATTR) {
      photo_trigger_result.photo_cnt_before_merge = attr.int_value();
    }
  }

  auto& query_photo_list = photo_trigger_result.query_photo_list;

  auto extract_from_packed_item_attr = [&]() {
    auto t0 = base::GetTimestamp();

    thread_local ks::platform::DataFrame df("");
    df.Clear();

    if (resp.item_size() != resp.item_attr().item_keys_size()) {
      LOG_EVERY_N(ERROR, 10) << "packed: size mismatch";
      return;
    }

    query_photo_list.reserve(resp.item_size());

    int i = 0;
    for (const auto& item : resp.item()) {
      uint64_t item_key = resp.item_attr().item_keys(i++);
      df.AddCommonRecoResult(item_key, item.reason(), item.score(), 0);

      query_photo_list.emplace_back();
      QueryPhoto &photo = query_photo_list.back();
      photo.recall_strategy_type = item.reason();
      photo.rank_score = item.score();
    }

    thread_local ks::platform::AttrValue attr("", 0);

    for (const auto& attr_value : resp.item_attr().attr_values()) {
      const std::string& attr_name = attr_value.name();

      attr.Clear();
      ks::platform::RecoUtil::ExtractPackedItemAttrToContext(&attr, attr_value, &df.GetCommonRecoResults());

      int idx = 0;
      for (auto& photo : query_photo_list) {
        const auto& reco_result = df.GetCommonRecoResults()[idx++];
        size_t attr_index = reco_result.GetAttrIndex();

        if (attr_name == "photo_id") {
          photo.photo_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "sub_recall_strategy_type") {
          photo.sub_recall_strategy_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "relevance_score") {
          photo.relevance_score = attr.GetDoubleValue(attr_index).value_or(0.0);
        } else if (attr_name == "is_qr") {
          photo.is_qr = (bool)(attr.GetIntValue(attr_index).value_or(0));
        } else if (attr_name == "qr_score") {
          photo.qr_score = attr.GetDoubleValue(attr_index).value_or(1.0);
        } else if (attr_name == "extend_type") {
          photo.extend_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "sub_extend_type") {
          photo.sub_extend_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "rewrite_query_set") {
          auto rewrite_query_set = attr.GetStringListValue(attr_index);
          if (rewrite_query_set.has_value()) {
            for (const auto& v : rewrite_query_set.value()) {
              photo.rewrite_query_set.insert(std::string(v));
            }
          }
        } else if (attr_name == "counter_ecpm") {
          photo.counter_ecpm = attr.GetDoubleValue(attr_index).value_or(1.0);
        } else if (attr_name == "is_fanstop_recall") {
          photo.is_fanstop_recall = (bool)(attr.GetIntValue(attr_index).value_or(0));
        } else if (attr_name == "ensemble_score") {
          photo.ensemble_score = attr.GetDoubleValue(attr_index).value_or(0.0);
        } else if (attr_name == "after_merge") {
          photo.after_merge = (bool)(attr.GetIntValue(attr_index).value_or(0));
        } else if (attr_name == "recall_tags") {
          auto recall_tags = attr.GetIntListValue(attr_index);
          if (recall_tags.has_value()) {
            photo.recall_tags.reserve(recall_tags.value().size());
            for (int64_t recall_tag : recall_tags.value()) {
              photo.recall_tags.push_back((int32_t)recall_tag);
            }
          }
        } else if (attr_name == "retrieval_weight") {
          photo.retrieval_weight = attr.GetDoubleValue(attr_index).value_or(0.0);
        }
      }
    }

    context_data_->dot_perf->Interval(base::GetTimestamp() - t0,
                                          "async_photo_trigger.extract_pack_v2");
  };

  extract_from_packed_item_attr();
}

void TriggerMerge::GetLiveTriggerResultFromTrans(
    const kuaishou::ad::SearchQueryTransportInfo& transport_info) {
  const auto& resp = transport_info.live_trigger_res();
  LiveTriggerResultPostproc(resp);
}

void TriggerMerge::LiveTriggerResultPostproc(const ks::platform::CommonRecoResponse& resp) {
  auto& live_trigger_result = context_data_->live_trigger_result;
  auto& query_trigger_item_list = live_trigger_result.query_trigger_item_list;
  auto& sem_live_ann_score_set = live_trigger_result.sem_live_ann_score_set;
  auto extract_from_packed_item_attr_trigger_item = [&]() {
    auto t0 = base::GetTimestamp();

    thread_local ks::platform::DataFrame df("");
    df.Clear();

    for (const auto& common_attr : resp.common_attr()) {
      if (common_attr.name() == "encode_live_knn_score") {
        common_attr.string_value();
        std::string decoded_value;
        if (!base::Base64Decode(common_attr.string_value(), &decoded_value)) {
          LOG_EVERY_N(WARNING, 10000) << "live_trigger common_attr: "
                                      << common_attr.ShortDebugString();
          continue;
        }
        if (!sem_live_ann_score_set.ParseFromString(decoded_value)) {
          LOG_EVERY_N(WARNING, 1000) << "live_trigger common_attr, decoded_value: " << decoded_value;
        }
      }
    }

    if (resp.item_size() != resp.item_attr().item_keys_size()) {
      LOG_EVERY_N(ERROR, 10) << "packed: size mismatch";
      return;
    }

    query_trigger_item_list.reserve(resp.item_size());

    int i = 0;
    for (const auto& item : resp.item()) {
      uint64_t item_key = resp.item_attr().item_keys(i++);
      df.AddCommonRecoResult(item_key, item.reason(), item.score(), 0);

      query_trigger_item_list.emplace_back();
      TriggerItem &trigger_item = query_trigger_item_list.back();
      trigger_item.recall_strategy_type = item.reason();
      trigger_item.relevance_score = item.score();
    }

    thread_local ks::platform::AttrValue attr("", 0);

    for (const auto& attr_value : resp.item_attr().attr_values()) {
      const std::string& attr_name = attr_value.name();

      attr.Clear();
      ks::platform::RecoUtil::ExtractPackedItemAttrToContext(&attr, attr_value, &df.GetCommonRecoResults());

      int idx = 0;
      for (auto& trigger_item : query_trigger_item_list) {
        const auto& reco_result = df.GetCommonRecoResults()[idx++];
        size_t attr_index = reco_result.GetAttrIndex();

        if (attr_name == "live_stream_id") {
          trigger_item.live_stream_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "user_id") {
          trigger_item.user_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "sub_recall_strategy_type") {
          trigger_item.sub_recall_strategy_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "relevance_score") {
          trigger_item.relevance_score = attr.GetDoubleValue(attr_index).value_or(0.0);
        } else if (attr_name == "is_qr") {
          trigger_item.is_qr = (bool)(attr.GetIntValue(attr_index).value_or(0));
        } else if (attr_name == "qr_score") {
          trigger_item.qr_score = attr.GetDoubleValue(attr_index).value_or(1.0);
        } else if (attr_name == "extend_type") {
          trigger_item.extend_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "sub_extend_type") {
          trigger_item.sub_extend_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "rewrite_query_set") {
          auto rewrite_query_set = attr.GetStringListValue(attr_index);
          if (rewrite_query_set.has_value()) {
            for (const auto& v : rewrite_query_set.value()) {
              trigger_item.rewrite_query_set.insert(std::string(v));
            }
          }
        } else if (attr_name == "is_fanstop_recall") {
          trigger_item.is_fanstop_recall = (bool)(attr.GetIntValue(attr_index).value_or(0));
        }  else if (attr_name == "sku_id_vec") {
          auto sku_id_set = attr.GetIntListValue(attr_index);
          if (sku_id_set.has_value()) {
            for (const auto& v : sku_id_set.value()) {
              trigger_item.sku_id_set.insert(v);
            }
          }
        } else if (attr_name == "sctr") {
          trigger_item.sctr = attr.GetDoubleValue(attr_index).value_or(0.0);
        } else if (attr_name == "rank_score") {
          trigger_item.rank_score = attr.GetDoubleValue(attr_index).value_or(0.0);
        } else if (attr_name == "sku_id") {
          trigger_item.sku_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "dmp_id") {
          trigger_item.dmp_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "spu_id") {
          trigger_item.spu_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "series_id") {
          trigger_item.series_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "item_type") {
          trigger_item.trigger_item_type = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "goods_id") {
          trigger_item.goods_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "dpa_id") {
          trigger_item.dpa_id = attr.GetIntValue(attr_index).value_or(0);
        } else if (attr_name == "book_id") {
          trigger_item.book_id = attr.GetIntValue(attr_index).value_or(0);
        }
      }
    }

    ks::infra::PerfUtil::IntervalLogStash(base::GetTimestamp() - t0,
                                          ks::ad_base::FLAGS_perf_name_space,
                                          "async_live_trigger.extract_pack_v2");
  };
  extract_from_packed_item_attr_trigger_item();
}

void TriggerMerge::FixTriggerDotAndCounter() {
  const auto& group = context_data_->ab_group_name;
  // photo
  context_data_->dot_perf->Interval(
      context_data_->photo_trigger_result.query_photo_list.size(),
      "photo_trigger_list_size", group);

  for (const auto& photo : context_data_->photo_trigger_result.query_photo_list) {
    // retrieval bitset 记录
    for (int32_t recall_tag : photo.recall_tags) {
      context_data_->retrieval_tag_map[photo.photo_id].insert(recall_tag);
    }
  }

  // live
  context_data_->dot_perf->Interval(
        context_data_->live_trigger_result.query_trigger_item_list.size(), "live_trigger_list_size",
        "live_trigger", group);
}

bool TriggerMerge::ProcessInner() {
  if (!Prepare()) {
    return false;
  }

  ks::ad_base::LocalKtraceSpan ktrace_span("TriggerMerge", __func__);

  {
    ks::ad_base::LocalKtraceSpan ktrace_span_1("trigger_res_from_req", __func__);
    const auto& transport_info = context_data_->ad_request->search_query_transport_info();
    GetPhotoTriggerResultFromTrans(transport_info);
    GetLiveTriggerResultFromTrans(transport_info);
    // fix 和原结构保持一致
    context_data_->photo_trigger_result.success = true;
    context_data_->live_trigger_result.success = true;
    FixTriggerDotAndCounter();
  }

  if (!SPDM_disable_trigger_merge_e2e_strategy(context_data_->spdm_ctx) &&
      (!SPDM_enable_twin_tower_goods(context_data_->spdm_ctx) ||
       context_data_->search_ad_interactive_form != +SearchAdInteractiveForm::GOODS_TAB)) {
    ks::ad_base::LocalKtraceSpan ktrace_span_1("e2e_twin_tower", __func__);
    E2eTwinTower e2e_twin_tower;
    e2e_twin_tower.ProcessV2(context_data_);
  }

  PROCESS_TIME_RECORD;

  // FIXME(weiyilong): 临时调试用
  bool adretr_realtime_miss_log =
      AdKconfUtil::enableAdRetrRealtimeMissLog() &&
      (context_data_->session_context->TryGetString("adretr_section_kess_name", "grpc_adRetrService") ==
       "ad-retr-search-server-multi-realtime");

  bool enable_goods_tab_align_search =
      SPDM_enable_goods_tab_align_search(context_data_->spdm_ctx) &&
      context_data_->search_ad_interactive_form == +SearchAdInteractiveForm::GOODS_TAB;

  {
    // 所有已经迁移的 photo 触发通路都跳过，只在这里取 trigger 结果统一放入 trigger data
    auto& photo_trigger_result = context_data_->photo_trigger_result;
    if (photo_trigger_result.success &&
        (context_data_->search_ad_interactive_form != +SearchAdInteractiveForm::GOODS_TAB ||
         enable_goods_tab_align_search)) {
      int32_t count = 0, miss = 0;
      std::unordered_map<int, int> strategy_total_count;
      std::unordered_map<int, int> strategy_miss_count;
      for (auto& query_photo : photo_trigger_result.query_photo_list) {
        int64_t photo_id = query_photo.photo_id;
        ++strategy_total_count[query_photo.recall_strategy_type];
        if (!context_data_->PhotoHasValidCreative(photo_id)) {
          ++miss;
          ++strategy_miss_count[query_photo.recall_strategy_type];
          if (adretr_realtime_miss_log &&
              query_photo.recall_strategy_type == RetrievalTag::SEARCHAD_QUERY_TO_RETR) {
            LOG_EVERY_N(INFO, 10000) << "adretr realtime miss photo id: " << photo_id;
          }
          continue;
        }
        if (enable_goods_tab_align_search) {
          TriggerSkuPhotoFromPhoto(query_photo);
          continue;
        }
        TriggerItem* p_trigger_photo = nullptr;
        p_trigger_photo = context_data_->trigger_data.AppendPhoto(photo_id);
        if (!p_trigger_photo) {
          continue;
        }
        ++count;
        p_trigger_photo->ConvertFromQueryPhoto(query_photo);
        p_trigger_photo->from_trigger = true;
      }
      context_data_->dot_perf->Count(miss, "query_node.missing_photo_cnt", "TRIGGER",
                                     context_data_->ab_group_name);
      context_data_->dot_perf->Interval(miss + count, "query_node.query_photo_list_size", "TRIGGER",
                                        context_data_->ab_group_name);
      context_data_->dot_perf->Interval(count, "query_node.query_photo_list_real_size", "TRIGGER",
                                        context_data_->ab_group_name);

      for (const auto [tag, tag_total] : strategy_total_count) {
        int tag_miss = strategy_miss_count[tag];
        int tag_count = tag_total - tag_miss;

        auto name = absl::Substitute("TRIGGER-$0", tag);

        context_data_->dot_perf->Count(tag_miss, "query_node.missing_photo_cnt", name,
                                      context_data_->ab_group_name);
        context_data_->dot_perf->Interval(tag_total, "query_node.query_photo_list_size", name,
                                          context_data_->ab_group_name);
        context_data_->dot_perf->Interval(tag_count, "query_node.query_photo_list_real_size", name,
                                          context_data_->ab_group_name);
      }
    }
  }

  if (context_data_->trigger_data.Empty()) {
    perf_->Count(1, "recall_photo_list_empty");
  } else {
    perf_->Count(1, "recall_photo_list_nonempty",
               std::to_string((int)context_data_->live_stream_only));
  }

  {
    // 所有已经迁移的 live 触发通路都跳过，只在这里取 trigger 结果统一放入 trigger data
    const auto* ad_index = context_data_->ad_index;
    std::unordered_map<int32_t, int32_t> strategy_live_miss;
    std::unordered_map<int32_t, int32_t> strategy_live_valid;
    int32_t live_miss = 0;
    int32_t live_count = 0;
    bool trigger_creative = SPDM_enable_author_live_creative(context_data_->spdm_ctx);

    auto& live_trigger_result = context_data_->live_trigger_result;
    std::vector<TriggerItem*> author_trigger;
    std::vector<TriggerItem*> sku_photo_trigger;
    std::vector<TriggerItem*> sku_live_trigger;
    std::vector<TriggerItem*> spu_trigger;
    std::vector<TriggerItem*> dmp_trigger;
    std::vector<TriggerItem*> series_trigger;
    std::vector<TriggerItem*> local_life_goods_trigger;
    std::vector<TriggerItem*> dpa_trigger;
    std::vector<TriggerItem*> book_trigger;
    std::unordered_map<int64_t, int64_t> error_info_map;
    if (live_trigger_result.success) {
      for (auto& query_trigger_item : live_trigger_result.query_trigger_item_list) {
        if (query_trigger_item.trigger_item_type == TriggerItemType::AUTHOR) {
          author_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::SKU_PHOTO) {
          sku_photo_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::SKU_LIVE) {
          sku_live_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::SKU_ALL) {
          sku_photo_trigger.push_back(&query_trigger_item);
          sku_live_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::LIVE) {
          if (context_data_->IsLiveStreamValid(query_trigger_item.live_stream_id)) {
            if (enable_goods_tab_align_search) {
              TriggerSkuLiveFromLive(query_trigger_item);
            }
            TriggerItem trigger_item = query_trigger_item;
            trigger_item.recall_node = GetName();
            context_data_->trigger_data.AppendLive(std::move(trigger_item));
            live_count++;
            ++strategy_live_valid[query_trigger_item.recall_strategy_type];
          } else {
            live_miss++;
            ++strategy_live_miss[query_trigger_item.recall_strategy_type];
          }
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::DMP) {
          dmp_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::SPU) {
          spu_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::SERIES_ID) {
          series_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::LOCAL_LIFE_GOODS) {
          local_life_goods_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::DPA_ID) {
          dpa_trigger.push_back(&query_trigger_item);
        } else if (query_trigger_item.trigger_item_type == TriggerItemType::BOOK_ID) {
          book_trigger.push_back(&query_trigger_item);
        } else {
          error_info_map[query_trigger_item.recall_strategy_type]++;
        }
      }
      if (trigger_creative) {
        if (context_data_->use_ad_table_invert) {
          author_trigger_trans_strategy_->Trans2CreativeV2(author_trigger);
        } else {
          author_trigger_trans_strategy_->Trans2Creative(author_trigger);
        }
      } else {
        author_trigger_trans_strategy_->Trans2PhotoLive(author_trigger);
      }
      if (sku_photo_trigger.size() > 0) {
        TriggerSkuPhoto(sku_photo_trigger);
      }
      if (sku_live_trigger.size() > 0) {
        TriggerSkuLive(sku_live_trigger);
      }
      if (dmp_trigger.size() > 0) {
        TriggerDmp(dmp_trigger);
      }
      if (spu_trigger.size() > 0) {
        TriggerSpu(spu_trigger);
      }
      if (series_trigger.size() > 0) {
        TriggerSerial(series_trigger);
      }
      if (local_life_goods_trigger.size() > 0) {
        TriggerLocalLifeGoods(local_life_goods_trigger);
      }
      if (dpa_trigger.size() > 0) {
        TriggerDpa(dpa_trigger);
      }
      if (book_trigger.size() > 0) {
        TriggerKwaiBook(book_trigger);
      }
      for (auto it : error_info_map) {
        context_data_->dot_perf->Interval(it.second, "live_trigger.item_type_error", absl::StrCat(it.first));
      }
    }
    context_data_->dot_perf->Count(live_miss, "query_node.missing_live_cnt", "TRIGGER",
                                     context_data_->ab_group_name);
    context_data_->dot_perf->Interval(live_miss + live_count, "query_node.query_live_list_size", "TRIGGER",
                                      context_data_->ab_group_name);
    context_data_->dot_perf->Interval(live_count, "query_node.query_live_list_real_size", "TRIGGER",
                                      context_data_->ab_group_name);
    std::string key_prefix = context_data_->session_context->
      TryGetString("query_photo_sku_key_prefix", "photosku_");
    for (const auto &kv : strategy_live_miss) {
      context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.strategy_live_miss",
                                        std::to_string(kv.first));
    }
    for (const auto &kv : strategy_live_valid) {
      context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.strategy_live_valid",
                                        std::to_string(kv.first));
    }
    for (const auto &kv : strategy_miss_item_count_) {
      context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.strategy_miss_item_count",
                                        std::to_string(kv.first),
                                        key_prefix);
    }
    for (const auto &kv : strategy_valid_item_count_) {
      context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.strategy_valid_item_count",
                                        std::to_string(kv.first),
                                        key_prefix);
    }
    const auto& trigger_sem_live_ann_score_set = live_trigger_result.sem_live_ann_score_set;
    auto &live_ann_score = context_data_->mmu_sem_live_ann_score;
    for (const auto& trigger_score : trigger_sem_live_ann_score_set.sem_live_ann_score()) {
      live_ann_score.insert({trigger_score.live_stream_id(),
                            {trigger_score.mmu_knn_score(), trigger_score.sem_knn_score()}});
    }
  }
  // 处理转换失败的复杂类型
  context_data_->trigger_data.Compact();

  query_photo_list_ = std::make_unique<LogicCommonList<TriggerItem>>();
  query_live_list_ = std::make_unique<LogicCommonList<TriggerItem>>();
  query_sku_photo_list_ = std::make_unique<LogicCommonList<TriggerItem>>();
  query_sku_live_list_ = std::make_unique<LogicCommonList<TriggerItem>>();
  strong_card_photo_list_ = std::make_unique<LogicCommonList<TriggerItem>>();

  TriggerData::SplitFunc split_func =
      [&](const TriggerItem *item) -> LogicCommonList<TriggerItem> * {
    if (item->recall_strategy_type == RetrievalTag::SEARCHAD_INTERVENE) {
      if (item->trigger_item_type == TriggerItemType::PHOTO) {
        context_data_->retrieval_tag_map[item->photo_id].insert(item->recall_strategy_type);
      } else if (item->trigger_item_type == TriggerItemType::LIVE) {
        context_data_->live_retrieval_tag_map[item->live_stream_id].insert(item->recall_strategy_type);
      }
      return nullptr;
    }
    if (context_data_->IsSearchStrongCard(*item)) {
      context_data_->retrieval_tag_map[item->photo_id].insert(item->recall_strategy_type);
      return strong_card_photo_list_.get();
    }
    if (item->trigger_item_type == TriggerItemType::PHOTO) {
      context_data_->retrieval_tag_map[item->photo_id].insert(item->recall_strategy_type);
      return query_photo_list_.get();
    }
    if (item->trigger_item_type == TriggerItemType::LIVE) {
      context_data_->live_retrieval_tag_map[item->live_stream_id].insert(item->recall_strategy_type);
      return query_live_list_.get();
    }
    if (item->trigger_item_type == TriggerItemType::SKU_LIVE) {
      context_data_->item_retrieval_tag_map[item->sku_id].insert(item->recall_strategy_type);
      return query_sku_live_list_.get();
    }
    if (item->trigger_item_type == TriggerItemType::SKU_PHOTO) {
      context_data_->item_retrieval_tag_map[item->sku_id].insert(item->recall_strategy_type);
      return query_sku_photo_list_.get();
    }
    return nullptr;
  };
  context_data_->trigger_data.Split(split_func);

  context_data_->target_counter.photo_recall = context_data_->photo_trigger_result.photo_cnt_before_merge;
  context_data_->target_counter.sku_recall = query_sku_live_list_->Size() + query_sku_photo_list_->Size();

  photo_merge_strategy_->Distinct(query_photo_list_.get());
  live_merge_strategy_->Distinct(query_live_list_.get());
  if (SPDM_enable_item_distinct_merge_strategy(context_data_->spdm_ctx)) {
    item_merge_strategy_->DistinctItem(query_sku_photo_list_.get(), query_sku_live_list_.get());
  } else {
    item_merge_strategy_->DistinctPhotoItem(query_sku_photo_list_.get());
    item_merge_strategy_->DistinctLiveItem(query_sku_live_list_.get());
  }

  context_data_->target_counter.photo_recall += strong_card_photo_list_->Size();
  context_data_->dot_perf->Interval(context_data_->target_counter.photo_recall,
                                    "target_counter.photo_recall");

  strong_card_merge_strategy_->MergeToPhoto(strong_card_photo_list_.get(), query_photo_list_.get());

  /**
   * distinct photo size.
   */
  std::unordered_map<int, int> strategy_ad_count;
  for (auto *photo : query_photo_list_->Items()) {
    ++strategy_ad_count[photo->recall_strategy_type];
  }

  for (const auto &kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second,
                                      "distinct.sub_strategy_ad_list_cnt",
                                      std::to_string(kv.first),
                                      context_data_->ab_group_name);
  }
  context_data_->dot_perf->Interval(query_photo_list_->Size(), "relevance_distinct_photo_size",
                                    context_data_->ab_group_name);

  /**
   * distinct live size.
   */
  strategy_ad_count.clear();
  for (auto *live : query_live_list_->Items()) {
    ++strategy_ad_count[live->recall_strategy_type];
  }

  for (const auto &kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second,
                                      "distinct.sub_strategy_ad_list_cnt",
                                      std::to_string(kv.first),
                                      context_data_->ab_group_name,
                                      "live");
  }
  context_data_->dot_perf->Interval(query_live_list_->Size(), "relevance_distinct_live_size",
                                    context_data_->ab_group_name);

  context_data_->target_counter.photo_distinct = query_photo_list_->Size();
  context_data_->dot_perf->Interval(context_data_->target_counter.photo_distinct,
                                    "target_counter.photo_distinct");

  /**
   * distinct sku size.
   */
  strategy_ad_count.clear();
  for (auto *sku : query_sku_live_list_->Items()) {
    if (sku == nullptr) { continue; }
    ++strategy_ad_count[sku->recall_strategy_type];
  }
  for (auto *sku : query_sku_photo_list_->Items()) {
    if (sku == nullptr) { continue; }
    ++strategy_ad_count[sku->recall_strategy_type];
  }
  for (const auto& kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second, "distinct.sub_strategy_ad_list_cnt",
                                      std::to_string(kv.first), context_data_->ab_group_name, "sku");
  }
  context_data_->target_counter.sku_distinct = query_sku_live_list_->Size() + query_sku_photo_list_->Size();
  context_data_->dot_perf->Interval(context_data_->target_counter.sku_distinct, "relevance_distinct_sku_size",
                                    context_data_->ab_group_name);

  // NOTE(weiyilong): ensemble_score 往后就不用了，拿来保存当前的顺序
  int32_t cnt = query_photo_list_->Size();
  for (auto* photo : query_photo_list_->Items()) {
    photo->ensemble_score = cnt--;
  }

  cnt = query_live_list_->Size();
  for (auto* live : query_live_list_->Items()) {
    live->ensemble_score = cnt--;
  }

  // 策略会把过滤的 trigger item 置 invalid，这里只需要 compact 就能全部移除
  context_data_->trigger_data.Compact();

  return true;
}

void TriggerMerge::TriggerSkuPhoto(const std::vector<TriggerItem*>& sku_photo_trigger_item_list) {
  std::unordered_map<int32_t, int32_t> sku_photo_miss;
  std::unordered_map<int32_t, int32_t> sku_photo_valid;
  context_data_->dot_perf->Count(1, "test_zd",
            "sku_photo", "into_trigger_sku_photo");
  for (const auto* query_trigger_item : sku_photo_trigger_item_list) {
    TriggerItem trigger_item = *query_trigger_item;
    bool has_valid_photo = false;
    if (context_data_->use_ad_table_invert) {
      if (context_data_->search_item_id_invert_table_v2) {
        const auto* ocpx_invert_list_ptr =
            context_data_->search_item_id_invert_table_v2->GetInvertList1Token(trigger_item.sku_id);
        if (ocpx_invert_list_ptr && ocpx_invert_list_ptr->size() > 0) {
          has_valid_photo = true;
        }
      }
    } else {
      if (context_data_->search_item_id_invert_table) {
        ks::ad_server::inner_loop_index::TokenType token;
        token.subtoken_1 = trigger_item.sku_id;
        const auto* ocpx_invert_list_ptr = context_data_->search_item_id_invert_table->GetInvertList(token);
        if (ocpx_invert_list_ptr && ocpx_invert_list_ptr->size() > 0) {
          has_valid_photo = true;
        }
      }
    }
    if (!has_valid_photo) {
      ++strategy_miss_item_count_[trigger_item.recall_strategy_type];
      AppendTransFailedTrigger(trigger_item);
      ++sku_photo_miss[trigger_item.recall_strategy_type];
      continue;
    }
    ++strategy_valid_item_count_[trigger_item.recall_strategy_type];
    trigger_item.recall_node = GetName();
    context_data_->trigger_data.AppendSkuPhoto(std::move(trigger_item));
    ++sku_photo_valid[trigger_item.recall_strategy_type];
  }
  for (const auto &kv : sku_photo_valid) {
    context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.sku_photo_valid", std::to_string(kv.first),
            context_data_->ab_group_name);
  }
  for (const auto &kv : sku_photo_miss) {
    context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.sku_photo_miss", std::to_string(kv.first),
            context_data_->ab_group_name);
  }
}


void TriggerMerge::TriggerLocalLifeGoods(const std::vector<TriggerItem*>& goods_trigger_item_list) {
  const auto* ad_index = context_data_->ad_index;
  int64_t goods_success = 0;
  int64_t goods_invert_miss = 0;
  int64_t goods_num = 0;
  int64_t goods_live_invalid = 0;

  const ad_server::inner_loop_index::TagInvertTable* goods_to_live_index = nullptr;
  if (context_data_->use_ad_table_invert) {
    if (!context_data_->spu_or_item_2creative_invert_table) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: spu_or_item_2creative_index_option";
      return;
    }
  } else {
    auto *token_invert_manager = context_data_->sp_bitmap->get_tag_invert_manager();
    goods_to_live_index =
        token_invert_manager ? token_invert_manager->GetTable("spu_or_item_2creative_index_option") : nullptr;
    if (!goods_to_live_index) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: spu_or_item_2creative_index_option";
      return;
    }
  }

  using ks::ad_server::inner_loop_index::TAG_PRI_TYPE;
  using ks::ad_server::inner_loop_index::TAG_ACTION_TYPE;
  ks::ad_server::inner_loop_index::TokenType token;
  token.subtoken_3 =  TAG_ACTION_TYPE::CHART;
  token.subtoken_2 = +TAG_PRI_TYPE::LOCAL_LIFE_GOODS;

  ks::ad_table::deprecated::TokenType token_v2;
  token_v2.subtoken_3 =  ks::ad_table::deprecated::TAG_ACTION_TYPE::CHART;
  token_v2.subtoken_2 = +ks::ad_table::deprecated::TAG_PRI_TYPE::LOCAL_LIFE_GOODS;

  for (auto* query_trigger_item : goods_trigger_item_list) {
    int64_t live_stream_id = 0;
    goods_num++;
    // goods 通过 WTLive 确定直播 id
    if (context_data_->use_ad_table_invert) {
      if (context_data_->spu_or_item_2creative_invert_table) {
        token_v2.subtoken_1 = query_trigger_item->goods_id;
        const auto *invert_list_ptr =
            context_data_->spu_or_item_2creative_invert_table->GetInvertList(token_v2);
        if (!invert_list_ptr) {
          goods_invert_miss++;
          AppendTransFailedTrigger(*query_trigger_item);
          continue;
        }
        for (const auto *vt : *invert_list_ptr) {
          if (vt->base.live_stream_id > 0) {
            live_stream_id = vt->base.live_stream_id;
            break;
          }
        }
      }
    } else {
      if (goods_to_live_index) {
        token.subtoken_1 = query_trigger_item->goods_id;
        const auto *invert_list_ptr = goods_to_live_index->GetInvertList(token);
        if (!invert_list_ptr) {
          goods_invert_miss++;
          AppendTransFailedTrigger(*query_trigger_item);
          continue;
        }
        for (const auto *vt : *invert_list_ptr) {
          if (vt->base.live_stream_id > 0) {
            live_stream_id = vt->base.live_stream_id;
            break;
          }
        }
      }
    }
    if (live_stream_id == 0) {
      goods_invert_miss++;
      AppendTransFailedTrigger(*query_trigger_item);
      continue;
    } else {
      query_trigger_item->live_stream_id = live_stream_id;
    }
    if (query_trigger_item->live_stream_id > 0 &&
      !context_data_->IsLiveStreamValid(query_trigger_item->live_stream_id)) {
      goods_live_invalid++;
      continue;
    }
    goods_success++;
    TriggerItem trigger_item = *query_trigger_item;
    trigger_item.recall_node = GetName();
    context_data_->trigger_data.AppendLive(std::move(trigger_item));
  }
  context_data_->dot_perf->Interval(goods_success, "trigger_merge.goods_success",
                                    context_data_->ab_group_name);
  context_data_->dot_perf->Interval(goods_num, "trigger_merge.goods_num",
                                    context_data_->ab_group_name);
  context_data_->dot_perf->Interval(goods_live_invalid, "trigger_merge.goods_live_invalid",
                                    context_data_->ab_group_name);
  context_data_->dot_perf->Interval(goods_invert_miss, "trigger_merge.goods_invert_miss",
                                    context_data_->ab_group_name);
}

void TriggerMerge::TriggerSkuLive(const std::vector<TriggerItem*>& sku_live_trigger_item_list) {
  const auto* ad_index = context_data_->ad_index;
  std::unordered_map<int32_t, int32_t> sku_live_valid;
  std::unordered_map<int32_t, int32_t> sku_live_creative_miss;
  std::unordered_map<int32_t, int32_t> sku_live_miss;

  const ad_server::inner_loop_index::TagInvertTable* sku_to_live_index = nullptr;
  if (!context_data_->use_ad_table_invert) {
    auto *token_invert_manager = context_data_->sp_bitmap->get_tag_invert_manager();
    sku_to_live_index =
        token_invert_manager ? token_invert_manager->GetTable("spu_or_item_2creative_index_option") : nullptr;
    if (!context_data_->use_ad_table_invert && !sku_to_live_index) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: spu_or_item_2creative_index_option";
    }
  }

  using ks::ad_server::inner_loop_index::TAG_PRI_TYPE;
  using ks::ad_server::inner_loop_index::TAG_ACTION_TYPE;
  ks::ad_server::inner_loop_index::TokenType token;
  std::vector<int32_t> action_type_list = { TAG_ACTION_TYPE::EXPLAIN, TAG_ACTION_TYPE::FUTURE };
  token.subtoken_2 = +TAG_PRI_TYPE::ITEM;

  ks::ad_table::deprecated::TokenType token_v2;
  std::vector<int32_t> action_type_list_v2 = {ks::ad_table::deprecated::TAG_ACTION_TYPE::EXPLAIN,
                                              ks::ad_table::deprecated::TAG_ACTION_TYPE::FUTURE};
  if (context_data_->search_ad_interactive_form == +SearchAdInteractiveForm::GOODS_TAB ||
    context_data_->ad_request->search_info().search_source() ==
        kuaishou::ad::SearchInfo_SearchSource_ACTIVITY_COMMODITY_SEARCH) {
    if (SPDM_enable_chart_live_trigger(context_data_->spdm_ctx)) {
      action_type_list_v2.push_back(ks::ad_table::deprecated::TAG_ACTION_TYPE::CHART);
    }
  }

  token_v2.subtoken_2 = +ks::ad_table::deprecated::TAG_PRI_TYPE::ITEM;

  for (auto* query_trigger_item : sku_live_trigger_item_list) {
    int64_t live_stream_id = 0;
    // sku 通过 WTLive 确定直播 id
    if (context_data_->use_ad_table_invert) {
      if (context_data_->spu_or_item_2creative_invert_table) {
        token_v2.subtoken_1 = query_trigger_item->sku_id;
        for (auto tag_action_type : action_type_list_v2) {
          token_v2.subtoken_3 = tag_action_type;
          const auto *invert_list_ptr =
              context_data_->spu_or_item_2creative_invert_table->GetInvertList(token_v2);
          if (!invert_list_ptr) {
            AppendTransFailedTrigger(*query_trigger_item);
            continue;
          }
          for (const auto *vt : *invert_list_ptr) {
            if (vt->base.live_stream_id > 0) {
              live_stream_id = vt->base.live_stream_id;
              break;
            }
          }
          if (live_stream_id > 0) {
            break;
          }
        }
      }
    } else {
      if (sku_to_live_index) {
        token.subtoken_1 = query_trigger_item->sku_id;
        for (auto tag_action_type : action_type_list) {
          token.subtoken_3 = tag_action_type;
          const auto *invert_list_ptr = sku_to_live_index->GetInvertList(token);
          if (!invert_list_ptr) {
            AppendTransFailedTrigger(*query_trigger_item);
            continue;
          }
          for (const auto *vt : *invert_list_ptr) {
            if (vt->base.live_stream_id > 0) {
              live_stream_id = vt->base.live_stream_id;
              break;
            }
          }
          if (live_stream_id > 0) {
            break;
          }
        }
      }
    }

    if (query_trigger_item->live_stream_id == 0) {
      if (live_stream_id == 0) {
        AppendTransFailedTrigger(*query_trigger_item);
        ++sku_live_miss[query_trigger_item->recall_strategy_type];
        continue;
      }
    }
    if (query_trigger_item->live_stream_id == 0) {
      query_trigger_item->live_stream_id = live_stream_id;
    }
    if (query_trigger_item->live_stream_id > 0 &&
      !context_data_->IsLiveStreamValid(query_trigger_item->live_stream_id)) {
      AppendTransFailedTrigger(*query_trigger_item);
      ++sku_live_creative_miss[query_trigger_item->recall_strategy_type];
      continue;
    }
    TriggerItem trigger_item = *query_trigger_item;
    trigger_item.recall_node = GetName();
    context_data_->trigger_data.AppendSkuLive(std::move(trigger_item));
    ++sku_live_valid[query_trigger_item->recall_strategy_type];
  }
  for (const auto &kv : sku_live_valid) {
    context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.sku_live_valid", std::to_string(kv.first),
            context_data_->ab_group_name);
  }
  for (const auto &kv : sku_live_creative_miss) {
    context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.sku_live_creative_miss", std::to_string(kv.first),
            context_data_->ab_group_name);
  }
  for (const auto &kv : sku_live_miss) {
    context_data_->dot_perf->Interval(kv.second,
            "trigger_merge.sku_live_miss", std::to_string(kv.first),
            context_data_->ab_group_name);
  }
}

void TriggerMerge::TriggerDmp(const std::vector<TriggerItem*>& dmp_trigger_item_list) {
  absl::flat_hash_set<int64_t> dmpids;
  for (auto dmp_trigger : dmp_trigger_item_list) {  // 目前只处理 349 路
    if (dmp_trigger->recall_strategy_type == RetrievalTag::SEARCHAD_SEARCH_KEYWORD_TO_LIVE) {
      dmpids.insert(dmp_trigger->dmp_id);
    }
  }
  const auto shared = AdKconfUtil::targetKeywordConfig();
  const auto &config_data = shared->data();
  std::string config_group_name =
      context_data_->session_context->TryGetString("keyword_recall_config", "base");
  const auto &abtest = config_data.abtest();
  auto iter = abtest.find(config_group_name);
  if (iter == abtest.end()) {
    LOG(ERROR) << "target keyword config error: " << config_group_name;
    return;
  }
  const auto &config = iter->second;
  auto recall_quota = config.search_quota();
  auto enable_unit_filter = config.enable_unit_filter();
  auto unit_keyword_max_size = config.unit_keyword_max_size();

  auto *ad_index = context_data_->ad_index;
  if (!ad_index) {
    LOG_EVERY_N(ERROR, 10000) << "ad_index is null";
    return;
  }

  ad_base::RoaringExpressions exp("keyword_population", ad_base::RoaringSearchOp::And);
  exp.AddExp(ad_server::kTargetTable)
          .AddIntTarget(ad_server::kTargetPopulation, ad_base::RoaringSearchType::IN,
            dmpids.begin(), dmpids.end());
  exp.AddExp(ad_server::kLiveStreamUserInfoTableName)
      .AddIntTargetIn(ad_server::kLiveUserIsAliveField, 1);
  exp.AddExp(ad_server::kAccountTable)
      .AddIntTargetIn(ad_server::kAccountAccountType,
                      std::vector<int64_t>(
                          {kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP,
                            kuaishou::ad::AdEnum_AdDspAccountType_ACCOUNT_ESP_MOBILE}));
  Roaring roaring = context_data_->roaring_table->Select(exp);
  absl::flat_hash_map<int64_t, int> live_id_cnt_map;

  std::vector<uint32_t> vec_token;
  int32_t roaring_size = roaring.cardinality();
  vec_token.resize(roaring_size);
  roaring.toUint32Array(vec_token.data());

  for (uint32_t unit_index : vec_token) {
    int64_t unit_id = ad_index->GetRoaringUnitId(unit_index);
    if (unit_id <= 0) {
      continue;
    }
    auto *unit = ad_target::TableWrapper::GetUnit(unit_id);
    context_data_->dot_perf->Count(1, "unit_null", "PrepareRelevanceTriggerData", "all");
    if (!unit) {
      context_data_->dot_perf->Count(1, "unit_null", "PrepareRelevanceTriggerData", "null");
    }
    if (!unit) {
      continue;
    }
    if (enable_unit_filter) {
      LOG_EVERY_N(INFO, 100000) << "enable keyword unit filter";
      auto *p_target = ad_target::TableWrapper::GetTarget(unit->target_id());
      if (p_target == nullptr) {
        continue;
      }
      const std::vector<int64_t> population = p_target->population();
      int keyword_cnt = 0;
      for (const auto dmpid : population) {
        if ((dmpid >=60000000 && dmpid <= 60500000) || (dmpid >= 85000001 && dmpid < 85480000)) {
          keyword_cnt += 1;
        }
      }
      if (keyword_cnt > unit_keyword_max_size) {
        LOG_EVERY_N(INFO, 100000) << "unit_id: " << unit_id << ", keyword_cnt: "
            << keyword_cnt << ", large than:" << unit_keyword_max_size << ", filter it";
        continue;
      }
    }

    if (!unit->has_unit_support_info_optional()) {
      continue;
    }
    auto* live_stream_user_info = ad_target::TableWrapper::GetLiveStreamUserInfo(unit->live_user_id());
    if (!live_stream_user_info) {
      continue;
    }
    if (!live_stream_user_info->is_live()) {
      continue;
    }
    live_id_cnt_map[live_stream_user_info->live_stream_id()] += 1;
  }

  std::vector<std::pair<int64_t, int>> data_pairs(live_id_cnt_map.begin(), live_id_cnt_map.end());
  std::sort(data_pairs.begin(), data_pairs.end(),
    [](std::pair<int64_t, int>& a, std::pair<int64_t, int>& b) {
      return a.second > b.second;
  });

  std::vector<int64_t> result;
  for (auto& it : data_pairs) {
    result.emplace_back(it.first);
  }

  std::vector<TriggerItem> trigger_live_streams;
  uint32_t count = 0u;
  for (const auto& live_stream_id : result) {
    if (ad_index != nullptr && !context_data_->IsLiveStreamValid(live_stream_id)) {
      ++count;
      continue;
    }
    trigger_live_streams.emplace_back();
    TriggerItem *p_trigger_live = &(trigger_live_streams.back());
    p_trigger_live->live_stream_id = live_stream_id;
    p_trigger_live->relevance_score = 1.0;
    p_trigger_live->sctr = 1.0;
    p_trigger_live->recall_strategy_type = RetrievalTag::SEARCHAD_SEARCH_KEYWORD_TO_LIVE;

    LOG_EVERY_N(INFO, 10000) << "[KeywordLiveRecall]livestream id=" << live_stream_id
                              << ",relevance_score=" << p_trigger_live->relevance_score
                              << ",sctr=" << p_trigger_live->sctr;
  }

  // quota for this node
  size_t ori_size = context_data_->trigger_data.Size();
  size_t live_size_before_quota = trigger_live_streams.size();
  if (trigger_live_streams.size() > recall_quota) {
    trigger_live_streams.resize(recall_quota);
  }

  for (auto& tmp_trigger_item : trigger_live_streams) {
    auto trigger_item = tmp_trigger_item;
    context_data_->trigger_data.AppendLive(std::move(trigger_item));
  }

  const auto& group = context_data_->ab_group_name;
  perf_->Count(count, "query_node.missing_live_cnt", GetName(), group);
  perf_->Interval(dmpids.size(), "query_node.dmpid_list_size", GetName(), group);
  perf_->Interval(live_size_before_quota, "query_node.query_live_list_size_before_quota", GetName(), group);
  perf_->Interval(context_data_->trigger_data.Size() - ori_size, "query_node.query_live_list_size",
                  GetName(), group);
  perf_->Interval(context_data_->trigger_data.Size() - ori_size, "query_node.query_photo_list_real_size",
                  GetName(), group);

  if (context_data_->is_user_target_white_list) {
    LOG(INFO) << "query: " << context_data_->org_query
              << " llsid=" << context_data_->llsid
              << " user_id=" << context_data_->ad_request->ad_user_info().id()
              << " keyword_to_live.return_live_size: " << trigger_live_streams.size();
  }
}

void TriggerMerge::TriggerSpu(const std::vector<TriggerItem*>& spu_trigger_item_list) {
  const ad_server::inner_loop_index::TagInvertTable* spu_to_live = nullptr;
  if (context_data_->use_ad_table_invert) {
    if (!context_data_->spu_or_item_2creative_invert_table) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: spu_or_item_2creative_index_option";
      return;
    }
  } else {
    auto *token_invert_manager = context_data_->sp_bitmap->get_tag_invert_manager();
    if (!token_invert_manager) {
      LOG_EVERY_N(ERROR, 10000) << "token_invert_manager is null";
      return;
    }
    spu_to_live = token_invert_manager->GetTable("spu_or_item_2creative_index_option");
    if (!spu_to_live) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: spu_or_item_2creative_index_option";
      return;
    }
  }

  using ks::ad_server::inner_loop_index::TAG_PRI_TYPE;
  using ks::ad_server::inner_loop_index::TAG_ACTION_TYPE;
  // delete: TAG_ACTION_TYPE::CHART
  std::vector<int32_t> action_type_list = { TAG_ACTION_TYPE::EXPLAIN, TAG_ACTION_TYPE::FUTURE };
  ks::ad_server::inner_loop_index::TokenType token;
  token.subtoken_2 = +TAG_PRI_TYPE::SPU;
  std::vector<TriggerItem> trigger_live_streams;
  uint32_t count = 0u;
  std::unordered_set<int64_t> live_set;

  std::vector<int32_t> action_type_list_v2 = {ks::ad_table::deprecated::TAG_ACTION_TYPE::EXPLAIN,
                                              ks::ad_table::deprecated::TAG_ACTION_TYPE::FUTURE};
  ks::ad_table::deprecated::TokenType token_v2;
  token_v2.subtoken_2 = +ks::ad_table::deprecated::TAG_PRI_TYPE::SPU;

  for (auto spu_trigger : spu_trigger_item_list) {  // 目前只处理 352 路
    if (spu_trigger->recall_strategy_type != RetrievalTag::SEARCHAD_QUERY_TO_SPU_LIVE) {
      continue;
    }
    auto spu_id = spu_trigger->spu_id;
    if (spu_id == 0) {
      continue;
    }
    if (context_data_->use_ad_table_invert) {
      for (auto tag_action_type : action_type_list_v2) {
        token_v2.subtoken_1 = spu_id;
        token_v2.subtoken_3 = tag_action_type;
        const auto *invert_list_ptr =
          context_data_->spu_or_item_2creative_invert_table->GetInvertList(token_v2);
        if (!invert_list_ptr) {
          count++;
          AppendTransFailedTrigger(*spu_trigger);
          continue;
        }
        bool find_live = false;
        for (const auto *vt : *invert_list_ptr) {
          const auto it = live_set.insert(vt->base.live_stream_id);
          if (it.second == false) {
            continue;
          }
          find_live = true;
          TriggerItem live_trigger_item = *spu_trigger;
          trigger_live_streams.emplace_back(live_trigger_item);
          auto& live_stream = trigger_live_streams.back();
          live_stream.live_stream_id = vt->base.live_stream_id;
        }
        if (!find_live) {
          AppendTransFailedTrigger(*spu_trigger);
        }
      }
    } else {
      for (auto tag_action_type : action_type_list) {
        token.subtoken_1 = spu_id;
        token.subtoken_3 = tag_action_type;
        const auto *invert_list_ptr = spu_to_live->GetInvertList(token);
        if (!invert_list_ptr) {
          count++;
          AppendTransFailedTrigger(*spu_trigger);
          continue;
        }
        bool find_live = false;
        for (const auto *vt : *invert_list_ptr) {
          const auto it = live_set.insert(vt->base.live_stream_id);
          if (it.second == false) {
            continue;
          }
          find_live = true;
          TriggerItem live_trigger_item = *spu_trigger;
          trigger_live_streams.emplace_back(live_trigger_item);
          auto& live_stream = trigger_live_streams.back();
          live_stream.live_stream_id = vt->base.live_stream_id;
        }
        if (!find_live) {
          AppendTransFailedTrigger(*spu_trigger);
        }
      }
    }
  }
  int64_t total_quota = 0;
  auto quota_it = context_data_->node_quota_config.find("query_to_spu_live");
  if (quota_it != context_data_->node_quota_config.end()) {
    total_quota = quota_it->second.total_quota();
  }
  if (trigger_live_streams.size()  > total_quota) {
    std::partial_sort(trigger_live_streams.begin(), trigger_live_streams.begin() + total_quota,
                      trigger_live_streams.end(), [](const TriggerItem a, const TriggerItem b) {
                        return a.relevance_score > b.relevance_score;
                      });
    trigger_live_streams.resize(total_quota);
  }
  auto raw_size = context_data_->trigger_data.Size();
  for (auto& trigger_item : trigger_live_streams) {
    auto tmp_trigger_item = trigger_item;
    context_data_->trigger_data.AppendLive(std::move(tmp_trigger_item));
  }
  context_data_->dot_perf->Interval(context_data_->trigger_data.Size() - raw_size,
                                "query_node.query_to_spu_live_list_real_size",
                                GetName(),
                                context_data_->ab_group_name);
  auto *perf_ = context_data_->dot_perf;
  if (perf_ == nullptr) {
    return;
  }
  const auto& group = context_data_->ab_group_name;
  perf_->Count(count, "query_node.missing_live_cnt", GetName(), group);
  perf_->Interval(context_data_->trigger_data.Size() - raw_size,
                                    "query_node.query_live_list_real_size", GetName(),
                                    group);
  perf_->Interval(trigger_live_streams.size(), "query_node.query_live_list_size", GetName(),
                  group);
  LOG_IF(INFO, context_data_->is_user_target_white_list)
      << "query: " << context_data_->org_query
      << " user_id=" << context_data_->ad_request->ad_user_info().id()
      << " llsid=" << context_data_->llsid
      << " query_to_spu_live.return_live_size: " << context_data_->trigger_data.Size() - raw_size;
}

void TriggerMerge::TriggerSerial(const std::vector<TriggerItem*>& serial_trigger_item_list) {
  const ad_server::inner_loop_index::TagInvertTable* series_id_to_creative = nullptr;
  if (context_data_->use_ad_table_invert) {
    if (!context_data_->series_id_invert_table) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: series_id_to_creative";
      return;
    }
  } else {
    auto* token_invert_manager = context_data_->sp_bitmap->get_tag_invert_manager();
    if (!token_invert_manager) {
      LOG_EVERY_N(ERROR, 10000) << "token_invert_manager is null";
      return;
    }
    series_id_to_creative = token_invert_manager->GetTable("series_id_index");
    if (!series_id_to_creative) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: series_id_to_creative";
      return;
    }
  }

  ks::ad_server::inner_loop_index::TokenType token;
  std::vector<TriggerItem> series_id_creatives;
  uint32_t count = 0u;

  std::unordered_set<int64_t> creative_id_set;

  for (auto series_trigger : serial_trigger_item_list) {
    if (series_trigger->recall_strategy_type != RetrievalTag::SEARCHAD_INNER_SERIES_RETR) {
      continue;
    }
    auto series_id = series_trigger->series_id;
    if (series_id == 0) {
      continue;
    }
    bool find_creative = false;
    if (context_data_->use_ad_table_invert) {
      const auto* invert_list_ptr = context_data_->series_id_invert_table->GetInvertList1Token(series_id);
      if (!invert_list_ptr) {
        count++;
        AppendTransFailedTrigger(*series_trigger);
        continue;
      }
      for (const auto* vt : *invert_list_ptr) {
        const auto it = creative_id_set.insert(vt->base.creative_id);
        if (it.second == false) {
          continue;
        }
        find_creative = true;
        TriggerItem series_trigger_item = *series_trigger;
        series_id_creatives.emplace_back(series_trigger_item);
        auto& series_item = series_id_creatives.back();
        series_item.creative_id = vt->base.creative_id;
      }
    } else {
      token.subtoken_1 = series_id;
      const auto* invert_list_ptr = series_id_to_creative->GetInvertList(token);
      if (!invert_list_ptr) {
        count++;
        AppendTransFailedTrigger(*series_trigger);
        continue;
      }
      for (const auto* vt : *invert_list_ptr) {
        const auto it = creative_id_set.insert(vt->base.creative_id);
        if (it.second == false) {
          continue;
        }
        find_creative = true;
        TriggerItem series_trigger_item = *series_trigger;
        series_id_creatives.emplace_back(series_trigger_item);
        auto& series_item = series_id_creatives.back();
        series_item.creative_id = vt->base.creative_id;
      }
    }
    if (!find_creative) {
      AppendTransFailedTrigger(*series_trigger);
    }
  }

  int64_t total_quota = 0;
  auto quota_it = context_data_->node_quota_config.find("series_to_creative");
  if (quota_it != context_data_->node_quota_config.end()) {
    total_quota = quota_it->second.total_quota();
  }
  if (series_id_creatives.size() > total_quota) {
    if (SPDM_enable_series_diversity(context_data_->spdm_ctx)) {
      std::unordered_map<int64_t, int32_t> series_cnt;
      for (auto& item : series_id_creatives) {
        item.rank_score = 1.0f + 1.0f / (++series_cnt[item.series_id] + 1);
      }
      std::partial_sort(series_id_creatives.begin(), series_id_creatives.begin() + total_quota,
                        series_id_creatives.end(),
                        [](const TriggerItem a, const TriggerItem b) { return a.rank_score > b.rank_score; });
    } else {
      std::partial_sort(
          series_id_creatives.begin(), series_id_creatives.begin() + total_quota, series_id_creatives.end(),
          [](const TriggerItem a, const TriggerItem b) { return a.creative_id > b.creative_id; });
    }
    series_id_creatives.resize(total_quota);
  }
  auto raw_size = context_data_->trigger_data.Size();
  for (auto& trigger_item : series_id_creatives) {
    auto tmp_trigger_item = trigger_item;
    context_data_->trigger_data.AppendCreative(std::move(tmp_trigger_item));
  }

  auto* perf_ = context_data_->dot_perf;
  if (perf_ == nullptr) {
    return;
  }
  const auto& group = context_data_->ab_group_name;
  perf_->Count(count, "query_node.missing_series_cnt", GetName(), group);
  perf_->Interval(context_data_->trigger_data.Size() - raw_size,
                  "query_node.series_to_creative_list_real_size", GetName(), group);
  perf_->Interval(series_id_creatives.size(), "query_node.series_to_creative_list_size", GetName(), group);
  LOG_IF(INFO, context_data_->is_user_target_white_list)
      << "query: " << context_data_->org_query
      << " user_id=" << context_data_->ad_request->ad_user_info().id() << " llsid=" << context_data_->llsid
      << " series_to_creative.return_live_size: " << context_data_->trigger_data.Size() - raw_size;
}

void TriggerMerge::TriggerDpa(const std::vector<TriggerItem*>& dpa_trigger_item_list) {
  if (!context_data_->enable_dpa_form_strong_card_ ||
      !context_data_->is_combo_search_first_page) {
    return;
  }
  if (!context_data_->use_ad_table_invert && !context_data_->dpa_product_id_invert_table) {
    LOG_EVERY_N(ERROR, 10000) << "GetTable failed: dpa_product_id_invert_table";
    return;
  }
  if (context_data_->use_ad_table_invert && !context_data_->dpa_product_id_invert_table_v2) {
    LOG_EVERY_N(ERROR, 10000) << "GetTable failed: dpa_product_id_invert_table_v2";
    return;
  }

  ks::ad_server::inner_loop_index::TokenType token;
  std::vector<TriggerItem> dpa_id_creatives;
  uint32_t miss_count = 0u;
  std::unordered_set<int64_t> distinct_photo_id;
  for (auto* p_dpa_trigger : dpa_trigger_item_list) {
    if (p_dpa_trigger == nullptr) { continue; }
    const TriggerItem& dpa_trigger = *p_dpa_trigger;
    if (dpa_trigger.recall_strategy_type != RetrievalTag::SEARCHAD_FORM_SUBMIT_DPA_ID_RETR) {
      continue;
    }
    auto dpa_id = dpa_trigger.dpa_id;
    bool find_creative = false;
    if (dpa_id == 0) {
      continue;
    }
    if (context_data_->use_ad_table_invert) {
      const auto* invert_list_ptr =
          context_data_->dpa_product_id_invert_table_v2->GetInvertList1Token(dpa_id);
      if (!invert_list_ptr) {
        miss_count++;
        AppendTransFailedTrigger(dpa_trigger);
        continue;
      }
      for (const auto* vt : *invert_list_ptr) {
        if (!vt || vt->base.photo_id <= 0) {
          continue;
        }
        const auto it = distinct_photo_id.insert(vt->base.photo_id);
        if (!it.second) {
          continue;
        }
        find_creative = true;
        dpa_id_creatives.emplace_back(dpa_trigger);
        auto& dpa_trigger_item = dpa_id_creatives.back();
        dpa_trigger_item.recall_node = GetName();
        dpa_trigger_item.creative_id = vt->base.creative_id;
        dpa_trigger_item.photo_id = vt->base.photo_id;
      }
    } else {
      token.subtoken_1 = dpa_id;
      const auto* invert_list_ptr = context_data_->dpa_product_id_invert_table->GetInvertList(token);
      if (!invert_list_ptr) {
        miss_count++;
        AppendTransFailedTrigger(dpa_trigger);
        continue;
      }
      for (const auto* vt : *invert_list_ptr) {
        if (!vt || vt->base.photo_id <= 0) {
          continue;
        }
        const auto it = distinct_photo_id.insert(vt->base.photo_id);
        if (!it.second) {
          continue;
        }
        find_creative = true;
        dpa_id_creatives.emplace_back(dpa_trigger);
        auto& dpa_trigger_item = dpa_id_creatives.back();
        dpa_trigger_item.recall_node = GetName();
        dpa_trigger_item.creative_id = vt->base.creative_id;
        dpa_trigger_item.photo_id = vt->base.photo_id;
      }
    }
    if (!find_creative) {
      AppendTransFailedTrigger(dpa_trigger);
    }
  }

  context_data_->dot_perf->Count(miss_count, "query_node.missing_dpa_cnt", GetName(),
                                 context_data_->ab_group_name);
  context_data_->dot_perf->Interval(dpa_id_creatives.size(), "query_node.dpa_to_creative_list_size",
                                    GetName(), context_data_->ab_group_name);

  int64_t total_quota = AdKconfUtil::dpaToCreativeTriggerItemQuota();

  if (dpa_id_creatives.size() > total_quota) {
    std::partial_sort(dpa_id_creatives.begin(), dpa_id_creatives.begin() + total_quota,
                      dpa_id_creatives.end(),
                      [](const TriggerItem a, const TriggerItem b) { return a.creative_id > b.creative_id; });
    dpa_id_creatives.resize(total_quota);
  }

  auto raw_size = context_data_->trigger_data.Size();
  for (auto& trigger_item : dpa_id_creatives) {
    context_data_->trigger_data.AppendCreative(std::move(trigger_item));
  }

  context_data_->dot_perf->Interval(context_data_->trigger_data.Size() - raw_size,
                                    "query_node.dpa_to_creative_list_real_size", GetName(),
                                    context_data_->ab_group_name);
  LOG_IF(INFO, context_data_->is_user_target_white_list)
      << "query: " << context_data_->org_query
      << " user_id=" << context_data_->ad_request->ad_user_info().id() << " llsid=" << context_data_->llsid
      << " dpa_to_creative.return_size: " << context_data_->trigger_data.Size() - raw_size;
}

void TriggerMerge::TriggerKwaiBook(const std::vector<TriggerItem*>& kwai_book_trigger_item_list) {
  if (!context_data_->use_ad_table_invert && !context_data_->kwai_book_id_invert_table) {
    LOG_EVERY_N(ERROR, 10000) << "GetTable failed: kwai_book_id_invert_table";
    return;
  }
  if (context_data_->use_ad_table_invert && !context_data_->kwai_book_id_invert_table_v2) {
    LOG_EVERY_N(ERROR, 10000) << "GetTable failed: kwai_book_id_invert_table_v2";
    return;
  }

  ks::ad_server::inner_loop_index::TokenType token;
  std::vector<TriggerItem> kwai_book_id_creatives;
  uint32_t miss_count = 0u;
  std::unordered_set<int64_t> distinct_photo_id;
  for (auto* p_kwai_book_trigger : kwai_book_trigger_item_list) {
    if (p_kwai_book_trigger == nullptr) { continue; }
    const TriggerItem& kwai_book_trigger = *p_kwai_book_trigger;
    auto kwai_book_id = kwai_book_trigger.book_id;
    if (kwai_book_id == 0) {
      continue;
    }
    bool find_creative = false;
    if (context_data_->use_ad_table_invert) {
      const auto* invert_list_ptr =
        context_data_->kwai_book_id_invert_table_v2->GetInvertList1Token(kwai_book_id);
      if (!invert_list_ptr) {
        miss_count++;
        AppendTransFailedTrigger(kwai_book_trigger);
        continue;
      }
      for (const auto* vt : *invert_list_ptr) {
        if (!vt || vt->base.photo_id <= 0) {
          continue;
        }
        const auto it = distinct_photo_id.insert(vt->base.photo_id);
        if (!it.second) {
          continue;
        }
        find_creative = true;
        kwai_book_id_creatives.emplace_back(kwai_book_trigger);
        auto& kwai_book_trigger_item = kwai_book_id_creatives.back();
        kwai_book_trigger_item.recall_node = GetName();
        kwai_book_trigger_item.creative_id = vt->base.creative_id;
        kwai_book_trigger_item.photo_id = vt->base.photo_id;
      }
    } else {
      token.subtoken_1 = kwai_book_id;
      const auto* invert_list_ptr = context_data_->kwai_book_id_invert_table->GetInvertList(token);
      if (!invert_list_ptr) {
        miss_count++;
        AppendTransFailedTrigger(kwai_book_trigger);
        continue;
      }
      for (const auto* vt : *invert_list_ptr) {
        if (!vt || vt->base.photo_id <= 0) {
          continue;
        }
        const auto it = distinct_photo_id.insert(vt->base.photo_id);
        if (!it.second) {
          continue;
        }
        find_creative = true;
        kwai_book_id_creatives.emplace_back(kwai_book_trigger);
        auto& kwai_book_trigger_item = kwai_book_id_creatives.back();
        kwai_book_trigger_item.recall_node = GetName();
        kwai_book_trigger_item.creative_id = vt->base.creative_id;
        kwai_book_trigger_item.photo_id = vt->base.photo_id;
      }
    }
    if (!find_creative) {
      AppendTransFailedTrigger(kwai_book_trigger);
    }
  }

  context_data_->dot_perf->Count(miss_count, "query_node.missing_kwai_book_cnt", GetName(),
                                 context_data_->ab_group_name);
  context_data_->dot_perf->Interval(kwai_book_id_creatives.size(),
                                    "query_node.kwai_book_to_creative_list_size",
                                    GetName(), context_data_->ab_group_name);

  int64_t total_quota = AdKconfUtil::kwaiBookToCreativeTriggerItemQuota();

  if (kwai_book_id_creatives.size() > total_quota) {
    std::partial_sort(kwai_book_id_creatives.begin(), kwai_book_id_creatives.begin() + total_quota,
                      kwai_book_id_creatives.end(),
                      [](const TriggerItem a, const TriggerItem b) { return a.creative_id > b.creative_id; });
    kwai_book_id_creatives.resize(total_quota);
  }

  auto raw_size = context_data_->trigger_data.Size();
  for (auto& trigger_item : kwai_book_id_creatives) {
    context_data_->trigger_data.AppendCreative(std::move(trigger_item));
  }

  context_data_->dot_perf->Interval(context_data_->trigger_data.Size() - raw_size,
                                    "query_node.kwai_book_to_creative_list_real_size", GetName(),
                                    context_data_->ab_group_name);
  LOG_IF(INFO, context_data_->is_user_target_white_list)
      << "query: " << context_data_->org_query
      << " user_id=" << context_data_->ad_request->ad_user_info().id() << " llsid=" << context_data_->llsid
      << " kwai_book_to_creative.return_size: " << context_data_->trigger_data.Size() - raw_size;
}

void TriggerMerge::AppendTransFailedTrigger(const TriggerItem& trigger_item) {
  auto item = trigger_item;
  item.SetInvalid(kuaishou::log::ad::AdTraceFilterCondition::SEARCH_TRIGGER_TRANS_FAILED_FILTER,
                      kuaishou::ad::AdTriggerNodeType::TARGET_MERGE_TYPE);
  context_data_->trigger_data.AppendItem(std::move(item));
}

void TriggerMerge::TriggerSkuPhotoFromPhoto(const QueryPhoto& query_photo) {
  const ad_server::inner_loop_index::TagInvertTable* photo_id_index = nullptr;
  if (context_data_->use_ad_table_invert) {
    if (!context_data_->photo_id_invert_table) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: photo_id_index";
      return;
    }
  } else {
    auto* token_invert_manager = context_data_->sp_bitmap->get_tag_invert_manager();
    if (!token_invert_manager) {
      LOG_EVERY_N(ERROR, 10000) << "token_invert_manager is null";
      return;
    }
    photo_id_index = token_invert_manager->GetTable("photo_id_index");
    if (!photo_id_index) {
      LOG_EVERY_N(ERROR, 10000) << "GetTable failed: photo_id_index";
      return;
    }
  }
  int64_t item_id = 0;
  // 每个 photo_id 获取一个 item_id; 同时将每个 photo_id 能获取到的 item_id 个数记录下来
  absl::flat_hash_set<int64_t> item_ids;
  int64_t photo_id = query_photo.photo_id;
  if (context_data_->use_ad_table_invert) {
    const auto* invert_list_ptr = context_data_->photo_id_invert_table->GetInvertList1Token(photo_id);
    if (!invert_list_ptr || invert_list_ptr->empty()) {
      LOG_EVERY_N(WARNING, 10000) << "photo_id_index miss or empty, id: " << photo_id;
      return;
    }
    for (const auto* vt : *invert_list_ptr) {
      if (vt != nullptr && vt->base.item_id > 0 &&
          vt->base.shop_item_type == kuaishou::ad::AdEnum::MERCHANT_PUT_PRODUCT) {
        item_id = vt->base.item_id;
        item_ids.insert(item_id);
      }
    }
  } else {
    ks::ad_server::inner_loop_index::TokenType token;
    token.subtoken_1 = photo_id;
    const auto* invert_list_ptr = photo_id_index->GetInvertList(token);
    if (!invert_list_ptr || invert_list_ptr->empty()) {
      LOG_EVERY_N(WARNING, 10000) << "photo_id_index miss or empty, id: " << photo_id;
      return;
    }
    for (const auto* vt : *invert_list_ptr) {
      if (vt != nullptr && vt->base.item_id > 0 &&
          vt->base.shop_item_type == kuaishou::ad::AdEnum::MERCHANT_PUT_PRODUCT) {
        item_id = vt->base.item_id;
        item_ids.insert(item_id);
      }
    }
  }
  perf_->Interval(item_ids.size(), "goods_tab_align_search", "item_ids.size");
  LOG_EVERY_N(INFO, 10000) << "photo_id: " << photo_id << " item_id: " << item_id
                           << " item_ids.size: " << item_ids.size();
  if (item_id > 0) {
    TriggerItem* trigger_item = context_data_->trigger_data.AppendSkuPhoto();
    if (!trigger_item) {
      return;
    }
    trigger_item->ConvertFromQueryPhoto(query_photo);
    trigger_item->sku_id = item_id;
    perf_->Count(1, "goods_tab_align_search", "TriggerSkuPhotoFromPhoto");
  }
}

void TriggerMerge::TriggerSkuLiveFromLive(const TriggerItem& trigger_item) {
  auto live_stream_id = trigger_item.live_stream_id;
  int64_t live_user_id = LiveStreamIndex::GetInstance()->GetNewAuthorId(live_stream_id);
  const auto* p_wt_live = ad_target::TableWrapper::GetWTLive(live_user_id);
  if (!p_wt_live) {
    LOG_EVERY_N(WARNING, 100000) << "WTLive miss, live stream id: " << live_stream_id;
    return;
  }
  auto item_size = 0;
  auto max_live_item_size = AdKconfUtil::liveSkuItemSize();
  auto add_sku_live = [&](int64_t item_id) {
    TriggerItem trigger_item_copy = trigger_item;
    trigger_item_copy.sku_id = item_id;
    context_data_->trigger_data.AppendSkuLive(std::move(trigger_item_copy));
    item_size++;
  };
  // 讲解 item
  const auto& explain_items = p_wt_live->explain_items();
  // 只取第一个 item_id
  if (explain_items.size() > 0) {
    add_sku_live(explain_items.at(0));
  }
  // 小黄车 item
  const auto& chart_items = p_wt_live->chart_items();
  for (auto& item_id : chart_items) {
    if (item_size >= max_live_item_size) {
      break;
    }
    add_sku_live(item_id);
  }
  perf_->Interval(item_size, "goods_tab_align_search", "item_size");
  if (item_size > 0) {
    perf_->Count(1, "goods_tab_align_search", "TriggerSkuLiveFromLive");
  }
}

}  // namespace ad_target_search
}  // namespace ks
