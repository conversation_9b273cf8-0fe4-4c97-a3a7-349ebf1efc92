#include "teams/ad/ad_target_search/engine/strategy/relevance/item_relevance_strategy.h"

#include <set>

#include "base/common/logging.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"

#include "teams/ad/ad_target_search/trace/trace_manager.h"
#include "teams/ad/ad_target_search/util/spdm/spdm_switches.h"
#include "teams/ad/ad_target_search/engine/context_data/context_data.h"
#include "teams/ad/ad_target_search/engine/strategy/enums.h"
#include "teams/ad/ad_target_search/engine/strategy/strategy.h"
#include "teams/ad/ad_target_search/util/cache_loader/photo_merchant_category.h"
#include "teams/ad/engine_base/search/cache_loader/live_stream_index.h"
#include "teams/ad/ad_target_search/engine/node/utility/tokenization.h"
#include "teams/ad/engine_base/search/util/table_extension/table_wrapper.h"
#include "teams/ad/ad_table/code_generator/ad_target_search/WTLiveProduct.h"

namespace ks {
namespace ad_target_search {

using BertInputMode = kuaishou::ad::algorithm::BertInputMode;
using SearchRelevanceInput = kuaishou::ad::search_ads::SearchRelevanceInput;
using BatchRedisResponse = ks::infra::RedisResponse<std::vector<std::string>>;

void ItemRelevanceStrategy::Prepare() {
  RelevanceStrategyBase::Prepare();
  return;
}

void ItemRelevanceStrategy::DistinctPhoto(LogicCommonList<TriggerItem>* trigger_item_list) {
  return;
}

void ItemRelevanceStrategy::DistinctLive(LogicCommonList<TriggerItem>* trigger_item_list) {
  return;
}

void ItemRelevanceStrategy::GetSkuFeatureFromRedis(
    std::unordered_map<int64_t, SkuRelevanceItem>* sku_relevance_items,
    const ReleConfItem& rele_conf) {
  auto *redis = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adSearchPhotoTextOffline",
                ks::engine_base::DependDataLevel::STRONG_DEPEND);
  if (!redis) {
    return;
  }

  std::vector<int64_t> sku_id_vec;
  std::vector<std::string> keys;
  keys.reserve(sku_relevance_items->size());
  sku_id_vec.reserve(sku_relevance_items->size());
  for (const auto &kv : *sku_relevance_items) {
    if (SPDM_enable_live_sku_cache_search(context_data_->spdm_ctx) &&
        kv.second.score_type == kuaishou::ad::AdTargetTransparentInfo::OFFLINE) {
      continue;
    }
    sku_id_vec.push_back(kv.first);
    const auto &key = rele_conf.feature_reids_prefix() + "_" + std::to_string(kv.first);
    keys.push_back(key);
  }
  std::vector<std::string> vals;
  const auto err_code = redis->MGet(keys, &vals);
  const auto err_name = ks::infra::err2str(err_code);
  if (vals.size() != keys.size()) {
    LOG(ERROR) << "Failed to MGet redis score, error: " << err_name
        << ", key size: " << keys.size()
        << ", value size: " << vals.size();
    context_data_->dot_perf->Count(keys.size(), "relevance_feature_redis",
        "no_response", context_data_->ab_group_name);
    return;
  }

  uint32_t index = 0;
  uint32_t miss_count_live = 0;
  uint32_t miss_count_photo = 0;
  uint32_t hit_count_live = 0;
  uint32_t hit_count_photo = 0;
  uint32_t parse_err_count = 0;
  for (auto &val : vals) {
    int64_t sku_id = sku_id_vec[index++];
    if (val.empty()) {
      if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Live) {
        miss_count_live++;
      } else if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Photo) {
        miss_count_photo++;
      }
      continue;
    }
    if (!(*sku_relevance_items)[sku_id].ori_feature.ParseFromString(val)) {
      (*sku_relevance_items)[sku_id].ori_feature.Clear();
      parse_err_count++;
      continue;
    }
    if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Live) {
      hit_count_live++;
    } else if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Photo) {
      hit_count_photo++;
    }
    (*sku_relevance_items)[sku_id].ready = true;
  }
  context_data_->dot_perf->Interval(hit_count_live, "relevance_feature_redis_new",
                                    "hit_live", context_data_->ab_group_name);
  context_data_->dot_perf->Interval(hit_count_photo, "relevance_feature_redis_new",
                                    "hit_photo", context_data_->ab_group_name);
  context_data_->dot_perf->Interval(miss_count_live, "relevance_feature_redis_new",
                                    "miss_live", context_data_->ab_group_name);
  context_data_->dot_perf->Interval(miss_count_photo, "relevance_feature_redis_new",
                                    "miss_photo", context_data_->ab_group_name);
  context_data_->dot_perf->Count(parse_err_count,
      "relevance_feature_redis", "parse_err", context_data_->ab_group_name);
}

void ItemRelevanceStrategy::GetSkuFeatureFromRedisSplit(
    std::unordered_map<int64_t, SkuRelevanceItem>* sku_relevance_items,
    const ReleConfItem& rele_conf) {
  auto *redis = ks::ad_base::KconfRedis::Instance().GetAdRedisPipelineClient("adSearchPhotoTextOffline",
                ks::engine_base::DependDataLevel::STRONG_DEPEND);
  if (!redis) {
    return;
  }

  int32_t batch_max_size = AdKconfUtil::relevanceSplitMaxCount();

  std::vector<std::pair<std::vector<int64_t>, BatchRedisResponse>> redis_requests;

  {
    std::vector<int64_t> sku_id_vec;
    std::vector<std::string> keys;
    for (const auto &[sku_id, relevance_item] : *sku_relevance_items) {
      const auto &key = rele_conf.feature_reids_prefix() + "_" + std::to_string(sku_id);
      sku_id_vec.push_back(sku_id);
      keys.push_back(key);

      if (keys.size() >= batch_max_size) {
        redis_requests.emplace_back(std::move(sku_id_vec), redis->MGet(keys));
        keys.clear();
      }
    }
    if (!keys.empty()) {
      redis_requests.emplace_back(std::move(sku_id_vec), redis->MGet(keys));
      keys.clear();
    }
  }

  uint32_t miss_count_live = 0;
  uint32_t miss_count_photo = 0;
  uint32_t hit_count_live = 0;
  uint32_t hit_count_photo = 0;
  uint32_t parse_err_count = 0;

  std::vector<std::string> vals;
  for (auto& [sku_id_vec, redis_resp] : redis_requests) {
    vals.clear();
    auto err_code = redis_resp.Get(&vals);
    const auto err_name = ks::infra::err2str(err_code);
    if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR || vals.size() != sku_id_vec.size()) {
      LOG(ERROR) << "Failed to MGet redis score, error: " << err_name
                 << ", sku_id_vec size: " << sku_id_vec.size() << ", value size: " << vals.size();
      context_data_->dot_perf->Count(sku_id_vec.size(), "relevance_feature_redis.split", "no_response",
                                     context_data_->ab_group_name);
      continue;
    }

    uint32_t index = 0;
    for (auto &val : vals) {
      int64_t sku_id = sku_id_vec[index++];
      if (val.empty()) {
        if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Live) {
          miss_count_live++;
        } else if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Photo) {
          miss_count_photo++;
        }
        continue;
      }
      if (!(*sku_relevance_items)[sku_id].ori_feature.ParseFromString(val)) {
        (*sku_relevance_items)[sku_id].ori_feature.Clear();
        parse_err_count++;
        continue;
      }
      if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Live) {
        hit_count_live++;
      } else if ((*sku_relevance_items)[sku_id].type == SkuRelevanceItem::ReleTriggerType::Photo) {
        hit_count_photo++;
      }
      (*sku_relevance_items)[sku_id].ready = true;
    }
  }

  context_data_->dot_perf->Interval(hit_count_live, "relevance_feature_redis_new",
                                    "hit_live", context_data_->ab_group_name);
  context_data_->dot_perf->Interval(hit_count_photo, "relevance_feature_redis_new",
                                    "hit_photo", context_data_->ab_group_name);
  context_data_->dot_perf->Interval(miss_count_live, "relevance_feature_redis_new",
                                    "miss_live", context_data_->ab_group_name);
  context_data_->dot_perf->Interval(miss_count_photo, "relevance_feature_redis_new",
                                    "miss_photo", context_data_->ab_group_name);
  context_data_->dot_perf->Count(parse_err_count,
      "relevance_feature_redis", "parse_err", context_data_->ab_group_name);
}

void ItemRelevanceStrategy::BuildModelRequest(
    std::vector<PredictorItem>* predictor_items,
    const ReleConfItem& rele_conf,
    const std::unordered_map<int64_t, SkuRelevanceItem>& sku_relevance_items,
    const std::string& call_tag) {
  std::vector<int32_t> input_ids;
  std::vector<int32_t> input_mask;
  std::vector<int32_t> segment_ids;
  Tokenize(&input_ids, &input_mask, &segment_ids, rele_conf.input_pattern(), rele_conf);

  uint32_t batch_size = 0;
  for (auto &kv : sku_relevance_items) {
    auto &rele_item = kv.second;
    if (!rele_item.ready) {
      context_data_->dot_perf->Count(1, "relevace_evaluator_live_count", "model_request_continue");
      continue;
    }
    if (batch_size == 0 || batch_size >= rele_conf.batch_size()) {
      if (predictor_items->size() >= rele_conf.model_limit()) {
        int32_t exceed_num = sku_relevance_items.size() - rele_conf.model_limit() * rele_conf.batch_size();
        context_data_->dot_perf->Interval(exceed_num,
         "sku_relevance_request_exceed_number", context_data_->ab_group_name);
        break;
      }
      predictor_items->emplace_back();
      auto &pred_item = predictor_items->back();
      pred_item.tag = call_tag + std::to_string(predictor_items->size());
      pred_item.request.set_llsid(context_data_->llsid);
      pred_item.request.set_user_id(
          context_data_->ad_request->ad_user_info().id());
      pred_item.request.add_cmd(rele_conf.cmd());
      pred_item.request.set_item_type(
          kuaishou::ad::algorithm::ItemType::AD_DSP);
      pred_item.request.set_predict_type(
          ::kuaishou::ad::algorithm::PredictType::MULTIMODAL_RELEVANCE);
      pred_item.request.set_skip_adlog_put(true);
      auto idx = predictor_items->size() - 1;
      pred_item.callback = [this, idx, predictor_items](const ModelResponse &res, int status) {
        auto &item = (*predictor_items)[idx];
        item.response.CopyFrom(res);
        item.status = status;
      };
      pred_item.ids.reserve(rele_conf.batch_size());
      batch_size = 0;
    }
    auto &pred_item = predictor_items->back();
    pred_item.ids.push_back(rele_item.sku_id);
    pred_item.request.mutable_item_id()->Add(++batch_size);

    auto *bert_input = pred_item.request.mutable_bert_input()->Add();
    bert_input->set_photo_id(rele_item.sku_id);
    bert_input->set_mode(BertInputMode::SKU);
    *bert_input->mutable_image() = std::move(rele_item.ori_feature.input_image());
    bert_input->mutable_input_ids()->Add(rele_item.ori_feature.input_token().begin(),
                                         rele_item.ori_feature.input_token().end());
    bert_input->mutable_input_ids()->Add(input_ids.begin(), input_ids.end());
    bert_input->mutable_input_mask()->Add(rele_item.ori_feature.input_mask().begin(),
                                          rele_item.ori_feature.input_mask().end());
    bert_input->mutable_input_mask()->Add(input_mask.begin(), input_mask.end());
    bert_input->mutable_segment_ids()->Add(rele_item.ori_feature.input_type().begin(),
                                           rele_item.ori_feature.input_type().end());
    bert_input->mutable_segment_ids()->Add(segment_ids.begin(), segment_ids.end());
  }
  context_data_->dot_perf->Interval(predictor_items->size(),
      "sku_relevance_request_size",
      context_data_->ab_group_name);
  context_data_->dot_perf->Interval(sku_relevance_items.size(),
      "sku_relevance_items_size",
      context_data_->ab_group_name);
}

void ItemRelevanceStrategy::Tokenize(std::vector<int32_t> *input_ids,
                                     std::vector<int32_t> *input_mask,
                                     std::vector<int32_t> *segment_ids,
                                    const int32_t pattern,
                                    const ReleConfItem& rele_conf) {
  if (!input_ids || !input_mask || !segment_ids) {
    return;
  }

  int token_size = rele_conf.token_size();
  std::vector<std::pair<int32_t, const kuaishou::ad::QueryToken *>> ori_tokens;
  std::string query = context_data_->norm_lower_query;
  std::vector<const kuaishou::ad::QueryToken *> query_tokens;

  /**
   * extend tokens.
   */
  for (const auto &token : semantics_) {
    query.append(token.value());
    query_tokens.push_back(&token);
  }

  for (const auto &token : context_data_->ad_request->query_token()) {
    auto offset = query.find(token.value());
    if (offset == std::string::npos) {
      offset = 9999999;
    }
    ori_tokens.emplace_back(offset, &token);
  }
  std::sort(ori_tokens.begin(), ori_tokens.end(),
            [](const std::pair<int32_t, const kuaishou::ad::QueryToken *> &l,
               const std::pair<int32_t, const kuaishou::ad::QueryToken *> &r) {
              return l.first < r.first;
            });
  for (const auto & pair : ori_tokens) {
    query_tokens.push_back(pair.second);
  }

  static auto *tokenizer = tokenization::SimpleTokenizer::GetInstance();
  std::vector<std::string> tokens;
  tokens.reserve(token_size);
  tokenizer->Tokenize(query_tokens, &tokens, token_size - 1);
  input_ids->reserve(token_size);
  input_mask->reserve(token_size);
  segment_ids->reserve(token_size);
  switch (pattern) {
    case 0:
      /*
      query: [token] [SEP] [PAD]
      */
      tokens.push_back("[SEP]");
      for (uint32_t i = 0; i < token_size; i++) {
        if (i < tokens.size()) {
          input_ids->push_back(tokenizer->ConvertTokensToId(tokens[i]));
          input_mask->push_back(1);
          segment_ids->push_back(1);
        } else {
          input_ids->push_back(tokenizer->ConvertTokensToId("[PAD]"));
          input_mask->push_back(0);
          segment_ids->push_back(0);
        }
      }
      break;
    case 1:
      /*
      query: [token] [PAD] [SEP]
      */
      for (uint32_t i = 0; i < token_size - 1; i++) {
        if (i < tokens.size()) {
          input_ids->push_back(tokenizer->ConvertTokensToId(tokens[i]));
        } else {
          input_ids->push_back(tokenizer->ConvertTokensToId("[PAD]"));
        }
        input_mask->push_back(1);
        segment_ids->push_back(1);
      }
      input_ids->push_back(tokenizer->ConvertTokensToId("[SEP]"));
      input_mask->push_back(1);
      segment_ids->push_back(1);
      break;
    default:
      break;
  }
  const auto relevance_debug_query_list = AdKconfUtil::relevanceDebugQueryList();
  if (relevance_debug_query_list->count(context_data_->norm_lower_query)) {
    std::string query_token_str = "";
    for (const auto &token : query_tokens) {
      query_token_str += token->value() + "_";
    }
    std::string query_id_str = "";
    const auto& ids = *input_ids;
    for (const auto &id : ids) {
      query_id_str += std::to_string(id) + "_";
    }
    context_data_->dot_perf->Interval(100.0,
                                  "debug_query_token_ids",
                                  context_data_->norm_lower_query,
                                  query,
                                  query_token_str,
                                  query_id_str);
  }
}

void ItemRelevanceStrategy::CalculatePosteriorScore(LogicCommonList<TriggerItem>* trigger_item_list,
                                                    const ReleConfItem& rele_conf_,
                                                    const ReleItemThreshRatio& rele_thresh_ratio_) {
  double qr_ratio = context_data_->session_context->TryGetDouble("qr_item_relevance_thresh_ratio", 1.0);
  double goods_tab_bigv_ratio = context_data_->session_context->TryGetDouble("goods_tab_bigv_ratio", 1.0);
  double thresh_score = rele_conf_.rele_score_thres();
  const auto& search_source = context_data_->ad_request->search_info().search_source();
  bool enable_goods_tab_bigv = context_data_->enable_search_bigv_card_other_tab &&
      (search_source == kuaishou::ad::SearchInfo::GOODS_SEARCH ||
       search_source == kuaishou::ad::SearchInfo::ACTIVITY_COMMODITY_SEARCH);
  double goods_search_rel_ratio = 1.0;
  if (search_source == kuaishou::ad::SearchInfo::GOODS_SEARCH) {
    goods_search_rel_ratio = context_data_->session_context->TryGetDouble("goods_search_rel_ratio", 1.0);
  }
  for (auto *query_sku : trigger_item_list->Items()) {
    double recall_strategy_ratio = 1.0;
    double bigv_ratio = 1.0;
    auto it = rele_thresh_ratio_.ratio().find(query_sku->recall_strategy_type);
    if (it != rele_thresh_ratio_.ratio().end()) {
      recall_strategy_ratio = it->second;
    }
    int64_t live_stream_id = query_sku->live_stream_id;
    int64_t live_stream_user_id = 0;
    if (enable_goods_tab_bigv && live_stream_id > 0) {
      live_stream_user_id = LiveStreamIndex::GetInstance()->GetNewAuthorId(live_stream_id);
    }
    if (live_stream_user_id > 0) {
      auto iter = context_data_->query_celebrity_list.find(live_stream_user_id);
      if (iter != context_data_->query_celebrity_list.end()) {
        bigv_ratio = goods_tab_bigv_ratio;
      }
    }
    if (query_sku->is_qr) {
      query_sku->posterior_score =
        query_sku->relevance_score > (thresh_score * qr_ratio * recall_strategy_ratio * bigv_ratio
         * goods_search_rel_ratio)
          ? query_sku->relevance_score : 0.0f;
    } else {
      query_sku->posterior_score =
        query_sku->relevance_score > (thresh_score * recall_strategy_ratio * bigv_ratio
         * goods_search_rel_ratio)
          ? query_sku->relevance_score : 0.0f;
    }
  }
}

void ItemRelevanceStrategy::Filter(LogicCommonList<TriggerItem>* trigger_item_list) {
  const auto &group = context_data_->ab_group_name;
  int32_t size_before_rank = trigger_item_list->Size();
  // perf before decision
  std::unordered_map<int, int> strategy_ad_count;
  std::unordered_map<int, int> strategy_filter_count;
  std::unordered_map<int, int> strategy_ad_count_all;
  for (auto *photo : trigger_item_list->Items()) {
    ++strategy_ad_count[photo->recall_strategy_type];
    auto it = context_data_->item_retrieval_tag_map.find(photo->sku_id);
    if (it != context_data_->item_retrieval_tag_map.end()) {
      for (auto recall : it->second) {
        ++strategy_ad_count_all[recall];
      }
    }
  }

  for (const auto &kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.item_before_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   context_data_->ab_group_name);
  }
  for (const auto &kv : strategy_ad_count_all) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.all_item_before_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   context_data_->ab_group_name);
  }
  int32_t query_match_product_name_size = 0;
  for (auto *photo : trigger_item_list->Items()) {
    if (photo->posterior_score >= 0.0000001f &&
        photo->relevance_type !=
            kuaishou::ad::AdTargetTransparentInfo::DEFAULT) {
      continue;
    }
    ++strategy_filter_count[photo->recall_strategy_type];
    if (!context_data_->enable_postpone_relevance_node) {
      photo->SetInvalid(kuaishou::log::ad::AdTraceFilterCondition::SEARCH_ITEM_RELEVANCE_FILTER,
                        kuaishou::ad::AdTriggerNodeType::TARGET_SKU_RELEVANCE_TYPE);
    }
  }
  context_data_->dot_perf->Interval(query_match_product_name_size,
                                 "relevance_evalutor.item_query_match_product_name_cnt");
  trigger_item_list->Compact();

  strategy_ad_count.clear();
  strategy_ad_count_all.clear();
  for (auto *photo : trigger_item_list->Items()) {
    ++strategy_ad_count[photo->recall_strategy_type];
    auto it = context_data_->item_retrieval_tag_map.find(photo->sku_id);
    if (it != context_data_->item_retrieval_tag_map.end()) {
      for (auto recall : it->second) {
        ++strategy_ad_count_all[recall];
      }
    }
  }

  for (const auto &kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.item_after_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   context_data_->ab_group_name);
  }
  for (const auto &kv : strategy_ad_count_all) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.all_item_after_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   context_data_->ab_group_name);
  }

  for (const auto &kv : strategy_filter_count) {
    context_data_->dot_perf->Interval(kv.second,
                                      "relevance_evaluator.sku_item_rele_filter_cnt",
                                      std::to_string(kv.first),
                                      context_data_->ab_group_name);
  }

  int32_t result_size = trigger_item_list->Size();
  int32_t filter_size = size_before_rank - result_size;
  context_data_->dot_perf->Interval(filter_size, "relevance_filter_item_size", group);
  context_data_->dot_perf->Interval(result_size, "relevance_result_item_size", group);

  if (size_before_rank > 0) {
    context_data_->dot_perf->Interval(1000.0 * filter_size / size_before_rank,
        "relevance_filter_item_ratio", group);
    context_data_->dot_perf->Count(1, "have_item_before_relevance", group);
  } else {
    context_data_->dot_perf->Count(1, "no_item_before_relevance", group);
  }
  if (result_size > 0) {
    context_data_->dot_perf->Count(1, "have_item_after_relevance", group);
  } else {
    context_data_->dot_perf->Count(1, "no_item_after_relevance", group);
  }
}


void ItemRelevanceStrategy::PhotoCategoryStrategy(
    LogicCommonList<TriggerItem>* query_photo_list,
    bool filter_if_no_cate) {
  if (query_photo_list->Size() == 0) {
    return;
  }
  auto &search_query_transport_info =
      context_data_->ad_request->search_query_transport_info();
  if (search_query_transport_info.query_category_size() == 0) {
    context_data_->dot_perf->Count(1, "item relevance no query category");
    if (filter_if_no_cate) {
      query_photo_list->Clear();
    }
    context_data_->dot_perf->Interval(query_photo_list->Size(),
        "item_relevance_node.no_cate_photo_item_cnt");
    return;
  }
  context_data_->dot_perf->Count(1, "item relevance has query category");
  uint32_t item_cate_miss = 0;
  uint32_t item_cate_match = 0;
  uint32_t item_cate_unmatch = 0;
  for (uint32_t i = 0u; i < query_photo_list->Size(); i++) {
    auto &photo = *(query_photo_list->At(i));
    int64_t sku_id = photo.sku_id;
    if (sku_id == 0) {
      context_data_->dot_perf->Count(1, "item relevance photo sku invalid");
      item_cate_miss++;
      photo.sub_match_type = Miss;
      continue;
    }
    std::set<int64_t> category3_ids;
    if (!PhotoMerchantCategory::GetInstance()->GetItemMerchantCategory(
        sku_id, &category3_ids, 3)) {
      item_cate_miss++;
      photo.sub_match_type = Miss;
      continue;
    }
    bool match = false;
    for (int j = 0; j < search_query_transport_info.query_category_size(); ++j) {
      if (category3_ids.find(search_query_transport_info.query_category(j)) != category3_ids.end()) {
        match = true;
        break;
      }
    }
    if (match) {
      photo.sub_match_type = Cate3match;
      item_cate_match++;
      continue;
    } else {
      item_cate_unmatch++;
      photo.sub_match_type = Unmatch;
    }
  }
  context_data_->dot_perf->Interval(item_cate_match,
        "item_relevance_node.cate_photo_item_match");
  context_data_->dot_perf->Interval(item_cate_unmatch,
      "item_relevance_node.cate_photo_item_unmatch");
  context_data_->dot_perf->Interval(item_cate_miss,
        "item_relevance_node.cate_photo_item_miss");
}

void ItemRelevanceStrategy::LiveCategoryStrategy(
    LogicCommonList<TriggerItem>* query_live_list,
    bool filter_if_no_cate) {
  if (query_live_list->Size() == 0) {
    return;
  }
  auto &search_query_transport_info =
      context_data_->ad_request->search_query_transport_info();
  if (search_query_transport_info.query_category_size() == 0) {
    if (filter_if_no_cate) {
      query_live_list->Clear();
    }
     context_data_->dot_perf->Count(1, "item relevance live query catagory invalid");
    return;
  }
  uint32_t item_cate_miss = 0;
  uint32_t item_cate_match = 0;
  uint32_t item_cate_unmatch = 0;
  for (uint32_t i = 0; i < query_live_list->Size(); ++i) {
    auto &live = *(query_live_list->At(i));
    if (live.sku_id == 0) {
      context_data_->dot_perf->Count(1, "item relevance live sku invalid");
      live.sub_match_type = Miss;
      continue;
    }
    int64_t cate3 = LiveStreamIndex::GetInstance()->GetCategory3Id(live.sku_id);
    if (cate3 == 0) {
      item_cate_miss++;
      live.sub_match_type = Miss;
      continue;
    }
    bool match = false;
    for (int i = 0; i < search_query_transport_info.query_category_size(); ++i) {
      if (search_query_transport_info.query_category(i) == cate3) {
        match = true;
        break;
      }
    }
    if (match) {
      live.sub_match_type = Cate3match;
      item_cate_match++;
      continue;
    } else {
      item_cate_unmatch++;
      live.sub_match_type = Unmatch;
    }
  }
  context_data_->dot_perf->Interval(item_cate_match,
      "item_relevance_node.cate_live_item_match");
  context_data_->dot_perf->Interval(item_cate_unmatch,
      "item_relevance_node.cate_live_item_unmatch");
  context_data_->dot_perf->Interval(item_cate_miss,
        "item_relevance_node.cate_live_item_miss");
}

float ItemRelevanceStrategy::TextMatchingScore(int64_t sku_id) const {
  std::string item_title_term;
  /**
   * get sku title.
   */
  const auto *p_wt_product = ad_target::TableWrapper::GetWTProduct(sku_id);
  if (p_wt_product) {
    item_title_term = p_wt_product->item_title_term();
    if (!p_wt_product->item_title_term().empty()) {
      context_data_->dot_perf->Count(1, "item_relevance.item_title_term",
                                     "fill", "photo");
    } else {
      context_data_->dot_perf->Count(1, "item_relevance.item_title_term",
                                     "empty", "photo");
      return -1.0f;
    }
  } else {
    const auto *p_wt_live_product =
    ad_target::TableWrapper::GetWTLiveProduct(sku_id);
    if (p_wt_live_product &&
        !p_wt_live_product->item_title_term().empty()) {
      item_title_term = p_wt_live_product->item_title_term();
      context_data_->dot_perf->Count(1, "item_relevance.item_title_term",
                                     "fill", "live");
    } else {
      context_data_->dot_perf->Count(1, "item_relevance.item_title_term",
                                     "empty", "live");
      return -1.0f;
    }
  }

  const float matching_score =
      context_data_->text_relevance_cal.GetQueryItemTitleTermRelevance(
          context_data_->text_parsed_query, item_title_term);

  if (matching_score < 0) {
    return matching_score;
  }

  if (context_data_->norm_lower_query == SPDM_skuTextMatchingDebugQuery()) {
    kuaishou::ad::ParsedQuery parsed_title;
    if (parsed_title.ParseFromString(item_title_term)) {
      std::string parsed_token;
      for (const auto & token : parsed_title.token()) {
        parsed_token.append(token.value()).append(",");
      }
      context_data_->dot_perf->Interval(matching_score * 100,
                                        "sku_text_matching_score_debug", context_data_->norm_lower_query,
                                        std::to_string(sku_id), parsed_token);
    }
  }

  return matching_score;
}

}  // namespace ad_target_search
}  // namespace ks
