#include "teams/ad/ad_target_search/engine/strategy/relevance/photo_relevance_strategy.h"

#include <set>

#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "base/common/logging.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_base/src/math/random/random.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_base.pb.h"
#include "teams/ad/ad_proto/kuaishou/ad/ad_service.pb.h"

#include "teams/ad/ad_target_search/trace/trace_manager.h"
#include "teams/ad/ad_target_search/util/spdm/spdm_switches.h"
#include "teams/ad/ad_target_search/engine/context_data/context_data.h"
#include "teams/ad/ad_target_search/engine/strategy/enums.h"
#include "teams/ad/ad_target_search/engine/strategy/strategy.h"
#include "teams/ad/ad_target_search/util/cache_loader/photo_merchant_category.h"
#include "teams/ad/engine_base/search/util/table_extension/table_wrapper.h"
#include "teams/ad/ad_table/code_generator/ad_target_search/WTSeries.h"

static const float kMerchantCategoryValue = 1.0f;

namespace ks {
namespace ad_target_search {

void PhotoRelevanceStrategy::Prepare() {
  merchant_photo_category3_match = kMerchantCategoryValue;
  merchant_photo_category2_match = kMerchantCategoryValue;
  merchant_photo_category_unmatch = kMerchantCategoryValue;
  merchant_photo_category_miss = kMerchantCategoryValue;

  RelevanceStrategyBase::Prepare();
  GetCategoryRatio();

  use_embedding_score_ratio_ =
      context_data_->session_context->TryGetInteger("use_embedding_score_ratio", 0);
}


void PhotoRelevanceStrategy::CalculateScore(
    LogicCommonList<TriggerItem>* trigger_item_list, std::vector<RelevanceItem>* relevance_items,
    const std::unordered_map<const TriggerItem*, int32_t>& relevance_item_map,
    const RelevanceLinear& linear_conf) {
  const auto &default_linear = linear_conf.default_linear();
  const auto &special_linear = linear_conf.special_linear();
  // 非一线城市凌晨放量
  int32_t cur_hour = ((absl::Now()).In(absl::LocalTimeZone())).hour;
  const auto black_city = AdKconfUtil::searchCpmThreshBlackCity();
  bool enable_midnight_expand =
      ad_target_search::SPDM_enable_search_midnight_expand(context_data_->spdm_ctx) &&
      !black_city->count(context_data_->city_region) && cur_hour >= 2 && cur_hour <= 6;
  float thres_ratio =
      context_data_->ad_request->search_info().page_num() > 0 ? linear_conf.linear_decay() : 1.0f;
  int all_rele_score = 0;
  int all_posterior_score = 0;

  double graph_retr_rele_ratio =
          context_data_->session_context->TryGetDouble("graph_emb_retr_rele_ratio", 1.0);

  // 把 relevance item 当中的各种 score , 根据决策树算法,
  // 填充到 photo 当中的 posterior_score
  // 决定是否通过相关性的，都依靠 posterior_score 唯一决定
  for (size_t i = 0; i < trigger_item_list->Size(); ++i) {
    auto *p_photo = trigger_item_list->At(i);
    auto r_iter = relevance_item_map.find(p_photo);
    if (r_iter == relevance_item_map.end()) {
      LOG_EVERY_N(ERROR, 100) << "relevance item not found";
      continue;
    }
    auto &item = relevance_items->at(r_iter->second);
    auto &photo = *p_photo;
    const auto iter = special_linear.find(photo.recall_strategy_type);
    const auto &linear = iter == special_linear.end() ?
                default_linear : iter->second;
    SetRelevanceScore(&item, &photo, use_embedding_score_ratio_);

    // 内流相关性 decay
    auto new_thres_ratio = thres_ratio;
    if (context_data_->is_inner_stream_request) {
      auto& inner_decay = context_data_->inner_stream_config.relevance_decay();
      auto iter = inner_decay.find(photo.recall_strategy_type);
      if (iter != inner_decay.end()) {
        new_thres_ratio *= iter->second;
      }
    }
    // 搜索激励广告盒子单列相关性 decay
    if (context_data_->is_ad_box_single_col_request) {
      auto& ad_box_single_col_decay = context_data_->ad_box_single_col_config.relevance_decay();
      auto iter = ad_box_single_col_decay.find(photo.recall_strategy_type);
      if (iter != ad_box_single_col_decay.end()) {
        new_thres_ratio *= iter->second;
      }
    }

    if ((photo.recall_strategy_type == RetrievalTag::SEARCHAD_E2E_TWIN_TOWER ||
         photo.recall_strategy_type == RetrievalTag::SEARCHAD_USER_HIGHVALUE_PHOTO) &&
        enable_midnight_expand) {
      new_thres_ratio *= linear_conf.linear_decay();
    }
    double posterior_score;
    // 获取双塔相关性配置
    RelevanceRecallConfigure_ConfigItem recall_config_item;
    // const auto shared = AdKconfUtil::relevanceRecallConfig();
    // const auto &abtest = shared->data().abtest();
    const auto shared = AdKconfUtil::relevanceRecallConfig();
    const auto &config = shared->data();
    const auto &abtest = config.abtest();
    const auto &recall_config_group_name =
      context_data_->session_context->TryGetString("relevance_recall_config", "base");
    auto it = abtest.find(recall_config_group_name);
    if (it != abtest.end()) {
      recall_config_item.CopyFrom(it->second);
    }
    // 双塔相关性分数--内流衰减系数: 目的是约束内流双塔相关性分下发量
    if (recall_config_item.two_tower_relevance_inner_decay_ratio() > 0.0f
      && photo.relevance_type == AdTargetTransparentInfo::EMBEDDING
      && context_data_->is_inner_stream_request) {
      photo.relevance_score *= recall_config_item.two_tower_relevance_inner_decay_ratio();
    }
    // 双塔相关性分数--非首页衰减系数: 目的是约束非首页双塔相关性分下发量
    if (recall_config_item.two_tower_relevance_page_gt0_decay_ratio() > 0.0f
      && photo.relevance_type == AdTargetTransparentInfo::EMBEDDING
      && context_data_->ad_request->search_info().page_num() > 0) {
      photo.relevance_score *= recall_config_item.two_tower_relevance_page_gt0_decay_ratio();
    }
    // 双塔相关性--使用统一的过滤阈值: 临时加，实验后可合并到相关性 Kconf:relevanceLinear.
    if (photo.relevance_type == AdTargetTransparentInfo::EMBEDDING
      && recall_config_item.two_tower_relevance_unified_threshold() > 0.0f) {
      auto rele_score = photo.relevance_score;
      auto threshold =
        recall_config_item.two_tower_relevance_unified_threshold() * new_thres_ratio;
      posterior_score = (rele_score > threshold) ? rele_score : 0.0f;
    } else {
      posterior_score = GetPhotoPosteriorScore(item, linear,
        photo.relevance_score, new_thres_ratio);
    }

    if (posterior_score > 0.0000001f)
      photo.posterior_score = posterior_score;
    all_rele_score += (item.use_embedding_score ? item.embedding_score : photo.relevance_score);
    all_posterior_score += photo.posterior_score;
  }
  if (trigger_item_list->Size() > 0) {
    context_data_->dot_perf->Interval(all_rele_score / trigger_item_list->Size(),
                                   "relevance_evaluator.all_rele_score",
                                   context_data_->ab_group_name);
    context_data_->dot_perf->Interval(all_posterior_score / trigger_item_list->Size(),
                                   "relevance_evaluator.all_posterior_score",
                                   context_data_->ab_group_name);
  }
}

void PhotoRelevanceStrategy::Filter(LogicCommonList<TriggerItem>* trigger_item_list) {
  const auto &group = context_data_->ab_group_name;
  int32_t size_before_rank = trigger_item_list->Size();
  // perf before decision
  std::unordered_map<int, int> strategy_ad_count;
  std::unordered_map<int, int> strategy_ad_count_all;
  bool first_page = (context_data_->page_num == 0);

  for (auto *photo : trigger_item_list->Items()) {
    ++strategy_ad_count[photo->recall_strategy_type];
    auto it = context_data_->retrieval_tag_map.find(photo->photo_id);
    if (it != context_data_->retrieval_tag_map.end()) {
      for (auto recall : it->second) {
        ++strategy_ad_count_all[recall];
      }
    }
  }

  for (const auto &kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.photo_item_before_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   std::to_string(first_page),
                                   context_data_->ab_group_name);
  }
  for (const auto &kv : strategy_ad_count_all) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.all_photo_item_before_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   std::to_string(first_page),
                                   context_data_->ab_group_name);
  }
  int32_t query_match_product_name_size = 0;
  for (auto *photo : trigger_item_list->Items()) {
    if (photo->query_match_product_name == true) {
      ++query_match_product_name_size;
      if (context_data_->enable_postpone_relevance_node) {
        photo->posterior_score = 1.0;
      }
    }
    if (!context_data_->enable_postpone_relevance_node) {
      if (photo->posterior_score < 0.0000001f && photo->query_match_product_name != true) {
        photo->SetInvalid(kuaishou::log::ad::AdTraceFilterCondition::SEARCH_PHOTO_RELEVANCE_FILTER,
                         kuaishou::ad::AdTriggerNodeType::TARGET_RELEVANCE_TYPE);
      }
    }
  }
  context_data_->dot_perf->Interval(query_match_product_name_size,
                                 "relevance_evalutor.query_match_product_name_cnt");
  trigger_item_list->Compact();

  strategy_ad_count.clear();
  strategy_ad_count_all.clear();
  for (auto *photo : trigger_item_list->Items()) {
    ++strategy_ad_count[photo->recall_strategy_type];
    auto it = context_data_->retrieval_tag_map.find(photo->photo_id);
    if (it != context_data_->retrieval_tag_map.end()) {
      for (auto recall : it->second) {
        ++strategy_ad_count_all[recall];
      }
    }
  }

  for (const auto &kv : strategy_ad_count) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.photo_item_after_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   std::to_string(first_page),
                                   context_data_->ab_group_name);
  }
  for (const auto &kv : strategy_ad_count_all) {
    context_data_->dot_perf->Interval(kv.second,
                                   "relevance_evaluator.all_photo_item_after_rank_erase_cnt",
                                   std::to_string(kv.first),
                                   std::to_string(first_page),
                                   context_data_->ab_group_name);
  }

  int32_t result_size = trigger_item_list->Size();
  int32_t filter_size = size_before_rank - result_size;
  context_data_->dot_perf->Interval(filter_size, "relevance_filter_photo_size", group);
  context_data_->dot_perf->Interval(result_size, "relevance_result_photo_size", group);
  context_data_->target_counter.photo_relevance = result_size;

  if (size_before_rank > 0) {
    context_data_->dot_perf->Interval(1000.0 * filter_size / size_before_rank,
        "relevance_filter_photo_ratio", group);
    context_data_->dot_perf->Count(1, "have_photo_before_relevance", group);
  } else {
    context_data_->dot_perf->Count(1, "no_photo_before_relevance", group);
  }
  if (result_size > 0) {
    context_data_->dot_perf->Count(1, "have_photo_after_relevance", group);
  } else {
    context_data_->dot_perf->Count(1, "no_photo_after_relevance", group);
  }
}

void PhotoRelevanceStrategy::PhotoCategoryStrategy(
    LogicCommonList<TriggerItem>* query_photo_list, bool filter) {
  auto &search_query_transport_info =
      context_data_->ad_request->search_query_transport_info();
  if (search_query_transport_info.query_category_size() == 0) {
    context_data_->dot_perf->Count(1, "no query category", context_data_->ab_group_name);
    return;
  }
  context_data_->dot_perf->Count(1, "has query category", context_data_->ab_group_name);
  std::vector<TriggerItem*> photo_list;
  uint32_t photo_miss = 0, photo_match = 0, photo_unmatch = 0;
  for (uint32_t i = 0u; i < query_photo_list->Size(); i++) {
    auto &photo = *(query_photo_list->At(i));
    std::set<int64_t> category3_ids;
    if (!PhotoMerchantCategory::GetInstance()->GetPhotoMerchantCategory(
        photo.photo_id, &category3_ids, 3)) {
      photo_miss++;
      photo.sub_match_type = Miss;
      photo.category_ratio = merchant_photo_category_miss;
      photo_list.emplace_back(&photo);
      continue;
    }
    bool match = false;
    for (int j = 0; j < search_query_transport_info.query_category_size(); ++j) {
      if (category3_ids.find(search_query_transport_info.query_category(j)) != category3_ids.end()) {
        match = true;
        break;
      }
    }
    if (match) {
      photo.sub_match_type = Cate3match;
      photo.category_ratio = merchant_photo_category3_match;
      photo_list.emplace_back(&photo);
      photo_match++;
      continue;
    }
    std::set<int64_t> category2_ids;
    if (!PhotoMerchantCategory::GetInstance()->GetPhotoMerchantCategory(
        photo.photo_id, &category2_ids, 2)) {
      photo_miss++;
      photo.sub_match_type = Miss;
      photo.category_ratio = merchant_photo_category_miss;
      photo_list.emplace_back(&photo);
      continue;
    }
    for (int j = 0; j < search_query_transport_info.query_category_size(); ++j) {
      if (category2_ids.find(search_query_transport_info.query_category(j)) != category2_ids.end()) {
        match = true;
        break;
      }
    }
    if (match) {
      photo.sub_match_type = Cate2match;
      photo.category_ratio = merchant_photo_category2_match;
      photo_list.emplace_back(&photo);
      photo_match++;
    } else {
      photo.sub_match_type = Unmatch;
      photo.category_ratio = merchant_photo_category_unmatch;
      photo_unmatch++;
      if (context_data_->enable_postpone_relevance_node) {
        photo.posterior_score = 0;
      }
    }
  }
  if (!context_data_->enable_postpone_relevance_node) {
    if (filter) {
      query_photo_list->ReplaceAll(std::move(photo_list), [&](TriggerItem* p_item) {
        if (p_item->IsInvalid()) {
          p_item->SetFilterInfo(AdTraceFilterCondition::SEARCH_PHOTO_CATEGORY_STRATEGY_FILTER,
                                kuaishou::ad::AdTriggerNodeType::TARGET_RELEVANCE_TYPE);
        } else {
          p_item->ClearFilterInfo();
        }
      });
    }
  }
  context_data_->dot_perf->Interval(photo_match,
        "relevance_node.cate_photo_match",
        context_data_->ab_group_name);
  context_data_->dot_perf->Interval(photo_unmatch,
      "relevance_node.cate_photo_unmatch",
      context_data_->ab_group_name);
  context_data_->dot_perf->Interval(photo_miss,
        "relevance_node.cate_photo_miss",
        context_data_->ab_group_name);
}

void PhotoRelevanceStrategy::GoodsTabFilter(
    LogicCommonList<TriggerItem>* query_photo_list, bool filter) {
  GetQueryTermWeight();
  if (query_total_weight_ <= 0 || query_term_weight_.size() == 0) {
    return;
  }
  std::vector<TriggerItem*> photo_list;
  uint32_t photo_miss = 0, photo_match = 0, photo_unmatch = 0;
  for (uint32_t i = 0u; i < query_photo_list->Size(); i++) {
    auto &photo = *(query_photo_list->At(i));
    if (photo.recall_strategy_type == RetrievalTag::SEARCHAD_QUERY_TO_SKU_PHOTO) {
      photo_match++;
      photo_list.emplace_back(&photo);
      continue;
    }
    float matchweight = 0;
    std::string title;
    if (!PhotoMerchantCategory::GetInstance()->GetPhotoMerchantTitle(
        photo.photo_id, &title)) {
      photo_miss++;
      if (context_data_->enable_postpone_relevance_node) {
        photo.posterior_score = 0;
      }
      continue;
    }
    for (auto &v : query_term_weight_) {
      if (title.find(v.first) != std::string::npos) {
        matchweight += v.second;
      }
    }
    if (matchweight/query_total_weight_ > AdKconfUtil::merchantTermMatchRatio()) {
      photo_match++;
      photo_list.emplace_back(&photo);
    } else {
      photo_unmatch++;
      if (context_data_->enable_postpone_relevance_node) {
        photo.posterior_score = 0;
      }
    }
  }
  if (!context_data_->enable_postpone_relevance_node) {
    if (filter) {
      query_photo_list->ReplaceAll(std::move(photo_list), [&](TriggerItem* p_item) {
        if (p_item->IsInvalid()) {
          p_item->SetFilterInfo(AdTraceFilterCondition::SEARCH_PHOTO_GOODS_TAB_FILTER,
                                kuaishou::ad::AdTriggerNodeType::TARGET_RELEVANCE_TYPE);
        } else {
          p_item->ClearFilterInfo();
        }
      });
    }
  }
  context_data_->dot_perf->Interval(photo_match,
        "new_relevance_node.merchant_title_match",
        context_data_->ab_group_name);
  context_data_->dot_perf->Interval(photo_unmatch,
      "new_relevance_node.merchant_title_unmatch",
      context_data_->ab_group_name);
  context_data_->dot_perf->Interval(photo_miss,
        "new_relevance_node.merchant_title_miss",
        context_data_->ab_group_name);
}


/**
 * Return -1 if the photo_id is not available, return 3 if series_id hits strong card tag.
 */
float PhotoRelevanceStrategy::TextMatchingScore(int64_t photo_id,
                                                int32_t type) const {
  switch (type) {
    case 1: {
      const auto *photo = ad_target::TableWrapper::GetWTPhoto(photo_id);
      context_data_->dot_perf->Count(1, "wt_photo_null", "TextMatchingScore", "all");
      if (!photo) {
        context_data_->dot_perf->Count(1, "wt_photo_null", "TextMatchingScore", "null");
      }
      if (!photo) {
        return -1.0;
      }
      if (photo->series_id() < 1) {
        return -1.0;
      }
      const auto& whitelist =
          context_data_->ad_request->search_query_transport_info().duanju_series_ids();
      if (std::find(whitelist.begin(), whitelist.end(), photo->series_id()) != whitelist.end()) {
        context_data_->dot_perf->Count(1, "photo_text_matching", "hit_whitelist");
        return 3.0;
      }
      const auto *series = ad_target::TableWrapper::GetWTSeries(photo->series_id());
      if (!series) {
        context_data_->dot_perf->Count(1, "photo_text_matching", "miss_series");
        return -1.0;
      }

      if (series->course_name_term().empty()) {
        context_data_->dot_perf->Count(1, "photo_text_matching", "miss_series_name");
        return -1.0;
      }
      const float matching_score =
          context_data_->text_relevance_cal.GetQueryItemTitleTermRelevance(
              context_data_->text_parsed_query, series->course_name_term());
      if (matching_score < 0) {
        context_data_->dot_perf->Count(1, "photo_text_matching", "error");
        return -1.0;
      }

      context_data_->dot_perf->Count(1, "photo_text_matching", "success");

      if (context_data_->norm_lower_query == SPDM_photoTextMatchingDebugQuery()) {
        kuaishou::ad::ParsedQuery parsed_course_name;
        if (parsed_course_name.ParseFromString(series->course_name_term())) {
          std::string parsed_token;
          for (const auto &token : parsed_course_name.token()) {
            parsed_token.append(token.value()).append(",");
          }
          context_data_->dot_perf->Interval(
              matching_score * 100, "photo_text_matching_score_debug",
              context_data_->norm_lower_query, std::to_string(photo_id),
              parsed_token);
        }
      }
      return matching_score;
    }
    case 2:
      return -1.0;
    case 3:
      return -1.0;
    default:
      return -1.0;
  }
}

void PhotoRelevanceStrategy::GetCategoryRatio() {
  auto categoryratio = AdKconfUtil::relMerchantCategoryRatio();
  auto it = categoryratio->find("photocategory3match");
  if (it != categoryratio->end()) {
    merchant_photo_category3_match = it->second;
  }
  it = categoryratio->find("photocategory2match");
  if (it != categoryratio->end()) {
    merchant_photo_category2_match = it->second;
  }
  it = categoryratio->find("photounmatch");
  if (it != categoryratio->end()) {
    merchant_photo_category_unmatch = it->second;
  }
  it = categoryratio->find("photomiss");
  if (it != categoryratio->end()) {
    merchant_photo_category_miss = it->second;
  }
}

}  // namespace ad_target_search
}  // namespace ks
