#pragma once
#include <string>

#include "teams/ad/ad_base/src/kconf/kconf_node.h"
#include "teams/ad/ad_brand/util/kconf/kconf_data.h"
#include "teams/ad/ad_brand/util/kconf/kconf_data.pb.h"
namespace ks {
namespace ad_brand {

using namespace ks::ad_base::kconf;  // NOLINT

struct BrandKconfUtil {
  /**
   * 放量开关，推全后可删除
  */
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableScFeedBrushNum, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashAdxAllExtendTime, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandSetReservationId, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, fixBrandDatafreamUpdate, true);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableChangefilterOrder, false);
  /**
   * 品牌 router 上移相关
  */
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandPredictServiceNew, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandPredictRequestDiff, false);

  // 开屏样本抽样比例阈值
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, brandSplashAckForPsRatio, 1.0)
  // 开屏直接出优先级最高 unit 开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashPriority, false);
  // 高粉用户阈值
  DEFINE_INT64_KCONF_NODE(ad.splashserver, minFanCount, 100000);
  // 品牌开屏预加载间隔
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashBrandPreferchInterval, 1);
  // 新用户生存时间(天)
  DEFINE_INT64_KCONF_NODE(ad.splashserver, newUserLiveDays, 14);
  // 老用户最大 ID
  DEFINE_INT64_KCONF_NODE(ad.splashserver, maxOldUserId, 1100000001);
  // 开屏低端机型过滤
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, lowDeviceModel, "NonePhone");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, lowIosDeviceModel, "NonePhone");
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, lowArIosDeviceModelSet);

  DEFINE_BOOL_KCONF_NODE(ad.brandserver, replaceKENVLaneId, false);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, brandRenQiBaoGroupBoostRatio);

  // 信息流获取历史浏览记录最大时间
  DEFINE_INT64_KCONF_NODE(ad.brandserver, browseHistoryRange, 61);
  // 是否启用开屏冷启动实验
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableColdStartExp, false);
  // 是否启动开屏库存实验
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashStorageExp, false);
  // 开屏冷启动使用最低版本号
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minColdExpExpVesion, "2.7.20");
  // 开屏冷启动商业测试启动时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, adColdStartTestStartTime, 0);
  // 开屏冷启动商业测试结束时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, adColdStartTestEndTime, 0);
  // 冷启动实验可见广告的广告组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adColdStartTestGroupNum);
  // 非全屏素材大小列表
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, materialSizeList);
  // 全屏素材大小列表
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, materialSizeFullScreenList);
  // 全屏图片素材大小列表
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, materialSizeFullScreenStaticList);
  // redis 访问超时时间
  DEFINE_INT64_KCONF_NODE(ad.brandserver, redisTimeout, 3);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, redisAdxTimeout, 5);
  // 索引请求业务端广告数据的时间间隔(秒)
  DEFINE_INT64_KCONF_NODE(ad.splashserver, indexQueryAdInterval, 60);
  // 是否加载本地广告数据
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLoadAds, false);
  // 是否获取 unit photo impression
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableUnitPhotoImpression, true);
  // 是否获取拉取广告主最近作品
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableAuthorPhotoService, true);
  // 是否加载 GIVT 黑名单
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableGIVTBlacklistP2p, true);
  // 地级市名称在返回索引中的位置
  DEFINE_STRING_INT64_MAP_KCONF(ad.splashserver, cityNameIndex);
  // 地级市名称别名映射
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, cityNameAlias);
  // 支持 page 字段
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minSupportPageIOSVersion, "7.4.30");
  DEFINE_INT64_KCONF_NODE(ad.brandserver, maxReqNumForBrowse, 2);
  // 预览配置
  DEFINE_KCONF_NODE_LOAD(PreviewSettingConf, ad.splashserver, previewSettingConf);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableAdmitSlidePage, false);
  // 是否支持 trace_info
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableTraceInfo, false);
  // brand 模型直连开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableBrandPredictDirect, false);
  DEFINE_BOOL_KCONF_NODE(ad.router, adRouterGetKessNameIgnoreEnv, false);
  // 追踪用户的请求信息
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, traceInfoUserSet);
  // 是否初始化行为列表( picasso 故障降级)
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableInitializeActionList, true);
  // 白名单用户
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, whiteSet);
  // 主站 ios 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKuaishouIos, "6.10.1");
  // 主站 android 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKuaishouAndroid, "6.10.1");
  // 主站 harmony 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKuaishouHarmony, "12.7.40");
  // 极速版 ios 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minNebulaIos, "1.1.1.12");
  // 极速版 android 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minNebulaAndroid, "1.10.2");
  // 开屏特殊机型放开最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minFoldDeviceModVersion, "12.8.20");
  // 极速版 harmony 最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minNebulaHarmony, "12.7.40");
  // 实时数据能够替换预加载数据的最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, replacePretchAdMinVersion, "12.1.20");
  // 直播代投头像优化最小版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, liveAgentAvaTarImproveMinVersion, "12.3.10");
  // 不出广告的最小消费
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, livePayDaily, 100.0);
  // 是否开启新用户保护策略
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableNewUserProtect, false);
  // trace log FeedRankUnits 的个性化填充
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, brandTraceLogScoreType);
  // 最小 adx creativeid
  DEFINE_INT64_KCONF_NODE(ad.splashserver, minAdxCreativeId, 1000000000000000);
  // adx 白名单用户
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adxWhiteSet);
  // 开屏负反馈策略，最低点击跳过次数
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashNegativePolicySkipCount, 2);
  // 开屏负反馈策略，负反馈有效期
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashNegativePolicyAvaliableHours, 6);
  // 开屏和信息流联合频控策略有效期
  DEFINE_INT64_KCONF_NODE(ad.brandserver, unionFrequencyAvaliableHours, 6);
  // 联合频控次数
  DEFINE_INT64_KCONF_NODE(ad.brandserver, unionFrequencyCount, 4);
  // 联合频控 creative 名单
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, unionFrequencySetList);
  // 主站用户最大浏览次数
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashAdDayLimit, 4);
  // 极速版用户最大浏览次数
  DEFINE_INT64_KCONF_NODE(ad.splashserver, nebulaSplashAdDayLimit, 6);
  // 频控单元白名单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, freUnitWhite);
  // 主站开屏时间间隔
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashAdShowInterval, 7200);
  // 极速版开屏时间间隔
  DEFINE_INT64_KCONF_NODE(ad.splashserver, nebulaSplashAdShowInterval, 3600);
  // 低广告价值人群开屏时间间隔
  DEFINE_PROTOBUF_NODE_KCONF(kconf::LowAdValuePersonFreq, ad.splashserver, lowAdValuePersonFreq);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::LastMshowNConf, ad.splashserver, lastMshowNConf);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::LastMshowNConf, ad.splashserver, lastMshowNConfForIndustry);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::LastMshowNConfForGame, ad.splashserver, lastMshowNConfForGame);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, lastmShowNIndustryIdSet);
  // redis 访问超时时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashRedisTimeout, 3);
  // 高峰期广告开屏一次出一次广告
  DEFINE_INT64_KCONF_NODE(ad.splashserver, OneByOneStartTime, 0);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, OneByOneEndTime, 0);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, OneByOneInterval, 1);
  // 是否允许 adx
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableAdx, false);
  // adx 测试 channel 广告
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, testChannelSet);
  // adx 测试 unit 广告
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, testAdxUnitSet);
  // adx 灰度 unit 广告
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, grayAdxUnitSet);
  // 是否使用曝光作为首刷
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableUseImpression, false);
  // 品牌 router 请求填充用户特征
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableBrandFillRouterUserFeature, false);
  // 跳过解析超时返回结果
  DEFINE_BOOL_KCONF_NODE(ad.adRank3, disableParseTimeoutResponse, false);
  DEFINE_INT32_KCONF_NODE(ad.adRank, perfUnifySampleRatio, 10)
  // 品牌算法 redis 访问超时时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, adBrandAlgoRedisTimeout, 3);
  // 返还比优化实验分组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, rrRefUserTagSet);
  // 最大预加载天数
  DEFINE_INT64_KCONF_NODE(ad.splashserver, maxPrefetchDay, 7);
  DEFINE_INT32_KCONF_NODE(ad.adserver, predictServerTimeoutMills, 30)  // 请求 predict server 的超时时间
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.splashserver, splashRrThres);       // 返还率 阈值
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, defaultCmdKey, "");  // 默认 cmd key
  DEFINE_INT64_KCONF_NODE(ad.splashserver, maxSelectCreative, 10);  // creative 选择数量最大值
  // unit 过滤比例. 取值:0~1, 0:表示不过滤，1:全部过滤
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.splashserver, unitFilterRatio);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableGetSplashLaunchInfo, true);
  // 是否开启预算隔离的调控
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableGroupAdjust, false);
  // 不展示的广告集合
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, disableShowUnitSet);
  // 预加载是否开启缓存过滤
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashExistFilter, false);
  // 素材全量下发 unit 白名单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashExistNoFilterUnitSet);
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, logoTitles);  // 开屏按照不同分组设置不同文案
  // eyemax 广告单元点击头像跳转控制
  DEFINE_STRING_INT64_MAP_KCONF(ad.splashserver, adPageButtonPageControl);
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, personalTextMap);
  // 请求 adx 超时时间
  DEFINE_INT32_KCONF_NODE(ad.splashserver, getAdxResultTimeoutMills, 120);
  DEFINE_INT32_KCONF_NODE(ad.splashserver, getUserPropertyTimeoutMills, 5);
  DEFINE_INT32_KCONF_NODE(ad.frontserver, splashPrefetchExtraTimeoutMs, 0);
  DEFINE_INT32_KCONF_NODE(ad.splashserver, brandExploreAdxExtraTimeoutMs, 0);
  // 返还比模型开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, rrGate, false);
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, splashRrAckForPsRatio, 1.0);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, baseTimeStamp, 1577808000);  // 2020 年 1 月 1 日 0 点 0 分
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, firstBrushPos);  // 启动下拉刷新的 pos_id
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, firstBrushWhiteCampaign);  // 启动 5n 页的 campaion_id
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, firstBrushWhiteUnit);  // 启动 5n 页的 unit_id
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, emptyAdSet);  // 空广告集合

  // 用户保护开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLahuoUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLossUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSleepUserProtect, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSetNebula1dayUserRetentionScore, true);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashExitUserProtect, false);
  // 只出指定广告名单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, forceDeliveryUnitSet);
  // 是否使用只出指定广告功能
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableForceDelivery, true);
  // 指定广告开始时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, forceDeliveryStartTime, 1613044800);
  // 指定广告结束时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, forceDeliveryEndTime, 1613059200);
  // eyemax 视频最长时间
  DEFINE_INT64_KCONF_NODE(ad.splashserver, preloadDurMs, 15000);
  // unit 主 app 最小版本号
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, unitMinVersion);
  // unit  极速版最小版本号
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, unitMinNebulaVersion);
  // pd 优先级调整
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.splashserver, pdUnitPriority);
  // 最大预投放时间, 超过该时间认为 adx 投放时间非法
  DEFINE_INT32_KCONF_NODE(ad.splashserver, maxAdxTime, 70);
  // 禁用 ssl 弹窗名单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, disallowShowSslErrorDialog);
  // 电商新样式卡片延迟时间
  DEFINE_INT64_KCONF_NODE(ad.brandserver, defaultCardDelayTimeMs, 4000)
  // 电商新样式卡片延迟时间
  DEFINE_INT64_KCONF_NODE(ad.brandserver, exploreDefaultCardDelayTimeMs, 4000)
  // 电商新样式轮播时间
  DEFINE_INT64_KCONF_NODE(ad.brandserver, defaultCarouselTimeMs, 3000)
  // action_bar 出现时间
  DEFINE_INT64_KCONF_NODE(ad.brandserver, defaultActionBarLoadTimeMs, 1000)
  // action_bar 变色时间延迟
  DEFINE_INT64_KCONF_NODE(ad.brandserver, defaultRealShowDelayTimeMs, 0)
  // 是否填充开屏实验 V2
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableFillSplashShowControlV2, false);
  // adx 调整客户端缓存过滤
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adxAdjustClientCacheGroupList);
  // 背景线程加载分值映射数据开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLoadScoreScaleData, false);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minM2uIos, "1.0.0");   // 一甜
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minM2uAndroid, "1.0.0");  // 一甜
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKmovieIos, "1.0.0");  // 快影
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKmovieAndroid, "1.0.0");  // 快影
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableHideLabel, false);                    // 是否隐藏广告标
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableAdxTransport, false);  // 是否启用新的 adx 服务

  // TRACE_LOG 概率
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, enableTraceRate, 0.0);
  // 是否启用 RTA 服务
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableRta, false);
  // 诊断平台用户
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, DiagnoseUserSet);
  // 单元 id 对应实验编号
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, testUnitToExpNum);
  // 是否 Adx 服务
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandAdxServer, false);
  // adx 测试时只能让白名单用户看到特定 unit 的广告
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, testAdxAdSet);
  // 频控白名单单元
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, frequencyUnitWhiteList)
  // 是否直接使用本地频控数据
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableFrequencyLocal, true);
  // 信息流频控最小时间间隔(分钟)
  DEFINE_INT64_KCONF_NODE(ad.brandserver, feedFrequencyMinInverval, 60)
  // 信息流点击频控最小时间间隔(天级)
  DEFINE_INT64_KCONF_NODE(ad.brandserver, feedClickFrequencyDayInterval, 0);
  // 信息流白名单频控最小时间间隔(分钟)
  DEFINE_INT64_KCONF_NODE(ad.brandserver, feedWhiteAdFrequencyMinInverval, 30)
  // 频控 abtest 白名单单元
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, frequencyAbtestUnitList)
  DEFINE_INT64_KCONF_NODE(ad.brandserver, UnitNegtiveDay, 28);
  // 信息流频控服务端最小时间间隔(分钟)
  DEFINE_INT64_KCONF_NODE(ad.brandserver, feedFrequencyServerMinInverval, 3)
  // 是否启用 order 层级频控
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableFrequencyByOrder, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableFrequencyByProductIndustry, true);
    DEFINE_INT64_KCONF_NODE(ad.brandserver, ProductNegtiveDay, 28);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, IndustryNegtiveDay, 14);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, frequencyRpcTimeInterval, 60);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashIndustryNegtiveDay, 14);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, splashAccountNegtiveDay, 14);
  // 请求 rta 超时时间
  DEFINE_INT32_KCONF_NODE(ad.splashserver, getRtaResultTimeoutMills, 50);
  // 发金币安卓最小版本控制
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minCoinKuaishouAndroid, "9.1.20");
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableAddRandomCtr, false)  // 是否把 ctr 值加一个小数
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, pkRandomGroupList)   // 品牌 pk 实验中品牌随机挑选流量的人群组
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, landingPageUnitList)  // 表单 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, lpsUncertaintyGroupList)  // 表单不确定度实验人群组
  DEFINE_DOUBLE_KCONF_NODE(ad.algorithm, brandUncertaintyCofficient, 1.0)  // 表单不确定度调节权重
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, brandLpsAvgGroupList)  // 表单均值实验人群组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashUnifyLpsUnitList)  // 开屏信息流统一 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, pdPriorityGroupList)  // pd 订单调整优先级人群组
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, LiveUnitList)  // 直播 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, ShopOrderUnitList)  // 小店下单 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandConvUnitList )  // 激活模型白名单 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandConvGroupList)  // 激活人群组
  DEFINE_PROTOBUF_NODE_KCONF(kconf::MockPsBiaCtr, ad.brandserver, mockPsBiaCtr);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, quickConsumptionGroupList)  // 近似购买人群组
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, lpsShopMergeGroupList)  // 内容优推用户小店付费加权人群组
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandLpsShopMergeWeight, 0.1)  // 内容优推用户小店付费加权权重
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandLpsShopUnitList)  // 内容优推用户小店付费 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, brandConvExpandOrientationUnit)   // 激活扩展人群包实验 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, prefetchNoOrientationLimitUnit)  // 预加载突破人群包限制
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, brandConvExpandOrientationGroup)  // 激活扩展人群包实验人群组
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandSmartCrowdAbUnit)   // 激活智能定向实验 unit 列表
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandSmartCrowdAbGroup)  // 激活智能定向人群组
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandGameConvOptLtvAlpha, 0.0)  // 品牌激活类型订单优化 ROI 权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, gameConvOptLtvGroupAlpha);  // 优化 ROI 各组权重
  // 智能定向
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandSmartCrowdUnitList)
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, brandSmartCrowdGroup)  // 智能定向 分组实验
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandSmartCrowdAvgScore, 1.0)  // 智能定向 平均 score
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, gameAccountForCrowd);  // 智能定向中账户映射
  DEFINE_PROTOBUF_NODE_KCONF(kconf::UnitAddOrientationGroupConf, ad.splashserver,
    unitAddOrientationGroupConf);  // 按分桶和人群包配置智能定向
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandMerchantCrowdByUnit);  // 电商智能定向 unit
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandCrowdWeightByUnit);  // 智能定向扩量权重修改配置
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandCrowdWeightByAccount);  // 智能定向扩量权重修改配置
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandSmartCrowdAvoidAccount);  // 智能定向扩包豁免广告主
  DEFINE_DOUBLE_KCONF_NODE(ad.algorithm, brandClearGamePriority, 0.0);  // 去除游戏优先级实验的优先级填充
  // 创意试投放策略
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandGameDetectionUnitList)  // 试投放策略测试广告主
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandGameDetectionGroup)  // 试投放策略分桶
  // 品牌种草
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3OptConfig);  // 品牌种草中广告组映射
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3OptConfigWide);  // 品牌种草宽口径映射
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3OptConfigCpr3);  // 品牌种草 CPR3 映射
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandR3BoostByCxr);  // 种草策略实验参数
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandR3BoostRatio, 1.0)  // 品牌种草命中权重
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandR3BoostByCxrBound, 10.0)  // 种草策略权重上限
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandR3GroupBoostRatio);  // 品牌种草分组权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandDianshangGroupBoostRatio);  // 品牌种草分组电商人群权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupScore);  // 品牌广告走 r3 模型流量桶均值
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupScoreWide);  // 品牌广告宽口径种草实验均值
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupScoreCpr3);  // 品牌广告 cpr3 种草实验均值
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupRatio);  // 品牌广告走 r3 模型加权倍数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupRatioWide);  // 品牌广告 r3 宽口径加权倍数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupRatioCpr3);  // 品牌广告 cpr3 加权倍数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupMaxBoost);  // 品牌广告走 r3 模型最高系数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupMaxBoostWide);  // 品牌广告走 r3 模型最高系数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupMaxBoostCpr3);  // 品牌广告走 cpr3 最高系数
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3StrategyPattern);  // 品牌广告 r3 策略模式选择
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3StrategyPatternWide);  // 品牌广告 r3 策略模式选择
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3StrategyPatternCpr3);  // 品牌广告 cpr3 策略模式选择
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandR3ModelPattern);  // 品牌广告 r3 模型分流
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupLooseScore);  // 品牌广告 r3 2.5 均值
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupLooseScoreWide);  // 品牌广告 r3 宽 2.5 均值
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandR3ModelGroupLooseScoreCpr3);  // 品牌广告 cpr3 2.5 均值
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, brandZhuiTouGroupScore);  // 品牌内容追投流量配置
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandSpuR3BoostRatioConfig);  // 商品种草分组权重
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandSpuBoost, false);  // 商品种草生效开关

  // 品牌广告支持 by 操作系统做差异化提权
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, splashSpecifyPlatformBoostRatio);

  // 开屏品效竞价策略实验配置
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, splashBidStrategyConfig)  // 开屏品效竞价门槛信息
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, splashBidUserGroup)  // 开屏品效竞价实验人群组
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, gameAppPackageFilterGroup)  // 游戏已安装过滤
  DEFINE_STRING_INT32_MAP_KCONF(ad.algorithm, brandUnit2optimizationTarget)  // 品牌广告优化目标配置
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, brandFeedRankExpGroupList)  // 信息流广告排序实验人群组
  // 品牌兼顾曝光和转化实验配置
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, balanceBrandDspGroupList)  // 实验人群组
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, balanceBrandDspFlowRatio, 1.0);  // 保量流量占比
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandProfitMarginGroupList);  // 利润率实验人群组
  DEFINE_STRING_INT64_MAP_KCONF(ad.algorithm, brandProfitMaxPesudoCpm);  // 利润率实验组的最大 pesudo_cpm
  DEFINE_STRING_STRING_MAP_KCONF(ad.algorithm, splashModelGroup);  //  开屏实验组配置
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, brandDeepConvPriorityExpGroup)  // 信息流深度转化优先级实验人群组
  DEFINE_DOUBLE_KCONF_NODE(ad.algorithm, brandDeepConvAddPriority, 1000000000.0);
  DEFINE_INT64_KCONF_NODE(ad.algorithm, brandDeepConvMaxEcpm, 1000000000);
  // 直播涨粉实验分组
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, splashFollowModelGroup);
  // 直播订单支付实验分组
  DEFINE_STRING_INT64_MAP_KCONF(ad.algorithm, LiveOrderModelGroup);
  // 品牌频控策略权重分实验人群组
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, feedFrequencyWeightGroupList);
  // 品牌信息流随机投放是否请求模型
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, isRequestPsModel, true);
  // 品牌信息流 5n 是否请求模型，出随机概率
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, isExplore5nPsModel, false);
  // 品牌信息流 5n 刷次，是否请求模型
  DEFINE_PROTOBUF_NODE_KCONF(kconf::FiveN, ad.brandserver, fiveN);
  // 品牌接短剧场景价格配置
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, brandDuanjuUnitPriceConfig);
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, brandDuanjuUnitMaxLtvConfig);
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, brandDuanjuOrderPlanMaxLtvConfig);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandDuanjuOrderPlan, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, brandDuanjuUnitLtvFilter, false);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandDuanjuMaxLtvRatio, 1.0);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, brandDuanjuMaxEcpm, ********);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandDuanjuOrderPlanMaxLtvRatio, 1.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandDuanjuPriceUpperRatio, 2.0);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.brandserver, brandDuanjuPriceAccountUpperRatio);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandDuanjuPriceLowerRatio, 0.5);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.brandserver, brandDuanjuPriceAccountLowerRatio);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, brandMinEcpmForPk, ********);
  // 特殊用户屏蔽广告策略
  DEFINE_PROTOBUF_NODE_KCONF(SpecialUserAndAccounts, ad.brandserver, specialUserAndAccounts);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandSVIPUser);
  // pd 订单优先级
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, pdPriority, -0.1);
  // rank 是否启用 bias 优先选择策略
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableRankSelectByBias, false);
  // 品牌效果 PK，指定品牌 unit 的伪 CPM
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, brandUnitFinallyPkEcpm);
  // T6979220 品牌 PD pk 效果，品牌 unit ecpm 提权系数调整
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandExplorePdPkEcpm, true);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandExplorePdPkEcpmExpRatio);
  DEFINE_INT64_DOUBLE_MAP_KCONF(ad.brandserver, brandExplorePdPkEcpmUnitRatio);
  // T7449716 PD 品效 pk 放开策略实验分析配置方式升级
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, switchBrandExplorePdPkEcomExp, true);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandExplorePdPkEcpmExpUnitSet);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandExplorePdPkEcpmExpUnitRatioConf);

  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandExplorePdPkEcpmMaxRatio, 10.0);
  // 品牌效果出价最大的伪 CPM
  DEFINE_INT64_KCONF_NODE(ad.brandserver, brandMaxPesudoCpm, 200000000);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableFillDspSourceDescription, false)  // 是否支持填充双排信息流文案
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, defaultDspSourceDescription, "活动");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, topicDspSourceDescription, "广告");
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableFillShowAdIn, false)  // 是否支持 show_ad_in 填充
  // ack 发送 kafka 比例
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, brandFeedAckForPsRatio, 1.0)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(
    ad.brandserver,
    liveStreamSchemaUrlFormat,
    "kwai://liveaggregatesquare?sourceType=1&liveSquareSource=12&liveStreamId=%s");
  // 给下游使用时，需要单独处理一下 first_load_more，用 first_load_more 表示精选冷启动的第二刷，可以出品牌广告
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, isPassPageFix, true);
  // 是否启用信息流高级卡片，仅 eyemax 支持
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableAdvancedCard, false);

  // 测试广告只能出在指定分组， 其他广告正常出
  DEFINE_PROTOBUF_NODE_KCONF(kconf::TestUnitsExp, ad.splashserver, testUnitsExp);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::TestUnitsExp, ad.splashserver, testJzUnitsExp);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::TestExploreInteractive, ad.brandserver, testExploreInteractive);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableTestExploreInteractive, false);
  // 开屏请求 ps 白名单用户
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashPsWhitelist);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashCtrUnits);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashCplUnits);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, ctrMtlTestUnitsV2);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adPurchaseTestUnits);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adPurchaseGameTestUnits);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableCtrMtlTestOnExplore, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableGamePurchaseUpdate, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableCtrMtlFeedSplit, false);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashLiveUnits);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashPlay5sUnits);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashIpvUnits);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, previewUnitPesudoCpm, 500000000);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, ctrMtlTestUnitsParamsV2);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, adPurchaseTestUnitsParams);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableEvoCtrFix, false);
  // 直播请求 ps 白名单
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, liveOrderSubmitUnits);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, liveFollowUnits);
  // Ps 访问率，用于访问降级
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, brandSplashPsRankRatio, 1.0);
  // 开屏是否都进行 Ps 访问
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashPsAllRank, false);
  // 对于全局 ctr 进行分组限制
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, adSplashCtrThresholdUpBoundByUserGroup);
  // 进房走深度观看分组
  DEFINE_SET_NODE_KCONF(std::string, ad.brandserver, liveTenPerDepWatchUnits);
  // 未曝光加权触达策略
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, brandUvReachWeight);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandUvReachGroup);
  DEFINE_STRING_INT64_MAP_KCONF(ad.splashserver, brandUvReachOrientation);
  // 激活目标曝光权重衰减
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableConversionUvDecay, false);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashConversionFrequencyWeightMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashCreativeFrequencyWeightMap);
  // 极速版易流失 redis 切换
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableUeNebulaRedisChange, false);
  // 开屏按钮样式
  DEFINE_PROTOBUF_NODE_KCONF(kconf::ClickButtonInfo, ad.splashserver, clickButtonInfo);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::ClickButtonInfo, ad.splashserver, specialClickButtonInfo);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, clickButtonSpecialUnits);

  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableClickButton, true);
  // 低版本支持的最长 eyemax
  DEFINE_INT64_KCONF_NODE(ad.splashserver, longestDurationEyemax, 60000);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, lowestVersionAppEyemaxIos, "9.2.50");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, lowestVersionAppEyemaxAndroid, "9.2.50");
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableLiveShowV2, false);
  // 是否启用互动开屏二期惊喜视频
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enablePlayableFullScreenVideo, false);
  // 高价值人群品牌开屏广告屏蔽
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, highvalueShieldPerson);
  // 高价值机型品牌开屏广告屏蔽
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, highvalueshieldModel, 0.0);
  // 猫狗拼开屏广告屏蔽
  DEFINE_PROTOBUF_NODE_KCONF(kconf::ECommerceSplashShield, ad.splashserver, eCommerceSplashShield);
  // eyemax 支持下载的最低版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, lowestVersionAppEyemaxDownload, "9.3.40");
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, regionRestrictionEyemax)
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, maxIosDownloadVersion,
                                        "15.6");  // ios 下载类的广告版本
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minAndroidDownloadVersion, "10.7.50");
  DEFINE_INT64_KCONF_NODE(ad.splashserver, minKuaishouRewriteTotalCpm, 30000);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, minNebulaRewriteTotalCpm, 30000);
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, minRewriteRatioThr, 0.00001);
  // 客户端 debug 白名单用户
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, clientDebugWhiteSet);
  // header 中携带 UA 信息
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, headerUAWhiteSet);
  // header 中携带 UA 信息
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandHeaderUA, false);
  // 开屏请求模型分组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashUseModelGroupSet);
  // 极速版开屏请求模型分组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashNebulaUseModelGroupSet);
  // 开屏随机投放不进行优选分组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashRandomSelectGroupSet);
  // 开屏频控约束优选分组
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashFrequencyScoreGroupSet);
  // 开屏创意优选开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableCreativePriority, false);
  // 开屏当天排期粒度频控权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashPlanFrequencyWeightMap);
  // 开屏排期粒度 uv 覆盖频控权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashPlanFrequencyUvWeightMap);
  // 开屏一周账号粒度频控权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashAccountFrequencyWeightMap);
  // 开屏最小频控分
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, splashMinFrequencyScore, 0.40);
  // 开屏 uv 覆盖最小频控分
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, splashUvMinFrequencyScore, 0.01);
  // 开屏区域点击权重
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, splashButtonClickWeight, 3.00);
  // 开屏区域点击权重 by user_group
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, adSplashButtonClickWeightByUserGroup);
  // 开屏区域可点特殊商测订单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adSplashButtonTestUnitSet);
  // 开屏区域可点特殊商测订单分组权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, adSplashButtonWeightTestUnit);
  // 开屏区域可点摇动订单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adSplashButtonShakeSet);
  // 开屏区域可点摇动权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, adSplashButtonWeightByShake);
  // 开屏区域可点扭动权重
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, adSplashButtonWeightByWring);
  // 区域可点城市名单
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, splashButtonClickCitySet);

  // 信息流优选级调整实验配置
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandPriorityTestGroupSet);
  // 信息流出价调整实验配置
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandBidTestGroupSet);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, bidPriorityWeight, 1.0);  // 保量流量占比
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableEraseRtaOldCache, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableEyeShowFeedShow, false);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::ShowLivePageStyle, ad.brand, showLivePageStyle);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, defaultActionBarColor, "168FFF");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minFeedshowIosVersion, "9.4.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minXiaoDianSearchBrand, "10.5.20");

  // 信息流透出率模型实验
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, feedDeliveryTestGroupSet);
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, feedDeliveryTestV2GroupSet);
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, feedDeliveryTestUnitSet);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.algorithm, feedDeliveryFixRatioMap);  // 利润率修复下发率问题短期策略
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, feedMtlDeliveryGroupSet);  // 利润率修复下发问题长期策略
  // 开屏小店成交分组实验
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, splashShopOrderUnitGroupSet);
  DEFINE_SET_NODE_KCONF(int64, ad.algorithm, splashShopOrderUnitList);
  // 个性化频控每日上限
  DEFINE_INT64_KCONF_NODE(ad.splashserver, maxKuaishouPersonalizeDayLimit, 12);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, maxNebulaPersonalizeDayLimit, 12);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, posToSplashCtrModule);
  // 一甜频控次数和时间间隔
  DEFINE_INT64_KCONF_NODE(ad.splashserver, m2uSplashAdShowInterval, 3600);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, m2uSplashAdDayLimit, 4);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, kmovieSplashAdShowInterval, 3600);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, kmovieSplashAdDayLimit, 4);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashNegativeControl, true);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enablePackHitOrientation, false);
  // 个性化互动开屏版本控制
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minPersonaliseSplashVersion, "9.5.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minLightInteractiveVersion, "9.6.10");
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, splashDyeingExpAccounts);
  // 快看点频控次数和时间间隔
  DEFINE_INT64_KCONF_NODE(ad.splashserver, KnewsSplashAdShowInterval, 300);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, KnewsSplashAdDayLimit, 11);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKnewsSplashVersion, "3.27");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minPopplayVersion, "9.6.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFeedLiveVersion, "9.4.20");
  DEFINE_PROTOBUF_NODE_KCONF(kconf::UnitSpecialVersion, ad.splashserver, unitSpecialVersion);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::PlatformBlackVersion, ad.splashserver, platformBlackVersion);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minShakeAndSlideVersion, "9.6.50");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minShakeVersion, "9.8.20");  // 摇动开屏版本限制
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFullPopplayVersion, "9.8.50");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minArPopplayVersion, "9.11.10");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minArPopplayIosVersion, "9.11.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minYellowCardVersion, "10.3.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minIosNebulaMiniAppVersion, "10.5.20");
  DEFINE_INT64_KCONF_NODE(ad.brandserver, popArAndroidApiLevel, 21);
  // 一甜、快影、快看点互动版本控制
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minM2uRotateVersion, "2.67");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKmovieRotateVersion, "5.44");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKnewsRotateVersion, "3.42");
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableReplaceJuzhenUrl, false);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minM2uAdxVersion, "2.67");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKmovieAdxVersion, "5.44");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKnewsAdxVersion, "3.42");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minM2uRotateAndShake, "4.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minKmovieRotateAndShake, "6.60");

  // 个性化开屏二期
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, personalRotateDefaultTitle, "扭动解锁神秘惊喜");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, personalShakeDefaultTitle, "摇动解锁神秘惊喜");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, personalRotateIpConstellationTitle, "用户正在扭");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, personalShakeIpConstellationTitle, "用户正在摇");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, personalRotateTimeTitle, ", 一起扭");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, personalShakeTimeTitle, ", 一起摇");
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enablePersonalUsersProperties, true);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minPersonalV2IosVersion, "10.1.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minPersonalV2AndroidVersion, "10.1.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minFallingEggsVersion, "10.7.50");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minFallingEggsAndroidVersion, "10.9.10");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, minInteractiveBoxVersion, "10.8.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minBrokenFrameVersion, "10.9.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minBrokenFrameInterActiveVersion, "11.10.10");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFeedInteractiveBoxVersion, "10.9.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minLiveReservationVersion, "10.10.50");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minLevitatingBallVersion, "11.1.10");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minSuperSearchBrand, "11.1.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minCategorySearchBrand, "12.4.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFamilySearchBrand, "11.4.10");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFloatFrameVersion, "11.2.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minProfileVersion, "11.3.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minTorchVersion, "11.4.10");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFeedIPersonalVersion, "11.1.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFeedGraphicVersion, "11.7.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFallingEggsExpandVersion, "11.7.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minAppDowmloadVersion, "11.7.50");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minCoinTokenVerson, "11.8.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minFloatFrameDowntimeVersion, "11.9.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minStoryVideoVersion, "11.9.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minSlotStyleVersion, "11.10.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minMerchantSearchBrand, "11.11.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minLightCustomVersion, "11.11.40");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minSimpleLiveRoomRealtimePersonVersion, "12.0.20");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minSearchShieldUserCard, "12.0.20");
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrokenFrameVersion, true);
  //心动盒子点击到落地页时间间隔
  DEFINE_INT32_KCONF_NODE(ad.splashserver, showWebDelayTimeMs, 5000);
  // 悬浮球广告频控间隔时间，单位：秒
  DEFINE_INT64_KCONF_NODE(ad.brandserver, levitatingBallShowInterval, 1800);
  // 悬浮球广告频控每天最大展示次数
  DEFINE_INT32_KCONF_NODE(ad.brandserver, levitatingBallMaxShowTimesOneDay, 6);
  // 悬浮球过滤实验账户名单
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, searchLevitatingBallAccountBlackList);
  // 获取广告主最近 N 条公开作品
  DEFINE_INT32_KCONF_NODE(ad.brandserver, getAuthorPhotoLimitNum, 10);
  DEFINE_INT32_KCONF_NODE(ad.brandserver, getFamilyMatrixPhotoLimitNum, 10);
  DEFINE_INT32_KCONF_NODE(ad.brandserver, getGameSearchPhotoLimitNum, 10);


  DEFINE_BOOL_KCONF_NODE(ad.brandserver, brandDauLossStrategy, false);
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, brandDauLossStrategyMap);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, brandDauLossGroupWeight);

  // 展示作品最小数量
  DEFINE_INT32_KCONF_NODE(ad.brandserver, minShowPhotoNum, 4);
  // 定时拉取广告主最近作品间隔，单位：秒
  DEFINE_INT64_KCONF_NODE(ad.brandserver, GetAuthorPhotoInterval, 60);
  // P 页定时获取曝光量间隔，单位：秒
  DEFINE_INT64_KCONF_NODE(ad.brandserver, getProfileUnitImpressionInterval, 10);
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, profileUnitToImpression);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, profileImpressionRatio, 1.05);

  // photo 定向
  DEFINE_STRING_INT64_MAP_KCONF(ad.brandserver, photoToUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, photoTestUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, exploreFollowGroupSet);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashUnloginUser, false);
  DEFINE_STRING_INT64_MAP_KCONF(ad.splashserver, specialUnitDuration);
  DEFINE_SET_NODE_KCONF(std::string, ad.brandserver, avoidKboxBizName);
  DEFINE_SET_NODE_KCONF(std::string, ad.brandserver, noLivingAvoidKboxBizName);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, firstBrushAmountControlMap);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::UnitRuleFilter, ad.splashserver, unitRuleFilter);
  // 强制设置 hot time 概率
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, enableHotTimeRate, 0.0);
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, enableAdNoImpression, 0.0);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, pullRefreshTimeminute, 120);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adxDebugCreative);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, redisDegradedRatio);
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, minVersionInteractiveVersionMap);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, shiledSplashVersion);
  DEFINE_SET_NODE_KCONF(std::string, ad.brandserver , shiledBrandVersion);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, shiledSplashDeviceMod);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, shieldFoldDeviceMod);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, foldPhoneUnitStyleWhiteList);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, foldPhoneInteractiveBlackList);
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, minIosVersionInteractiveVersionMap);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableCreativeUp, true);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableRandomSamplingPerf, true);
  DEFINE_SET_NODE_KCONF(int64, ad.frontserver, simplifyTraceLogUserWhiteSet);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, brandSamplingPerfRatio, 100);  // 打点采样率
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minTvcVersion, "10.2.50");  // tvc 最低版本号
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minTVCIos, "11.2.50");  // mini tvc 最低版本号
  DEFINE_STRING_STRING_MAP_KCONF(ad.splashserver, apiVersionToOsVersionMap);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, RealtimeShowClientVersion, "10.3.30");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, m2uRealtimeShowClientVersion, "2.9.4");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, kmovieRealtimeShowClientVersion, "2.9.4");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, knewsRealtimeShowClientVersion, "2.9.4");
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, realtimeShowWhiteUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, enableRealtimeShowWhiteUser);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandWeiliangUnitId);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandWeiliangOderId);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandWeiliangAccountId);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, unloginUserNoOrderControl, true);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, redisFailedNoOrderControl, true);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableSplashFreAction, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, disableBrandCdn, false);
  DEFINE_INT64_KCONF_NODE(ad.splashserver, maxTraceCreativePerUnit, 10);  // 打点采样率
  DEFINE_INT32_KCONF_NODE(ad.splashserver, adxQualtityListLen, 5);
  // 智能定向前缀配置
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandSmartCrowdPrefixByGroup);
  // adx 质量分对应系数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashAdxQualityCofMap);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::SplashAdxQualityDspCofMap, ad.splashserver, splashAdxQualityDspCofMap);
  // adx unit 维度质量分对应系数
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, splashAdxQualityCofByUnit);
  // adx 质量分白名单
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, adxQualityUnitWhiteSet);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.splashserver, testAdxQuality2Score);
  // plc
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandPlcPhotoWhiteList);
  DEFINE_STRING_INT32_MAP_KCONF(ad.splashserver, unit2DarkLightMode);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableAppLightInterActive, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableExploreAdxReqAddOsType, true);
  DEFINE_INT32_KCONF_NODE(ad.brandserver, appLightInterActiveType, 0);
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, recoredPrefetchTime, 0.00001);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, maxCreativeNumPS, 5);  // 请求 PS 最大创意数
  DEFINE_INT32_KCONF_NODE(ad.splashserver, sendBrandPrefetchPsRatio, 0);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, prefetchMaxCreativeNumPS, 5);  // 预加载请求 PS 最大创意数
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandNeedPrefetchPsUnit);
  DEFINE_INT32_KCONF_NODE(ad.splashserver, maxCachePsScoreNum, 150);
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, sendPrefetchKafakaRatio, 100);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, asyncGetPrefetchPs, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enablePersonalNoAds, true);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, disableMerchantCoronaFlow, false);
  DEFINE_INT32_KCONF_NODE(ad.brandserver, maxBrandRiskControlCommand, 5000);  // 预加载请求 PS 最大创意数
  DEFINE_INT32_KCONF_NODE(ad.brandserver, brandRiskContrlReloadTime, 60);  // 预加载请求 PS 最大创意数
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableRiskControl, true);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::UnitFrequencyConf, ad.splashserver, unitFrequencyConf);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::OptimizationTargetFrequencyConf, ad.splashserver,
    optimizationTargetFrequencyConf);
  // 快手小店买家首页接品牌广告版控
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minMerchantFeedVersion, "10.8.50");
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, adxIpdxWhiteAccount);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, adxIpdxWhiteUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, adxIpdxBlackUnit);
  DEFINE_DOUBLE_KCONF_NODE(ad.splashserver, degradRatio, 0);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableInteractiveStyleOptimize, true);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, abNoBrandQuery);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, abNoBrandQueryV2);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandGameRandomPsUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandGameRandomPsOptTarget);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, plcPhotoPageSet);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::BrandPlcPosAdmit, ad.splashserver, brandPlcPosAdmit);
  DEFINE_STRING_INT64_MAP_KCONF(ad.splashserver, whiteUnitToSensitive);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::InteractionSensitiveConf, ad.splashserver, interactionSensitiveConf);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableInteractionSensitive, false);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::BrandUnitCpmConf, ad.splashserver, brandUnitCpmConf);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::BrandCityFrequencyConf, ad.splashserver, brandCityFrequencyConf);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::InspireUnitConf, ad.brand, inspireUnitConf);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableOrientationForSplashInspire, false);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, brandBidTestSet);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, rtaNoIdfaFiterDspSet);
  DEFINE_BOOL_KCONF_NODE(ad.algorithm, fixProfitProblem, true)  // 修复线上问题
  // P 页广告控量配置
  DEFINE_PROTOBUF_NODE_KCONF(UnitImpressionControl, ad.brandserver, unitImpressionControl);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, enableHotStartEyemaxType);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, enableHotEyemaxStartPage);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver, hotStartEyemaxBlackCity);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, hotEyemaxClientVersion, "11.5.10");
  DEFINE_SET_NODE_KCONF(int64, firefly.brand, LBSUnitSet);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableSelectByDistance, true)  // 允许 poi
  DEFINE_INT32_KCONF_NODE(ad.brandserver, maxPoiSizePerCreative, 100);  // 每个创意最多的 Poi 个数
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableUserPoiInfo, false);  // poi 样式开关
  // 品牌激活大 R 策略验证
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, brandPayLevelSwitch, false);
  DEFINE_STRING_STRING_MAP_KCONF(ad.brandserver, brandUnitGroup2industry);
  DEFINE_STRING_DOUBLE_MAP_KCONF(ad.brandserver, brandPayLevel2Weight);
  // 开屏接简易直播间开关
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, splashEyemaxSimpleRoom, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableStoryVideoInTube, true);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableBrandPaid2, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, disableNegativePolicySkip, true);
  DEFINE_SET_NODE_KCONF(std::string, ad.splashserver,
                        brandLastShowFreOptimizeSet)  // 优化目标白名单开启刚看过此广告
  DEFINE_INT64_KCONF_NODE(ad.splashserver, brandUnitLastShowFreInterval, 300);      // unit 看过频控 5 min
  DEFINE_INT64_KCONF_NODE(ad.splashserver, brandCreativeLastShowFreInterval, 600);  // unit 看过频控 10 min
  DEFINE_INT64_KCONF_NODE(ad.brandserver, fiveNProductDefaultNum, 50);  // unit 看过频控 10 min
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, poiRedisName, "brandPoiInfo");
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, brandTableSchemaBuildKey, "");
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableBrandNewPoi, true);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableBrandSearchBallGuiyin, true);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::UnitToPackagetTarget, ad.splashserver, unitToPackagetTarget);
  DEFINE_KCONF_NODE_LOAD(AdBrandHideUserInfo, ad.brand, adBrandHideUserInfo);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableLiveShowAttentionButton, false);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, minLightCustomVersionV2, "12.1.10");
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, showAttentionButtonUnit);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, industryIdFrequencySet);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::EyemaxChooseLimitTest, ad.splashserver, eyemaxChooseLimitTest);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, realtimReplacePrefetchUserId);
  DEFINE_SET_NODE_KCONF(int64, ad.splashserver, realtimReplacePrefetchUnit);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableBrandAutoAd, false);  // 品牌自助投放开关
  DEFINE_STRING_BOOL_MAP_KCONF(ad.brandserver, adBrandResponseValidCheck);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::SplashFrequencyConf, ad.splashserver, splashFrequencyConf);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::RepMaterialConf, ad.splashserver, repMaterialConf);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::RepMaterialConf, ad.splashserver, simMaterialConf);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableUpdateLivingShowV3, true);
  DEFINE_PROTOBUF_NODE_KCONF(SplashAdxQualityCof, ad.splashserver, splashAdxQualityCofV2);
  DEFINE_KCONF_NODE_LOAD(ModifyPrefetchConf, ad.splashserver, modifyPrefetchConf);
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, dpMacroReplaceWhiteList);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableMacroReplaceForCallbackOptimization, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableAllUrlMacroReplace, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableNotReplaceRequestId, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableEventAppInvokedNotReplaceRequestId, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, fixIntelligentOrientation, true);
  DEFINE_SET_NODE_KCONF(int64, ad.brand, notReplaceRequestIdList);
  DEFINE_INT64_KCONF_NODE(ad.brandserver, realShowInfoTimeInterval, 1800);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::CommonUnitStyleVersion, ad.splashserver, commonUnitStyleVersion);
  DEFINE_PROTOBUF_NODE_KCONF(CreativeVersionControlSceneConfig,
                             ad.brandserver,
                             creativeVersionControlSceneConfig);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableExploreAdxLiveInfo, false);
  DEFINE_DOUBLE_KCONF_NODE(ad.brandserver, searchCpmUnitImpressionRatio, 1.0);  // 品专 CPM 投放方式曝光比例
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, unitPhotoImpressionTemplateId,
                                        "ad_brand-brand_online_flow_control_engine");
  DEFINE_INT64_KCONF_NODE(ad.brandserver, zhuitouPhotoShowNum, 10);  // 最多记录的追投自然作品条数
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, disableExploreNebulaIOSAds, false);
  // 鸿蒙流量相关
  DEFINE_TAILNUMBERV2_KCONF(ad.splashserver, splashHarmonyWhiteAccountTail, "100;;;");
  DEFINE_PROTOBUF_NODE_KCONF(kconf::SplashHarmonyUnitControl, ad.splashserver, splashHarmonyUnitControl);
  // T7507471 商业化品牌安全需求
  DEFINE_PROTOBUF_NODE_KCONF(kconf::AccountSensitiveTagConfig, ad.brandserver, accountSensitiveTagConfig);
  // T6625907 矩阵开屏-传输品牌投放 ecpm
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableMatrixFillPriceM2u, false);
  DEFINE_BOOL_KCONF_NODE(ad.splashserver, enableMatrixFillPriceKmovie, false);
  DEFINE_INT64_INT64_MAP_KCONF(ad.splashserver, matrixFillPriceAccountOverride);
  DEFINE_INT64_INT64_MAP_KCONF(ad.splashserver, matrixFillPriceUnitOverride);
  // T6641699 开屏广告素材自动延期 N 小时
  DEFINE_PROTOBUF_NODE_KCONF(CreativeExpirationTime, ad.buyer, creativeExpirationTimeConfig);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.splashserver, adBrandCreativeExpirationRedis,
                                        "adBrandCreativeExpirationTime");
  // T6960977 信息流 5n 刷次放开实验
  DEFINE_INT64_INT64_MAP_KCONF(ad.brandserver, brand5nBrushNumUnitConf);
  // T7166630 信息流轻定制搜索品专预览优化
  DEFINE_SET_NODE_KCONF(int64, ad.brandserver, brandPreviewUserBlackList);
  // T7360572 品牌开屏 CDN 域名容灾演练问题修复
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, brandFixCdnInvalid, false);
  // T6861172 品牌广告双列内流账户黑名单
  DEFINE_SET_NODE_KCONF(int64_t, ad.brandserver, brandInnerExploreAccountBlackList);
  DEFINE_SET_NODE_KCONF(int64_t, ad.brandserver, brandInnerExploreTestUnitSet);

  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableFixSchemaUrlForAll, false);
  DEFINE_SET_NODE_KCONF(int64_t, ad.brandserver, fixSchemaUrlPosIds);
  DEFINE_PROTOBUF_NODE_KCONF(kconf::OsVersionFilterConfig, ad.brandserver, brandOsVersionFilterConfig);
  DEFINE_KCONF_NODE_LOAD(TransDspOrientationConfig, ad.brand, transDspOrientation);
  DEFINE_SET_NODE_KCONF(int64_t, ad.brandserver, searchFoldModBlackStyleType);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableGpuMachinesOpt, false);
  DEFINE_BOOL_KCONF_NODE(ad.brandserver, enableGpuMachinesRandomRequestOpt, false);
  DEFINE_STRING_KCONF_NODE_WITH_DEFAUTL(ad.brandserver, adBrandChargeInfoMask, "");
  DEFINE_DOUBLE_KCONF_NODE(ad.adRank3, splashRouterDoubleDiffEpsilon, 1e-5);
};
}  // namespace ad_brand
}  // namespace ks
