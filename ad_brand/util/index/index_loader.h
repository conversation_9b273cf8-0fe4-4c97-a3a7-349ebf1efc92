#pragma once

#include <kenv/service_meta.h>
#include <stdio.h>
#include <sys/stat.h>
#include <sys/types.h>

#include <algorithm>
#include <fstream>
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/common/stl_logging.h"
#include "base/encoding/base64.h"
#include "teams/ad/ad_base/src/kess/ad_kess_client.h"
#include "teams/ad/ad_base/src/kess/macros.h"
#include "teams/ad/ad_brand/util/common/ad_table_key_util.h"
#include "teams/ad/ad_brand/util/common/perf.h"
#include "teams/ad/ad_brand/util/index/ad_brand_deliver_unit.h"
#include "teams/ad/ad_brand/util/index/ad_brand_deliver_creative.h"
#include "teams/ad/ad_brand/util/kconf/kconf.h"
#include "teams/ad/ad_proto/kuaishou/ad/brandv2/info/ad_brand_info_query_service.kess.grpc.pb.h"
#include "teams/ad/ad_table/table_env/loader.h"
#include "third_party/protobuf_v3/src/google/protobuf/text_format.h"

namespace ks {
namespace ad_brand {

using kuaishou::ad::brandv2::info::kess::AdBrandQueryRpcService;

class IndexLoader {
 public:
  // mahang: 方便测试，加载本地伪造数据
  static bool LoadAds(AdBrandAvaibleAdResponse* resp, const std::string& txt_name) {
    struct stat st;
    if (stat(txt_name.c_str(), &st) == 0) {
      FILE* file = fopen(txt_name.c_str(), "rb");
      if (file != nullptr) {
        char* buf = new char[st.st_size + 1];
        memset(buf, 0, st.st_size);
        fread(buf, 1, st.st_size, file);
        resp->Clear();
        ::google::protobuf::TextFormat::ParseFromString(buf, resp);
        delete[] buf;
        buf = nullptr;
      }
    }

    return true;
  }

  static bool LoadFromAdtable(AdBrandAvaibleAdResponse* resp) {
    auto data_frame_env = ks::ad_table::AdTableInstance::GetDB().GetDataframeEnv();
    auto unit_df = data_frame_env->Lookup("ad_brand_deliver_unit");
    if (!unit_df) {
      LOG(ERROR) << "unit not find";
      return false;
    }
    auto ceative_data_frame_env = ks::ad_table::AdTableInstance::GetDB().GetDataframeEnv();
    auto creative_df = data_frame_env->Lookup("ad_brand_deliver_creative");
    if (!creative_df) {
      LOG(ERROR) << "creative not find";
      return false;
    }
    std::vector<int64_t> unit_vec;
    auto& col = unit_df->Column("id");
    for (int i = 0; i < unit_df->RowSize(); ++i) {
      unit_vec.emplace_back(unit_df->GetValue<int64_t>(unit_df->Row(i), col));
    }
    std::sort(unit_vec.begin(), unit_vec.end(), std::greater<int64_t>());
    for (const auto& unit_id : unit_vec) {
      auto* unit_info_index = unit_df->Find(unit_id).GetStruct<ks::ad_table::ad_brand_deliver_unit>();
      if (unit_info_index == nullptr) {
        LOG(ERROR) << "unit_id: " << unit_id << " unit_info_index is null";
        continue;
      }
      const std::string* org_msg_ptr = &unit_info_index->origin_msg();
      if (org_msg_ptr == nullptr) {
        LOG(ERROR) << "org_msg_ptr is null";
        continue;
      }
      std::string org_msg_decode = "";
      if (!base::Base64Decode(*org_msg_ptr, &org_msg_decode)) {
        LOG(ERROR) << "decode msg failed: " << *org_msg_ptr;
        continue;
      }
      ks::ad_brand::Doc doc;
      if (!doc.ParseFromString(org_msg_decode)) {
        LOG(ERROR) << "parse msg failed: " << org_msg_decode;
        continue;
      }
      std::unordered_map<int64_t, std::vector<std::shared_ptr<Creative>>> unit_to_creative_infos;
      auto creative_unit_df =
          creative_df->Eq("ad_brand_deliver_creative.unit_id", Gen128Key(doc.unit_info().unit_id()));
      auto& col = creative_unit_df.Column("origin_msg");
      for (int i = 0; i < creative_unit_df.RowSize(); ++i) {
        auto row_id = creative_unit_df.Row(i);
        const auto* brand_ptr =
            creative_unit_df.GetRowWrapper(row_id).GetStruct<ks::ad_table::ad_brand_deliver_creative>();
        if (brand_ptr == nullptr) {
          LOG(ERROR) << "brand ptr is null";
          continue;
        }
        const std::string* creative_org_msg_ptr = &brand_ptr->origin_msg();
        if (creative_org_msg_ptr == nullptr) {
          LOG(ERROR) << "org_msg_ptr is null";
          continue;
        }
        std::string creative_org_msg_decode = "";
        if (!base::Base64Decode(*creative_org_msg_ptr, &creative_org_msg_decode)) {
          LOG(ERROR) << "decode msg failed: " << *creative_org_msg_ptr;
          continue;
        }
        auto creative_info_ptr = std::make_shared<Creative>();
        if (!creative_info_ptr.get()->ParseFromString(creative_org_msg_decode)) {
          LOG(ERROR) << "parse msg failed: " << org_msg_decode;
          continue;
        }
        int64_t unit_id = creative_info_ptr->unit_id();
        unit_to_creative_infos[unit_id].emplace_back(creative_info_ptr);
      }
      for (auto [unit_id, creative_vec] : unit_to_creative_infos) {
        std::sort(creative_vec.begin(), creative_vec.end(),
                  [](const auto& c1, const auto& c2) { return c1->creative_id() > c2->creative_id(); });
        for (const auto& creative_index : creative_vec) {
          doc.add_creative_info()->CopyFrom(*creative_index);
        }
      }
      resp->add_ad_full_info_list()->CopyFrom(doc);
    }
    // PROD 线上环境不落索引文件
    if (!ks::infra::KEnv::GetKWSInfo() || (ks::infra::KEnv::GetKWSInfo()->GetServiceStage() != "PROD")) {
      std::ofstream ads_txt("table_ads.txt", std::ios_base::out | std::ios_base::trunc);
      if (ads_txt.is_open()) {
        ads_txt << resp->Utf8DebugString();
        ads_txt.close();
      }
    }
    return true;
  }
};

}  // namespace ad_brand
}  // namespace ks
