#include "teams/ad/ad_brand/processor/after_visit_graph_mixer.h"

#include <string>
#include <unordered_map>
#include <unordered_set>

#include "perfutil/perfutil.h"
#include "teams/ad/ad_base/src/klog/ad_ylog.h"
#include "teams/ad/ad_base/src/klog/ad_engine_chain_trace_log.h"
#include "teams/ad/ad_base/src/redis/kconf_redis.h"
#include "teams/ad/engine_base/dragon_node/ad_rpc_proxy.h"
#include "teams/ad/engine_base/dragon_node/skydome_node.h"
#include "teams/ad/ad_brand/util/kconf/kconf.h"
#include "teams/ad/ad_brand/util/common/ad_reponse_valid_check.h"
#include "teams/ad/ad_brand/engine/server/init.h"
#include "teams/ad/ad_brand/util/trace/trace_manager.h"

DECLARE_bool(open_chain_log);

namespace ks::ad_brand::dragon {

void AfterVisitGraph::Mix(ks::platform::AddibleRecoContextInterface* c) {
  auto ps_context = ks::engine_base::dragon::SkydomeBaseNode::GetAdContext<AdContext>(c);
  if (!ps_context) {
    return;
  }
  ContextData& context_data = *(ps_context->GetMutableContextData<ContextData>());

  auto& brand_request = *(c->GetMutablePtrCommonAttr<::kuaishou::ad::AdRequest>
                            (ks::engine_base::dragon::AdRpcProxyPrepare::kContextRequestKey));
  auto* brand_response = c->GetMutablePtrCommonAttr<::kuaishou::ad::AdResponse>
                            (ks::engine_base::dragon::AdRpcProxyPrepare::kContextResponseKey);

  if (!brand_response) {
    LOG(ERROR) << "brand_response is nullptr";
    return;
  }

  ValidCheck(&context_data);

  // 打点
  // 后处理
  context_data.node_cost_total["total_cost"] = base::GetTimestamp() - context_data.start_ts;
  if (context_data.is_splash_request_) {
    SplashAfterProcess(context_data);
  } else if (context_data.is_search_request_ || context_data.is_eapi_search_request_) {
    SearchAfterProcess(context_data);
  } else if (context_data.is_content_request_ || context_data.is_tube_request_) {
    DotContentResponse(context_data);
  } else if (context_data.is_corona_request_) {
    DotCoronaResponse(context_data);
  } else {
    ExploreAfterProcess(context_data);
  }
  YLOG_PB("adBrandServer", "first", context_data.llsid_, context_data.user_id_, \
            ks::ad::ylog::LOG_TYPE::response, *brand_response);
  // send chain kafka
  if (ks::ad_base::AdKessClient::Instance().IsTestEnv() && FLAGS_open_chain_log && \
    ks::infra::kenv::ServiceMeta::GetRpcTraceContext().GetRpcStressTestCtx().GetBizName().empty()) {
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog("request", true, \
    "adBrandServer", "", context_data.user_id_, context_data.llsid_, \
    ks::ad_base::massage_to_json(brand_request));
    ks::ad_base::AdEngineChainTraceLogManager::GetInstance()->SendTraceLog("response", true, \
    "adBrandServer", "", context_data.user_id_, context_data.llsid_, \
    ks::ad_base::massage_to_json(*brand_response));
  }
  if (BrandKconfUtil::fixBrandDatafreamUpdate()) {
    context_data.data_frame_env.reset();
  }
  return;
}

void AfterVisitGraph::ValidCheck(ContextData* context_data) {
  AdResponseValidCheck checker(context_data);
  auto* mutable_response = context_data->ad_response_->mutable_result();
  for (auto iter = mutable_response->begin(); iter != mutable_response->end();) {
    bool valid = checker.IsVaildAdResponse(*iter);
    if (!valid) {
      iter = mutable_response->erase(iter);
    } else {
      ++iter;
    }
  }
}

void AfterVisitGraph::SplashAfterProcess(const ContextData& context_data) {
  // 打点
  DotResponse(context_data);
  LogInfo(context_data, context_data.ad_response_);
}

void AfterVisitGraph::SearchAfterProcess(const ContextData& context_data) {
  // 搜索打点
  DotSearchResponse(context_data);
  LogInfo(context_data, context_data.ad_response_);
}

void AfterVisitGraph::DotContentResponse(const ContextData& context_data) {
  for (const auto& ad_result : context_data.ad_response_->result()) {
    int64_t pos_id = ad_result.ad_deliver_info().universe_ad_deliver_info().pos_id();
    int64_t unit_id = ad_result.ad_deliver_info().ad_base_info().unit_id();
    int64_t creative_id = ad_result.ad_deliver_info().ad_base_info().creative_id();
    std::string live_stream_id =
        ad_result.ad_deliver_info().ad_base_info().brand_ad_data_v2().t_v_c_info().live_stream_id();
    context_data.sampling_perf_->Count(1, "ad.brand_content", "ad_count", "pos",
                                       std::to_string(pos_id));
    context_data.sampling_perf_->Count(1, "ad.brand_content", "ad_count", "unit",
                                       std::to_string(unit_id));
  }
  LogInfo(context_data, context_data.ad_response_);
}

void AfterVisitGraph::DotCoronaResponse(const ContextData& context_data) {
  for (const auto& ad_result : context_data.ad_response_->result()) {
    int64_t pos_id = ad_result.ad_deliver_info().universe_ad_deliver_info().pos_id();
    int64_t unit_id = ad_result.ad_deliver_info().ad_base_info().unit_id();
    context_data.sampling_perf_->Count(1, "ad.brand_corona", "ad_count", "pos",
                                       std::to_string(pos_id));
    context_data.sampling_perf_->Count(1, "ad.brand_corona", "ad_count", "unit",
                                       std::to_string(unit_id));
  }
  LogInfo(context_data, context_data.ad_response_);
}

void AfterVisitGraph::ExploreAfterProcess(const ContextData& context_data) {
  // 信息流打点
  DotExploreResponse(context_data);
  LogInfo(context_data, context_data.ad_response_);
}

void AfterVisitGraph::DotResponse(const ContextData& context_data) {
  for (const auto& ad_result : context_data.ad_response_->result()) {
    std::unordered_set<std::string> res_splash_ids;
    std::string res_splash_id =
      ad_result.ad_deliver_info().ad_base_info().splash_info().base_info().splash_id();
    context_data.sampling_perf_->Count(1, "ad.ad_brand", "ad_count", "pos",
                                       std::to_string(context_data.pos_id_),
                                       std::to_string(context_data.splash_request_type_));
    context_data.sampling_perf_->Count(1, "ad.ad_brand", "ad_count", "unit",
                                       std::to_string(ad_result.ad_deliver_info().ad_base_info().unit_id()),
                                       std::to_string(context_data.splash_request_type_),
                                       std::to_string(context_data.is_fold_device_));
    if (ad_result.ad_deliver_info().ad_base_info().splash_info().splash_ad_show_type() ==
        kuaishou::ad::AdDataV2_SplashInfo::REALTIME_SHOW) {
      context_data.sampling_perf_->Count(
          1, "ad.ad_brand", "ad_count", "realtime_show",
          std::to_string(ad_result.ad_deliver_info().ad_base_info().unit_id()));
    }
    if (res_splash_ids.count(res_splash_id) > 0) {
    } else {
       res_splash_ids.insert(res_splash_id);
    }
  }
  context_data.sampling_perf_->Interval(context_data.ad_response_->result().size(),
      "ad.ad_brand", "ad_result_size", std::to_string(context_data.splash_request_type_),
      std::to_string(context_data.pos_id_), context_data.platform_);

  if (context_data.ad_response_->result().size() > 0) {
    ks::infra::PerfUtil::CountLogStash(1, "ad.ad_brand", "send_node",
      "send", std::to_string(context_data.splash_request_type_),
      std::to_string(context_data.pos_id_), context_data.platform_);
  }
}

void AfterVisitGraph::SetDiagnoseTraceInfo(const ContextData& context_data) {
  if (BrandKconfUtil::DiagnoseUserSet()->count(context_data.user_id_) <= 0 &&
      !context_data.preview_setting_map_->FindUser(context_data.user_id_)) {
    return;
  }
  std::string trace_json = TRACE_LOG.ToString();

  // eg: 1512089902_splash_realtime
  // eg: 1512089902_splash_prefetch
  static const char redis_key_format[] = "$0_splash_$1";
  std::string key("");
  std::string request_type("");
  if (context_data.splash_request_type_ == SplashRequestType::REALTIME) {
    request_type = "realtime";
  } else {
    request_type = "prefetch";
  }

  key = absl::Substitute(redis_key_format, std::to_string(context_data.user_id_), request_type).data();
  if (context_data.is_feed_request_) {
    key = absl::Substitute("$0_feed", std::to_string(context_data.user_id_)).data();
  }
  auto* client = ks::ad_base::KconfRedis::Instance().GetAdRedisClient("adBrandDiagnose");
  if (!client) {
    LOG(WARNING) << "no redis client for adBrandDiagnose";
    return;
  }
  client->ListPushLeft(key, trace_json);
  client->Expire(key, 86400*60);
  VLOG(4) << "brand_diagnose redis key: " << key << " value: " << trace_json;
}

void AfterVisitGraph::LogInfo(const ContextData& context_data,
                                    kuaishou::ad::AdResponse* brand_response) {
  brand_response->mutable_debug_str()->set_debug_str(context_data.trace_info_->ToString());
  VLOG(4) << "llsid is " << context_data.llsid_ << " Debug InFo " << context_data.trace_info_->ToString();
  if (context_data.is_user_trace_white_list) {
    LOG(INFO) << "user id: " << context_data.user_id_ << "llsid is " << context_data.llsid_ << " Debug InFo "
              << context_data.trace_info_->ToString();
  }
  VLOG(4) << "TRACE LOG is: " << TRACE_LOG.ToString();
  if (brand_response->result_size()) {
    for (auto& kv : context_data.node_cost_total) {
      context_data.sampling_perf_->Interval(kv.second, "ad.ad_brand", "noempty_time_cost", kv.first,
                                      std::to_string(context_data.pos_id_),
                                      std::to_string(context_data.splash_request_type_));
    }
    for (auto& kv : context_data.node_cost_process) {
      context_data.sampling_perf_->Interval(kv.second, "ad.ad_brand", "noempty_time_cost.process", kv.first,
                                            std::to_string(context_data.pos_id_),
                                            std::to_string(context_data.splash_request_type_));
    }
    for (auto& kv : context_data.strategy_cost_total) {
      context_data.sampling_perf_->Interval(kv.second, "ad.ad_brand", "strategy_time_cost", kv.first,
                                      std::to_string(context_data.pos_id_),
                                      std::to_string(context_data.splash_request_type_));
    }
    for (auto& kv : context_data.strategy_cost_process) {
      context_data.sampling_perf_->Interval(kv.second, "ad.ad_brand", "strategy_time_cost.process", kv.first,
                                      std::to_string(context_data.pos_id_),
                                      std::to_string(context_data.splash_request_type_));
    }
  } else {
    context_data.sampling_perf_->Count(1, "ad.ad_brand", "response_ad_list_empty");
  }

  if (context_data.is_feed_request_ || context_data.is_tube_inspire_request_) {
    TRACE_LOG.EndTrace(SplashTraceLogItem::SenseType::FEED);
  } else if (context_data.is_splash_request_) {
    TRACE_LOG.EndTrace(SplashTraceLogItem::SenseType::SPLASH);
  } else if (context_data.is_search_request_) {
    TRACE_LOG.EndTrace(SplashTraceLogItem::SenseType::SEARCH);
  }
}

void AfterVisitGraph::DotSearchResponse(const ContextData& context_data) {
  for (const auto& ad_result : context_data.ad_response_->result()) {
    int64_t pos_id = ad_result.ad_deliver_info().universe_ad_deliver_info().pos_id();
    int64_t unit_id = ad_result.ad_deliver_info().ad_base_info().unit_id();
    int64_t creative_id = ad_result.ad_deliver_info().ad_base_info().creative_id();
    context_data.sampling_perf_->Count(1, "ad.brand_search", "ad_count", "pos",
                                       std::to_string(pos_id));
    context_data.sampling_perf_->Count(1, "ad.brand_search", "ad_count", "unit",
                                       std::to_string(unit_id));
    context_data.sampling_perf_->Count(1, "ad.brand_search", "ad_count", "search_key",
                                       context_data.search_key_);
  }
}

void AfterVisitGraph::DotExploreResponse(const ContextData& context_data) {
  for (const auto& ad_result : context_data.ad_response_->result()) {
    int64_t pos_id = ad_result.ad_deliver_info().universe_ad_deliver_info().pos_id();
    int64_t unit_id = ad_result.ad_deliver_info().ad_base_info().unit_id();
    int64_t creative_id = ad_result.ad_deliver_info().ad_base_info().creative_id();
    context_data.sampling_perf_->Count(1, "ad.brand_explore", "ad_count", "pos",
                                       std::to_string(pos_id));
    context_data.sampling_perf_->Count(1, "ad.brand_explore", "ad_count", "unit",
                                       std::to_string(unit_id));
    context_data.sampling_perf_->Count(1, "ad.ad_brand", "send_node",
        "send", std::to_string(pos_id), context_data.platform_);
  }
}

}  // namespace ks::ad_brand::dragon
