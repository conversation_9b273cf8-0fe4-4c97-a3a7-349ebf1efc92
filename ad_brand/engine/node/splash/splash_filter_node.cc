#include "teams/ad/ad_brand/engine/node/splash/splash_filter_node.h"

#include <string>
#include <unordered_set>
#include <vector>

#include "glog/stl_logging.h"
#include "ks/base/abtest/abtest_instance.h"
#include "teams/ad/ad_brand/engine/strategy/strategy_manager.h"
#include "teams/ad/ad_brand/util/adx/adx_manager.h"
#include "teams/ad/ad_brand/util/cache_loader/brand_creative_rep_material_dict.h"
#include "teams/ad/ad_brand/util/common/perf.h"

using ks::abtest::AbtestInstance;

namespace ks {
namespace ad_brand {

bool SplashFilterNode::ProcessInner() {
  session_data_ = context_->GetMutableContextData<ContextData>();
  PROCESS_TIME_RECORD;  // 耗时监控
  VLOG(4) << "[Node] Filter Node";
  // 筛选
  if (session_data_->splash_request_type_ == SplashRequestType::NEW_PREFETCH) {
    SelectPrefetchAds();
  } else {
    // 获取所有能够实时展现的 unit
    FetchRealtimeShowUnit();
    SelectRealTimeAds();
    SelectRealTimeCachedAds();
    SimMaterialFilter();
  }
  TraceAfterFilter();
  if (session_data_->unit_items_.size() == 0) {
    return false;
  }
  return true;
}

void SplashFilterNode::FetchRealtimeShowUnit() {
  if (!session_data_->enbale_splash_realtime_show_) {
    return;
  }
  // 未登录用户不出实时展现广告
  if (session_data_->pos_id_ != Kmovie && session_data_->pos_id_ != M2u &&
      session_data_->pos_id_ != KnewsSplash) {
    if (session_data_->ad_request_->ad_user_info().is_unlogin_user()) {
      return;
    }
  }
  for (auto& unit_item : session_data_->unit_items_) {
    bool all_unit_can_realtime_show =
        AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP, "all_unit_can_realtime_show",
                                   session_data_->user_id_, session_data_->device_id_, false);
    if (BrandKconfUtil::realtimeShowWhiteUnit()->count(unit_item->unit_id) == 0 &&
        !all_unit_can_realtime_show) {
      continue;
    }
    const Doc* doc = unit_item->get_doc();
    if (!doc) {
      continue;
    }
    if (unit_item->is_adx_unit() > 0) {
      // ADX 不支持实时开屏
      continue;
    }
    for (const auto& creative_info : doc->creative_info()) {
      // 图片物料并且没有预加载
      const ad_table::ad_brand_deliver_creative* creative_index = nullptr;
      creative_index = session_data_->creative_df->Find(creative_info.creative_id())
                           .GetStruct<ad_table::ad_brand_deliver_creative>();
      if (nullptr == creative_index) {
        LOG_EVERY_N(WARNING, 10000) << "creative_index is nullptr, creative_id:"
                                    << creative_info.creative_id();
        continue;
      }
      const auto creative_id = creative_index->id();
      if (creative_index->splash_material_type() == kuaishou::ad::brandv2::info::SplashMaterialType::STATIC &&
          session_data_->req_splash_id_set_.find(creative_id) == session_data_->req_splash_id_set_.end() &&
          unit_item->delivery_mode() != kuaishou::ad::brandv2::info::RTA) {
        session_data_->enable_realtime_show_creative_ids.emplace(creative_id);
      }
      // 矩阵产品支持视频实时
      if (session_data_->pos_id_ == Kmovie || session_data_->pos_id_ == M2u ||
          session_data_->pos_id_ == KnewsSplash) {
        if (session_data_->req_splash_id_set_.find(creative_id) == session_data_->req_splash_id_set_.end()) {
          session_data_->enable_realtime_show_creative_ids.emplace(creative_id);
        }
      }
    }
    session_data_->sampling_perf_->Interval(session_data_->enable_realtime_show_creative_ids.size(),
                                            "ad.ad_brand", "realtime_show_creative_count");
    // TODO(duantao) 根据开屏广告类型是图片添加白名单
    // VLOG(4) << "enable realtime show creative : "
    //         << "req_splash_id_set_ " << session_data_->req_splash_id_set_ << "unit_id:" << unit_id
    //         << "creative_id: " << session_data_->enable_realtime_show_creative_ids;
  }
}

void SplashFilterNode::SelectPrefetchAds() {
  // 构建策略
  StrategyManager* p_strategy_manager = context_->GetContextSystem<StrategyManager>();
  if (BrandKconfUtil::enableChangefilterOrder()) {
    STRATEGY_FILTER_V2(SelectUnitsBySchedule);
    STRATEGY_FILTER_V2(SelectUnitsByTestAds);
    STRATEGY_FILTER_V2(SelectUnitsByShield);
    STRATEGY_FILTER_V2(SelectUnitsByPlatform);
    STRATEGY_FILTER_V2(SelectUnitsByRiskControl);
    STRATEGY_FILTER_V2(SelectUnitsByUserAssignment);
    STRATEGY_FILTER_V2(SelectCreativesByPlatform);

    STRATEGY_FILTER_V2(SelectCreativePrefetch);  // 创意筛选，放最后
  } else {
    STRATEGY_FILTER_V2(SelectUnitsBySchedule);
    STRATEGY_FILTER_V2(SelectUnitsByUserAssignment);
    STRATEGY_FILTER_V2(SelectUnitsByTestAds);
    STRATEGY_FILTER_V2(SelectUnitsByShield);
    STRATEGY_FILTER_V2(SelectUnitsByPlatform);
    STRATEGY_FILTER_V2(SelectCreativesByPlatform);
    STRATEGY_FILTER_V2(SelectUnitsByRiskControl);

    STRATEGY_FILTER_V2(SelectCreativePrefetch);  // 创意筛选，放最后
  }
}

void SplashFilterNode::SelectRealTimeAds() {
  // 构建策略
  StrategyManager* p_strategy_manager = context_->GetContextSystem<StrategyManager>();
  STRATEGY_FILTER_V2(SelectUnitsBySchedule);
  STRATEGY_FILTER_V2(SelectUnitsByConfirmStatus);
  STRATEGY_FILTER_V2(SelectUnitsByTestAds);
  STRATEGY_FILTER_V2(SelectUnitsByCptDuration);
  STRATEGY_FILTER_V2(SelectUnitsByAppStartType);
  STRATEGY_FILTER_V2(SelectUnitsByFirstBrushOnly);
  // 临时填充 unit
  std::vector<UnitItemPtr> unit_items_tmp;
  unit_items_tmp.assign(session_data_->unit_items_.begin(), session_data_->unit_items_.end());
  STRATEGY_FILTER_V2(SelectUnitsByClientCache);
  if ((!session_data_->is_harmony_request_) &&
      AbtestInstance::GetBoolean(ks::AbtestBiz::AD_DSP, "enable_realtime_splash_adx", session_data_->user_id_,
                                 session_data_->device_id_, false)) {
    // 实时 Adx, 仅生效于未缓存的用户
    if (session_data_->unit_items_.empty()) {
      AdxManager adx_manager(session_data_);
      // 请求 adx , 并从 adx_response 解析结果填充到 unit_items_ 里
      adx_manager.GetSplashRealTimeAdxResult(unit_items_tmp);
      STRATEGY_FILTER_V2(SelectUnitsByClientCache);
    }
  }
  STRATEGY_FILTER_V2(SelectUnitsByFrequency);
  STRATEGY_FILTER_V2(SelectUnitsByRiskControl);
  STRATEGY_FILTER_V2(SelectUnitsByShield);
  STRATEGY_FILTER_V2(SelectUnitsByRoundNumber);
  STRATEGY_FILTER_V2(SelectUnitsByDistance);
  STRATEGY_FILTER_V2(SelectCreativeRealtime);  // 创意筛选，放最后
}

void SplashFilterNode::SimMaterialFilter() {
  // 相似物料频控配置
  kconf::RepMaterialConf::RepConf sim_conf;
  std::string sim_material_exp_name = AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "sim_material_exp_name", session_data_->abtest_user_info_, "");
  auto sim_material_conf_ptr = BrandKconfUtil::simMaterialConf();
  if (!sim_material_conf_ptr) {
    return;
  }
  auto& sim_material_conf = sim_material_conf_ptr->data();
  if (sim_material_conf.exp_to_config().count(sim_material_exp_name) > 0) {
    sim_conf = sim_material_conf.exp_to_config().at(sim_material_exp_name);
  } else {
    return;
  }
  std::unordered_set<std::string> sim_photo_md5;
  int64_t curent_time_ms = base::GetTimestamp() / 1000;
  auto* rep_material_manager = BrandCreativeRepMaterial::GetInstance();
  if (!rep_material_manager) {
    return;
  }
  for (const auto& [redis_timestamp_ms , creative_id] : session_data_->timestamp_2_creative_) {
    if (curent_time_ms - redis_timestamp_ms < sim_conf.rep_interval_time() * 1000) {
      const auto& creative_sim_photo_ids = rep_material_manager->GetCreativeSimPhotoMd5(creative_id);
      for (const auto& material_photo_md5 : creative_sim_photo_ids) {
        sim_photo_md5.emplace(material_photo_md5);
      }
    }
  }

  session_data_->sampling_perf_->Interval(sim_photo_md5.size(), "ad.ad_brand", "sim_material_photo_md5_size",
                                          sim_material_exp_name);
  if (sim_photo_md5.empty()) {
    return;
  }
  // 重复素材过滤
  auto IsPassSimMaterial = [&](int64_t creative_id) -> bool {
    if (sim_conf.rep_interval_time() <= 0) {
      return true;
    }
    // 行业
    if (sim_conf.industry_ids_size() > 0) {
      auto* creative_info = session_data_->p_index_->GetCreative(creative_id);
      if (creative_info == nullptr) {
        return false;
      }
      const Doc* doc = session_data_->p_index_->GetDoc(creative_info->unit_id());
      if (!doc) {
        LOG(ERROR) << "Null pointer to doc for unit_id " << creative_info->unit_id();
        return false;
      }
      const auto& account_info = doc->account_info();
      uint64_t doc_industry_id = 0;
      if (account_info.has_industry_info()) {
        doc_industry_id = account_info.industry_info().primary_industry_id();
      }
      bool hit_industry_id = false;
      for (const auto conf_industry_id : sim_conf.industry_ids()) {
        if (conf_industry_id == doc_industry_id) {
          hit_industry_id = true;
          break;
        }
      }
      if (!hit_industry_id) {
        return true;
      }
    }
    // pos_id
    bool hit_pos_id = false;
    for (auto conf_pos_id : sim_conf.pos_ids()) {
      if (conf_pos_id == session_data_->pos_id_) {
        hit_pos_id = true;
        break;
      }
    }
    if (!hit_pos_id) {
      return true;
    }
    // 相似素材过滤
    auto creative_sim_photo_md5s = rep_material_manager->GetCreativeSimPhotoMd5(creative_id);
    for (const auto& material_sim_photo_id : creative_sim_photo_md5s) {
      if (sim_photo_md5.count(material_sim_photo_id) > 0) {
        return false;
      }
    }
    return true;
  };

  for (const auto& creative_id : session_data_->client_creative_id_set_) {
    if (!IsPassSimMaterial(creative_id)) {
      session_data_->creative_filter_ads_.emplace(creative_id);
    }
  }
  for (const auto& creative_id : session_data_->enable_realtime_show_creative_ids) {
    if (!IsPassSimMaterial(creative_id)) {
      session_data_->creative_filter_ads_.emplace(creative_id);
    }
  }
  session_data_->sampling_perf_->Interval(session_data_->creative_filter_ads_.size(), "ad.ad_brand",
                                          "sim_creative_filter_ads", sim_material_exp_name);
}

void SplashFilterNode::SelectRealTimeCachedAds() {
  // 重复曝光频控
  kconf::RepMaterialConf::RepConf dup_conf;
  std::string rep_material_exp_name = AbtestInstance::GetString(
      ks::AbtestBiz::AD_DSP, "rep_material_exp_name", session_data_->abtest_user_info_, "");
  auto rep_material_conf_ptr = BrandKconfUtil::repMaterialConf();
  if (!rep_material_conf_ptr) {
    return;
  }
  const auto& rep_material_conf = rep_material_conf_ptr->data();
  if (rep_material_conf.exp_to_config().count(rep_material_exp_name) > 0) {
    dup_conf = rep_material_conf.exp_to_config().at(rep_material_exp_name);
  } else {
    return;
  }
  // 历史曝光的创意信息
  std::unordered_set<std::string> pic_md5;
  std::unordered_set<std::string> photo_md5;
  int64_t curent_time_ms = base::GetTimestamp() / 1000;
  auto* rep_material_manager = BrandCreativeRepMaterial::GetInstance();
  if (!rep_material_manager) {
    return;
  }
  for (const auto& [redis_timestamp_ms , creative_id] : session_data_->timestamp_2_creative_) {
    if (curent_time_ms - redis_timestamp_ms < dup_conf.rep_interval_time() * 1000) {
      for (const auto& material_pic_md5 : rep_material_manager->GetCreativePicMd5s(creative_id)) {
        pic_md5.emplace(material_pic_md5);
      }
      for (const auto& material_photo_md5 : rep_material_manager->GetCreativePhotoMd5s(creative_id)) {
        photo_md5.emplace(material_photo_md5);
      }
    }
  }
  session_data_->sampling_perf_->Interval(pic_md5.size(), "ad.ad_brand", "rep_material_pic_md5_size",
                                          rep_material_exp_name);
  session_data_->sampling_perf_->Interval(photo_md5.size(), "ad.ad_brand", "rep_material_photo_md5_size",
                                          rep_material_exp_name);
  if (pic_md5.empty() && photo_md5.empty()) {
    return;
  }
  // 重复素材过滤
  auto IsPassDupMaterial = [&](int64_t creative_id) -> bool {
    if (dup_conf.rep_interval_time() <= 0) {
      return true;
    }
    // 行业
    if (dup_conf.industry_ids_size() > 0) {
      auto* creative_info = session_data_->p_index_->GetCreative(creative_id);
      if (creative_info == nullptr) {
        return false;
      }
      const Doc* doc = session_data_->p_index_->GetDoc(creative_info->unit_id());
      if (!doc) {
        LOG(ERROR) << "Null pointer to doc for unit_id " << creative_info->unit_id();
        return false;
      }
      const auto& account_info = doc->account_info();
      uint64_t doc_industry_id = 0;
      if (account_info.has_industry_info()) {
        doc_industry_id = account_info.industry_info().primary_industry_id();
      }
      bool hit_industry_id = false;
      for (const auto conf_industry_id : dup_conf.industry_ids()) {
        if (conf_industry_id == doc_industry_id) {
          hit_industry_id = true;
          break;
        }
      }
      if (!hit_industry_id) {
        return true;
      }
    }
    // pos_id
    bool hit_pos_id = false;
    for (auto conf_pos_id : dup_conf.pos_ids()) {
      if (conf_pos_id == session_data_->pos_id_) {
        hit_pos_id = true;
        break;
      }
    }
    if (!hit_pos_id) {
      return true;
    }
    // 重复素材过滤
    auto creative_pic_md5s = rep_material_manager->GetCreativePicMd5s(creative_id);
    for (const auto& material_pic_id : creative_pic_md5s) {
      if (pic_md5.count(material_pic_id) > 0) {
        return false;
      }
    }
    auto creative_photo_md5s = rep_material_manager->GetCreativePhotoMd5s(creative_id);
    for (const auto& material_photo_id : creative_photo_md5s) {
      if (photo_md5.count(material_photo_id) > 0) {
        return false;
      }
    }
    return true;
  };

  for (const auto& creative_id : session_data_->client_creative_id_set_) {
    if (!IsPassDupMaterial(creative_id)) {
      session_data_->creative_filter_ads_.emplace(creative_id);
    }
  }
  for (const auto& creative_id : session_data_->enable_realtime_show_creative_ids) {
    if (!IsPassDupMaterial(creative_id)) {
      session_data_->creative_filter_ads_.emplace(creative_id);
    }
  }
  session_data_->sampling_perf_->Interval(session_data_->creative_filter_ads_.size(), "ad.ad_brand",
                                          "dup_creative_filter_ads", rep_material_exp_name);
}

void SplashFilterNode::TraceAfterFilter() {
  std::vector<int64_t> alive_unit_ids;
  for (auto& unit_item : session_data_->unit_items_) {
    if (nullptr == unit_item) {
      continue;
    }
    alive_unit_ids.emplace_back(unit_item->unit_id);
  }
  TRACE_LOG.AddFlowFunnel(kuaishou::log::splash::SplashStep::FILTER_STEP, "splash_filter",
                          session_data_->unit_items_.size(), {}, alive_unit_ids);
}

}  // namespace ad_brand
}  // namespace ks
