#pragma once

#include <any>
#include <vector>
#include <string>
#include <functional>
#include <memory>

#include "teams/ad/ad_table/table/key128.h"
#include "teams/ad/ad_table/table/row_wrapper.h"
#include "teams/ad/ad_table/table/table_schema.h"

#include "kconf/tail_number.h"

namespace ks::engine_base {
  struct SearchConvertedFilterDefineProductConfig;
}  // namespace ks::engine_base

namespace ks {
namespace ad_table {

class GeneratorFactory {
 public:
  struct Context {
    bool enable_unit_over_budget_check = true;
    bool enable_account_over_budget_check = true;
    bool target_budget_control = true;
    bool enable_budget_control_mgr = true;
    bool enable_smooth_control = true;
    bool enable_smb_coldstart_cost_level_fix = false;
    bool enable_outer_unit_photo_p2l = false;
    bool enable_new_index_switch_spu = false;
    bool enable_payload_search_mingtou_filter = true;
    bool enable_coldstart_index = false;
    bool inner_photo = false;
    bool outer_team = false;
    bool universe = false;
    int32_t dark_budget_control_btr_default  = 0;
    int32_t velocity_btr_limit = 0;
    int32_t smooth_control_unit_tail_limit = 0;
    int64_t start_day_ts = 0;
    int64_t start_ts = 0;
    int64_t now_ms = 0;
    int64_t now_time_second = 0;
    int64_t merchant_storewide_recall_key = ********;
    int64_t merchant_storewide_min_cost = 0;
    int64_t mature_photo_recall_key = ********;
    int32_t mature_photo_recall_cost = 50;
    int64_t invert_list_length_for_chart = 5;
    std::any* custom_var;

    #ifdef BIDWORD_SEARCH_FILTER_FUNCTION
      std::shared_ptr<engine_base::SearchConvertedFilterDefineProductConfig> search_define_product_config = nullptr;  // NOLINT
      int64_t search_converted_filter_define_product_time_range = 0;
    #endif
    void Refresh();
  };

  using Generator = std::function<void(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*)>;

  using Generator64 = std::function<void(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key64>*)>;

  static Generator Get(const std::string& name);
  static Generator64 Get64(const std::string& name);

  static void InterestTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void AudienceTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void PopulationTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                     std::vector<Key128>*);
  static void PaidAudienceTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*);
  static void FansStarTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void BusinessInterestTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
  static void InterestVideoTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                        std::vector<Key128>*);
  static void BehaviorInterestKeywordsTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                                   std::vector<Key128>*);
  static void PurchaseIntentionTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                            std::vector<Key128>*);
  static void CelebrityTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);
  static void AggregateBidding(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);

  static void CorePopulationTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                         std::vector<Key128>*);
  static void AgeTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RegionTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void GenderTargetModify(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static bool ProgCreativeModify(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void SiteAndriModify(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void SiteNebulaAndriModify(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);
  static void SiteNebulaIosModify(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void SiteIosModify(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void ConvertedFilterAccountId(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*);
  static void ConvertedFilterUnitId(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);
  static void ConvertedFilterCampaignId(const Context&, const TableSchema&, ConstRowWrapper,
                                        std::vector<Key128>*);
  static void ConvertedFilterOcpxActionType(const Context&, const TableSchema&, ConstRowWrapper,
                                            std::vector<Key128>*);
  static void ConvertedFilterCityCorporationId(const Context&, const TableSchema&, ConstRowWrapper,
                                               std::vector<Key128>*);
  static void ConvertedFilterCityAppPackageNameId(const Context&, const TableSchema&, ConstRowWrapper,
                                                  std::vector<Key128>*);
  static void ConvertedFilterFilterTimeRange(const Context&, const TableSchema&, ConstRowWrapper,
                                             std::vector<Key128>*);
  static void ConvertedFilterDefineProductHash(const Context&, const TableSchema&, ConstRowWrapper,
                                               std::vector<Key128>*);
  static void ConvertedFilterCityProductId(const Context&, const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
  static void MinIosVersion(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void MaxIosVersion(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void MinAndroidVersion(const Context&, const TableSchema&, ConstRowWrapper,
                                std::vector<Key128>*);
  static void MaxAndroidVersion(const Context&, const TableSchema&, ConstRowWrapper,
                                std::vector<Key128>*);
  static void EnginePopulation(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void EnginePopulationHard(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void ExcludeEnginePopulation(const Context&, const TableSchema&, ConstRowWrapper,
                                      std::vector<Key128>*);
  static bool EspAppealUserId(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void AppPackageName(const Context&, const TableSchema&, ConstRowWrapper,
                             std::vector<Key128>*);
  static void PackageNameSize(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitCreative(const Context&, const TableSchema&, ConstRowWrapper,
                                std::vector<Key128>*);
  static void RiskLimitCreativeId(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitCoverId(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskLimitRiskLabel(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskLimitPhotoId(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskLimitUnit(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void RiskLimitUserId(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitLiveId(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitIndustryId(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitAccountId(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskLimitUnitId(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitProductName(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void RiskLimitLicenceNum(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitLiveUserId(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitAppId(const Context&, const TableSchema&, ConstRowWrapper,
                             std::vector<Key128>*);
  static void RiskLimitMiniApp(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void ProgrammaticCreativeListByPhotoIdSearch(const Context&,
                                           const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
  static void RiskLimitGlobalAppId(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void RiskLimitPackageId(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskLimitFanstopAuthorId(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*);
  static void RiskLimitItemId(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void NebulaRiskIndustryRegion(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*);
  static void NebulaRiskIndustrySchedule(const Context&, const TableSchema&, ConstRowWrapper,
                                         std::vector<Key128>*);
  static void NebulaRiskIndustryAge(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);
  static void NebulaRiskIndustryRate(const Context&, const TableSchema&, ConstRowWrapper,
                                     std::vector<Key128>*);
  static void RiskIndustryRegion(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskIndustrySchedule(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void RiskIndustryAge(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskIndustryRate(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool UnitChargeInfoBuild(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  // payload 迁移
  static void FixAuthorIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key64>*);
  static void FixP2LAuthorIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key64>*);
  static void FixP2LIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key64>*);
  static void FixPhotoAndP2LIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key64>*);
  static void FixPhotoIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key64>*);
  static void FixPhotoAuthorIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key64>*);
  static void FixMobileAuthorIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                                     std::vector<Key64>*);
  static void FixLiveStreamIdIndex(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key64>*);
  static void FixMobileDirectLiveStreamIdIndex(const Context&, const TableSchema&,
                                   ConstRowWrapper, std::vector<Key64>*);
  static void PhotoAndP2LIdIndex(const Context&, const TableSchema&,
                                 ConstRowWrapper, std::vector<Key64>*);
  static void PhotoAndP2LIdWithSearchIndex(const Context&, const TableSchema&,
                                           ConstRowWrapper, std::vector<Key64>*);
  static void P2LIdIndex(const Context&, const TableSchema&,
                         ConstRowWrapper, std::vector<Key64>*);
  static void SpuGoodItemIndex(const Context&, const TableSchema&,
                               ConstRowWrapper, std::vector<Key64>*);
  static void PhotoIdIndex(const Context&, const TableSchema&,
                           ConstRowWrapper, std::vector<Key64>*);
  static void OriginalPhotoIndex(const Context&, const TableSchema&,
                                 ConstRowWrapper, std::vector<Key64>*);
  static void OriginalPhotoNewIndex(const Context&, const TableSchema&,
                                  ConstRowWrapper, std::vector<Key64>*);
  static void CategoryIndex(const Context&, const TableSchema&,
                            ConstRowWrapper, std::vector<Key64>*);
  static void FixCategoryIndex(const Context&, const TableSchema&,
                               ConstRowWrapper, std::vector<Key64>*);
  static void HostingPhotoIndex(const Context&, const TableSchema&,
                                ConstRowWrapper, std::vector<Key64>*);
  static void MerchantStoreWideIndex(const Context&, const TableSchema&,
                                  ConstRowWrapper, std::vector<Key64>*);
  static void CreativeIdIndex(const Context&, const TableSchema&,
                              ConstRowWrapper, std::vector<Key64>*);
  static void ItemIdIndex(const Context&, const TableSchema&,
                          ConstRowWrapper, std::vector<Key64>*);
  static void AuthorIdIndex(const Context&, const TableSchema&,
                          ConstRowWrapper, std::vector<Key64>*);
  static void PhotoAuthorIdIndex(const Context&, const TableSchema&,
                                 ConstRowWrapper, std::vector<Key64>*);
  static void LiveStreamIdIndex(const Context&, const TableSchema&,
                                  ConstRowWrapper, std::vector<Key64>*);
  static void DirectLiveStreamIdIndex(const Context&, const TableSchema&,
                                      ConstRowWrapper, std::vector<Key64>*);
  static void MobileDirectLiveStreamIdIndex(const Context&, const TableSchema&,
                                            ConstRowWrapper, std::vector<Key64>*);
  static void UnitPhotoIdIndex(const Context&, const TableSchema&,
                               ConstRowWrapper, std::vector<Key64>*);
  static void CampaignPhotoIdIndex(const Context&, const TableSchema&,
                                   ConstRowWrapper, std::vector<Key64>*);
  static void NewCusLiveAuthorIdIndex(const Context&, const TableSchema&,
                                    ConstRowWrapper, std::vector<Key64>*);
  static void NewCusPhotoAuthorIdIndex(const Context&, const TableSchema&,
                                      ConstRowWrapper, std::vector<Key64>*);
  static void SmbColdStartLiveAuthorIdIndex(const Context&, const TableSchema&,
                                        ConstRowWrapper, std::vector<Key64>*);
  static void UnitP2LIdIndex(const Context&, const TableSchema&,
                             ConstRowWrapper, std::vector<Key64>*);
  static void UnitPhotoP2lIdIndex(const Context&, const TableSchema&,
                                  ConstRowWrapper, std::vector<Key64>*);
  static void ProductOcpxIndex(const Context&, const TableSchema&,
                               ConstRowWrapper, std::vector<Key64>*);
  static void OcpxP2LIdStableFixIndex(const Context&, const TableSchema&,
                                      ConstRowWrapper, std::vector<Key64>*);
  static void P2LLiveOcpxStableFixIndex(const Context&, const TableSchema&,
                                        ConstRowWrapper, std::vector<Key64>*);
  static void OcpxPhotoAndP2LIdStableFixIndex(const Context&, const TableSchema&,
                                          ConstRowWrapper, std::vector<Key64>*);
  static void OcpxMobilePhotoAndP2LIdStableIndex(const Context&, const TableSchema&,
                                            ConstRowWrapper, std::vector<Key64>*);
  static void OcpxPhotoLiveIdStableFixIndex(const Context&, const TableSchema&,
                                            ConstRowWrapper, std::vector<Key64>*);
  static void ColdstartOcpxPhotoLiveIdStableFixIndex
                (const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void MobileOcpxDirectLiveStreamIdStableFixIndex
                (const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  // 移动端三期索引替换
  static void V3MobileDirectLiveStreamIdIndex
                (const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void V3MobileAuthorIdIndex
                (const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void V3OcpxMobilePhotoAndP2LIdStableIndex
                (const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void V3MobileOcpxDirectLiveStreamIdStableIndex
                (const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void UnitIdIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void LiveUnitIdIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void P2LUnitIdIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void PhotoUnitIdIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void ColdStartUnitIdIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void ColdStartCreativeIdIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  // nobid 单独索引
  static void AuthorIdNobidIndex(const Context&, const TableSchema&,
                                 ConstRowWrapper, std::vector<Key64>*);

  static void MobileAuthorIdIndex(const Context&, const TableSchema&,
                                  ConstRowWrapper, std::vector<Key64>*);
  static void SpuOrItem2CreativeIndex(const Context&, const TableSchema&,
                                      ConstRowWrapper, std::vector<Key64>*);
  static void ColdStartSpuOrItem2CreativeIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void SpuOrItem2CreativeForPhotoIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void SpuOrItem2CreativeForPhotoIndexV2(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void EcomSpuOrItem2CreativeIndex(const Context&, const TableSchema&,
                ConstRowWrapper, std::vector<Key64>*);
  static void EcomSpuOrItem2CreativeForPhotoIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void OuterDirectLiveCreativeIdIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void FanstopAuthorFlagIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void AccountSpuPhotoIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void SdpaOcpxIdIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void CidUnitPhotoIdIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void ColdStartOcpxPhotoAndP2LIdStableFixIndex(
                    const Context&, const TableSchema&,
                    ConstRowWrapper, std::vector<Key64>*);
  static void UnitColdStartPhotoIdIndex(
                  const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void StorewideOcpxLiveIdIndex(
                  const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void StorewideUnitP2LIdIndex(
                  const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void CidHighBidIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  static void OneModelSidIndex(const Context&, const TableSchema&,
                  ConstRowWrapper, std::vector<Key64>*);
  /**************************************************** 垂类 ************************************************/
  static void InterestTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void AudienceTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void PopulationTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                     std::vector<Key128>*);
  static void PaidAudienceTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*);
  static void FansStarTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void BusinessInterestTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
  static void InterestVideoTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                        std::vector<Key128>*);
  static void BehaviorInterestKeywordsTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,    // NOLINT
                                                   std::vector<Key128>*);
  static void PurchaseIntentionTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                            std::vector<Key128>*);
  static void CelebrityTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);
  static void CorePopulationTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                         std::vector<Key128>*);
  static void AgeTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RegionTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void GenderTargetModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static bool ProgCreativeModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void MinIosVersionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void MaxIosVersionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void MinAndroidVersionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                std::vector<Key128>*);
  static void MaxAndroidVersionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                std::vector<Key128>*);
  static void EnginePopulationUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void EnginePopulationHardUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void ExcludeEnginePopulationUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                      std::vector<Key128>*);
  static bool EspAppealUserIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void AppPackageNameUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                             std::vector<Key128>*);
  static void SysPackageNameUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                             std::vector<Key128>*);
  static void PackageNameSizeUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitCreativeUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                std::vector<Key128>*);
  static void RiskLimitCreativeIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitCoverIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskLimitRiskLabelUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskLimitPhotoIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskLimitUnitUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                            std::vector<Key128>*);
  static void RiskLimitUserIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitLiveIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitIndustryIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitAccountIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskLimitUnitIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskLimitProductNameUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void RiskLimitLicenceNumUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitLiveUserIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                  std::vector<Key128>*);
  static void RiskLimitAppIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                             std::vector<Key128>*);
  static void RiskLimitGlobalAppIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void RiskLimitPackageIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskLimitItemIdUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskIndustryRegionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                 std::vector<Key128>*);
  static void RiskIndustryScheduleUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                                   std::vector<Key128>*);
  static void RiskIndustryAgeUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                              std::vector<Key128>*);
  static void RiskIndustryRateUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void HasPrivacyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void HasPermissionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void HasVersionUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void IncludePosIdTargetUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void ExcludePosIdTargetUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void DurationLimitModifyUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool UnitChargeInfoBuildUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskLimitMiniAppUniverse(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>* keys);
  // 海外 Generator 逻辑
  static bool I18nRiskLimitUnitLevel(const Context&, const TableSchema&, ConstRowWrapper,
                                     std::vector<Key128>*);

  static bool I18nRiskLimitCreativeLevel(const Context&, const TableSchema&, ConstRowWrapper,
                                         std::vector<Key128>*);

  static void RiskLimitUnitSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void MinIosVersionSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void MaxIosVersionSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void MinAndroidVersionSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void MaxAndroidVersionSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void AppPackageNameSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool EspAppealUserIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool FanstopItemTypeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool FanstopOrderTypeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void ExcludeEnginePopulationSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void PackageNameSizeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void InterestTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void CelebrityTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void PopulationTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void FansStarTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void AgeTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void PurchaseIntentionTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RegionTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void BehaviorInterestKeywordsTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void GenderTargetModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskIndustryRegionSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskIndustryAgeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void NebulaRiskIndustryRegionSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void NebulaRiskIndustryAgeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static void RiskLimitCreativeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool ProgCreativeModifySearch(const Context&, const TableSchema&, ConstRowWrapper,
                               std::vector<Key128>*);
  static bool ConvertedFilterAccountIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                       std::vector<Key128>*);
  static bool ConvertedFilterUnitIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                    std::vector<Key128>*);
  static bool ConvertedFilterCampaignIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                        std::vector<Key128>*);
  static bool ConvertedFilterOcpxActionTypeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                            std::vector<Key128>*);
  static bool ConvertedFilterCityCorporationIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                               std::vector<Key128>*);
  static bool ConvertedFilterCityAppPackageNameIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                                  std::vector<Key128>*);
  static bool ConvertedFilterFilterTimeRangeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                             std::vector<Key128>*);
  static bool ConvertedFilterDefineProductHashSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                               std::vector<Key128>*);
  static bool ConvertedFilterCityProductIdSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
  static bool WinfoWordIdMatchTypeSearch(const Context&, const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
  static bool BonusSupportGroupAccountIdBonusAccountTypeSearch(const Context&,
                                           const TableSchema&, ConstRowWrapper,
                                           std::vector<Key128>*);
};

}  // namespace ad_table
}  // namespace ks
