#include "teams/ad/ad_table/op_factory/generator_factory.h"

#include <random>
#include <vector>
#include <atomic>
#include <tuple>

#include "base/time/timestamp.h"
#include "absl/time/clock.h"
#include "absl/time/time.h"
#include "absl/container/flat_hash_map.h"
#include "teams/ad/ad_proto/kuaishou/ad/common/enums.pb.h"
#include "teams/ad/ad_table/table_env/loader.h"
#include "teams/ad/ad_table/utils/hash_function.h"
#include "teams/ad/engine_base/budget_common/budget_control_status_receiver.h"
#include "teams/ad/engine_base/budget_common/budget_status_receiver.h"
#include "teams/ad/engine_base/kconf/kconf.h"
#include "teams/ad/ad_base/src/utility/util.h"
#include "teams/ad/engine_base/budget_common/utils.h"
#include "teams/ad/ad_base/src/budget_common/billing_unit_over_budget.h"
#include "teams/ad/ad_base/src/common/basis_enum_types.h"

#include "teams/ad/ad_table/code_generator/ad_target_feed/Creative.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Unit.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Target.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Campaign.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/Account.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/IndustryV3.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/SiteExtInfo.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/AdAppRelease.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/AdRiskFlowLimitResult.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/AdRiskFlowLimitCommand.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/LiveStreamUserInfo.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/AdDspMiniApp.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTUnit.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/FeedPayload.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTProduct.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTPhoto.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTAuthor.h"
#include "teams/ad/ad_table/code_generator/ad_target_feed/WTLive.h"

using kuaishou::log::ad::AdTraceFilterCondition;

namespace ks {
namespace ad_table {

DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enablePayloadSearchMingtouFilter, true)
DEFINE_BOOL_KCONF_NODE(ad.adtarget3, enableNewIndexSwitchSpu, false)
DEFINE_INT64_KCONF_NODE(ad.adtarget2, MaturePhotoRecallKey, 20230512)
DEFINE_INT32_KCONF_NODE(ad.adtarget2, MaturePhotoRecallCost, 50)
DEFINE_INT64_KCONF_NODE(ad.adtarget2, HostingPhotoRecallKey, 20230717)
DEFINE_INT32_KCONF_NODE(ad.adtarget2, HostingPhotoRecallMinCost, 50)
DEFINE_INT32_KCONF_NODE(ad.adtarget2, HostingPhotoRecallMaxCost, 50)
DEFINE_INT64_KCONF_NODE(ad.adtarget2, MerchantStorewideRecallKey, 20231122)
DEFINE_INT64_KCONF_NODE(ad.adtarget2, MerchantStorewideMinCost, 0)
DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableOuterUnitPhotoP2l, false)
DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableSmbColdstartCostLevelFix, false)
DEFINE_BOOL_KCONF_NODE(ad.adtarget2, enableColdStartUnitIdIndex, false)
DEFINE_INT64_KCONF_NODE(ad.adtarget2, invertListLengthForChart, 5)
namespace internal {

constexpr int64_t kStep = 1000;
constexpr int64_t kMaxStep = kStep * kStep * kStep * kStep;

int64_t OsVersionTrans(double version) {
  return static_cast<int64_t>(version * kMaxStep);
}

int64_t GenDateIdKey(int64_t day_timestamp, int64_t level_id) {
  base::uint128 union_key;
  union_key.first = day_timestamp;
  union_key.second = level_id;
  uint64_t sign = base::Hash128to64(union_key);
  return *(reinterpret_cast<int64_t *>(&sign));
}

}  // namespace internal

constexpr int64_t kDefaultTokenValue = 0;
inline Key64 HashOf4Payload(int64_t t1, int64_t t2, int64_t t3) {
  return HashOfKey64(t1, t2, t3);
}

inline Key64 HashOfToken1(int64_t t1) {
  return HashOfKey64(t1);
}
inline Key64 HashOfToken2(int64_t t1, int64_t t2) {
  return HashOfKey64(t1, t2);
}


inline bool in_outer_team() {
  return AdTableInstance::GetAdIndexType() == +ad_base::AdIndexType::Thanos;
}

inline bool is_universe() {
  return (AdTableInstance::GetAdIndexType() == +ad_base::AdIndexType::Universe);
}

inline bool is_inner_photo() {
  return AdTableInstance::GetAdIndexType() == +ad_base::AdIndexType::InnerPhoto;
}

BETTER_ENUM(TAG_PRI_TYPE, int32_t, DEFAULT = 0, SPU = 1, ITEM = 2)
BETTER_ENUM(TAG_ACTION_TYPE, int32_t, DEFAULT = 0, CHART = 1, EXPLAIN = 2, FUTURE = 3)
BETTER_ENUM(QueueType, int32_t, kUnknown = 0, kHard = 1, kSoft = 2, kHardAndSoft = 3)

int64_t GetStartMicrosOfDay(const absl::Time &time_now, const absl::Time::Breakdown &bd) {
  const int64_t kSecondsInMirco = 1000 * 1000;
  auto seconds = absl::ToUnixSeconds(time_now);
  return (seconds - bd.hour * 3600 - bd.minute * 60 - bd.second) * kSecondsInMirco;
}

int64_t GetStartSecondsOfDay(const absl::Time &time_now, const absl::Time::Breakdown &bd) {
  auto seconds = absl::ToUnixSeconds(time_now);
  return seconds - bd.hour * 3600 - bd.minute * 60 - bd.second;
}

int64_t GetStartMicrosOfDay() {
  auto now = absl::Now();
  const auto kLocalTimeZone = absl::LocalTimeZone();
  auto bd = now.In(kLocalTimeZone);
  return GetStartMicrosOfDay(now, bd);
}

void GeneratorFactory::Context::Refresh() {
  enable_unit_over_budget_check = ks::engine_base::AdKconfUtil::enableUnitOverBudgetCheck();
  enable_account_over_budget_check = ks::engine_base::AdKconfUtil::enableAccountOverBudgetCheck();
  target_budget_control = ks::engine_base::AdKconfUtil::enableTargetBudgetControl();
  enable_budget_control_mgr = ks::engine_base::AdKconfUtil::enableBudgetControlMgr();
  dark_budget_control_btr_default = ks::engine_base::AdKconfUtil::darkBudgetControlBtrDefault();
  enable_smooth_control = ks::engine_base::AdKconfUtil::enableSmoothControl();
  velocity_btr_limit = ks::engine_base::AdKconfUtil::velocityBtrLimit();
  smooth_control_unit_tail_limit = ks::engine_base::AdKconfUtil::smoothControlTailLimit();
  start_day_ts = GetStartMicrosOfDay();
  start_ts = base::GetTimestamp();
  enable_smb_coldstart_cost_level_fix = enableSmbColdstartCostLevelFix();
  enable_outer_unit_photo_p2l = enableOuterUnitPhotoP2l();
  enable_new_index_switch_spu = enableNewIndexSwitchSpu();
  enable_payload_search_mingtou_filter = enablePayloadSearchMingtouFilter();
  enable_coldstart_index = enableColdStartUnitIdIndex();
  inner_photo = is_inner_photo();
  now_time_second = base::GetTimestamp() / 1e6;
  outer_team = in_outer_team();
  universe = is_universe();
  merchant_storewide_recall_key = MerchantStorewideRecallKey();
  merchant_storewide_min_cost = MerchantStorewideMinCost();
  mature_photo_recall_key = MaturePhotoRecallKey();
  mature_photo_recall_cost = MaturePhotoRecallCost();
  invert_list_length_for_chart = invertListLengthForChart();
#ifdef BIDWORD_SEARCH_FILTER_FUNCTION
  search_define_product_config =
        std::make_shared<engine_base::SearchConvertedFilterDefineProductConfig>(
          ks::engine_base::AdKconfUtil::searchConvertedFilterDefineProductConfig()->data());
  search_converted_filter_define_product_time_range =
          ks::engine_base::AdKconfUtil::searchConvertedFilterDefineProductTimeRange();
#endif
}

const int64_t DARK_AD_FILTER = 100;
const int64_t GROUP_PACING_OFFSET = 1000;

enum BudgetPacingStatus {
  BPS_PASS_OK = 0,
  BPS_PACING_BRAKING = 6,
  BPS_OVER_SERVER_SHOW = 7,
};

bool is_resource_exists(const std::vector<int64_t>& resource_ids, int64_t resource_id) {
  return std::binary_search(resource_ids.begin(), resource_ids.end(), resource_id);
}

int PacingCheckNew(int64_t unit_id, const ks::engine_base::UnitChargeInfo &unit_charge_info) {
  int filter_reason = BPS_PASS_OK;
  if (unit_charge_info.velocity_status == kuaishou::ad::BudgetStatusPacingType::BRAKING_FULL_CLOSE) {
    filter_reason = BPS_OVER_SERVER_SHOW;
    LOG_EVERY_N(INFO, 100000) << "filter_by_braking_full_close, unit_id:" << unit_id;
  } else if (unit_charge_info.velocity_status == kuaishou::ad::BudgetStatusPacingType::BRAKING_HALF_OPEN &&
             unit_charge_info.velocity_btr > 0) {
    static thread_local base::PseudoRandom generator(base::GetTimestamp());
    if (generator.GetDouble() * 1000000 > unit_charge_info.velocity_btr * 1.0) {
      filter_reason = BPS_PACING_BRAKING;
      LOG_EVERY_N(INFO, 100000) << "filter_by_braking_half_open unit_id:" << unit_id
                               << ", btr:" << unit_charge_info.velocity_btr;
    }
  }
  return filter_reason;
}


GeneratorFactory::Generator64 GeneratorFactory::Get64(const std::string& name) {
  static absl::flat_hash_map<std::string, Generator64> generator64_factory = {
    // Payload 倒排使用
    {"FixAuthorIdIndex", FixAuthorIdIndex},
    {"FixP2LAuthorIdIndex", FixP2LAuthorIdIndex},
    {"FixP2LIdIndex", FixP2LIdIndex},
    {"FixPhotoIdIndex", FixPhotoIdIndex},
    {"FixPhotoAndP2LIdIndex", FixPhotoAndP2LIdIndex},
    {"FixPhotoAuthorIdIndex", FixPhotoAuthorIdIndex},
    {"FixMobileAuthorIdIndex", FixMobileAuthorIdIndex},
    {"FixLiveStreamIdIndex", FixLiveStreamIdIndex},
    {"FixMobileDirectLiveStreamIdIndex", FixMobileDirectLiveStreamIdIndex},
    {"PhotoAndP2LIdIndex", PhotoAndP2LIdIndex},
    {"PhotoAndP2LIdWithSearchIndex", PhotoAndP2LIdWithSearchIndex},
    {"P2LIdIndex", P2LIdIndex},
    {"SpuGoodItemIndex", SpuGoodItemIndex},
    {"PhotoIdIndex", PhotoIdIndex},
    {"OriginalPhotoIndex", OriginalPhotoIndex},
    {"OriginalPhotoNewIndex", OriginalPhotoNewIndex},
    {"CategoryIndex", CategoryIndex},
    {"FixCategoryIndex", FixCategoryIndex},
    {"HostingPhotoIndex", HostingPhotoIndex},
    {"MerchantStoreWideIndex", MerchantStoreWideIndex},
    {"CreativeIdIndex", CreativeIdIndex},
    {"ItemIdIndex", ItemIdIndex},
    {"AuthorIdIndex", AuthorIdIndex},
    {"PhotoAuthorIdIndex", PhotoAuthorIdIndex},
    {"LiveStreamIdIndex", LiveStreamIdIndex},
    {"DirectLiveStreamIdIndex", DirectLiveStreamIdIndex},
    {"MobileDirectLiveStreamIdIndex", MobileDirectLiveStreamIdIndex},
    {"UnitPhotoIdIndex", UnitPhotoIdIndex},
    {"CampaignPhotoIdIndex", CampaignPhotoIdIndex},
    {"NewCusLiveAuthorIdIndex", NewCusLiveAuthorIdIndex},
    {"NewCusPhotoAuthorIdIndex", NewCusPhotoAuthorIdIndex},
    {"SmbColdStartLiveAuthorIdIndex", SmbColdStartLiveAuthorIdIndex},
    {"UnitP2LIdIndex", UnitP2LIdIndex},
    {"UnitPhotoP2lIdIndex", UnitPhotoP2lIdIndex},
    {"ProductOcpxIndex", ProductOcpxIndex},
    {"OcpxP2LIdStableFixIndex", OcpxP2LIdStableFixIndex},
    {"P2LLiveOcpxStableFixIndex", P2LLiveOcpxStableFixIndex},
    {"OcpxPhotoAndP2LIdStableFixIndex", OcpxPhotoAndP2LIdStableFixIndex},
    {"OcpxMobilePhotoAndP2LIdStableIndex", OcpxMobilePhotoAndP2LIdStableIndex},
    {"OcpxPhotoLiveIdStableFixIndex", OcpxPhotoLiveIdStableFixIndex},
    {"ColdstartOcpxPhotoLiveIdStableFixIndex", ColdstartOcpxPhotoLiveIdStableFixIndex},
    {"MobileOcpxDirectLiveStreamIdStableFixIndex", MobileOcpxDirectLiveStreamIdStableFixIndex},
    {"V3MobileDirectLiveStreamIdIndex", V3MobileDirectLiveStreamIdIndex},
    {"V3MobileAuthorIdIndex", V3MobileAuthorIdIndex},
    {"V3OcpxMobilePhotoAndP2LIdStableIndex", V3OcpxMobilePhotoAndP2LIdStableIndex},
    {"V3MobileOcpxDirectLiveStreamIdStableIndex", V3MobileOcpxDirectLiveStreamIdStableIndex},
    {"AuthorIdNobidIndex", AuthorIdNobidIndex},
    {"UnitIdIndex", UnitIdIndex},
    {"LiveUnitIdIndex", LiveUnitIdIndex},
    {"P2LUnitIdIndex", P2LUnitIdIndex},
    {"PhotoUnitIdIndex", PhotoUnitIdIndex},
    {"ColdStartUnitIdIndex", ColdStartUnitIdIndex},
    {"ColdStartCreativeIdIndex", ColdStartCreativeIdIndex},
    {"MobileAuthorIdIndex", MobileAuthorIdIndex},
    {"SpuOrItem2CreativeIndex", SpuOrItem2CreativeIndex},
    {"ColdStartSpuOrItem2CreativeIndex", ColdStartSpuOrItem2CreativeIndex},
    {"SpuOrItem2CreativeForPhotoIndex", SpuOrItem2CreativeForPhotoIndex},
    {"EcomSpuOrItem2CreativeIndex", EcomSpuOrItem2CreativeIndex},
    {"EcomSpuOrItem2CreativeForPhotoIndex", EcomSpuOrItem2CreativeForPhotoIndex},
    {"OuterDirectLiveCreativeIdIndex", OuterDirectLiveCreativeIdIndex},
    {"FanstopAuthorFlagIndex", FanstopAuthorFlagIndex},
    {"AccountSpuPhotoIndex", AccountSpuPhotoIndex},
    {"SdpaOcpxIdIndex", SdpaOcpxIdIndex},
    {"CidUnitPhotoIdIndex", CidUnitPhotoIdIndex},
    {"ColdStartOcpxPhotoAndP2LIdStableFixIndex", ColdStartOcpxPhotoAndP2LIdStableFixIndex},
    {"UnitColdStartPhotoIdIndex", UnitColdStartPhotoIdIndex},
    {"StorewideOcpxLiveIdIndex", StorewideOcpxLiveIdIndex},
    {"StorewideUnitP2LIdIndex", StorewideUnitP2LIdIndex},
    {"CidHighBidIndex", CidHighBidIndex},
    {"OneModelSidIndex", OneModelSidIndex},
  };

  auto iter = generator64_factory.find(name);
  if (iter != generator64_factory.end()) {
    return iter->second;
  }

  return {};
}

GeneratorFactory::Generator GeneratorFactory::Get(const std::string& name) {
  static absl::flat_hash_map<std::string, Generator> generator_factory = {
      {"UnitChargeInfoBuild", UnitChargeInfoBuild},
      {"InterestTargetModify", InterestTargetModify},
      {"AudienceTargetModify", AudienceTargetModify},
      {"PopulationTargetModify", PopulationTargetModify},
      {"PaidAudienceTargetModify", PaidAudienceTargetModify},
      {"FansStarTargetModify", FansStarTargetModify},
      {"BusinessInterestTargetModify", BusinessInterestTargetModify},
      {"InterestVideoTargetModify", InterestVideoTargetModify},
      {"BehaviorInterestKeywordsTargetModify", BehaviorInterestKeywordsTargetModify},
      {"PurchaseIntentionTargetModify", PurchaseIntentionTargetModify},
      {"CelebrityTargetModify", CelebrityTargetModify},
      {"CorePopulationTargetModify", CorePopulationTargetModify},
      {"AgeTargetModify", AgeTargetModify},
      {"RegionTargetModify", RegionTargetModify},
      {"GenderTargetModify", GenderTargetModify},
      {"ProgCreativeModify", ProgCreativeModify},
      {"SiteAndriModify", SiteAndriModify},
      {"SiteNebulaAndriModify", SiteNebulaAndriModify},
      {"SiteNebulaIosModify", SiteNebulaIosModify},
      {"SiteIosModify", SiteIosModify},
      {"MinIosVersion", MinIosVersion},
      {"MaxIosVersion", MaxIosVersion},
      {"MinAndroidVersion", MinAndroidVersion},
      {"MaxAndroidVersion", MaxAndroidVersion},
      {"EnginePopulation", EnginePopulation},
      {"EnginePopulationHard", EnginePopulationHard},
      {"ExcludeEnginePopulation", ExcludeEnginePopulation},
      {"EspAppealUserId", EspAppealUserId},
      {"PackageNameSize", PackageNameSize},
      {"AppPackageName", AppPackageName},
      {"RiskLimitCreative", RiskLimitCreative},
      {"RiskLimitUnit", RiskLimitUnit},

      {"RiskLimitPackageId", RiskLimitPackageId},
      {"RiskLimitGlobalAppId", RiskLimitGlobalAppId},
      {"RiskLimitAppId", RiskLimitAppId},
      {"RiskLimitLiveUserId", RiskLimitLiveUserId},
      {"RiskLimitLicenceNum", RiskLimitLicenceNum},
      {"RiskLimitProductName", RiskLimitProductName},
      {"RiskLimitUnitId", RiskLimitUnitId},
      {"RiskLimitItemId", RiskLimitItemId},
      {"RiskLimitFanstopAuthorId", RiskLimitFanstopAuthorId},
      {"RiskLimitAccountId", RiskLimitAccountId},
      {"RiskLimitIndustryId", RiskLimitIndustryId},
      {"RiskLimitLiveId", RiskLimitLiveId},
      {"RiskLimitUserId", RiskLimitUserId},
      {"RiskLimitPhotoId", RiskLimitPhotoId},
      {"RiskLimitCreativeId", RiskLimitCreativeId},
      {"RiskLimitCoverId", RiskLimitCoverId},
      {"RiskLimitRiskLabel", RiskLimitRiskLabel},
      {"RiskLimitMiniApp", RiskLimitMiniApp},
      {"AggregateBidding", AggregateBidding},
      {"ProgrammaticCreativeListByPhotoIdSearch", ProgrammaticCreativeListByPhotoIdSearch},
      {"NebulaRiskIndustryRegion", NebulaRiskIndustryRegion},
      {"NebulaRiskIndustrySchedule", NebulaRiskIndustrySchedule},
      {"NebulaRiskIndustryAge", NebulaRiskIndustryAge},
      {"NebulaRiskIndustryRate", NebulaRiskIndustryRate},
      {"RiskIndustryRegion", RiskIndustryRegion},
      {"RiskIndustrySchedule", RiskIndustrySchedule},
      {"RiskIndustryAge", RiskIndustryAge},
      {"RiskIndustryRate", RiskIndustryRate},



      // 垂类
    #ifdef AD_TARGET_UNIVERSE_FILTER_FUNCTION
      {"InterestTargetModifyUniverse", InterestTargetModifyUniverse},
      {"AudienceTargetModifyUniverse", AudienceTargetModifyUniverse},
      {"PopulationTargetModifyUniverse", PopulationTargetModifyUniverse},
      {"PaidAudienceTargetModifyUniverse", PaidAudienceTargetModifyUniverse},
      {"FansStarTargetModifyUniverse", FansStarTargetModifyUniverse},
      {"BusinessInterestTargetModifyUniverse", BusinessInterestTargetModifyUniverse},
      {"InterestVideoTargetModifyUniverse", InterestVideoTargetModifyUniverse},
      {"BehaviorInterestKeywordsTargetModifyUniverse", BehaviorInterestKeywordsTargetModifyUniverse},
      {"PurchaseIntentionTargetModifyUniverse", PurchaseIntentionTargetModifyUniverse},
      {"CelebrityTargetModifyUniverse", CelebrityTargetModifyUniverse},
      {"CorePopulationTargetModifyUniverse", CorePopulationTargetModifyUniverse},
      {"AgeTargetModifyUniverse", AgeTargetModifyUniverse},
      {"RegionTargetModifyUniverse", RegionTargetModifyUniverse},
      {"GenderTargetModifyUniverse", GenderTargetModifyUniverse},
      {"ProgCreativeModifyUniverse", ProgCreativeModifyUniverse},
      {"MinIosVersionUniverse", MinIosVersionUniverse},
      {"MaxIosVersionUniverse", MaxIosVersionUniverse},
      {"MinAndroidVersionUniverse", MinAndroidVersionUniverse},
      {"MaxAndroidVersionUniverse", MaxAndroidVersionUniverse},
      {"EnginePopulationUniverse", EnginePopulationUniverse},
      {"EnginePopulationHardUniverse", EnginePopulationHardUniverse},
      {"ExcludeEnginePopulationUniverse", ExcludeEnginePopulationUniverse},
      {"EspAppealUserIdUniverse", EspAppealUserIdUniverse},
      {"AppPackageNameUniverse", AppPackageNameUniverse},
      {"SysPackageNameUniverse", SysPackageNameUniverse},
      {"PackageNameSizeUniverse", PackageNameSizeUniverse},
      {"RiskLimitCreativeUniverse", RiskLimitCreativeUniverse},
      {"RiskLimitCreativeIdUniverse", RiskLimitCreativeIdUniverse},
      {"RiskLimitCoverIdUniverse", RiskLimitCoverIdUniverse},
      {"RiskLimitRiskLabelUniverse", RiskLimitRiskLabelUniverse},
      {"RiskLimitPhotoIdUniverse", RiskLimitPhotoIdUniverse},
      {"RiskLimitUnitUniverse", RiskLimitUnitUniverse},
      {"RiskLimitUserIdUniverse", RiskLimitUserIdUniverse},
      {"RiskLimitLiveIdUniverse", RiskLimitLiveIdUniverse},
      {"RiskLimitIndustryIdUniverse", RiskLimitIndustryIdUniverse},
      {"RiskLimitAccountIdUniverse", RiskLimitAccountIdUniverse},
      {"RiskLimitUnitIdUniverse", RiskLimitUnitIdUniverse},
      {"RiskLimitProductNameUniverse", RiskLimitProductNameUniverse},
      {"RiskLimitLicenceNumUniverse", RiskLimitLicenceNumUniverse},
      {"RiskLimitLiveUserIdUniverse", RiskLimitLiveUserIdUniverse},
      {"RiskLimitAppIdUniverse", RiskLimitAppIdUniverse},
      {"RiskLimitGlobalAppIdUniverse", RiskLimitGlobalAppIdUniverse},
      {"RiskLimitPackageIdUniverse", RiskLimitPackageIdUniverse},
      {"RiskLimitItemIdUniverse", RiskLimitItemIdUniverse},
      {"RiskIndustryRegionUniverse", RiskIndustryRegionUniverse},
      {"RiskIndustryScheduleUniverse", RiskIndustryScheduleUniverse},
      {"RiskIndustryAgeUniverse", RiskIndustryAgeUniverse},
      {"RiskIndustryRateUniverse", RiskIndustryRateUniverse},
      {"RiskLimitMiniAppUniverse", RiskLimitMiniAppUniverse},
      {"HasPrivacyUniverse", HasPrivacyUniverse},
      {"HasPermissionUniverse", HasPermissionUniverse},
      {"HasVersionUniverse", HasVersionUniverse},
      {"IncludePosIdTargetUniverse", IncludePosIdTargetUniverse},
      {"ExcludePosIdTargetUniverse", ExcludePosIdTargetUniverse},
      {"DurationLimitModifyUniverse", DurationLimitModifyUniverse},
      {"UnitChargeInfoBuildUniverse", UnitChargeInfoBuildUniverse},
    #endif

    #ifdef AD_TABLE_I18N_FUNCTION
      {"I18nRiskLimitUnitLevel", I18nRiskLimitUnitLevel},
      {"I18nRiskLimitCreativeLevel", I18nRiskLimitCreativeLevel},
    #endif

    #ifdef AD_TARGET_SEARCH_FILTER_FUNCTION
      {"RiskLimitUnitSearch", RiskLimitUnitSearch},
      {"MinIosVersionSearch", MinIosVersionSearch},
      {"MaxIosVersionSearch", MaxIosVersionSearch},
      {"MinAndroidVersionSearch", MinAndroidVersionSearch},
      {"MaxAndroidVersionSearch", MaxAndroidVersionSearch},
      {"AppPackageNameSearch", AppPackageNameSearch},
      {"EspAppealUserIdSearch", EspAppealUserIdSearch},
      {"FanstopItemTypeSearch", FanstopItemTypeSearch},
      {"FanstopOrderTypeSearch", FanstopOrderTypeSearch},
      {"ExcludeEnginePopulationSearch", ExcludeEnginePopulationSearch},
      {"PackageNameSizeSearch", PackageNameSizeSearch},
      {"InterestTargetModifySearch", InterestTargetModifySearch},
      {"CelebrityTargetModifySearch", CelebrityTargetModifySearch},
      {"PopulationTargetModifySearch", PopulationTargetModifySearch},
      {"FansStarTargetModifySearch", FansStarTargetModifySearch},
      {"AgeTargetModifySearch", AgeTargetModifySearch},
      {"PurchaseIntentionTargetModifySearch", PurchaseIntentionTargetModifySearch},
      {"RegionTargetModifySearch", RegionTargetModifySearch},
      {"BehaviorInterestKeywordsTargetModifySearch", BehaviorInterestKeywordsTargetModifySearch},
      {"GenderTargetModifySearch", GenderTargetModifySearch},
      {"RiskIndustryRegionSearch", RiskIndustryRegionSearch},
      {"RiskIndustryAgeSearch", RiskIndustryAgeSearch},
      {"NebulaRiskIndustryRegionSearch", NebulaRiskIndustryRegionSearch},
      {"NebulaRiskIndustryAgeSearch", NebulaRiskIndustryAgeSearch},
      {"RiskLimitCreativeSearch", RiskLimitCreativeSearch},
      {"ProgCreativeModifySearch", ProgCreativeModifySearch},
      {"ConvertedFilterAccountIdSearch", ConvertedFilterAccountIdSearch},
      {"ConvertedFilterUnitIdSearch", ConvertedFilterUnitIdSearch},
      {"ConvertedFilterCampaignIdSearch", ConvertedFilterCampaignIdSearch},
      {"ConvertedFilterOcpxActionTypeSearch", ConvertedFilterOcpxActionTypeSearch},
      {"ConvertedFilterCityCorporationIdSearch", ConvertedFilterCityCorporationIdSearch},
      {"ConvertedFilterCityAppPackageNameIdSearch", ConvertedFilterCityAppPackageNameIdSearch},
      {"ConvertedFilterFilterTimeRangeSearch", ConvertedFilterFilterTimeRangeSearch},
      {"ConvertedFilterDefineProductHashSearch", ConvertedFilterDefineProductHashSearch},
      {"ConvertedFilterCityProductIdSearch", ConvertedFilterCityProductIdSearch},
    #endif

    #ifdef BIDWORD_SEARCH_FILTER_FUNCTION
      {"WinfoWordIdMatchTypeSearch", WinfoWordIdMatchTypeSearch},
      {"RiskLimitUnitSearch", RiskLimitUnitSearch},
      {"MinIosVersionSearch", MinIosVersionSearch},
      {"MaxIosVersionSearch", MaxIosVersionSearch},
      {"MinAndroidVersionSearch", MinAndroidVersionSearch},
      {"MaxAndroidVersionSearch", MaxAndroidVersionSearch},
      {"AppPackageNameSearch", AppPackageNameSearch},
      {"EspAppealUserIdSearch", EspAppealUserIdSearch},
      {"FanstopItemTypeSearch", FanstopItemTypeSearch},
      {"FanstopOrderTypeSearch", FanstopOrderTypeSearch},
      {"ExcludeEnginePopulationSearch", ExcludeEnginePopulationSearch},
      {"PackageNameSizeSearch", PackageNameSizeSearch},
      {"InterestTargetModifySearch", InterestTargetModifySearch},
      {"CelebrityTargetModifySearch", CelebrityTargetModifySearch},
      {"PopulationTargetModifySearch", PopulationTargetModifySearch},
      {"FansStarTargetModifySearch", FansStarTargetModifySearch},
      {"AgeTargetModifySearch", AgeTargetModifySearch},
      {"PurchaseIntentionTargetModifySearch", PurchaseIntentionTargetModifySearch},
      {"RegionTargetModifySearch", RegionTargetModifySearch},
      {"BehaviorInterestKeywordsTargetModifySearch", BehaviorInterestKeywordsTargetModifySearch},
      {"GenderTargetModifySearch", GenderTargetModifySearch},
      {"RiskIndustryRegionSearch", RiskIndustryRegionSearch},
      {"RiskIndustryAgeSearch", RiskIndustryAgeSearch},
      {"NebulaRiskIndustryRegionSearch", NebulaRiskIndustryRegionSearch},
      {"NebulaRiskIndustryAgeSearch", NebulaRiskIndustryAgeSearch},
      {"RiskLimitCreativeSearch", RiskLimitCreativeSearch},
      {"ProgCreativeModifySearch", ProgCreativeModifySearch},
      {"ConvertedFilterAccountIdSearch", ConvertedFilterAccountIdSearch},
      {"ConvertedFilterUnitIdSearch", ConvertedFilterUnitIdSearch},
      {"ConvertedFilterCampaignIdSearch", ConvertedFilterCampaignIdSearch},
      {"ConvertedFilterOcpxActionTypeSearch", ConvertedFilterOcpxActionTypeSearch},
      {"ConvertedFilterCityCorporationIdSearch", ConvertedFilterCityCorporationIdSearch},
      {"ConvertedFilterCityAppPackageNameIdSearch", ConvertedFilterCityAppPackageNameIdSearch},
      {"ConvertedFilterFilterTimeRangeSearch", ConvertedFilterFilterTimeRangeSearch},
      {"ConvertedFilterDefineProductHashSearch", ConvertedFilterDefineProductHashSearch},
      {"ConvertedFilterCityProductIdSearch", ConvertedFilterCityProductIdSearch},
    #endif

    #ifdef AD_TARGET_SPLASH_FILTER_FUNCTION
      {"RiskLimitUnitSearch", RiskLimitUnitSearch},
      {"MinIosVersionSearch", MinIosVersionSearch},
      {"MaxIosVersionSearch", MaxIosVersionSearch},
      {"MinAndroidVersionSearch", MinAndroidVersionSearch},
      {"MaxAndroidVersionSearch", MaxAndroidVersionSearch},
      {"AppPackageNameSearch", AppPackageNameSearch},
      {"EspAppealUserIdSearch", EspAppealUserIdSearch},
      {"FanstopItemTypeSearch", FanstopItemTypeSearch},
      {"FanstopOrderTypeSearch", FanstopOrderTypeSearch},
      {"ExcludeEnginePopulationSearch", ExcludeEnginePopulationSearch},
      {"PackageNameSizeSearch", PackageNameSizeSearch},
      {"InterestTargetModifySearch", InterestTargetModifySearch},
      {"CelebrityTargetModifySearch", CelebrityTargetModifySearch},
      {"PopulationTargetModifySearch", PopulationTargetModifySearch},
      {"FansStarTargetModifySearch", FansStarTargetModifySearch},
      {"AgeTargetModifySearch", AgeTargetModifySearch},
      {"PurchaseIntentionTargetModifySearch", PurchaseIntentionTargetModifySearch},
      {"RegionTargetModifySearch", RegionTargetModifySearch},
      {"BehaviorInterestKeywordsTargetModifySearch", BehaviorInterestKeywordsTargetModifySearch},
      {"GenderTargetModifySearch", GenderTargetModifySearch},
      {"RiskIndustryRegionSearch", RiskIndustryRegionSearch},
      {"RiskIndustryAgeSearch", RiskIndustryAgeSearch},
      {"NebulaRiskIndustryRegionSearch", NebulaRiskIndustryRegionSearch},
      {"NebulaRiskIndustryAgeSearch", NebulaRiskIndustryAgeSearch},
      {"RiskLimitCreativeSearch", RiskLimitCreativeSearch},
      {"ProgCreativeModifySearch", ProgCreativeModifySearch},
      {"ConvertedFilterAccountIdSearch", ConvertedFilterAccountIdSearch},
      {"ConvertedFilterUnitIdSearch", ConvertedFilterUnitIdSearch},
      {"ConvertedFilterCampaignIdSearch", ConvertedFilterCampaignIdSearch},
      {"ConvertedFilterOcpxActionTypeSearch", ConvertedFilterOcpxActionTypeSearch},
      {"ConvertedFilterCityCorporationIdSearch", ConvertedFilterCityCorporationIdSearch},
      {"ConvertedFilterCityAppPackageNameIdSearch", ConvertedFilterCityAppPackageNameIdSearch},
      {"ConvertedFilterFilterTimeRangeSearch", ConvertedFilterFilterTimeRangeSearch},
      {"ConvertedFilterDefineProductHashSearch", ConvertedFilterDefineProductHashSearch},
      {"ConvertedFilterCityProductIdSearch", ConvertedFilterCityProductIdSearch},
      {"BonusSupportGroupAccountIdBonusAccountTypeSearch", BonusSupportGroupAccountIdBonusAccountTypeSearch},
    #endif
  };

  auto iter = generator_factory.find(name);
  if (iter != generator_factory.end()) {
    return iter->second;
  }

  return {};
}

static std::tuple<const Unit*, const Campaign*, const Account*>
ExtensionUnit(const TableSchema& unit_table_schema, ConstRowWrapper unit_row) {
  auto* unit_ptr = unit_row.GetStruct<Unit>();

  static auto campaign_id_column_desc = unit_table_schema.FindColumnDescByName("campaign_id");
  auto campaign_row = *campaign_id_column_desc.GetExtraValue<ConstRowWrapper*>(unit_row);
  auto* campaign_ptr = campaign_row.GetStruct<Campaign>();

  static auto& campaign_table_schema = *campaign_id_column_desc.GetReferenceTableSchema();
  static auto account_id_column_desc = campaign_table_schema.FindColumnDescByName("account_id");
  auto* account_ptr = account_id_column_desc.GetExtraValue<ConstRowWrapper*>(campaign_row)
                                            ->GetStruct<Account>();

  return {unit_ptr, campaign_ptr, account_ptr};
}

static std::tuple<const Creative*, const Unit*, const Campaign*, const Account*>
ExtensionCreative(const TableSchema& creative_table_schema, ConstRowWrapper creative_row) {
  auto* creative_ptr = creative_row.GetStruct<Creative>();

  static auto unit_id_column_desc = creative_table_schema.FindColumnDescByName("unit_id");
  auto unit_row = *unit_id_column_desc.GetExtraValue<ConstRowWrapper*>(creative_row);
  auto* unit_ptr = unit_row.GetStruct<Unit>();

  static auto& unit_table_schema = *unit_id_column_desc.GetReferenceTableSchema();
  static auto campaign_id_column_desc = unit_table_schema.FindColumnDescByName("campaign_id");
  auto campaign_row = *campaign_id_column_desc.GetExtraValue<ConstRowWrapper*>(unit_row);
  auto* campaign_ptr = campaign_row.GetStruct<Campaign>();

  static auto& campaign_table_schema = *campaign_id_column_desc.GetReferenceTableSchema();
  static auto account_id_column_desc = campaign_table_schema.FindColumnDescByName("account_id");
  auto* account_ptr = account_id_column_desc.GetExtraValue<ConstRowWrapper*>(campaign_row)
                                            ->GetStruct<Account>();

  return {creative_ptr, unit_ptr, campaign_ptr, account_ptr};
}

static const Target* ExtensionUnitTarget(const TableSchema& unit_table_schema, ConstRowWrapper unit_row) {
  static auto target_id_column_desc = unit_table_schema.FindColumnDescByName("target_id");
  return target_id_column_desc.GetExtraValue<ConstRowWrapper*>(unit_row)->GetStruct<Target>();
}


bool GeneratorFactory::UnitChargeInfoBuild(const Context& context, const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);

  int64_t start_day_ts = context.start_day_ts;
  auto start_ts = context.start_ts;
  auto now_ms = start_ts / 1000;

  if (context.enable_unit_over_budget_check &&
      ks::ad_base::BillingUnitOverBudget::GetInstance()->UnitOverBudgetCheck(unit_ptr->id(), start_ts)) {
    LOG_EVERY_N(INFO, 100000) << "billing unit over budget, unit_id:" << unit_ptr->id();
    keys->emplace_back(HashOf(int64_t(AdTraceFilterCondition::CREATIVE_OVER_BUDGET_BY_STATE)));
    return true;
  }
  // 磁力金牛套餐不做账户校验，做订单校验
  if (unit_ptr->unit_feature() == kuaishou::ad::AdEnum::UNIT_ESP_COMBO_FEATURE) {
    if (ks::ad_base::BillingUnitOverBudget::GetInstance()->OrderOverBudgetCheck(
          campaign_ptr->combo_order_id(), start_ts)) {
      LOG_EVERY_N(INFO, 100000) << "billing order over budget, order_id:" << campaign_ptr->combo_order_id();
      keys->emplace_back(HashOf(int64_t(AdTraceFilterCondition::ORDER_BALANCE_NOT_ENOUGH)));
      return true;
    }
  } else {
    if (context.enable_account_over_budget_check &&
        ks::ad_base::BillingUnitOverBudget::GetInstance()->AccountOverBudgetCheck(unit_ptr->account_id(),
          start_ts)) {
      LOG_EVERY_N(INFO, 100000) << "billing account over budget, account_id:" << unit_ptr->account_id();
      keys->emplace_back(HashOf(int64_t(AdTraceFilterCondition::CREATIVE_OVER_BUDGET_BY_STATE)));
      return true;
    }
  }

  if ((unit_ptr->begin_time() > 0 && now_ms < unit_ptr->begin_time()) ||
      (unit_ptr->end_time() > 0 && now_ms > unit_ptr->end_time())) {
    keys->emplace_back(HashOf(int64_t(AdTraceFilterCondition::BEGIN_TIME)));
    return true;
  }

  ks::engine_base::UnitChargeInfo budget_status_charge_info;

  int64_t version = 0;
  bool old_find = ks::engine_base::BudgetStatusReceiver::GetInstance()->GetUnitChargeInfo(
      unit_ptr->id(), start_day_ts, &budget_status_charge_info, &version);

  if (context.target_budget_control) {
    bool is_filter = false;
    if (budget_status_charge_info.left_budget <= 0) {
      is_filter = true;
    }
    if (is_filter) {
      // TODO(liulong) 先写死为 unit 过滤，后面 budget 需要回传一下被过滤的原因
      LOG_EVERY_N(INFO, 1000000) << "unit_filter_by_left_budget_new, unit_id:" << unit_ptr->id()
                                << ",left_budget:" << budget_status_charge_info.left_budget;
      keys->emplace_back(HashOf(int64_t(AdTraceFilterCondition::UNIT_UNAVAILABLE)));
      return true;
    }
  }

  if (context.enable_budget_control_mgr) {
    do {
      int32_t btr = 0;  // 通过概率 [0, 100]

      if (is_resource_exists(unit_ptr->resource_ids(), 24)) {
        continue;
      }
      // 激励视频
      int32_t old_dark_control_code = 0;
      int64_t old_today_date = 0;
      version = -1;
      bool find = ks::engine_base::BudgetStatusReceiver::GetInstance()->GetUnitBudgetChargeTagBtr(
          unit_ptr->id(), start_day_ts, ks::ad_base::BudgetControlTagConvert(16), &btr,
          &old_dark_control_code, &old_today_date, &version);

      if (!find) {
        btr = context.dark_budget_control_btr_default;
        LOG_EVERY_N(INFO, 1000000) << "dark_budget_control,unit_id:" << unit_ptr->id()
                                  << ",not find,btr_default:" << btr;
        ks::infra::PerfUtil::CountLogStash(1, "ad.target", "unit_budget_control_status_not_find_num");
      }
      if (btr <= ks::ad_base::AdRandom::GetInt(0, 99)) {
        keys->emplace_back(HashOf(DARK_AD_FILTER));

        LOG_EVERY_N(INFO, 100000000)
            << "unit_filter_by_budget_control,unit_id:" << unit_ptr->id() << ",btr:" << btr;
      } else {
        LOG_EVERY_N(INFO, 100000000) << "dark_ad_allowed_pass,unit_id:" << unit_ptr->id() << ",btr:" << btr;
      }
    } while (0);
  }

  if (context.enable_smooth_control &&
      (unit_ptr->id()%100 < context.smooth_control_unit_tail_limit) &&
      budget_status_charge_info.velocity_btr < context.velocity_btr_limit) {
    budget_status_charge_info.velocity_btr = context.velocity_btr_limit;
  }

  for (int group_idx = 0; group_idx < 10; ++group_idx) {
    auto hack_reason = PacingCheckNew(unit_ptr->id(), budget_status_charge_info);
    if (hack_reason == BPS_OVER_SERVER_SHOW) {
      break;
    }
    if (hack_reason != BPS_PASS_OK) {
      keys->emplace_back(HashOf(group_idx + GROUP_PACING_OFFSET));
    }
  }
  return true;
}

void GeneratorFactory::InterestTargetModify(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->interest()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::AudienceTargetModify(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->audience()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::PopulationTargetModify(const Context& context, const TableSchema& table_schema,
                                              ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->population()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::PaidAudienceTargetModify(const Context& context, const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->paid_audience()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::FansStarTargetModify(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->fans_star()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::BusinessInterestTargetModify(const Context& context, const TableSchema& table_schema,
                                                    ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->business_interest()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::InterestVideoTargetModify(const Context& context, const TableSchema& table_schema,
                                                 ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->interest_video()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::BehaviorInterestKeywordsTargetModify(const Context& context,
                                                            const TableSchema& table_schema,
                                                            ConstRowWrapper row_wrapper,
                                                            std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->behavior_interest_keyword()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::PurchaseIntentionTargetModify(const Context& context, const TableSchema& table_schema,
                                                     ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->purchase_intention_label()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::CelebrityTargetModify(const Context& context, const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->celebrity_label()) {
      keys->emplace_back(HashOf(v));
    }
  }
}

void GeneratorFactory::CorePopulationTargetModify(const Context& context, const TableSchema& table_schema,
                                                  ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode()) {
    for (auto v : target->core_population_label()) {
      keys->emplace_back(HashOf(v));
    }
  }
}

void GeneratorFactory::AgeTargetModify(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode() || (target->expand_mode() & (1 << 0))) {
    for (auto v : target->age()) {
      keys->emplace_back(HashOf(v));
    }
  }
}

void GeneratorFactory::AggregateBidding(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);
  auto* wt_unit = unit_ptr->wt_unit_id_ref_WTUnit();
  if (creative_ptr && wt_unit && creative_ptr->shell_creative() == 1) {
    keys->emplace_back(HashOf(wt_unit->clue_aggr_third_cate_id()));
  }
}

void GeneratorFactory::RegionTargetModify(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode() || (target->expand_mode() & (1 << 1))) {
    for (auto v : target->region()) {
      keys->emplace_back(HashOf(v));
    }
  }
}
void GeneratorFactory::GenderTargetModify(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  if (!target->target_expansion_mode() || (target->expand_mode() & (1 << 2))) {
    for (auto v : target->gender()) {
      keys->emplace_back(HashOf(v));
    }
  }
}

void GeneratorFactory::SiteAndriModify(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  const auto* site = row_wrapper.GetStruct<Creative>()->site_id_ref_SiteExtInfo();
  if (site && site->site_id()) {
    keys->emplace_back(HashOf(site->android_version()));
  }
}
void GeneratorFactory::SiteNebulaAndriModify(const Context& context, const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  const auto* site = row_wrapper.GetStruct<Creative>()->site_id_ref_SiteExtInfo();
  if (site && site->site_id()) {
    keys->emplace_back(HashOf(site->nebula_android_version()));
  }
}
void GeneratorFactory::SiteNebulaIosModify(const Context& context, const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  const auto* site = row_wrapper.GetStruct<Creative>()->site_id_ref_SiteExtInfo();
  if (site && site->site_id()) {
    keys->emplace_back(HashOf(site->nebula_ios_version()));
  }
}
void GeneratorFactory::SiteIosModify(const Context& context, const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  const auto* site = row_wrapper.GetStruct<Creative>()->site_id_ref_SiteExtInfo();
  if (site && site->site_id()) {
    keys->emplace_back(HashOf(site->ios_version()));
  }
}

bool GeneratorFactory::ProgCreativeModify(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static auto func = []() {
    absl::flat_hash_map<int64_t, std::vector<int64_t>> ret;
    // kProgExpType 依赖全局变量 kProgCreativeExpMap 进行构建
    for (int32 n = 0; n <= 128; ++n) {
      std::vector<int64_t> tmp;
      int32 i = 1;
      int exp_type = 1;
      while (i <= n) {
        if (i & n) {
          tmp.push_back(exp_type);
        }
        i = i << 1;
        exp_type++;
      }
      ret.emplace(n, tmp);
    }
    return ret;
  };
  static auto prog_map = func();

  auto* creative_ptr = row_wrapper.GetStruct<Creative>();
  auto tmp = creative_ptr->creative_score() >> 24;
  std::vector<int64_t>* values = nullptr;
  auto iter = prog_map.find(tmp);
  if (iter != prog_map.end()) {
    values = &(iter->second);
  } else {
    values = &(prog_map[0]);
  }
  for (auto v : *values) {
    keys->emplace_back(HashOf(v));
  }
  return true;
}

void GeneratorFactory::MinIosVersion(const Context& context, const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  keys->emplace_back(HashOf(internal::OsVersionTrans(target->min_ios_version_raw())));
}
void GeneratorFactory::MaxIosVersion(const Context& context, const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  keys->emplace_back(HashOf(internal::OsVersionTrans(target->max_ios_version_raw())));
}
void GeneratorFactory::MinAndroidVersion(const Context& context, const TableSchema& table_schema,
                                         ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  keys->emplace_back(HashOf(internal::OsVersionTrans(target->min_android_version_raw())));
}
void GeneratorFactory::MaxAndroidVersion(const Context& context, const TableSchema& table_schema,
                                         ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target = ExtensionUnitTarget(table_schema, row_wrapper);
  keys->emplace_back(HashOf(internal::OsVersionTrans(target->max_android_version_raw())));
}

void GeneratorFactory::EnginePopulation(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);

  for (auto p : target_ptr->engine_population()) {
    keys->emplace_back(HashOf(CityHash::Convert(absl::StrCat(account_ptr->user_id(), p))));
    if (unit_ptr->live_user_id() != account_ptr->user_id()) {
      keys->emplace_back(HashOf(CityHash::Convert(absl::StrCat(unit_ptr->live_user_id(), p))));
    }
  }
}

void GeneratorFactory::EnginePopulationHard(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);

  for (auto p : target_ptr->engine_population_hard()) {
    keys->emplace_back(HashOf(CityHash::Convert(absl::StrCat(account_ptr->user_id(), p))));
    if (unit_ptr->live_user_id() != account_ptr->user_id()) {
      keys->emplace_back(HashOf(CityHash::Convert(absl::StrCat(unit_ptr->live_user_id(), p))));
    }
  }
}

void GeneratorFactory::ExcludeEnginePopulation(const Context& context, const TableSchema& table_schema,
                                               ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);

  for (auto p : target_ptr->exclude_engine_population()) {
    keys->emplace_back(HashOf(CityHash::Convert(absl::StrCat(account_ptr->user_id(), p))));
    if (unit_ptr->live_user_id() != account_ptr->user_id()) {
      keys->emplace_back(HashOf(CityHash::Convert(absl::StrCat(unit_ptr->live_user_id(), p))));
    }
  }
}

bool GeneratorFactory::EspAppealUserId(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);

  if (target_ptr->engine_population().empty() &&
      target_ptr->engine_population_hard().empty() &&
      target_ptr->exclude_engine_population().empty()) {
    return false;
  }
  keys->emplace_back(HashOf(account_ptr->user_id()));
  return true;
}
void GeneratorFactory::AppPackageName(const Context& context, const TableSchema& table_schema,
                                      ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);
  auto* unit_ptr = row_wrapper.GetStruct<Unit>();
  auto* app_release_ptr = unit_ptr->package_id_ref_AdAppRelease();

  int64_t app_package_name = 0;
  if (app_release_ptr) {
    app_package_name = app_release_ptr->package_name_id();
  } else {
    app_package_name = unit_ptr->city_app_package_name_id();
  }

  if (target_ptr->disable_installed_app_switch() != 0) {
    app_package_name = CityHash::Convert("");
  }

  keys->emplace_back(HashOf(app_package_name));
}

void GeneratorFactory::PackageNameSize(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto* unit_ptr = row_wrapper.GetStruct<Unit>();
  auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);
  int32_t package_name_size = !target_ptr->package_name().empty();
  keys->emplace_back(HashOf(package_name_size));
}

void RiskLimitField(int type, int64_t v, int64_t white_account_id, std::vector<Key128>* keys,
                    int64_t account_type) {
  if (v == 0) {
    return;
  }

  static auto* limit_table = AdTableInstance::GetDB().Lookup("AdRiskFlowLimitResult");
  static auto* command_table = AdTableInstance::GetDB().Lookup("AdRiskFlowLimitCommand");
  if (!limit_table || !command_table) {
    return;
  }

  if (white_account_id != 0) {
    static auto* white_table = AdTableInstance::GetDB().Lookup("RiskIndustryWhiteAccountResult");
    if (!white_table) {
      return;
    }

    auto white_row = white_table->Find(white_account_id);
    if (white_row) {
      return;
    }
  }

  auto* risk_result = limit_table->Find(CityHash::Convert(v, type)).GetStruct<AdRiskFlowLimitResult>();
  if (!risk_result) {
    return;
  }

  for (auto cmd : risk_result->limit_command_id()) {
    auto* risk_cmd = command_table->Find(cmd).GetStruct<AdRiskFlowLimitCommand>();
    if (risk_cmd) {
      auto it = std::find(risk_cmd->account_types().begin(), risk_cmd->account_types().end(), account_type);
      if (risk_cmd->account_types().empty() || it != risk_cmd->account_types().end()) {
        for (auto hash : risk_cmd->city_risk_hash()) {
          keys->emplace_back(HashOf(hash));
        }
      }
    }
  }
}

void GeneratorFactory::RiskLimitUserId(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_USER_ID, account_ptr->user_id(), 0, keys,
                 account_ptr->account_type());
}

void GeneratorFactory::RiskLimitLiveId(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* live_ptr = unit_ptr->live_user_id_ref_LiveStreamUserInfo();

  if (live_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_LIVE_ID, live_ptr->live_stream_id(), 0, keys,
                   account_ptr->account_type());
  }
}
void GeneratorFactory::RiskLimitIndustryId(const Context& context, const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_INDUSTRY_ID, account_ptr->industry_id_v3(),
                 unit_ptr->account_id(), keys, account_ptr->account_type());
}
void GeneratorFactory::RiskLimitAccountId(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_ACCOUNT_ID, unit_ptr->account_id(), 0, keys,
                 account_ptr->account_type());
}
void GeneratorFactory::RiskLimitUnitId(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_UNIT_ID, unit_ptr->id(), 0, keys,
                 account_ptr->account_type());
}

void GeneratorFactory::RiskLimitProductName(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_PRODUCT_NAME, account_ptr->city_product_id(), 0, keys,
                 account_ptr->account_type());
}
void GeneratorFactory::RiskLimitMiniApp(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* mini_app = unit_ptr->mini_app_unit_id_ref_AdDspMiniApp();
  if (mini_app) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_MINI_APP_ID, mini_app->mini_app_id_platform_hash(), 0,
                   keys, account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitLicenceNum(const Context& context, const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_LICENCE_NUM, account_ptr->city_license_no(), 0, keys,
                 account_ptr->account_type());
}
void GeneratorFactory::RiskLimitLiveUserId(const Context& context, const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_LIVE_USER_ID, unit_ptr->live_user_id(), 0, keys,
                 account_ptr->account_type());
}
void GeneratorFactory::RiskLimitAppId(const Context& context, const TableSchema& table_schema,
                                      ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* app_release_ptr = unit_ptr->package_id_ref_AdAppRelease();
  if (app_release_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_APP_ID, app_release_ptr->app_id(), 0, keys,
                   account_ptr->account_type());
  }
}
void GeneratorFactory::RiskLimitGlobalAppId(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* app_release_ptr = unit_ptr->package_id_ref_AdAppRelease();
  if (app_release_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_GLOBAL_APP_ID, app_release_ptr->global_app_id(), 0, keys,
                   account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitPackageId(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  auto* app_release_ptr = unit_ptr->package_id_ref_AdAppRelease();

  if (app_release_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_APP_PACKAGE_ID, app_release_ptr->package_id(), 0, keys,
                   account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitFanstopAuthorId(const Context& context, const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);
  if (creative_ptr &&
      creative_ptr->live_creative_type() != kuaishou::ad::AdEnum::LIVE_STREAM_CREATIVE_TYPE &&
      creative_ptr->live_creative_type() != kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_FANS_USER_ID, creative_ptr->author_id(), 0, keys,
                   account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitItemId(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
  if (unit_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_ITEM_ID, unit_ptr->item_id(), 0, keys,
                   account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitUnit(const Context& context, const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);

  auto* live_ptr = unit_ptr->live_user_id_ref_LiveStreamUserInfo();
  auto* app_release_ptr = unit_ptr->package_id_ref_AdAppRelease();

  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_USER_ID, account_ptr->user_id(), 0, keys,
                 account_ptr->account_type());
  if (live_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_LIVE_ID, live_ptr->live_stream_id(), 0, keys,
                   account_ptr->account_type());
  }

  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_INDUSTRY_ID, account_ptr->industry_id_v3(),
                 unit_ptr->account_id(), keys, account_ptr->account_type());

  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_ACCOUNT_ID, unit_ptr->account_id(), 0, keys,
                 account_ptr->account_type());
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_UNIT_ID, unit_ptr->id(), 0, keys,
                 account_ptr->account_type());
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_PRODUCT_NAME, account_ptr->city_product_id(), 0, keys,
                 account_ptr->account_type());
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_LICENCE_NUM, account_ptr->city_license_no(), 0, keys,
                 account_ptr->account_type());
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_LIVE_USER_ID, unit_ptr->live_user_id(), 0, keys,
                 account_ptr->account_type());

  if (app_release_ptr) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_APP_ID, app_release_ptr->app_id(), 0, keys,
                   account_ptr->account_type());
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_GLOBAL_APP_ID, app_release_ptr->global_app_id(), 0, keys,
                   account_ptr->account_type());
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_APP_PACKAGE_ID, app_release_ptr->package_id(), 0, keys,
                   account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitCreative(const Context& context, const TableSchema& table_schema,
                                         ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);

  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_PHOTO_ID, creative_ptr->photo_id(), 0, keys,
                 account_ptr->account_type());
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_CREATIVE_ID, creative_ptr->id(), 0, keys,
                 account_ptr->account_type());
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_COVER_ID, creative_ptr->cover_id(), 0, keys,
                 account_ptr->account_type());

  for (auto k : creative_ptr->risk_labels()) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_RISK_LABEL, k, 0, keys, account_ptr->account_type());
  }
}

void GeneratorFactory::RiskLimitPhotoId(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_PHOTO_ID, creative_ptr->photo_id(), 0, keys,
                 account_ptr->account_type());
}

void GeneratorFactory::RiskLimitCreativeId(const Context& context, const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_CREATIVE_ID, creative_ptr->id(), 0, keys,
                 account_ptr->account_type());
}

void GeneratorFactory::RiskLimitCoverId(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);
  RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_COVER_ID, creative_ptr->cover_id(), 0, keys,
                 account_ptr->account_type());
}

void GeneratorFactory::RiskLimitRiskLabel(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  auto [creative_ptr, unit_ptr, campaign_ptr, account_ptr] = ExtensionCreative(table_schema, row_wrapper);
  for (auto k : creative_ptr->risk_labels()) {
    RiskLimitField(+kuaishou::ad::AdEnum::LIMIT_TYPE_RISK_LABEL, k, 0, keys, account_ptr->account_type());
  }
}


template <const char* path, int type> struct RiskIndustry {
  bool Gen(const TableSchema& table_schema, ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
    auto [unit_ptr, campaign_ptr, account_ptr] = ExtensionUnit(table_schema, row_wrapper);
    auto* target_ptr = ExtensionUnitTarget(table_schema, row_wrapper);

    if (account_ptr->industry_id_v3() == 0) {
      return false;
    }

    static auto* limit_table = AdTableInstance::GetDB().Lookup("RiskIndustryInitiative");
    static auto* white_table = AdTableInstance::GetDB().Lookup("RiskIndustryWhiteAccountResult");
    if (!limit_table || !white_table) {
      return false;
    }
    if (white_table->Find(unit_ptr->account_id())) {
      return false;
    }

    static auto desc = limit_table->GetTableSchema().FindColumnDescByName(path);
    auto row = limit_table->Find(internal::GenDateIdKey(account_ptr->industry_id_v3() * 1000, type));
    if (!row) {
      return false;
    }
    auto value = desc.GetValue<const std::vector<int64_t>*>(row);
    for (auto v : *value) {
      keys->emplace_back(HashOf(v));
    }
    return true;
  }
};

void GeneratorFactory::NebulaRiskIndustryRegion(const Context& context, const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "forbidden_region_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 2> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::NebulaRiskIndustrySchedule(const Context& context, const TableSchema& table_schema,
                                                  ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "schedule_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 2> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::NebulaRiskIndustryAge(const Context& context, const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "forbidden_age_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 2> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::NebulaRiskIndustryRate(const Context& context, const TableSchema& table_schema,
                                              ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "rate_limiter_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 2> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::RiskIndustryRegion(const Context& context, const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "forbidden_region_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 0> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::RiskIndustrySchedule(const Context& context, const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "schedule_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 0> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::RiskIndustryAge(const Context& context, const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "forbidden_age_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 0> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}
void GeneratorFactory::RiskIndustryRate(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key128>* keys) {
  static const char field[] = "rate_limiter_list";
  static RiskIndustry<field, -1> gen1;
  gen1.Gen(table_schema, row_wrapper, keys);
  static RiskIndustry<field, 0> gen2;
  gen2.Gen(table_schema, row_wrapper, keys);
}

// 直播统一召回融合
static bool IsOuterDirectlive(ConstRowWrapper row_wrapper) {
  auto creative = reinterpret_cast<Creative*>(row_wrapper.GetStruct<FeedPayload>()->creative_ptr);
  return creative->live_creative_type() == kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT;
}

// 搜索直投广告判断，用于默认在所有 含有短视频/短视频引流直播间 的倒排里过滤
static bool IsSearchAd(const GeneratorFactory::Context& context, ConstRowWrapper row_wrapper) {
  if (!context.enable_payload_search_mingtou_filter) {
    // 不打开开关时，不进行过滤
    return false;
  }
  auto unit = reinterpret_cast<Unit*> (row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  return unit->ad_type() == 1;
}

void GeneratorFactory::FixAuthorIdIndex(const Context& context, const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper, std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  if (payload->author_id == 0 || IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::FixP2LAuthorIdIndex(const Context& context,
                                           const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper,
                                           std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (payload->author_id == 0 || IsOuterDirectlive(row_wrapper)
      || creative->live_creative_type() != 2
      || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::FixP2LIdIndex(const Context& context,
                                     const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper,
                                     std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 2 || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::FixPhotoAndP2LIdIndex(const Context& context,
                                             const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper,
                                             std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 2 &&
      creative->live_creative_type() != 0 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::FixPhotoIdIndex(const Context& context,
                                       const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper,
                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::FixPhotoAuthorIdIndex(const Context& context,
                                             const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper,
                                             std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (payload->author_id == 0 || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::FixMobileAuthorIdIndex(const Context& context,
                                              const TableSchema& table_schema,
                                              ConstRowWrapper row_wrapper,
                                              std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (payload->author_id == 0 || IsOuterDirectlive(row_wrapper)
      || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
      payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  if (campaign->type() == 14) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::FixLiveStreamIdIndex(const Context& context,
                                            const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper,
                                            std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() == 0 || payload->live_stream_id == 0 ||
      IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->live_stream_id));
}

void GeneratorFactory::FixMobileDirectLiveStreamIdIndex(const Context& context,
                                                        const TableSchema& table_schema,
                                                        ConstRowWrapper row_wrapper,
                                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  // 本身隔离了外循环, 变更条件时注意外循环隔离
  if (creative->live_creative_type() != 1 || payload->live_stream_id == 0) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE
    && payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端二期不建
  if (campaign->type() == 14) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->live_stream_id));
}

void GeneratorFactory::PhotoAndP2LIdIndex(const Context& context,
                                          const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper,
                                          std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (creative->live_creative_type() != 2 &&
      creative->live_creative_type() != 0 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::PhotoAndP2LIdWithSearchIndex(const Context& context,
                                                    const TableSchema& table_schema,
                                                    ConstRowWrapper row_wrapper,
                                                    std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 2 &&
      creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::P2LIdIndex(const Context& context,
                                  const TableSchema& table_schema,
                                  ConstRowWrapper row_wrapper,
                                  std::vector<Key64>* keys) {
  // 本身隔离了外循环，需要变更时注意外循环隔离
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 2 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::SpuGoodItemIndex(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto wt_product = reinterpret_cast<WTProduct*>(payload->wt_product);
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (wt_product == nullptr || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_product->good_quality() == 1 ||
      wt_product->good_quality() == 2) {
    return;
  }
  if (context.enable_new_index_switch_spu) {
    keys->emplace_back(HashOfToken1(wt_product->ecom_spu_id()));
  } else {
    keys->emplace_back(HashOfToken1(wt_product->spu_id_v1()));
  }
}

void GeneratorFactory::PhotoIdIndex(const Context& context,
                                    const TableSchema& table_schema,
                                    ConstRowWrapper row_wrapper,
                                    std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->photo_id()));
}

void GeneratorFactory::OriginalPhotoIndex(const Context& context,
                                          const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper,
                                          std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  const int64_t& key = context.mature_photo_recall_key;
  const int32_t& cost = context.mature_photo_recall_cost;
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  // 原创表
  if (wt_photo != nullptr && wt_photo->similar_type() != 2) {
    return;
  }
  if (creative->ecom_statistics_cost() < cost) {
    return;
  }
  keys->emplace_back(HashOfToken1(key));
}

void GeneratorFactory::OriginalPhotoNewIndex(const Context& context,
                                             const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper,
                                             std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  const int64_t key = MaturePhotoRecallKey();
  const int32_t cost = MaturePhotoRecallCost();
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  // 原创表
  if (wt_photo == nullptr) {
    return;
  }
  if (wt_photo->similar_type() != 2) {
    return;
  }
  if (creative->ecom_statistics_cost() < cost) {
    return;
  }
  keys->emplace_back(HashOfToken1(key));
}

void GeneratorFactory::CategoryIndex(const Context& context,
                                     const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper,
                                     std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_product = reinterpret_cast<WTProduct*>(payload->wt_product);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  // 商品三级类目表
  if (wt_product == nullptr) {
    return;
  }
  keys->emplace_back(HashOfToken1(wt_product->category_level_3_id()));
}

void GeneratorFactory::FixCategoryIndex(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_product = reinterpret_cast<WTProduct*>(payload->wt_product);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  // 商品表
  if (wt_product == nullptr) {
    return;
  }
  if (wt_photo == nullptr) {
    return;
  }
  keys->emplace_back(HashOfToken1(wt_product->product_tag()));
}

void GeneratorFactory::HostingPhotoIndex(const Context& context,
                                         const TableSchema& table_schema,
                                         ConstRowWrapper row_wrapper,
                                         std::vector<Key64>* keys) {
  int64_t key = HostingPhotoRecallKey();
  int32_t min_cost = HostingPhotoRecallMinCost();
  int32_t max_cost = HostingPhotoRecallMaxCost();

  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  // 商品托管表
  if (campaign->scene_oriented_type() != 8
        && campaign->scene_oriented_type() != 13
        && campaign->scene_oriented_type() != 20
        && campaign->scene_oriented_type() != 24
        && campaign->scene_oriented_type() != 38) {
    return;
  }
  if (creative->ecom_statistics_cost() < min_cost) {
    return;
  }
  if (creative->ecom_statistics_cost() > max_cost) {
    return;
  }
  keys->emplace_back(HashOfToken1(key));
}

void GeneratorFactory::MerchantStoreWideIndex(const Context& context,
                                              const TableSchema& table_schema,
                                              ConstRowWrapper row_wrapper,
                                              std::vector<Key64>* keys) {
  const int64_t& key = context.merchant_storewide_recall_key;
  const int64_t& min_cost = context.merchant_storewide_min_cost;

  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  auto unit = reinterpret_cast<Unit*> (row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  // 非短带 campaign->type() 跳过
  if (campaign->type() != 13 && campaign->type() != 17
      && campaign->type() != 18 && campaign->type() != 20) {
    return;
  }
  // 非全站优化目标跳过
  if (unit->ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS) {
    return;
  }
  if (wt_photo == nullptr) {
    return;
  }
  if (creative->ecom_statistics_cost() < min_cost) {
    return;
  }
  keys->emplace_back(HashOfToken1(key));
}

void GeneratorFactory::CreativeIdIndex(const Context& context,
                                       const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper,
                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  keys->emplace_back(HashOfToken1(creative->id()));
}

void GeneratorFactory::ItemIdIndex(const Context& context,
                                   const TableSchema& table_schema,
                                   ConstRowWrapper row_wrapper,
                                   std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto unit = reinterpret_cast<Unit*> (row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (unit->item_id() == 0 || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(unit->item_id()));
}

void GeneratorFactory::AuthorIdIndex(const Context& context,
                                     const TableSchema& table_schema,
                                     ConstRowWrapper row_wrapper,
                                     std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  if (payload->author_id == 0 || IsOuterDirectlive(row_wrapper)
      || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::PhotoAuthorIdIndex(const Context& context,
                                          const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper,
                                          std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (payload->author_id == 0 || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::LiveStreamIdIndex(const Context& context,
                                         const TableSchema& table_schema,
                                         ConstRowWrapper row_wrapper,
                                         std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() == 0 || payload->live_stream_id == 0 ||
      IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->live_stream_id));
}

void GeneratorFactory::DirectLiveStreamIdIndex(const Context& context,
                                               const TableSchema& table_schema,
                                               ConstRowWrapper row_wrapper,
                                               std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  // 本身隔离了外循环
  // 需要变动 live_creative_type != 1 这个条件时，需要考虑外循环隔离
  if (creative->live_creative_type() != 1 || payload->live_stream_id == 0) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->live_stream_id));
}

void GeneratorFactory::MobileDirectLiveStreamIdIndex(const Context& context,
                                                     const TableSchema& table_schema,
                                                     ConstRowWrapper row_wrapper,
                                                     std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  // 本身隔离了外循环, 变更条件时注意外循环隔离
  if (creative->live_creative_type() != 1 || payload->live_stream_id == 0) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE
    && payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端二期不建
  if (campaign->type() == 14) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->live_stream_id));
}

void GeneratorFactory::UnitPhotoIdIndex(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  bool is_outer_p2l = context.enable_outer_unit_photo_p2l && context.outer_team;
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (is_outer_p2l) {
    if (creative->live_creative_type() != 0 && creative->live_creative_type() != 4) {
      return;
    }
  } else {
    if (creative->live_creative_type() != 0) {
      return;
    }
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(), creative->unit_id()));
}

void GeneratorFactory::CampaignPhotoIdIndex(const Context& context,
                                            const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper,
                                            std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (context.outer_team || context.universe) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    creative->campaign_id()));
}

void GeneratorFactory::NewCusLiveAuthorIdIndex(const Context& context,
                                                const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper,
                                                std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto account_wt_author = reinterpret_cast<WTAuthor*>(payload->account_wt_author);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (account_wt_author == nullptr) {
    return;
  }
  if (creative->live_creative_type() == 0) {
    return;
  }
  if (context.now_time_second - account_wt_author->first_cost_date() > 7 * 24 * 60 * 60 &&
      account_wt_author->first_cost_date() != 0) {
    return;
  }
  if (creative->live_creative_type() == 1) {
    keys->emplace_back(
        HashOf4Payload(payload->live_stream_id, unit->ocpx_action_type(), creative->live_creative_type()));
  } else {
    keys->emplace_back(
        HashOf4Payload(creative->photo_id(), unit->ocpx_action_type(), creative->live_creative_type()));
  }
}


void GeneratorFactory::NewCusPhotoAuthorIdIndex(const Context& context,
                                                const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper,
                                                std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto now_time_second = base::GetTimestamp() / 1e6;
  auto account_wt_author = reinterpret_cast<WTAuthor*>(payload->account_wt_author);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);

  if (account_wt_author == nullptr) {
    return;
  }
  if (creative->live_creative_type() != 2 && creative->live_creative_type() != 0
      || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (account_wt_author->first_cost_date() != 0 &&
      now_time_second - account_wt_author->first_cost_date() > 7 * 24 * 60 * 60) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(), unit->ocpx_action_type()));
}

void GeneratorFactory::SmbColdStartLiveAuthorIdIndex(const Context& context,
                                                     const TableSchema& table_schema,
                                                     ConstRowWrapper row_wrapper,
                                                     std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto account_wt_author = reinterpret_cast<WTAuthor*>(payload->account_wt_author);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  if (IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  // 作者消耗分层
  if (context.enable_smb_coldstart_cost_level_fix) {
    if (account_wt_author == nullptr ||
        !(account_wt_author->cost_level_92d_int() >= 1 &&
          account_wt_author->cost_level_92d_int() <= 2)) {
      return;
    }
  } else {
    if (account_wt_author == nullptr ||
        account_wt_author->cost_level_92d_int() > 1) {
      return;
    }
  }
  if (creative->live_creative_type() == 1) {
    keys->emplace_back(HashOf4Payload(payload->live_stream_id,
                                      unit->ocpx_action_type(),
                                      creative->live_creative_type()));
  } else {
    keys->emplace_back(HashOf4Payload(creative->photo_id(),
                                      unit->ocpx_action_type(),
                                      creative->live_creative_type()));
  }
}


void GeneratorFactory::UnitP2LIdIndex(const Context& context,
                                      const TableSchema& table_schema,
                                      ConstRowWrapper row_wrapper,
                                      std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != 2) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    creative->unit_id()));
}

void GeneratorFactory::UnitPhotoP2lIdIndex(const Context& context,
                                           const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper,
                                           std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != 2 && creative->live_creative_type() != 0) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    creative->unit_id()));
}

void GeneratorFactory::ProductOcpxIndex(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (creative->live_creative_type() != kuaishou::ad::AdEnum::UNKNOWN_LIVE_CREATIVE_TYPE) {
    return;
  }
  if (unit->item_id() == 0) {
    return;
  }
  keys->emplace_back(HashOfToken2(unit->item_id(), unit->ocpx_action_type()));
}

void GeneratorFactory::OcpxP2LIdStableFixIndex(const Context& context,
                                               const TableSchema& table_schema,
                                               ConstRowWrapper row_wrapper,
                                               std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (creative->live_creative_type() != 2 || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    unit->ocpx_action_type()));
}

void GeneratorFactory::P2LLiveOcpxStableFixIndex(const Context& context,
                                                 const TableSchema& table_schema,
                                                 ConstRowWrapper row_wrapper,
                                                 std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (creative->live_creative_type() != 2 || IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken2(payload->live_stream_id,
                                    unit->ocpx_action_type()));
}

void GeneratorFactory::OcpxPhotoAndP2LIdStableFixIndex(const Context& context,
                                                       const TableSchema& table_schema,
                                                       ConstRowWrapper row_wrapper,
                                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (creative->live_creative_type() != 2 && creative->live_creative_type() != 0
      || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (context.inner_photo && (campaign->inner_photo_tag() == 2 ||
      unit->inner_photo_tag() == 2 ||
      creative->inner_photo_tag() == 2)) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    unit->ocpx_action_type()));
}

void GeneratorFactory::OcpxMobilePhotoAndP2LIdStableIndex(const Context& context,
                                                          const TableSchema& table_schema,
                                                          ConstRowWrapper row_wrapper,
                                                          std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (creative->live_creative_type() != 2 && creative->live_creative_type() != 0
      || IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE
    && payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端二期不建
  if (campaign->type() == 14) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    unit->ocpx_action_type()));
}


void GeneratorFactory::OcpxPhotoLiveIdStableFixIndex(const Context& context,
                                                     const TableSchema& table_schema,
                                                     ConstRowWrapper row_wrapper,
                                                     std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (creative->live_creative_type() == 1) {
    keys->emplace_back(HashOf4Payload(payload->live_stream_id,
                                      unit->ocpx_action_type(),
                                      creative->live_creative_type()));
  } else {
    keys->emplace_back(HashOf4Payload(creative->photo_id(),
                                      unit->ocpx_action_type(),
                                      creative->live_creative_type()));
  }
}

void GeneratorFactory::ColdstartOcpxPhotoLiveIdStableFixIndex(const Context& context,
                                                              const TableSchema& table_schema,
                                                              ConstRowWrapper row_wrapper,
                                                              std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  if (IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  // 直播冷启打标
  int tag = wt_live->inner_live_coldstart_tag();
  if (tag != 1 && tag != 2 && tag != 3) {
    return;
  }
  if (creative->live_creative_type() == 1) {
    keys->emplace_back(HashOf4Payload(payload->live_stream_id,
                                      unit->ocpx_action_type(),
                                      creative->live_creative_type()));
  } else {
    keys->emplace_back(HashOf4Payload(creative->photo_id(),
                                      unit->ocpx_action_type(),
                                      creative->live_creative_type()));
  }
}

void GeneratorFactory::MobileOcpxDirectLiveStreamIdStableFixIndex(const Context& context,
                                                                  const TableSchema& table_schema,
                                                                  ConstRowWrapper row_wrapper,
                                                                  std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  // 本身隔离了外循环， 变更此条件时需要注意外循环隔离
  if (creative->live_creative_type() != 1 || payload->live_stream_id == 0) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
      payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端二期不建
  if (campaign->type() == 14) {
    return;
  }
  keys->emplace_back(HashOfToken2(payload->live_stream_id,
                                    unit->ocpx_action_type()));
}

void GeneratorFactory::V3MobileDirectLiveStreamIdIndex(const Context& context,
                                                       const TableSchema& table_schema,
                                                       ConstRowWrapper row_wrapper,
                                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  // 本身隔离了外循环， 变更此条件时需要注意外循环隔离
  if (creative->live_creative_type() != 1 || payload->live_stream_id == 0) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
      payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端三期平切
  if (campaign->type() == 14 &&
      campaign->charge_mode() != kuaishou::ad::AdEnum::ORDER) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->live_stream_id));
}

void GeneratorFactory::V3MobileAuthorIdIndex(const Context& context,
                                             const TableSchema& table_schema,
                                             ConstRowWrapper row_wrapper,
                                             std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (payload->author_id == 0 ||
      IsOuterDirectlive(row_wrapper) ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
      payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端三期平切
  if (campaign->type() == 14 &&
      campaign->charge_mode() != kuaishou::ad::AdEnum::ORDER) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::V3OcpxMobilePhotoAndP2LIdStableIndex(const Context& context,
                                                            const TableSchema& table_schema,
                                                            ConstRowWrapper row_wrapper,
                                                            std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (creative->live_creative_type() != 2 &&
      creative->live_creative_type() != 0 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE
    && payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端三期平切
  if (campaign->type() == 14 &&
      campaign->charge_mode() != kuaishou::ad::AdEnum::ORDER) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(), unit->ocpx_action_type()));
}

void GeneratorFactory::V3MobileOcpxDirectLiveStreamIdStableIndex(const Context& context,
                                                                 const TableSchema& table_schema,
                                                                 ConstRowWrapper row_wrapper,
                                                                 std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  // 本身隔离了外循环， 变更此条件时需要注意外循环隔离
  if (creative->live_creative_type() != 1 ||
      payload->live_stream_id == 0) {
    return;
  }
  // 只建移动版和粉条
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE
    && payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端三期平切
  if (campaign->type() == 14 &&
      campaign->charge_mode() != kuaishou::ad::AdEnum::ORDER) {
    return;
  }
  keys->emplace_back(HashOfToken2(payload->live_stream_id, unit->ocpx_action_type()));
}

void GeneratorFactory::AuthorIdNobidIndex(const Context& context,
                                          const TableSchema& table_schema,
                                          ConstRowWrapper row_wrapper,
                                          std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (payload->author_id == 0 ||
      IsOuterDirectlive(row_wrapper) ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (unit->speed() != kuaishou::ad::AdEnum::SPEED_NO_BID) {
    return;
  }
  if (campaign->type() == 14 &&
      campaign->charge_mode() == kuaishou::ad::AdEnum::ORDER) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

void GeneratorFactory::UnitIdIndex(const Context& context,
                                   const TableSchema& table_schema,
                                   ConstRowWrapper row_wrapper,
                                   std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  keys->emplace_back(HashOfToken1(creative->unit_id()));
}

void GeneratorFactory::LiveUnitIdIndex(const Context& context,
                                       const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper,
                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 1) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->unit_id()));
}

void GeneratorFactory::P2LUnitIdIndex(const Context& context,
                                      const TableSchema& table_schema,
                                      ConstRowWrapper row_wrapper,
                                      std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 2 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->unit_id()));
}

void GeneratorFactory::PhotoUnitIdIndex(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() != 0 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  keys->emplace_back(HashOfToken1(creative->unit_id()));
}
// 冷启动创意索引
void GeneratorFactory::ColdStartUnitIdIndex(const Context& context,
                                            const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper,
                                            std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  if (!context.enable_coldstart_index) {
    return;
  }
  // 只保留直播直投
  if (creative->live_creative_type() != 1) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  int tag = wt_live->inner_live_coldstart_tag();
  if (tag == 1 || tag == 2 || tag == 3) {
    keys->emplace_back(HashOfToken1(creative->unit_id()));
  }
}

// 冷启动创意索引
void GeneratorFactory::ColdStartCreativeIdIndex(const Context& context,
                                                const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper,
                                                std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  if (!context.enable_coldstart_index) {
    return;
  }
  // 只保留直播直投
  if (creative->live_creative_type() != 1) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  int tag = wt_live->inner_live_coldstart_tag();
  if (tag == 1 || tag == 2 || tag == 3) {
    keys->emplace_back(HashOfToken1(creative->id()));
  }
}

void GeneratorFactory::MobileAuthorIdIndex(const Context& context,
                                           const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper,
                                           std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (payload->author_id == 0 ||
      IsOuterDirectlive(row_wrapper) ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_ESP_MOBILE &&
      payload->account_type != kuaishou::ad::AdEnum::ACCOUNT_FANSTOP_V2) {
    return;
  }
  // 移动端二期不建
  if (campaign->type() == 14) {
    return;
  }
  keys->emplace_back(HashOfToken1(payload->author_id));
}

/*
  wt_live 生成
*/
void GeneratorFactory::SpuOrItem2CreativeIndex(const Context& context,
                                              const TableSchema& table_schema,
                                              ConstRowWrapper row_wrapper,
                                              std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  auto tag_gen = [&keys](const std::vector<int64_t>& source,
                        int64_t primary_type, int64_t action_type) {
    for (auto val_tag : source) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };
  std::vector<int64_t> tmp_keys;
  const int64_t& chart_list_length = context.invert_list_length_for_chart;
  auto tag_gen_with_trunc = [&keys, &tmp_keys, &chart_list_length]
                            (const std::vector<int64_t>& source,
                            int64_t primary_type,
                            int64_t action_type) {
    tmp_keys = source;
    if (tmp_keys.size() > chart_list_length) {
      tmp_keys.resize(chart_list_length);
    }
    for (auto val_tag : tmp_keys) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };
  do {
    tag_gen(wt_live->explain_spus(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::EXPLAIN);
    tag_gen_with_trunc(wt_live->chart_spus(), +TAG_PRI_TYPE::SPU,
                       +TAG_ACTION_TYPE::CHART);
    tag_gen(wt_live->future_spus(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::FUTURE);
  } while (false);
  do {
    tag_gen(wt_live->explain_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::EXPLAIN);
    tag_gen_with_trunc(wt_live->chart_items(), +TAG_PRI_TYPE::ITEM,
                       +TAG_ACTION_TYPE::CHART);
    tag_gen(wt_live->future_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::FUTURE);
  } while (false);
}

void GeneratorFactory::ColdStartSpuOrItem2CreativeIndex(const Context& context,
                                                        const TableSchema& table_schema,
                                                        ConstRowWrapper row_wrapper,
                                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  // 只返回冷启直播间的商品映射
  int tag = wt_live->inner_live_coldstart_tag();
  if (tag != 1 && tag != 2 && tag != 3) {
    return;
  }
  auto tag_gen = [&keys](const std::vector<int64_t>& source,
                         int64_t primary_type, int64_t action_type) {
    for (auto val_tag : source) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };
  std::vector<int64_t> tmp_keys;
  const int64_t& chart_list_length = context.invert_list_length_for_chart;
  auto tag_gen_with_trunc = [&keys, &tmp_keys, &chart_list_length]
                            (const std::vector<int64_t>& source,
                            int64_t primary_type,
                            int64_t action_type) {
    tmp_keys = source;
    if (tmp_keys.size() > chart_list_length) {
      tmp_keys.resize(chart_list_length);
    }
    for (auto val_tag : tmp_keys) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };
  do {
    tag_gen(wt_live->explain_spus(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::EXPLAIN);
    tag_gen_with_trunc(wt_live->chart_spus(), +TAG_PRI_TYPE::SPU,
                      +TAG_ACTION_TYPE::CHART);
    tag_gen(wt_live->future_spus(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::FUTURE);
  } while (false);
  do {
  tag_gen(wt_live->explain_items(), +TAG_PRI_TYPE::ITEM,
          +TAG_ACTION_TYPE::EXPLAIN);
  tag_gen_with_trunc(wt_live->chart_items(), +TAG_PRI_TYPE::ITEM,
                     +TAG_ACTION_TYPE::CHART);
  tag_gen(wt_live->future_items(), +TAG_PRI_TYPE::ITEM,
          +TAG_ACTION_TYPE::FUTURE);
  } while (false);
}
/*
  wt_photo  生成
*/
void GeneratorFactory::SpuOrItem2CreativeForPhotoIndex(const Context& context,
                                                       const TableSchema& table_schema,
                                                       ConstRowWrapper row_wrapper,
                                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_photo == nullptr) {
    return;
  }
  // 过滤消耗为 0 的创意
  if (creative->ecom_statistics_cost() <= 0
      || creative->ecom_statistics_merchant_order_num() <= 0) {
    return;
  }
  auto tag_gen = [&keys](const std::vector<int64_t>& source,
                         int64_t primary_type, int64_t action_type) {
    for (auto val_tag : source) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };

  do {
    tag_gen(wt_photo->ad_spu_ids(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::DEFAULT);
    tag_gen(wt_photo->ad_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::DEFAULT);
  } while (false);
}
// 和 v1 一致 可以删除
void GeneratorFactory::
      SpuOrItem2CreativeForPhotoIndexV2(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_photo == nullptr) {
    return;
  }
  // 过滤消耗为 0 的创意
  if (creative->ecom_statistics_cost() <= 0
      || creative->ecom_statistics_merchant_order_num() <= 0) {
    return;
  }
  auto tag_gen = [&keys](const std::vector<int64_t>& source,
                         int64_t primary_type, int64_t action_type) {
    for (auto val_tag : source) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };

  do {
    tag_gen(wt_photo->ad_spu_ids(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::DEFAULT);
    tag_gen(wt_photo->ad_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::DEFAULT);
  } while (false);
}

void GeneratorFactory::EcomSpuOrItem2CreativeIndex(const Context& context,
                                                   const TableSchema& table_schema,
                                                   ConstRowWrapper row_wrapper,
                                                   std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_live = reinterpret_cast<WTLive*>(payload->wt_live);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_live == nullptr) {
    return;
  }
  auto tag_gen = [&keys](const std::vector<int64_t>& source,
                         int64_t primary_type, int64_t action_type) {
    for (auto val_tag : source) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };

  std::vector<int64_t> tmp_keys;
  const int64_t& chart_list_length = context.invert_list_length_for_chart;
  auto tag_gen_with_trunc = [&keys, &tmp_keys, &chart_list_length]
                            (const std::vector<int64_t>& source,
                            int64_t primary_type,
                            int64_t action_type) {
    tmp_keys = source;
    if (tmp_keys.size() > chart_list_length) {
      tmp_keys.resize(chart_list_length);
    }
    for (auto val_tag : tmp_keys) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };

  do {
    keys->emplace_back(HashOf4Payload(wt_live->explain_ecom_spu_id(),
                                      +TAG_PRI_TYPE::SPU,
                                      +TAG_ACTION_TYPE::EXPLAIN));
    tag_gen_with_trunc(wt_live->chart_ecom_spu_ids(), +TAG_PRI_TYPE::SPU,
                       +TAG_ACTION_TYPE::CHART);
    tag_gen(wt_live->future_ecom_spu_ids(), +TAG_PRI_TYPE::SPU,
            +TAG_ACTION_TYPE::FUTURE);
  } while (false);

  do {
    tag_gen(wt_live->explain_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::EXPLAIN);
    tag_gen_with_trunc(wt_live->chart_items(), +TAG_PRI_TYPE::ITEM,
                       +TAG_ACTION_TYPE::CHART);
    tag_gen(wt_live->future_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::FUTURE);
  } while (false);
}


void GeneratorFactory::EcomSpuOrItem2CreativeForPhotoIndex(const Context& context,
                                                           const TableSchema& table_schema,
                                                           ConstRowWrapper row_wrapper,
                                                           std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_photo == nullptr) {
    return;
  }
  auto tag_gen = [&keys](const std::vector<int64_t>& source,
                         int64_t primary_type, int64_t action_type) {
    for (auto val_tag : source) {
      keys->emplace_back(HashOf4Payload(val_tag, primary_type, action_type));
    }
  };

  do {
    keys->emplace_back(HashOf4Payload(wt_photo->ecom_spu_id(),
                                      +TAG_PRI_TYPE::SPU,
                                      +TAG_ACTION_TYPE::DEFAULT));
    tag_gen(wt_photo->ad_items(), +TAG_PRI_TYPE::ITEM,
            +TAG_ACTION_TYPE::DEFAULT);
  } while (false);
}

void GeneratorFactory::OuterDirectLiveCreativeIdIndex(const Context& context,
                                                      const TableSchema& table_schema,
                                                      ConstRowWrapper row_wrapper,
                                                      std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  if (creative->live_creative_type() ==
      kuaishou::ad::AdEnum::DIRECT_LIVE_STREAM_FOR_NON_MERCHANT) {
    keys->emplace_back(HashOfToken1(creative->id()));
  }
}

void GeneratorFactory::FanstopAuthorFlagIndex(const Context& context,
                                              const TableSchema& table_schema,
                                              ConstRowWrapper row_wrapper,
                                              std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto live_wt_author = reinterpret_cast<WTAuthor*>(payload->live_wt_author);
  auto account_wt_author = reinterpret_cast<WTAuthor*>(payload->account_wt_author);
  if (creative->live_creative_type() != 0) {
    if (live_wt_author != nullptr && live_wt_author->fanstop_author_flag() > 0) {
      keys->emplace_back(HashOfToken1(live_wt_author->fanstop_author_flag()));
    }
  } else {
    if (account_wt_author != nullptr && account_wt_author->fanstop_author_flag() > 0) {
      keys->emplace_back(HashOfToken1(account_wt_author->fanstop_author_flag()));
    }
  }
}

void GeneratorFactory::AccountSpuPhotoIndex(const Context& context,
                                            const TableSchema& table_schema,
                                            ConstRowWrapper row_wrapper,
                                            std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  auto wt_product = reinterpret_cast<WTProduct*>(payload->wt_product);
  if (unit->ocpx_action_type() != kuaishou::ad::AD_STOREWIDE_ROAS) {
    return;
  }
  if (campaign->type() != 13) {
    return;
  }
  if (wt_product == nullptr) {
    return;
  }
  if (context.enable_new_index_switch_spu) {
    keys->emplace_back(HashOf4Payload(creative->account_id(),
                                      wt_product->ecom_spu_id(),
                                      creative->photo_id()));
  } else {
    keys->emplace_back(HashOf4Payload(creative->account_id(),
                                      wt_product->spu_id_v1(),
                                      creative->photo_id()));
  }
}

void GeneratorFactory::SdpaOcpxIdIndex(const Context& context,
                                       const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper,
                                       std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  if (unit->dpa_product_id() <= 0) {
    return;
  }
  keys->emplace_back(HashOfToken2(unit->dpa_product_id(),
                                    unit->ocpx_action_type()));
}

void GeneratorFactory::CidUnitPhotoIdIndex(const Context& context,
                                           const TableSchema& table_schema,
                                           ConstRowWrapper row_wrapper,
                                           std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  bool is_outer_p2l = context.enable_outer_unit_photo_p2l && context.outer_team;
  if (unit->ocpx_action_type() != kuaishou::ad::CID_ROAS &&
      unit->ocpx_action_type() != kuaishou::ad::CID_EVENT_ORDER_PAID) {
    return;
  }
  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (is_outer_p2l) {
    if (creative->live_creative_type() != 0 &&
        creative->live_creative_type() != 4) {
      return;
    }
  } else {
    if (creative->live_creative_type() != 0) {
      return;
    }
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(), creative->unit_id()));
}

void GeneratorFactory::ColdStartOcpxPhotoAndP2LIdStableFixIndex(const Context& context,
                                                                const TableSchema& table_schema,
                                                                ConstRowWrapper row_wrapper,
                                                                std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto unit = reinterpret_cast<Unit*>(row_wrapper.GetStruct<FeedPayload>()->unit_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (creative->live_creative_type() != 2 && creative->live_creative_type() != 0 ||
      IsSearchAd(context, row_wrapper)) {
    return;
  }
  if (wt_photo == nullptr) {
    return;
  }
  // 只返回冷启的商品映射
  int photo_tag = wt_photo->inner_photo_coldstart_tag();
  int p2l_tag = wt_photo->inner_p2l_coldstart_tag();
  if ((photo_tag == 0 || photo_tag == 4) && (p2l_tag == 0 || p2l_tag == 4)) {
    return;
  }
  if (context.inner_photo &&
      (campaign->inner_photo_tag() == 2 ||
       unit->inner_photo_tag() == 2 ||
       creative->inner_photo_tag() == 2)) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    unit->ocpx_action_type()));
}

void GeneratorFactory::UnitColdStartPhotoIdIndex(const Context& context,
                                                 const TableSchema& table_schema,
                                                 ConstRowWrapper row_wrapper,
                                                 std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  bool is_outer_p2l = context.enable_outer_unit_photo_p2l && context.outer_team;

  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 直播不建立表
  if (is_outer_p2l) {
    if (creative->live_creative_type() != 0 && creative->live_creative_type() != 4) {
      return;
    }
  } else {
    if (creative->live_creative_type() != 0) {
      return;
    }
  }
  if (wt_photo == nullptr) {
    return;
  }
  // 只返回冷启的商品映射
  int photo_tag = wt_photo->inner_photo_coldstart_tag();
  int p2l_tag = wt_photo->inner_p2l_coldstart_tag();
  if ((photo_tag == 0 || photo_tag == 4) && (p2l_tag == 0 || p2l_tag == 4)) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    creative->unit_id()));
}
void GeneratorFactory::ProgrammaticCreativeListByPhotoIdSearch(const Context& context,
                                                    const TableSchema& table_schema,
                                                    ConstRowWrapper row_wrapper,
                                                    std::vector<Key128>* keys) {
  static auto auto_deliver_type_col_ = table_schema.FindColumnDescByName("auto_deliver_type");
  auto auto_deliver_type = auto_deliver_type_col_.GetValue<int64>(row_wrapper);

  static auto photo_id_col_ = table_schema.FindColumnDescByName("photo_id");
  auto photo_id = photo_id_col_.GetValue<int64>(row_wrapper);

  // 只要程序化素材单元
  if (static_cast<kuaishou::ad::AdEnum::AutoDeliverType>(auto_deliver_type) !=
      kuaishou::ad::AdEnum::PROGRAMMATIC_PHOTO) {
    return;
  }
  // 只要 photo_id != 0
  if (photo_id == 0) {
    return;
  }
  keys->emplace_back(HashOf(photo_id));
  return;
}

void GeneratorFactory::StorewideOcpxLiveIdIndex(const Context& context,
                                                const TableSchema& table_schema,
                                                ConstRowWrapper row_wrapper,
                                                std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  auto unit = reinterpret_cast<Unit*>(payload->unit_ptr);

  if (IsOuterDirectlive(row_wrapper) || IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 只展开直播全站
  if (campaign->scene_oriented_type() != 21) {
    return;
  }
  if (creative->live_creative_type() != 1) {
    return;
  }
  keys->emplace_back(HashOf4Payload(payload->live_stream_id,
                                    unit->ocpx_action_type(),
                                    creative->live_creative_type()));
}

void GeneratorFactory::StorewideUnitP2LIdIndex(const Context& context,
                                               const TableSchema& table_schema,
                                               ConstRowWrapper row_wrapper,
                                               std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto campaign = reinterpret_cast<Campaign*>(payload->campaign_ptr);
  auto unit = reinterpret_cast<Unit*>(payload->unit_ptr);

  if (IsSearchAd(context, row_wrapper)) {
    return;
  }
  // 只展开直播全站
  if (campaign->scene_oriented_type() != 21) {
    return;
  }
  keys->emplace_back(HashOfToken2(creative->photo_id(),
                                    creative->unit_id()));
}

void GeneratorFactory::CidHighBidIndex(const Context& context,
                                       const TableSchema& table_schema,
                                       ConstRowWrapper row_wrapper,
                                       std::vector<Key64>* keys) {
  auto high_bid_map
        = std::any_cast<std::shared_ptr<
          absl::flat_hash_map<int64_t, std::vector<int64_t>>>>
          (*context.custom_var);
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto creative = reinterpret_cast<Creative*>(payload->creative_ptr);
  auto iter = high_bid_map->find(creative->id());
  if (iter != high_bid_map->end()) {
    for (auto& cid : iter->second) {
      keys->emplace_back(HashOfToken1(cid));
    }
  } else {
    keys->emplace_back(HashOfToken1(creative->id()));
  }
}


void GeneratorFactory::OneModelSidIndex(const Context& context,
                                        const TableSchema& table_schema,
                                        ConstRowWrapper row_wrapper,
                                        std::vector<Key64>* keys) {
  auto payload = row_wrapper.GetStruct<FeedPayload>();
  auto wt_photo = reinterpret_cast<WTPhoto*>(payload->wt_photo);
  if (wt_photo == nullptr) {
    return;
  }
  keys->emplace_back(HashOfToken1(wt_photo->sid()));
}

}  // namespace ad_table
}  // namespace ks
