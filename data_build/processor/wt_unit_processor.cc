#include <string>
#include "base/common/logging.h"
#include "teams/ad/data_build/processor/base_processor.h"

namespace ks {
namespace ad {
namespace build_service {
DECLARE_TABLEROW_PROCESSOR(wt_unit)

bool wt_unit_Processor::admit(SchemaFreeIns *sf_ins) {
  if (sf_ins == nullptr || sf_ins->table_row == nullptr) return false;
  LOG_FIRST_N(INFO, 10) << "admit for table=" << table_name_;
  if (!active_creative_status_admit(sf_ins)) {
    return false;
  }
  auto table_row = sf_ins->table_row;
  auto put_status = static_cast<kuaishou::ad::AdEnum::PutStatus>(
     table_row->GetValue<int32_t>("put_status"));
  auto review_status = static_cast<kuaishou::ad::AdEnum::ReviewStatus>(
     table_row->GetValue<int32_t>("review_status"));
  auto community_review_status = static_cast<kuaishou::ad::AdEnum::ReviewStatus>(
     table_row->GetValue<int32_t>("community_review_status"));
  sf_ins->trace_status_code = IndexTraceStatusCode::SUCCESS;
  if (put_status != kuaishou::ad::AdEnum_PutStatus_PUT_STATUS_OPEN) {
    sf_ins->trace_status_code = IndexTraceStatusCode::OFFLINE_NOT_OEPEN_UNIT;
  } else if (community_review_status != kuaishou::ad::AdEnum_ReviewStatus_REVIEW_THROUGH &&
           review_status != kuaishou::ad::AdEnum_ReviewStatus_REVIEW_THROUGH) {
    sf_ins->trace_status_code = IndexTraceStatusCode::OFFLINE_NOT_REVIEW_THROUGH_UNIT;
  }
  return sf_ins->trace_status_code == IndexTraceStatusCode::SUCCESS;
}

bool wt_unit_Processor::parse(SchemaFreeIns *sf_ins) {
  static int64_t TABLE_HASH_CODE = (1UL << 63) - 1;
  if (sf_ins == nullptr || sf_ins->table_row == nullptr) return true;
  LOG_FIRST_N(INFO, 10) << "admit for table=" << table_name_;
  auto row = sf_ins->table_row;
  auto playlet_name = row->GetValue<std::string>("playlet_name");
  if (!playlet_name.empty()) {
    auto playlet_name_hash = base::CityHash64(playlet_name.c_str(), playlet_name.size());
    playlet_name_hash = playlet_name_hash & TABLE_HASH_CODE;
    row->SetValue("playlet_name_hash", playlet_name_hash);
    LOG_EVERY_N(INFO, 1000) << "playlet_name=" << playlet_name << " hash=" << playlet_name_hash;
  }
  return true;
}

REGISTER_TABLEROW_PROCESSOR(wt_unit);
}  // namespace build_service
}  // namespace ad
}  // namespace ks
