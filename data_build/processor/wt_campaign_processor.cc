#include "base/common/logging.h"
#include "teams/ad/data_build/processor/base_processor.h"

namespace ks {
namespace ad {
namespace build_service {
DECLARE_TABLEROW_PROCESSOR(wt_campaign)
bool wt_campaign_Processor::admit(SchemaFreeIns* sf_ins) {
  if (sf_ins == nullptr || sf_ins->table_row == nullptr) return false;
  LOG_FIRST_N(INFO, 10) << "admit for table=" << table_name_;
  if (!active_creative_status_admit(sf_ins)) {
    return false;
  }
  return true;
}

bool wt_campaign_Processor::parse(SchemaFreeIns* sf_ins) {
  if (sf_ins == nullptr || sf_ins->table_row == nullptr)
    return true;
  LOG_FIRST_N(INFO, 10) << "admit for table=" << table_name_;
  auto row = sf_ins->table_row;
  return true;
}

REGISTER_TABLEROW_PROCESSOR(wt_campaign);
}  // namespace build_service
}  // namespace ad
}  // namespace ks
