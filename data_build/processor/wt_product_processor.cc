#include "base/common/logging.h"
#include "teams/ad/data_build/processor/base_processor.h"

namespace ks {
namespace ad {
namespace build_service {
DECLARE_TABLEROW_PROCESSOR(wt_product)

bool wt_product_Processor::admit(SchemaFreeIns *sf_ins) {
  if (sf_ins == nullptr || sf_ins->table_row == nullptr) return false;
  LOG_FIRST_N(INFO, 10) << "admit for table=" << table_name_;
  if (!active_creative_status_admit(sf_ins)) {
    return false;
  }
  auto table_row = sf_ins->table_row;
  auto id = table_row->GetValue<int64_t>("id");
  auto product_id = table_row->GetValue<int64_t>("product_id");
  return product_id > 0 && id == product_id;
}

bool wt_product_Processor::parse(SchemaFreeIns *sf_ins) {
  if (sf_ins == nullptr || sf_ins->table_row == nullptr) return true;
  LOG_FIRST_N(INFO, 10) << "admit for table=" << table_name_;
  auto row = sf_ins->table_row;
  return true;
}

REGISTER_TABLEROW_PROCESSOR(wt_product);
}  // namespace build_service
}  // namespace ad
}  // namespace ks
